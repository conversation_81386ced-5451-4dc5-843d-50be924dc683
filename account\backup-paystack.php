<?php
/**
 * PayStack Integration Backup Script
 * 
 * This script creates a backup of all files related to PayStack integration.
 */

// Include the main functions file
require_once('functions.php');

// Check if user is super admin
if (!super_admin()) {
    die('Access denied. Super admin privileges required.');
}

// Define files with PayStack customizations that need to be preserved
$paystack_customized_files = [
    // Core files with PayStack customizations
    'account/ajax.php',
    'account/functions.php',
    'account/index.php',
    'account/super.php',
    'account/js/cloud.js',
    'account/js/cloud.min.js',
    // Add any other files that contain PayStack customizations
    'config.php'
];

// Backup directory
$backup_dir = MC_CLOUD_PATH . '/paystack_backup_' . date('Y-m-d_H-i-s');

/**
 * Create backup of PayStack customized files
 */
function create_paystack_backup($files, $backup_dir) {
    // Create backup directory
    if (!file_exists($backup_dir)) {
        if (!mkdir($backup_dir, 0755, true)) {
            return ['status' => 'error', 'message' => 'Failed to create backup directory'];
        }
    }
    
    $backed_up_files = [];
    $failed_files = [];
    
    // Create subdirectories as needed
    foreach ($files as $file) {
        $file_path = MC_CLOUD_PATH . '/' . $file;
        $backup_file_path = $backup_dir . '/' . $file;
        
        // Create directory structure if it doesn't exist
        $backup_file_dir = dirname($backup_file_path);
        if (!file_exists($backup_file_dir)) {
            if (!mkdir($backup_file_dir, 0755, true)) {
                $failed_files[] = ['file' => $file, 'reason' => 'Failed to create backup subdirectory'];
                continue;
            }
        }
        
        // Copy the file to backup
        if (file_exists($file_path)) {
            if (copy($file_path, $backup_file_path)) {
                $backed_up_files[] = $file;
            } else {
                $failed_files[] = ['file' => $file, 'reason' => 'Failed to copy file'];
            }
        } else {
            $failed_files[] = ['file' => $file, 'reason' => 'File does not exist'];
        }
    }
    
    return [
        'status' => empty($failed_files) ? 'success' : 'partial',
        'backed_up' => $backed_up_files,
        'failed' => $failed_files,
        'backup_dir' => $backup_dir
    ];
}

// Run the backup
$backup_result = create_paystack_backup($paystack_customized_files, $backup_dir);

// Output the result
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayStack Integration Backup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .success {
            background-color: #dff0d8;
            border-left: 4px solid #5cb85c;
            padding: 15px;
            margin-bottom: 20px;
        }
        .partial {
            background-color: #fcf8e3;
            border-left: 4px solid #f0ad4e;
            padding: 15px;
            margin-bottom: 20px;
        }
        .error {
            background-color: #f2dede;
            border-left: 4px solid #d9534f;
            padding: 15px;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .file-list {
            margin-bottom: 20px;
        }
        .file-list h3 {
            margin-bottom: 10px;
        }
        .file-list ul {
            list-style-type: none;
            padding-left: 0;
        }
        .file-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .file-list li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PayStack Integration Backup</h1>
        
        <?php if ($backup_result['status'] === 'success'): ?>
            <div class="success">
                <h2>Backup Completed Successfully!</h2>
                <p>All PayStack integration files have been backed up successfully.</p>
                <p>Backup location: <code><?php echo htmlspecialchars($backup_result['backup_dir']); ?></code></p>
            </div>
        <?php elseif ($backup_result['status'] === 'partial'): ?>
            <div class="partial">
                <h2>Backup Partially Completed</h2>
                <p>Some files could not be backed up. See details below.</p>
                <p>Backup location: <code><?php echo htmlspecialchars($backup_result['backup_dir']); ?></code></p>
            </div>
        <?php else: ?>
            <div class="error">
                <h2>Backup Failed</h2>
                <p>Error: <?php echo htmlspecialchars($backup_result['message']); ?></p>
            </div>
        <?php endif; ?>
        
        <div class="file-list">
            <h3>Successfully Backed Up Files</h3>
            <?php if (empty($backup_result['backed_up'])): ?>
                <p>No files were backed up successfully.</p>
            <?php else: ?>
                <ul>
                    <?php foreach ($backup_result['backed_up'] as $file): ?>
                        <li><?php echo htmlspecialchars($file); ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
        
        <?php if (!empty($backup_result['failed'])): ?>
            <div class="file-list">
                <h3>Failed Backups</h3>
                <ul>
                    <?php foreach ($backup_result['failed'] as $failure): ?>
                        <li>
                            <?php echo htmlspecialchars($failure['file']); ?>: 
                            <?php echo htmlspecialchars($failure['reason']); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <p>
            <a href="super.php" class="btn">Return to Admin Panel</a>
        </p>
    </div>
</body>
</html>
