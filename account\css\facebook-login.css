/* Facebook Login Button Styling */
.mc-btn-facebook {
    background-color: #E03274 !important; /* Pink: #E03274 */
    color: white !important;
    margin-left: 10px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    max-width: 200px;
    width: auto !important;
    transition: all 0.3s ease;
    flex: 0 0 auto; /* Don't grow or shrink */
    font-size: 15px !important; /* Explicitly set font size */
    font-weight: 500 !important; /* Match other buttons */
    letter-spacing: 0.3px; /* Match other buttons */
    line-height: 40px !important; /* Match other buttons */
}

.mc-btn-facebook:hover {
    background-color: #EA7351 !important; /* Orange: #EA7351 */
}

.mc-btn-facebook i {
    margin-right: 5px;
}

/* Layout adjustments */
.mc-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.mc-text-area {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
}

.mc-text-area.mc-left-align {
    justify-content: flex-start;
    margin-left: 5px;
}

.mc-text-area .mc-text {
    margin: 0 5px;
}

.mc-text-area .mc-btn-login-box,
.mc-text-area .btn-registration-box,
.mc-text.mc-btn-login-box,
.mc-text.btn-registration-box {
    color: #E03274; /* Match the Facebook button color */
    font-weight: bold;
    cursor: pointer; /* Add hand cursor */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .mc-bottom {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: space-between;
    }

    .mc-bottom .mc-btn {
        flex: 0 1 auto;
        min-width: 120px;
        width: auto !important;
    }

    .mc-btn-facebook {
        margin-left: 10px;
        max-width: 200px;
    }

    /* Ensure the main button and Facebook button have equal width */
    .mc-bottom .btn-register,
    .mc-bottom .btn-login {
        flex: 1;
        max-width: calc(50% - 15px);
    }

    .mc-bottom .mc-btn-facebook {
        flex: 1;
        max-width: calc(50% - 15px);
    }
}

@media (max-width: 465px) {
    .mc-bottom {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 10px;
    }

    .mc-bottom .mc-btn {
        flex: 0 0 calc(50% - 5px);
        margin: 0;
        width: calc(50% - 5px) !important;
        height: 45px; /* Slightly taller for better touch targets */
    }

    .mc-btn-facebook {
        margin-left: 0 !important;
    }

    /* Ensure text is centered on small screens */
    .mc-btn-facebook i {
        margin-right: 8px;
    }
}

/* Extra small devices */
@media (max-width: 380px) {
    .mc-bottom {
        flex-direction: column;
    }

    .mc-bottom .mc-btn {
        flex: 0 0 100%;
        width: 100% !important;
        margin-bottom: 10px;
    }

    .mc-btn-facebook {
        margin-left: 0 !important;
        /* Removed font-size override to maintain consistent text size */
    }
}
