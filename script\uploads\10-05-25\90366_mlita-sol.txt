Mlita Solutions (Pty) Ltd
2024/585214/07
202458521407
9308896258

f9BGCKGf
3dba8f15661f
He<PERSON><PERSON> credentials.

URL: https://srv253660298.host.ultaserver.net:8083/
Username: admin
password: VpqxLQLIB^&b^2i!Z4nhVPg5k

SSH information

IP: **************
Username: root
Password: WIaHIpEqTrUJDAgh2f
Port: 22

root@**************:22
sudo chown -R admin:admin /home/<USER>/web/app.masichat.com/public_html
sudo chown -R admin:admin /home/<USER>/web/test.app.masichat.com/public_html

SELECT id, email, credits FROM users WHERE id = 63;

Ml1t@S0l
Ml1t@-
M@51Ml1t@S0l
<EMAIL> vtiger
root@**************:22
Ml1t@S0lut10n5

<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>

********

mc_33491494

<!-- Masi Chat -->
<script id="chat-init" src="https://app.masichat.com/account/js/init.js?id=5836022"></script>
 
https://app.masichat.com/account/payfast.php
https://app.masichat.com/account?tab=membership

https://test.app.masichat.com/account/paystack.php

https://app.masichat.com/account/shopify.php

https://test.app.masichat.com/account/payfast.php
https://test.masichat.com/account?tab=membership

/home/<USER>/web/app.masichat.com/public_html

journalctl -u app.masichat.com -f

Merchant ID: ********
Merchant Key: fhlpo8vmsycgo
Security Passphrase: Ml1taS0lut10ns


open router sk-or-v1-****************************************************************  VSCode's
           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

igrow 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 windsurf 

Requesty sk-+vcQ2LEnTWS9niIWFy9XcJkJop4fwOFs28aFmXhnoE0NMWaEMGsaK8mqvIK5YtWMqxow//TJ+9PvrjFxKBfWUTN/PhhPz6pQTLv4Ma/MFwc=

Gemini -AIzaSyDq6FTGG2928TvgEupjj69GhwNuWW9D8WU

Shopify
Client credentials
Client ID
3ea47a1d476025b90744fd9d8867b875
Client secret
8967173192f5e4413ace3583f3d06a41
Created just now
https://admin.shopify.com/oauth/install_custom_app?client_id=3ea47a1d476025b90744fd9d8867b875&no_redirect=true&signature=eyJleHBpcmVzX2F0IjoxNzQyMzc5OTUzLCJwZXJtYW5lbnRfZG9tYWluIjoibWFzaS1jaGF0Lm15c2hvcGlmeS5jb20iLCJjbGllbnRfaWQiOiIzZWE0N2ExZDQ3NjAyNWI5MDc0NGZkOWQ4ODY3Yjg3NSIsInB1cnBvc2UiOiJjdXN0b21fYXBwIiwibWVyY2hhbnRfb3JnYW5pemF0aW9uX2lkIjoxNTg5NTM4NTR9--b243efb34c1257f02405709731ea9aa4bb1ea81e
            
PayPal 
Display App Name
Masi Chat

Client ID
AdbHXfE6HXMy0G3LVeON8NwVonLhKOEuwDnsWj7PoXRPCFUc23UFzuxRHyYkgB2hgwBxsGUMK5m8F4f5

Secret key 1
EHjYyExhZDBWwTxx1tTR3zmnbpqsT7n43FdNQVWpgvuzjfb9LaRZQsX1t_DwgfNoOH7gg__ksA2AiPnR

sandbox mode.
Client ID
AYACHtBoMTkUXmAXVnGUVEr9NkrxTduiq36Q0RWlOBaFfYDSqUMSIbSQglA9WuTmt84C-jQ24A9i4aa1

Secret key
EB0Rg3ra-gP4esJ9PvrZX_bGRx-wRHqjZzBWH71pz32JqAeiXr9rVuL5_hTj7xoBtJG91py2dZhLv-8V

Paystack 
Live Secret Key
************************************************
Live Public Key
pk_live_71bb6aed7b980b014158135ef6cea6e6a2272940


Test Secret Key
sk_test_5a1b5874dbd15648f6fdc8c29e0d47aa89561174

Test Public Key
pk_test_210bbd3ef6c31cca37cc70a94ff99ee78276deb5


e47b02
Orange: The RGB value rgb(234, 115, 81) converts to the HEX code #EA7351.#ea7351
Pink: The RGB value rgb(224, 50, 116) converts to the HEX code #E03274.#e03274
#EA7351, #E03274
white - 255, 255, 255


QNBsNpfekv-k28qJtP06m
Ef52KS9RMb-kODrXM9C39
UQNEkyU0ET-xYmZIv8dl5
srfFQBjmJW-J4b8j5uumN
vPh4XFuINa-SWDIKhgN3z
LbOrNui5xh-6GslTmdms0
EaJ6gUy4OL-tk8zZfxhAq
JBCMkB8b3Y-ODniFG1WyR

{"paystack_request":{"email":"<EMAIL>","amount":650000,"plan_code":"PLN_fo4mexs0440xv4j","metadata":{"client_reference_
define('CLOUD_SMTP_HOST', 'smtp.mailersend.net');
define('CLOUD_SMTP_USERNAME', '<EMAIL>');
define('CLOUD_SMTP_PASSWORD', 'hoPcd4I19oK1TJq2');
define('CLOUD_SMTP_PORT', 587);
define('CLOUD_SMTP_SENDER', '<EMAIL>');
define('CLOUD_SMTP_SENDER_NAME', 'Masi Chat');

google/gemma-3-27b-it:free







Understanding the Target Market and Customer Needs
Who is our ideal customer?
• What industries, business sizes, and specific roles are most likely to need our communication and bot solutions?
ideally sme b2b but any company that needs customer support 



zixflowc

 /root/.ssh/id_rsa.pub
The key fingerprint is:
SHA256:SPiwtUy805P/Jrz5aYQL3G6LRuM3fsWuzoSQXAYwPI0 <EMAIL>
The key's randomart image is:
+---[RSA 3072]----+
|     .o+.        |
|     oE...       |
|    o =.  o      |
|     O * =       |
|    . B.S. . .   |
|       .=+o.. o  |
|       o =oo.o   |
|        o.O=+..  |
|       ..+=@B.   |
+----[SHA256]-----+
root@srv253660298:~#

ssh <EMAIL>
ssh <EMAIL>

ssh-copy-id <EMAIL>
ssh-copy-id <EMAIL>

[development]
test.app.masichat.com ansible_user=root

[production]
app.masichat.com ansible_user=root



To restart Apache, use sudo systemctl restart apache2 or sudo service apache2 restart, and 
for Nginx, use sudo systemctl restart nginx or sudo service nginx restart. 

sudo systemctl restart apache2
systemctl restart nginx

The authenticity of host 'test.app.masichat.com (**************)' can't be established.
ED25519 key fingerprint is SHA256:3l++y12UkG/IYPmKCjkuHHpOvOk9k8ZVFaQgGykEilc.
This key is not known by any other names
Are you sure you want to continue connecting (yes/no/[fingerprint])? yes
/usr/bin/ssh-copy-id: INFO: attempting to log in with the new key(s), to filter out any that are already installed
/usr/bin/ssh-copy-id: INFO: 1 key(s) remain to be installed -- if you are prompted now it is to install the new keys
<EMAIL>'s password:

Number of key(s) added: 1

Now try logging into the machine, with:   "ssh '<EMAIL>'"
and check to make sure that only the key(s) you wanted were added.

/usr/bin/ssh-copy-id: INFO: Source of key(s) to be installed: "/root/.ssh/id_rsa.pub"
The authenticity of host 'app.masichat.com (**************)' can't be established.
ED25519 key fingerprint is SHA256:3l++y12UkG/IYPmKCjkuHHpOvOk9k8ZVFaQgGykEilc.
This host key is known by the following other names/addresses:
    ~/.ssh/known_hosts:4: [hashed name]
Are you sure you want to continue connecting (yes/no/[fingerprint])? yes
/usr/bin/ssh-copy-id: INFO: attempting to log in with the new key(s), to filter out any that are already installed

/usr/bin/ssh-copy-id: WARNING: All keys were skipped because they already exist on the remote system.
                (if you think this is a mistake, you may want to use -f option)

URL: https://srv253660298.host.ultaserver.net:8083/
Username: admin
password: VpqxLQLIB^&b^2i!Z4nhVPg5k

SSH information

IP: **************
Username: root
Password: WIaHIpEqTrUJDAgh2f
Port: 22

root@**************:22
sudo chown -R admin:admin /home/<USER>/web/app.masichat.com/public_html
sudo chown -R admin:admin /home/<USER>/web/test.app.masichat.com/public_html

SELECT id, email, credits FROM users WHERE id = 63;



ansible all -a "stat /home/<USER>/web/"
/home/<USER>/web/app.masichat.com
ansible all -a "ls -lah /home/<USER>/web/"
ansible all -a "cat /home/<USER>/web/app.masichat.com/public_html/index.php | head -n 20"

grep root /home/<USER>/conf/web/app.masichat.com/nginx.conf
grep root /home/<USER>/conf/web/test.app.masichat.com/nginx.conf

grep root /home/<USER>/conf/web/app.masichat.com/nginx.ssl.conf
grep root /home/<USER>/conf/web/test.app.masichat.com/nginx.ssl.conf

grep root /home/<USER>/conf/web/app.masichat.com/apache2.conf
grep root /home/<USER>/conf/web/test.app.masichat.com/apache2.conf

grep root /home/<USER>/conf/web/app.masichat.com/apache2.ssl.conf
grep root /home/<USER>/conf/web/test.app.masichat.com/apache2.ssl.conf





