Knowledge Base
Search for:
GETTING STARTED
Optimal configuration
Ma<PERSON> is a powerful tool, but its abundance of features can pose a challenge when it comes to quick setup. To streamline the process, we provide a list of the most commonly used features that we advise setting up. By incorporating all these features, you will have access to the most essential and beneficial functionalities.

Activate email notifications from Settings > Notifications. Make sure to activate the following options: Agent email notifications, Sounds admin > Incoming conversations and messages, and Push notifications. For more information on how notifications work, you can click here.
Note, the SMTP is already activated, but you may want to use your own anyway. Configure the SMTP server under Settings > Notifications > SMTP. Afterwards, test the email feature by sending a test email from Settings > Notifications > Send an agent email notification.
Navigate to Settings > Messages and set up the follow-up message and offline message.
Navigate to Settings > Miscellaneous > Performance optimization and turn off any features that are not being used.
In case you are utilizing the chatbot, make sure to check out the optimal configuration here.
Having problems?
Chat widget not displaying
You may not see the chat because you have disabled it in the settings area. To fix this, visit the settings section and deselect all options related to that: Chat > Manual initialization, Chat > Login initialization, Chat > Hide chat outside of office hours

Blocking zoom on iOS devices
When using the chat on iPhones the textarea is automatically zoomed when the user’s start typing a new message. To stop the zoom Enter the code below into the <head> area of all the pages that include the chat.

<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no" />
Conversations are not visible to administrators or agents
The conversations may not be showing due to the following reasons.

The agent has been given a department, yet the conversations have not been assigned to that specific department.
One or more of the following settings have been activated: Miscellaneous > Routing, Miscellaneous > Queue, Miscellaneous > Hide conversations of other agents
You are using the chatbot and the Artificial Intelligence > Human takeover option has been activated.
For cases 1 and 2, make sure to log in with the correct admin/agent or check your admin/agent profile to ensure that there are no departments assigned. In case 3, please check the archived conversations.

Membership
The membership tier is based on the number of messages sent each month and agents quotas. You buy messages quotas and agents quotas and the cost is linked to both the number of messages sent and the number of registered agents and admins.
All messages are counted: chat messages from agents and users, direct messages, messages from users and agents, messages from the Dialogflow and Open AI chatbot.
When the monthly message count exceeds the subscription quota or the membership is expired and not renewed, the admin area is disabled. The chat still works and receives messages, only the admin area is disabled. No messages are lost.
When you buy a paid membership, and the membership quota is reached, the account is suspended.
The free quota is available only in the free plan. The free plan quotas are 100 messages a month, 1 agent, maximum 10 users.
Free membership accounts that are older than 6 months and have been inactive for the past 6 months are automatically deleted.
CONVERSATIONS
Manage conversations
Conversations have a total of four different statuses: mark as read, archive, delete and restore. You can manage the status of a conversation by opening it in the conversations area and then clicking any of the corresponding icon buttons in the top right of the conversation window.

Search for conversations
You can for conversations by department ID, assigned agent ID, conversation title, conversation ID, message text, message attachments name, user first name, user last name, user email

Information
When you empty the trash, all the conversations in the trash are permanently deleted.
When a user sends a new message to an archived or trashed conversation, the conversation is automatically restored and will now be visible in the Inbox area.
Trashed conversations are deleted automatically after 30 days.
When a user is deleted, all the conversations and messages are permanently deleted too.
An agent can delete their messages by opening the message menu and clicking Delete. The message menu becomes visible when you hover the mouse cursor over the message.
The left conversations list use auto-pagination, which is limited to 100 results per scroll.
Text editor and automated messages features
The text editor of the admin area and automated messages(example: welcome and subscribe messages) can be used to create stylized messages:

Links formatting — All text links are automatically converted to clickable hyperlinks. To set the link name, append the following string to the URL: #mc-example. Replace example with your desired link name and white spaces with —.
Text formatting — The editor also supports text formatting syntax:
To make text bold, surround it with *: *your text*.
To make text italic, surround it with __: __your text__.
To make text strikethrough, surround it with ~: ~your text~.
To insert a single-line code comment, surround it with`: `your text`.
To insert a code block, surround it with “`: “`your text“`.
HTML and other code languages — For security reasons, no HTML, JavaScript (JS), or other code languages are permitted. However, you can use HTML snippets by utilizing custom rich messages (discussed below).
To insert a line break into a message, use the keyboard combination SHIFT + ENTER or CTRL + ENTER.
Merge fields
Merge fields are strings replaced by external values when used. Merge fields can be used in any message or automated message, including chatbot messages.

Code	Description
{user_name}	Full name of the active user.
{user_email}	Email of the active user.
{agent_name}	Full name of the active agent.
{agent_email}	Email of the active agent.
Rich messages
Rich messages are special messages with interactive features like buttons, dropdowns, or inputs. They allow an agent to request information from the user via a user input form or to display interactive contents. Rich messages can be inserted into a chat message using shortcodes. Shortcodes accept various parameters like title and description. The available rich messages are listed below.

How it works:
1

Create and send
Create a rich message by inserting the shortcode into the text editor of the admin area. Customize all of the parameters with your information and send your message.


2

Message is displayed
When a shortcode is used, the user sees the rich message (not the shortcode) and can select or enter the required information to complete the form submission.


3

User’s response is submitted
Once the rich message form has been filled out and sent by the user, a success message is shown and the form data is saved.


Rich Messages
Name	Shortcode	Description
Card	[card image=”URL” header=”TITLE” description=”Lorem ipsum dolor sit amete” link=”URL” link-text=”Purchase” extra=”$599″ target=”_blank”]	Call-to-action card with an image, title, description, link, and more.
Slider	[slider image-1=”URL” header-1=”TITLE” description-1=”Lorem ipsum dolor sit amete” link-1=”URL” link-text-1=”Purchase” extra-1=”$599″ image-2=”URL” header-2=”TITLE” description-2=”Lorem ipsum dolor sit amete” link-2=”URL” link-text-2=”Purchase” extra-2=”$599″ target=”_blank”]	Slider of call-to-action cards with an image, title, description, link, and more. You can add up to 10 slides.
Slider images	[slider-images images=”URL,URL,URL”]	Slider of images.
Chips	[chips options=”A,B,C”]	List of buttons.
Buttons	[buttons options=”A,B,C”]	List of buttons.
Select	[select options=”A,B,C”]	Dropdown list of options.
Inputs	[inputs values=”A,B,C” button=”Send now”]	List of text inputs.
Email	[email name=”true” last-name=”true” phone=”true” phone-required=”false” placeholder=””]	Form to collect the user’s email and phone number. All attributes are optional. Follow up settings used as default values. Add the attribute required-messaging-apps=”true” to force users to provide their email and phone on messaging apps. Merge fields are supported.
Timetable	[timetable]	Timetable.
Articles	[articles link=”https://masichat.com/knowledge-base/#human-takeover“]	Articles with search area. The link attribute is used as fallback message for Facebook Messenger, WhatsApp, Telegram messages.
List	[list values=”A,B,- C,- D,E” numeric=”true”]	Text list. Prefix an item with the – char to make it an inner item.
List double	[list values=”A:X,B:Y,C:Z”]	Text list with titles.
List image	[list-image values=”URL:A,URL:B,URL:C”]	Text list with titles and images.
Table	[table header=”A,B,C” values=”A:B:C,A:B:C,A:B:C”]	Table.
Button	[button link=”https://masichat.com/” name=”Click here” target=”_blank” style=”link”]	Display a link or open an article. The attribute target=”_blank” is optional and open the link in a new window. The attribute style=”link” is optional and change the button design. To open an article on click the link value must be #article-ID, replace ID with the article ID.
Video	Display a YouTube or Vimeo video. The value of the attribute type can be youtube or vimeo. The attribute id is the ID of the video, get it from the URL. The attribute height is optional and sets the video height in px.
Image	[image url=”https://masichat.com/media/admin.png”]	Image.
Share	[share fb=”https://masichat.com/” tw=”https://masichat.com/” li=”https://masichat.com/” pi=”https://masichat.com/” wa=”https://masichat.com/”]	Social share buttons.

Show more
Commas
If your texts include commas you need to replace all commas with the characters \,.

Global parameters
All of the rich messages support the following parameters:

Parameters	Description
id=”123″	The ID of the rich message (used also to save the JSON data).
title=”ABC”	The rich message title.
message=”ABC”	The rich message description that appears underneath the title.
success=”ABC”	The message that appears when the user completes and sends the rich message. The user input is appended to this message.
settings=”ABC”	Extra field for optional extra values.
Use rich messages in the chatbot
You have to create a Dialogflow chatbot, then simply enter the rich message shortcode into the text response of the Intent.

Rich message translations
To translate a rich message string, the original rich message text must be in English, add the exact English text and its translations in Setting > Translations.

Show a rich message on chat initialization
To display a rich message, such as a list of buttons, when a user initiates a chat for the first time, insert the rich message shortcode into the welcome message.

Custom rich messages
You can create custom rich messages with your own custom content by going to Settings > Miscellaneous. Currently, custom rich messages are static and there are no interactive options available as there are with shortcodes. However, you can insert custom HTML codes.

HTML codes
When creating a custom rich message, you can use the following codes:

Code	Description
<a href=”https://www.google.com” target=”_blank” class=”mc-rich-btn mc-btn”>Click here</a>	Link with button design.
<a href=”https://www.google.com” target=”_blank” class=”mc-rich-btn mc-btn-text”>Click here</a>	Link.
<div class=”mc-image”><img src=”https://via.placeholder.com/1500×600″ class=”mc-image” /></div>	Image that zoom on click.
Built-in messages
The built-in messages are pre-programmed messages sent automatically by Masi Chat. You can find them by going to Settings > Messages.

Welcome message
Send a message to new users when they visit the website for the first time.

Text formatting is supported.
Merge fields are supported.
Rich messages are supported.
The welcome message is not sent to Slack.
Conversations containing only the welcome message (and no response) are automatically archived.
Follow up message
If no agents respond within the specified time interval, a message will be sent to request the user’s details, such as their email.

Text formatting is supported.
Merge fields are supported.
You can send a confirmation email to the user by filling in the Follow-up Email fields.
If the delay is not set, a dynamic time interval is utilized and it is determined as follows: If Settings > Miscellaneous > Office hours is configured, and the current time falls within the defined office hours, or if at least one agent is online, then the delay will be set to 15 seconds. In all other cases, the delay will be set to 5 seconds.
Follow-up messages are sent a maximum of once every 24 hours.
If the user provides an email address and the newsletter feature is enabled, the email address will be subscribed.
The follow-up message is sent only to users without email address.
If the chatbot’s human takeover feature is activated, the follow-up message is only sent during human takeover.
Rating
Display the feedback form to rate the conversation when it is archived.

The rating message is not compatible with the messaging channels.
The rating is visible in the right panel of the conversations area and in the agent ratings report of the reports area.
Offline message
Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.

Text formatting is supported.
Merge fields are supported.
To learn more about the office hours option, please click here.
The offline message is sent to the same user maximum 1 time per hour.
If the chatbot’s human takeover feature is activated, the offline message is only sent during human takeover.
If you or any other agent is online using Slack, the offline message will not be sent.
Privacy message
Present a privacy message accompanied by Accept and Decline buttons. The user’s approval by clicking on the Accept button is required to start using the chat. This feature ensures privacy policy enforcement and GDPR compliance.

The privacy message is not shown if the Settings > Users > Require registration option is enabled.
The privacy message is also sent to messaging channels like WhatsApp, but the user does not have the option to approve or decline the privacy policy. The messaging functionalities are not blocked either. The message is sent after the user initiates the conversation by sending their first message.
Pop-up message
Show a pop-up notification to all users.

The popup message is always shown until the user manually closes it; then it stays closed.
Attachments
Here, you can access information regarding the uploading of files and the message’s attachments.

The following image formats are displayed automatically:.jpg, .jpeg, .png.
For instructions on sending attachments with a chatbot, click here.
Information
Here, you can access information regarding a variety of features associated with conversations.

Tags
Tags enable more efficient organization and grouping of conversations. Please refer to the information below for further details.

You can manage the tags from Settings > Admin > Tags.
Tags can be assigned to conversations through different methods: via the admin area by navigating to Settings > Automations > More, through Dialogflow actions, and by inserting the JS variable MC_DEFAULT_TAGS into a page showing the chat or into a web page displaying the chat or the tickets panel.
To locate conversations with specific tags, simply enter the tag names into the search bar located at the top-left corner of the admin conversations area, or you can select the desired tag from the dedicated filters menu on top-left. The tags filter is visible only if at least one tag is assigned to a conversation.
Check the Starred tag option to display the starred tag icon in the conversations area. This allows you to select conversations assigned to the first tag with one click. The first tag on top is always the starred tag.
You can disable the tags from Settings > Admin > Disable tags.
Notes
Notes allow users to add comments to conversations that are only visible to agents and admins.

If you are usign OpenAI, we suggest enabling the note data scraping option.
Transcript
The full conversation can be sent to the user by the agent or admin as a transcript file.

Agents and admins can send conversation transcripts to users by clicking the Transcript button in the top-right corner of the admin’s conversation window.
Agents and admins can automatically send the transcript to the user when the conversation is archived by using the close message available at Settings > Messages & Forms > Close message.
The transcript can be sent to the user only if the user has an email address.
If the conversation has been translated, the transcript will also include the translated messages.
Miscellaneous
The date and time format is automatically detected based on the browser’s language settings.
USERS
Manage users
Manage users from the Users area in the left menu of the admin area.

Import users
You can import users from Settings > Users > Import users. Only CSV files are supported. You can download an example CSV file here. In the example file, the first row is the header and the columns Height and Hair color are custom user fields added from Settings > Users > Custom fields.

Search users
You can search users by name, surname, email, and custom fields.

Delete users
You can delete a user by opening the User edit box and then clicking Delete user. To delete multiple users at once, select the users you want to delete from the Users table and then click the top right Delete icon.

When a user is deleted, all of their conversations and messages are automatically deleted permanently.
The conversation attachments will be deleted permanently.
If a user of a deleted user come back to the website, a new user is automatically created.
Visitors are automatically deleted every 24 hours.
User table extra columns
To display additional columns in the user table go to Settings > Admin > Users table additional columns and add the new columns. The value of each column is the slug of the user detail or extra user detail you want to display. The slug is lowercase and with spaces replaced by the char –. For example, the slug of the extra user detail “Date of birth” is “date-of-birth”.

User types
Type	Description
user	A “user” is any user with an email.
lead	A “lead” is any user with no user details, who is automatically registered, and with at least one conversation.
visitor	A “visitor” is any user who has not started a conversation. Note: Visitors are automatically deleted every 24 hours.
Agents
Manage, create, and delete agents and admins from the Users area.

Configure agents’ privileges and permissions from Settings > Admin > Agent privileges.
You can create a supervisor from Settings > Admin > Supervisor. The Supervisor is a special agent with specific privileges, it must be an administrator. You can add multiple supervisors by adding comma separated admin IDs.
To create an agent or ad admin, go to the users area and click the button Add user on the top right.
Only agents and admins can log in the Masi Chat admin area.
Collect user details
You can gather user details, such as their name and email, through various methods:

With a pre-chat form using the registration form.
With the Follow-up message.
With the chatbot flows.
Registration
The registration form is a pre-chat form that requires the user to enter specific information before starting the chat.

You can use the registration form as a pre-chat form by limiting the information requested to the user to only the user’s email address or the user name, for example.
The log-in form is shown only if the email field is enabled.
You can automatically log in a user via URL parameters.
Users Info
Miscellaneous
New users are automatically displayed in the user table in real time.
To view online users enable Settings > Users > Register all visitors.
To receive the online user notification you have to enable Settings > Notifications > Online users notification, Settings > Miscellaneous > Push Notifications and Settings > Users > Register all visitors (activated by default). The feature covers all user types, including visitors and leads. Notifications are only sent once per user within a 24-hour period.
Agents and admins can set their status to online or offline from the bottom-left profile panel. If the option Settings > Notifications > Away mode is active, the offline status is activated automatically when the agent or admin has been inactive in the admin area for at least 10 minutes. Inactivity is defined as not performing any mouse clicks, movements, or key presses. The automatic offline status feature is not enabled on mobile devices.
The users table use auto-pagination, which is limited to 100 results per scroll.
Login verification URL
The Login Verification URL feature allows Masi Chat to authenticate external logins and automatically register and log in users from an external source. You can configure this feature under Settings > Users > Login Verification URL in Masi Chat.

When a user attempts to log in, Masi Chat will query the specified URL. The URL should return a JSON object with the following structure:

{
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "profile_image": "https://masichat.com/user.svg",
        "department": null,
        "password": "",
        "details": [
            {
                "slug": "location",
                "name": "Location",
                "value": "New York, United States"
            },
            {
                "slug": "country_code",
                "name": "Country code",
                "value": "America/New_York"
            }
            ...
        ]
    }
Required Fields:

first_name (required): The first name of the user.
email (recommended): For proper identification and communication.
profile_image (optional): A URL to the user’s profile image.
Additional Details:

You can include custom user details under the details array.
Ensure your external server can handle authentication securely and return the appropriate JSON format.
Security Tip:

Protect your external URL with appropriate authentication mechanisms, such as API keys or OAuth, to avoid unauthorized access.

Assign an agent to a conversation
You can assign an agent to a conversation in several ways:

Via the Q&A set data feature.
Via the flows actions feature.
Via the queue or routing features.
By enabling the Settings > Chat > Agents menu option. In this case, the user will be required to select an agent before starting a new conversation.
Via Settings > Automations > More.
SETTINGS
Office hours
You can set the office hours timetable from Settings > Miscellaneous > Office hours. Office hours are used for:

Sending the offline message.
Disabling and hiding the chat during out-of-office hours.
Disabling the chatbot during regular office hours and enabling it during out-of-office hours.
More information
Blank values in the timetable settings are considered out-of-office hours.
The office hours are in UTC format. Your UTC is generated automatically when you click the field Settings > Miscellaneous > UTF offset. To manually get the UTC offset of your area, go to: https://browserspy.dk/ or wikipedia.org/wiki/List_of_UTC_time_offsets and copy the offset (e.g. for UTC −12:00, enter -12). Only integers are accepted. If your offset is not an integer (e.g. UTC -12:30 or UTC -12:45), try searching for an alternative UTC offset. If you can’t find an integer offset, you will need to manually adjust the times in the office hours table to fix the gap.
The date and time format of the timetable matchs automatically the one used in the country of the browser language of the user.
Articles
Knowledge base articles provide instant answers to customers to help reduce customer support volume. You can access the articles from the left Masi Chat menu.

Display articles
The articles can be shown in the chat dashboard by enabling them from Settings > Articles > Display in dashboard.
Alternatively, articles can be shared in any chat conversation via the rich message shortcode, [articles].
URL rewrite
You can enable the URL rewrite from Settings Articles > URL rewrite. The default URLs of Masi Chat are not user-friendly and they include parameters. This rewrites the URLs of the categories and article pages. The categories URL changes from articles.php?categories=ID to articles/category/ID. The article URL changes from articles.php?article_id=ID to articles/ID.

Change articles with the name of your articles page and set the articles page URL in Settings > Articles > Articles page URL. You can find the Articles page URL at https://app.masichat.com/account/?tab=installation

More information
If you use categories, all the articles must be assigned to a category.
If there is at least one translated article in the user’s language, only the translated articles are displayed. Otherwise, all articles are displayed in the original language.
Force the articles page to be shown in a specific language by adding the URL parameter lang=LANGUAGE-CODE. Replace LANGUAGE-CODE with the two-letters language code.
If the multilingual via translation feature is activated, articles and their categories will be translated automatically.
Articles are synchronized automatically with the Dialogflow knowledge base if the Artificial Intelligence app is installed.
You can create an internal chat link to an article with the button rich message.
You can create a link to a specific article using its URL. Example:https://articles.app.masichat.com/5836022/manage-conversations .
You can create a link to an article category using its URL. Example:https://articles.app.masichat.com/5836022/category/conversations.
The articles are always included into the training of your OpenAI chatbot.
If some block is not saved, e.g. text block, disable all browser extentions and try again.
To hide the chat widget insert the following code into the articles page: <script>var MC_DISABLED = true;</script>.
Language and translations
Masi Chat is fully multilingual and provides powerful features to detect the user’s language on the fly.

Edit translations
To edit the languages of both chat and admin, go to Settings > Translations. Some settings, such as e-mail contents, are directly translatable and therefore do not need to be translated here.

Chat language
Masi Chat is already translated into 41 languages.

Go to Settings > Chat and check the Language option. Set it to auto to automatically use the chat language of the user’s browser or the language saved in the user profile.
Multilingual Admin
To translate the admin area follow the steps below:

Translate the texts in your language from the Settings > Translations.
To set the admin area language you have three options:

Activate the option Settings > Admin > Automatically translate admin area. This feature automatically translate the admin area to match the agent profile language or the agent browser language.
Departments
Departments give you the power to distribute conversations and assign various agents to specific departments. For example, you can create a department entitled “Sales” and assign specific conversations to that department. To start using departments, follow the steps below:

Go to Settings > Miscellaneous and add, delete and manage the departments. After saving, reload the page.
Go to Users > Agents and edit an agent, you will see a new field where you can set the department of the agent.
Reload the page and you’re done! In the Conversations area, you will now see an option to set the department.
Settings
Display in dashboard Displays the departments’ list in the chat dashboard and force users to choose a department before starting a conversation.
Display images Displays the department image instead of the department color.
Display in conversation list Displays the department color in the conversation list of the admin area.
One conversation per department Restrict users from opening multiple conversations within the same department, allowing only one conversation to be active per department.
Label Replace the label Departments (plural) with another text. The name is displayed in the admin and tickets area.
Label single Replace the label Department (singular) with another text. The name is displayed in the admin and tickets area.
Dashboard title Set the title of the chat dashboard list. Default: Departments.
How it works
Agents and admins with no assigned department always see the conversations of all departments.
Agents and admins with an assigned department can only access conversations, users, and agents within that department.
When a conversation is assigned to a new department, an email notification is sent to all of the agents assigned to the new department.
The chatbot can assign a department to the active conversation through the Q&A set data feature, the flows actions feature, or Dialogflow actions.
How to assign a department to a conversation
You can assign a department to a conversation in several ways:

Via the Q&A set data feature.
Via the flows actions feature.
Via Settings > Miscellaneous > Departments settings > Display in dashboard. In this case, the user will be required to select a department before starting a new conversation.
Via Settings > Automations > More.
Queue and routing
When the queue is activated via Settings > Miscellaneous > Queue, or routing is activated via Settings > Miscellaneous > Routing, Masi Chat automatically assigns the users conversations to all available agents proportionately.

Only online agents are counted as “available” agents and will receive new conversations.
Conversations are assigned proportionally between all online agents.
Admins are not included; admins always see all the conversations.
Agents must archive a conversation to mark it as completed; this will automatically give them access to the next conversation in the queue. A conversation is active if it’s not deleted, or archived.
Agents can switch their status between online and offline by hovering over their profile image and then clicking the label of the profile pop-up at the bottom-left of the admin area.
Agents can only search and filter their conversations.
Agents can only view their conversations; however, they can see all of the conversations of a single user.
To enable agents to view all unassigned conversations, activate Settings > Miscellaneous > Hide conversations of other agents and View unassigned conversations.
Queue and routing are compatible with the departments.
If human takeover is active, the queue or routing is activated only on human takeover.
More information – Queue only
When the queue is activated users enter into a queue automatically when an agent’s chat limit is reached. When a user enters the queue, a message with the current position in the queue and the estimated waiting time is displayed. Masi Chat automatically assigns the conversations to all available agents proportionately. When an agent marks a conversation as completed (by archiving it), the queue is updated and a new conversation is received.

If a user is in the queue and leaves (e.g. by closing the browser) for more than 1 minute, the conversation is saved; however, once the user comes back, the queue is reset and the user will lose their previous position. If the user leaves, the conversation remains unassigned and therefore invisible to agents, but only visible to admins.
You can use the following merge fields in the queue message: {position}, {minutes}. They will be replaced by the real values in real-time.
The waiting time is displayed in minutes and is calculated as follows: queue position X response time = waiting time. For example, if a user is 5th in the queue, and the response time has been set to 4 minutes (via Settings > Miscellaneous > Queue), then the total wait time displayed to the user will be 20 minutes.
When the sound option is active, a sound is played when it’s the user’s turn.
For conversations started from messaging apps like WhatsApp, it is not possible to respect the limit of conversations per agent, all conversations will be immediately and proportionally assigned to an agent.
Use the offline message to prevent the chat from showing the queue update message to the user.
To test the queue, follow the steps below:

To simulate multiple users and agents, open the chat in multiple different browsers (e.g. Opera, Firefox, Brave, Chrome, etc.). Each browser can simulate two users/agents: one in normal mode and one in “private” or “incognito” mode.
To reset the chat and start a new user session, open the browser console, enter MCF.reset(), and press ENTER.
More information – Routing only
When the routing is activated Masi Chat automatically assigns the users conversations to all available agents proportionately.

If the Routing > Disable online status check option is active, The conversations are distributed proportionally among all agents, regardless of whether they are online or offline.
When an agent comes back online after being offline, all unassigned conversations are automatically assigned to them.
When routing is active agents can manually route conversations to other agents from the right panel of the conversations area.
If the conversation is archived and the user reopens it in the future by sending a new message, if the assigned agent in the conversation is offline, the conversation is assigned to another agent.
Manual routing
When the routing is activated via Settings > Miscellaneous > Hide conversation of other agents agents see only their own conversations and can select the unassigned ones.

Agents menu: displays the agents’ menu to assign the active conversation to another agent.
Routing if offline: if the conversation is archived and the user reopens it in the future by sending a new message, if the assigned agent in the conversation is offline, the conversation is assigned to another online if there is at least one, otherwise to no agent.
View unassigned conversations: allow agents to view the unassigned conversations, when an agent replies the conversation is automatically assigned to him and the conversation is removed in real-time from the admin area of the other agents. Check this option to enable the manual routing.
Email piping (coming soon)
Email piping lets you, your agents, and your users reply to chat messages via email.

Activation
To activate the email piping go to Settings > Notifications > Email piping and enter your POP3/IMAP email server information, then set the email address to use for the email piping in Settings > Notifications > SMTP and you’re done! The email address must be the one to which the email piping server connects to. Masi Chat will send all emails from this email address and you, your agents, and your users will reply to this email address.

Information
The email address of Settings > Notifications > SMTP > Sender email must match the one used by the email piping server.
The Notifications > User email notifications setting is enabled automatically when email piping is active. When agents reply in Masi Chat, emails are always sent to the user.
If you’re using the Artificial Intelligence app, and the settings Settings > Artificial Intelligence > Human takeover is active, no emails are sent if the chatbot knows the answer.
If the Convert all emails option is active:
All emails sent to your inbox will be converted to chat messages, mind that all emails, including spam, promotional emails and more would be wrongly converted too.
Email sent by email addresses of agents and admins will be ignored, use another email address for testing.
Each email sent directly to the email piping address will generate a new user conversation.
You can not use the same email address used for the email piping to reply. All your Masi Chat agents and admins must use a different email address.
Agents must reply via email from the same email address registered in Masi Chat.
The email is sent to the agents only if they are offline. If they are online no emails are sent.
For Google Gmail and Google Workspace emails, you need to replace your Google password with a Google app password, details on https://support.google.com/accounts/answer/185833 (Create & use App Passwords section). Enter imap.gmail.com as host and 993 as port.
The Delimiter option add a text at the top of all emails: ### Please type your reply above this line ###. This text tells Masi Chat to delete all the content below it and it’s useful to cut out all of the reply quotes. Activate it if you see duplicated messages in the chat. If this option is active, you and your users can not use the string ### in the emails.
Email attachments are supported.
In order for email piping to work, email replies must contain the Masi Chat recipient’s name. Ex. Masi Chat | MC2457-4734 <<EMAIL>>. All major email clients like Outlook Web, Outlook, Gmail, Yahoo Mail, support the recipient’s name by default via chatbot reply or reply all buttons.
Because of the nature of how emails work, each email client uses their personal codes, delimiters, and automatic strings, and more. For this reason, some chat messages converted from emails can contain invalid texts. We are working to optimize as many email clients as possible, for now, the following email clients are optimized and should not return any invalid text: Outlook Web, Outlook, Gmail, Yahoo Mail.
Email piping supports departments, if the user’s conversation is assigned to a department, the email notifications are sent only to the agents assigned to that department.
If the email includes the reply-to attribute, it will serve as the primary user email address and notifications will be sent to that email.
Direct messages
Direct messages allow you to send а single chat message, email, or text message, to a single user or several users. Read the information below to understand how it works.

To send a message go to the Users area and click the direct message icon. Direct message buttons are also available in the profile box of the user.
To send a message to all users, enter All in the User IDs field.
To send a message to a group of users, enter their IDs in the User IDs field. You can check the users and then click the direct message icon to enter the IDs automatically.
All message types support merge fields.
Direct chat message
The chat message is sent to the active conversation, if any, otherwise to a new conversation.
If the Settings > Notifications > User email notifications option is active, all users with an email are notified via email.
If the Settings > Notifications > Text message notifications > Active for users option is active, all users with a phone number are notified via text message.
If the Settings > Notifications > Push notifications option is active, a push notification is sent to all users.
The message is also sent to messaging apps such as WhatsApp and Messenger if the user is connected to them.
To send a message to all users of a single messaging app enter one of the following strings: whatsapp, messenger, instagram, telegram, twitter, zalo, wechat, viber, line. Enter tickets to send a message only to the users who created a Masi Chat ticket via the Tickets app.
Direct email
Email header and email signature are automatically included in all emails, set them from Settings > Notifications.
The HTML language is supported.
The subject supports merge fields.
Direct text messages
To enable direct text messages you must enable the SMS in Settings > Notifications > Text message notifications.
Direct WhatsApp template messages
For more details click here.
Automations
Automations allow running multilingual automatic tasks when conditions set by you are met.

To delete a condition, set it to empty. To disable an automation, delete all conditions.
Automations are sent only 1 time to users.
If you used an automation in the past, and users already received it, you need to delete the automation and create a new one to show it to the users.
Repeat only works if used in conjunction with date time.
When date time is used in conjunction with the criteria is exactly, the value must not contain hours and minutes (hh:mm). Ex. 25/10/2021 (25/10/2021 10:30 will not work).
The Settings > Users > Register all visitors option must be active if there are message automations that are executed on page load.
City, countries, languages work automatically only if both the settings Settings > Users > Register all visitors and Settings > Users > Full visitor details are enabled.
Cities work only if the user detail location is set and equal to city, country, or if the user detail city is set.
Countries work only if the user details country_code, or country is set, or if the user detail location is set and equal to city, country. The country name must be in english.
languages work only if the user details browser_language, or language is set.
A visitor is a Returning visitor only if it visits the website again after 24h or more.
The pop-ups appear only if the chat is closed, and they overwrite the default pop-up. You can check the message fallback option to send a message instead of showing the pop-up if the chat is open.
You may need to enable Settings > Users > Register all visitors if you want to send an automated message to new users.
Chat messages and popup fallback messages are sent only if the last user or agent message of the conversation is older than 10 minutes. This feature prevents unwanted automated messages to be sent during an agent-user conversation.
The Custom variable condition check for the JavaScript variables with the given names and values. For example, the condition example=ABC is met if into the chat’s page there is this JavaScript code: var example = “ABC”;. Add multiple variables separated by commas.
Automations are not compatible with the messaging apps like WhatsApp, Messenger, Instagram, Telegram, Twitter, Zalo, WeChat, Viber, Line.
Newsletter
Follow the steps below to complete the synchronization with your newsletter service. The user is subscribed in the following cases: registration form, follow-up message, subscribe message, email shortcode.

Mailchimp
To get the Key, follow the docs at https://mailchimp.com/help/about-api-keys/.
To get the List ID, follow the docs at https://mailchimp.com/help/find-audience-id/.
Brevo
To get the Key, go to https://account.brevo.com/advanced/api.
To get the List ID, go to https://my.brevo.com/lists.
SendGrid
To get the Key, go to https://app.sendgrid.com/settings/api_keys.
To get the List ID, enter in SendGrid and click Marketing > Contacts(https://mc.sendgrid.com/contacts), then click on a list, or create a new one. The last part of the URL is the list ID. Ex. https://mc.sendgrid.com/contacts/lists/8558c1e7-3c99-4428-a68f-78df2e437f8e (the list ID is 8558c1e7-3c99-4428-a68f-78df2e437f8e).
Elastic Email
To get the Key, enter in Elastic Email, then click Settings from the top right profile menu. Click Create Additional API key (https://elasticemail.com/account#/settings/new/create-api), set a name and choose Plugin or Full access, or Custom and make sure the permission Contacts is set to View & Modify.
The List ID is the list name. Important! Existing list’s or segment names must not have any spaces in them.
Campaign Monitor
To get the Key, enter in Campaign Monitor, then click Account settings from the top right profile menu. Click API keys > Generate API key.
To get the List ID, enter in Campaign Monitor, then click List and subscribers from the top menu. Select a list and then click Settings from the left menu and copy the List API ID.
HubSpot
To get the Key, enter in HubSpot, then get go to Settings > Integrations > Private apps and create a new app.
Enter app name, then open the Scope tab and add the following scopes: crm.lists.write, crm.lists.read, crm.objects.contacts.read, crm.objects.contacts.write.
Click Create app and copy the token. Paste the token into the Key field of Masi Chat.
To get the List ID, enter in HubSpot, then get it from Contacts > List > List details.
Moosend
To get the Key, enter in Moosend, then click Settings > API key.
To get the List ID, enter in Moosend, then get it from Audience > Email lists.
GetResponse
To get the List ID, enter in GetResponse, then get it from Lists > Your list > Settings(https://app.getresponse.com/lists). Copy the List token value.
To get the Key visit https://app.getresponse.com/api.
ConvertKit
To get the Key, enter in ConvertKit, then get it from Settings > Advanced > API secret.
To get the List ID, enter in ConvertKit, then get it from Grow > Landing Pages & Forms. Open you form and copy the numeric part of the URL. For example the ID of the form with URL https://app.convertkit.com/forms/designers/3003412/edit is 3003412.
ActiveCampaign
To get the Key, enter in ActiveCampaign, then get it from Account settings > Developer.
To get the List ID, enter in ActiveCampaign, then get it from Left menu > Lists or Left menu > Contacts > Lists. Open your list and copy the list ID in the URL. For example the ID of the list with URL https://masichat.activehosted.com/app/contacts/?listid=1&status=1 is 1. The list ID must be in this format: domain:list-ID. The domain is the first part of your dashboard URL. For example the domain of https://masichat.activehosted.com/ is masichat and the final value to insert in Masi Chat is masichat:1.
MailerLite
To get the Key, enter in MailerLite, then get it from Left menu > Integrations > MailerLite API.
To get the List ID, enter in MailerLite, then get it from Left menu > Subscribers > Groups. View a group and copy the Group ID from the URL (e.g. ….group=*****************…).
Mailjet
To get the Key, visit https://app.mailjet.com/account/apikeys, or enter in Mailjet, then get it from Account settings > Account settings.
To get the List ID, enter in MailerLite, then get it from Contacts > Contacts lists > Your list. Get the List ID by clicking the ? icon next to the lists’s email, e.g. 1202546.
Sendy
To get the Key, enter the Sendy settings area and copy the API KEY.
To get the List ID, enter your brand, then click View all lists and copy the list ID. The list ID setting must also include your Sendy full URL. Enter the URL plus the list ID spearated by the char |, e.g. https://example.com|TDf6o892Mx11VXGC51ui567u.
SendFox
To get the Key, enter in SendFox, then get it from Settings > API > Personal Access Tokens.
To get the List ID, enter in SendFox, then get it from Audience > Lists. Open a list and copy the ID from the URL (e.g. the List ID of the URL https://sendfox.com/dashboard/lists/489151/contacts is 489151).
More settings
Saved replies
Saved replies, also known as canned messages, refer to a collection of pre-written messages that agents can quickly access and employ in the chat editor.

Saved replies can be printed by typing # followed by the saved reply name, plus a space.
The saved replies pop-up can be opened by typing ##.
Use \n to do a line break.
If you are using the Dialogflow chatbot, the saved replies panel search will include results from Dialogflow Intents.
You have the option to add all Dialogflow Intents to the saved replies list by using the option at Settings > Artificial Intelligence > Google > Add Intents to saved replies.
NOTIFICATIONS
Notifications
To understand how notifications work and when they are sent read the information below. If you think notifications are not working, most probably you’re just testing them in the wrong way, please read the information below before asking for support.

Email notifications
Both agents and users can receive an email notification when a new message is received.

Email notifications for admin and agents
When a user sends their first message, if the conversation is assigned to a department, an email is sent only to the agents assigned to that department, if the conversation is assigned to a specific agent, an email is sent only to that agent, otherwise an email is sent to all agents who are not online at the moment. Subsequent emails are sent only to the last agent in the conversation.
Email notifications are sent only if the last agent in the conversation is offline.
If you’re using the Artificial Intelligence app, and Settings > Artificial Intelligence > Human takeover is active, no emails are sent if the chatbot knows the answer.
To prevent admins from receiving email notifications check Settings > Notifications > Do not send email notifications to admins.
Email notifications for users
When an agent sends a message to a user, an email is sent to the user only if the user is offline.
Only 1 email is sent. Subsequent messages will not trigger a new email alert.
Create the email
To manage the emails and create the contents go to Settings > Notifications. You can use text and HTML. New lines are automatically converted to <br />. You can use the following merge fields in the email. Merge fields are automatically replaced with the updated information.

Code	Description
{recipient_name}	The name of the user or agent who is receiving the email.
{sender_name}	The name of the user or agent who was sending the message that triggered the email notification.
{sender_profile_image}	The profile image of the user or agent who was sending the message that triggered the email notification.
{message}	The links to any attachments that were part of the message that triggered the email notification.
{attachments}	Emails may not be delivered for several reasons; below are the most common ones:
Cron job
Masi Chat automatically manages email notifications, ensuring that emails include the full conversation and are sent only once. This process is fully automated. Simply enable Settings > Notifications > Email notifications via cron job to activate this feature.

Problems?
Emails may not be delivered several reasons; below are the most common ones:

Reason	Description	Solution
Hosting problems	The email server of your web hosting provider is not able to send emails or the emails are sent but they are automatically detected as spam and deleted by the email clients.	If the settings at Settings > Notifications > SMTP are not set, our server will send the emails instead. Contact your web hosting provider regarding email support or use your SMTP server by activating it in Settings > Notifications > SMTP.
SMTP problems	The email is not sent also if you activated the SMTP option in the Notifications area.	If you don’t receive the emails make sure they are working by sending a test email from Settings > Notifications > Send a user email notification or Send an agent email notification. If you don’t receive the test email, your SMTP server, is not working. Open the browser console for more details about the error. Because this is not an issue related to Masi Chat, the support doesn’t cover it, please contact your server/hosting/SMTP support instead. You can use sendgrid.com, you can send 40000 emails for 30 days for free, then 100/day forever for free.
Google Gmail	The email is not sent also if you activated the SMTP option in the Notifications area and you are trying to the Gmail SMTP server.	If you’re using Gmail enter smtp.gmail.com as host and set 465 or 587 as port. You need also to allow access to Gmail to less secure apps, you can do it from https://myaccount.google.com/lesssecureapps.

Show more
Push notifications
Push notifications are like Desktop notifications but with some key difference. Desktop notifications, also called Web notifications, are requested directly by the client while Push notifications come from a server. The main difference is that the Push notifications work always, also if your device is offline, or Masi Chat is not open. Also, they are persistent and always visible until closed.

How to Enable Push Notifications:
Go to: Settings > Notifications > Push Notifications.
Toggle On: Active for agents and/or Active for users.
Click Save Changes
Then Reload and refresh the admin area to apply the changes.

Push notifications for admin and agents
When an agent click the notification the admin area is open and the right conversation is selected. If the admin area is already open the notification will not open a new tab.
When a user sends their first message, a Push notification is sent to all validated agents; subsequent notifications are sent only to the last agent in the conversation.
If you’re using the Artificial Intelligence app, and Settings > Artificial Intelligence > Human takeover is active, no notifications are sent if the chatbot knows the answer.
If the Routing setting is active, only the agent assigned to the conversation receive the notification, admins see all conversations but don’t receive notifications.
If queue or routing settings are active, only the agent assigned to the conversation receive the notification. Notifications are not sent for messages sent by the user while waiting in queue. Admins see all conversations but don’t receive notifications.
If a user conversation is assigned to a department, only the agents assigned to that department receive the notification.
If a user conversation is not assigned to any department, only agents with no assigned department receive the notification.
Notifications are sent only if the last agent in the conversation is offline.
If human takeover is active, no notifications are sent if the chatbot knows the answer.
If Push notifications are not working, make sure you allowed the notifications, in Chrome you can check this from Privacy and Security > Site settings > Notifications. If they don’t work on a mobile device, try these steps too: install the PWA or delete it and install it again, restart the mobile device.
If you are using Chrome, to enable Push notifications also when the tab is closed you must enable the Continue running background apps when Google Chrome is closed option from Chrome > Setting > System (chrome://settings/system).
If Push notifications are not working, and you are using Windows, ensure that the notification feature is not being blocked by Windows. To do this, click on the Start button and search for Notifications. Then, open the Turn app notifications on or off option and verify that your browser is not being blocked.
If Push notifications are not working, ensure that the Masi Chat admin area is not visible and open. If the admin area is open, please minimize it or open another window or browser tab.
If Push notifications are not working, your system might be blocking them. If you are using Windows 10+ check this link. If you are on Mac check this link. For all devices also check this link.
Desktop notifications
Desktop notifications
Desktop notifications are not sent if the user is viewing a page with include the chat (the user browser is open and visible, and the active page contains the chat).
Desktop notifications are not supported on iPhone and iOS devices.
When Push notifications are active, they replace desktop notifications.
If desktop notifications aren’t working, try the solutions in the Push notifications section.
Text message notifications
Both agents and users can be notified via text message when a new message comes in. Text message notifications are provided by Twilio. Twilio is a paid service, but it is cheap, and you can use the trial version to test text message notifications for free.

Activation
Go to https://www.twilio.com and create a new account.
Verify your phone number. 
Complete the form and choose SMS. 
Once into the dashboard, click Get a Trial Number, then Choose this number. 
Enter the Masi Chat admin area, go to Settings > Notifications > Text message notifications, and paste account SID, auth token, trial number (or a purchased phone number). Find all details in the Twilio dashboard.
If you’re using the trial version, you can send notifications only to verified numbers. Enter the Twilio dashboard and click the left button # (Phone Numbers), then click Verified Caller IDs and add your phone number. 
To start using the service live and to receive text messages in Masi Chat you need to purchase a phone number. After the purchase go to Phone numbers > Manage > Active numbers, click your number, and enter the URL of Masi Chat, get it from Masi Chat > Settings > Notifications > Text message notifications > Get configuration URL. 
You’re done.
Information
Ensure that your agents and admins are assigned a phone number so that agent text messages can be sent to the appropriate numbers.
All phone numbers must have the country code starting with the symbol + (eg. +***********).
You can get the user’s phone number via registration form, follow-up message, or via [email] shortcode. You can manually enter the user phone from the admin area or via API.
When a user sends their first message, if the conversation is assigned to a department, the text message is sent only to the agents assigned to that department, if the conversation is assigned to a specific agent, the text message is sent only to that agent, otherwise the text message is sent to all agents who are not online at the moment. Subsequent text messages are sent only to the last agent in the conversation.
Text message notifications are sent only if the last agent in the conversation is offline.
If you’re using the Artificial Intelligence app, and human takeover is active, no text messages are sent if the chatbot knows the answer.
To use the Sender ID feature enter the sender name in the Sender number field.
Sound notifications
When Settings > Notifications > Sounds are enabled, a sound will play whenever a new message or conversation is received.
Audio will only play when the user or agent engages with the document by clicking somewhere in the admin area or on the page displaying the chat widget. This is because of a security feature of the browsers.
If the sound is set to repeat, it will loop as long as the admin area or the chat widget page is not open or minimized. If the the admin area or the chat widget page is open and visible, you will not hear the sound.
ARTIFICIAL INTELLIGENCE
The settings below are related to the Artificial Intelligence app.

Installation
From Settings > Apps, click Artificial Intelligence and click Activate.
Human takeover
The human takeover happens in these cases:

When a human agent replies to a user’s message.
When the Settings > Artificial Intelligence > Human takeover option is active. It is triggered automatically when the chatbot fails to understand a user’s message or it can be requested manually by the user explicitly asking to contact a human agent.
General information
When the human takeover is activated the chatbot is automatically disabled for as long as the last human agent that replied to the conversation is online. If no agents have responded to the conversation yet, the chatbot will remain active. After that, the chatbot is activated again but no default fallback messages will be sent within 10 days of human takeover if the chatbot doesn’t know the answer to the user’s question in the same conversation. You can force a Dialogflow message to be always sent by adding to the Intent the custom Payload value “force-message”: true.
The chatbot is fully activated again if the conversation is archived or deleted.
If you’re using Slack, no messages will be sent if the chatbot knows the answer. Once the human takeover is active all conversation’s messages are sent to Slack.
When a human agent reply to a user the human takeover is activated automatically.
The human takeover feature is compatible with OpenAI and will continue to function even if the Dialogflow chatbot is deactivated.
To fully disable the chatbot on human takeover, check the option Human takeover > Disable chatbot.
If the human takeover is already active, it will not be activated again, and the human takeover message will not be sent. The Fallback message will be sent instead.
Human takeover option
When the option Human takeover is active the following happens:

If enabled, the request is sent only if the message sent by the user is longer than 3 chars and contains at least two words. This optimization prevents mistaken requests.
The conversations to which the chatbot was able to answer correctly, are marked as read and moved at the bottom of the Inbox list.
When the user confirms the human takeover, or if it is automatic, the following happens:

The conversation is marked as unread and moved on top of the Inbox.
Any future user messages in the same conversation sent within 10 days of the human takeover will trigger agent notifications.
If agents email notifications are active, an email notification is sent to the agent assigned to the conversation, or, if the user’s conversation is assigned to a department, to the agents assigned to that department, otherwise it is sent to all offline agents.
If Push notifications are active, a push notification is sent to the agent assigned to the conversation, or, if the user’s conversation is assigned to a department, to the agents assigned to that department, otherwise it is sent to all offline agents.
If text message notifications are active, a text message is sent to the agent assigned to the conversation, or, if the user’s conversation is assigned to a department, to the agents assigned to that department, otherwise it is sent to all offline agents.
No human takeover requests within 10 days of the human takeover will be sent in the same conversation.
The follow-up message and offline message are activated.
If queue settings are active, the queue or routing is activated.
Manual human takeover – Dialogflow Only
You can manually trigger the human takeover by creating a new Dialogflow Intent with the following Custom Payload response: { “human-takeover”: true }.
The manual takeover automatically notify agents via email and leave the conversation marked as unread.
To manually send a human take over request use the chips rich message with ID mc-human-takeover. Ex. [chips id=”mc-human-takeover” options=”Human support,Cancel” message=””].
Manual human takeover – OpenAI Only
The human takeover can be requested manually by the user explicitly asking to contact a human agent. This setting is active by default. This setting is compatible only with the following OpenAI models: gpt-4o-mini, gpt-4o, gpt-4-turbo, gpt-4.
OpenAI Assistant
To enable human takeover on an OpenAI Assistant add the following function in the OpenAI functions area:

{
    "name": "mc-human-takeover",
    "description": "I want to contact a human support agent or team member. I want human support.",
    "parameters": {
        "type": "object",
        "properties": {},
        "required": []
    }
}
WhatsApp, Messenger, Telegram, and other messaging apps
The make the human takeover request work on WhatsApp, Messenger, Telegram and the other messaging apps, you need to create a new Intent with human-takeover as input Context, nothing as output Context, a list of the user’s most common confirmation messages as Training phrases(e.g. ok, yes) and { “human-takeover”: true } as Custom Payload response .

Smart reply
Smart Reply suggests quick responses in real-time during a conversation. Once active, you will see the suggested replies in the conversation area, if any.

Information
The Smart Reply feature initially checks for suggestions from the Dialogflow chatbot, provided it is active. If there is at least one suggestion, it will promptly display the results. However, if Dialogflow is inactive, or there are Dialogflow suggestions, the OpenAI suggestions will be returned instead.
If the language detection feature is active, the smart replies will use language detection as well.
If the multilingual via translation feature is enabled, the smart replies will utilize multilingual translation as well.
To read the complete text of a smart reply, hover the mouse pointer over it for a duration of 3 seconds.
To restore the previous message, press the keyboard shortcuts CTRL + Z.
Optimal configuration for the chatbot
Masi Chat provides powerful tools to assist you in the process of creating the chatbot.

Activate Artificial Intelligence > Google > Dialogflow chatbot. If you have general or specific inquiries that require a definitive response, or if you want to use rich messages like buttons, the Dialogflow chatbot is the best option and it can work alongside the OpenAI chatbot. The Dialogflow and OpenAI chatbots can work together simultaneously. More details here.
Keep improviding the chatbot from the chatbot training window.
Activate Artificial Intelligence > Smart Reply. More details here.
Activate the following OpenAI settings: Chatbot, Spelling Correction, Dialogflow spelling correction, Rewrite Message Button. More details here.
Train your OpenAI chatbot with your own content, more details here.
Set the value of Google > Dialogflow Intent detection confidence to 0.81.
If your website receives traffic from multiple countries, consider activating the following Google options: multilingual via translation, automatic translation, language detection.
Change the chatbot name from Settings > Users > Bot name.
Chatbot training window
You can open the chatbot training window from the conversation area by hovering the mouse over a message, opening the message’s menu, and selecting Train chatbot.

If Dialogflow is active, a new Intent will be added to the main Dialogflow agent.
If OpenAI is active, the OpenAI chatbot will be trained automatically with the new information. You can control the questions and answers generated from this window from Chatbot > Training > Questions and answers.
The Services to update option let you choose what chatbots to update. When updating a Dialogflow Intent, a new OpenAI question and answer will be generated.
The training window is not compatible with OpenAI assistants.
Problems?
The most frequent reasons for OpenAI or Google not functioning properly are listed below. For more details about the issue, open the browser developer tools and then the console tab, send a message through the Masi Chat chat, and an error should appear in the console. On Chrome you can open the console from Settings > More tools > Developer tools > Console.

OpenAI and Google
There is a human takeover.
OpenAI
Click the Settings > Artificial Intelligence > OpenAI > Troubleshoot problems button and check for any error. If there is an error, it will be related to your OpenAI account, follow the error message instructions to fix the issue.
Make sure to check the option Settings > Artificial Intelligence > OpenAI > Chatbot.
You reached the quote limit or you are encountering billing problems with your OpenAI account. Check it at https://platform.openai.com/account/usage.
If Dialogflow is enabled, OpenAI may not work correctly because your Dialogflow agent does not have the Fallback Intent. Please verify its presence on the Intents page, and if it’s missing, you can create it again by following the instructions here. To quickly check if this is the issue, you can disable Dialogflow and send a message consisting of 2-3 words. Then, check if the browser console has errors.
Make sure to read the general information.
If the training is not working as intended, for instance, if the chatbot is not responding to questions relevant to the training sources, try to delete all training sources from Chatbot > Training > Informaion > Delete all training data and train the chatbot again.
If your training sources are from a website, and the website is multilingual, make sure the lang attribute of the <html> tag contains the correct language.
Delete all of your OpenAI settings, leave only the OpenAI key and try again.
Try to use the OpenAI assistant instead.
You can use our OpenAI key by Settings > Artificial Intelligence > OpenAI > Sync mode to Automatic. If this resolves your problems, it means that the issue originates from your OpenAI account. You need to purchase credits to use this option.
Google
Click the Settings > Artificial Intelligence > Google > Troubleshoot problems button and check for any error. If there is an error, it will be related to your Google account, follow the error message instructions to fix the issue.
Make sure to check the option Settings > Artificial Intelligence > Google > Dialogflow chatbot.
If your synchronization was not successful we suggest reviewing our documentation and repeating the synchronization steps to correct any errors.
You selected the wrong agent location.
Make sure are not using a mega agent.
Make sure to read the general information.
OpenAI
The settings below are related to the Artificial Intelligence app.

Synchronization
Automatic sync mode
The automatic sync mode is ready-to-use and does not require any additional settings.
If you want to activate the chatbot, check Settings > Artificial Intelligence > OpenAI > Chatbot.
You need to purchase credits to use this option.
Manual sync mode
The OpenAI (ChatGPT) integration gives your chatbot the ability to answer general questions about almost anything you can imagine. To start using it, follow the steps below.

Register at https://beta.openai.com/signup.
Get an API key from https://beta.openai.com/account/api-keys and paste it into Settings > Artificial Intelligence > OpenAI > API key
Enable the chatbot from Settings > Artificial Intelligence > OpenAI > Chatbot.
Information
If OpenAI is not working, click here.
If Dialogflow is active, the OpenAI query is performed only if the chatbot does not know the answer to the user’s question and if the length of the user’s message is greater than 4 characters.
The following settings are compatible with OpenAI: Smart reply, Human takeover, Disable for the tickets area, Disable during office hours, Bot response delay, Reply to user emails, Reply to user text messages.
The following Google settings are compatible with OpenAI: Multilingual, Multilingual via translation, Automatic translation, Language detection
If a human takes control, the OpenAI chatbot is deactivated, and is remains deactivated also if the agent goes offline. The Dialogflow chatbot continues to function whenever necessary.
The default model is gpt-3.5-turbo.
OpenAI supports the use of voice messages through speech recognition.
Settings
See information about most OpenAI settings here.

Chatbot mode
This feature is related to the Settings > Artificial Intelligence > OpenAI > Chatbot mode option. It allows you to configure the information OpenAI will use to respond to user messages.

Only general questions OpenAI will respond only to general questions and questions related to previous user messages in the same conversation, without using any information you provide. This is the default setting.
Only questions related to your sources OpenAI will only respond to questions related to the information you provide. The user messages will be ignored. You have to first train the chatbot with your sources.
All questions OpenAI will only to questions related to the information you provide, to general questions, and to questions related to previous user messages in the same conversation. You have to first train the chatbot with your sources. This is the recommended mode.
Assistant Use this mode if you are using an OpenAI Assistant.
Assistant
This features allow you to use your own Assistants created at https://platform.openai.com/assistants.

You have to set Settings > Artificial Intelligence > OpenAI > Sync mode to Manual and use your own API key.
You can use multiple assistants by adding their IDs in Settings > Artificial Intelligence > OpenAI Assistants – Department linking and selecting Assistant in Settings > Artificial Intelligence > OpenAI > Chatbot mode. To activate an assistant, both the assistant and the conversations must be assigned to a department.
The assistant is used only for the chatbot, and smart replies, not for the message rewriting and other features.
Chatbot
The OpenAI chatbot feature functions similarly to Dialogflow, providing automated responses to user messages. Select the Chatbot mode setting to specify the questions that the chatbot is capable of responding to. Utilize the human takeover feature to enable the chatbot to redirect the chat to a human agent as necessary.

Fallback message
The fallback message is sent when OpenAI is unable to understand the user question. If the Dialogflow chatbot is enabled, the fallback message will be turned off and the Dialogflow chatbot’s fallback message will be utilized instead.

Prompt
The prompt instructs OpenAI on how to respond by providing relevant information that can be utilized to answer user inquiries. To comprehend the process, refer to the example prompts listed below.

If you choose to activate the human takeover feature, you need to tell OpenAI to respond with I don’t know if it is unable to provide an answer to the user’s question. You should always include the following text in the prompt: Respond “I don’t know”, if not sure about the answer.

Prompt – Message rewriting
This prompt instructs OpenAI on how to rewrite a message when the Message rewrite button is active. This prompt should be in English. Masi Chat automatically add the following text when required: and use the user langauge, add greetings.

Replies from the training sources can be utilized with the prompt feature as they are compatible.

Spelling correction
This feature automatically fix any spelling mistakes in the agent’s message.

Smart reply
This feature allow to enable the Smart Reply feature also if the chatbot is not active. If the chatbot is active the Smart Reply feature will use OpenAI automatically.

Dialogflow spelling correction
In the case where Dialogflow is unable to find the appropriate response to the user’s message, this function verifies if there are any spelling errors. If any such errors exist, Dialogflow is prompted again with the correctly spelled version of the message for an accurate response. Although it has a higher priority, this feature can still work together with the Google search spelling correction feature.

Message rewrite button
This feature adds a button to the text field of the conversation area, click on it to rewrite your message and make it more friendly and professional. Greetings will be added automatically if there is no previous agent messages. If your language is not English, you have to edit your agent/admin profile and set the correct language.

Speech recognition
When this option is active, audio messages will be converted to written text.

You do not need to activate this feature for the chatbot to understand audio messages. The speech recognition feature of the chatbot is always enabled.
For this feature to work better, the user language should be know. To automatically detect the user’s language, you can enable the language detection feature.
The text will be displayed alongside the audio player.
The generated text is compatible with the translation features.
Source links
When this option is active, the response will automatically include the links to the sources and training data used for generating the answer. To include articles as sources you have first to set the articles page in Settings > Articles > Articles page URL.

Note data scraping
When this option is active, you will see a menu in the notes panel. The menu enables the automatic extraction of specific information from the user’s messages.

OpenAI parameters
Masi Chat allows you to adjust various OpenAI parameters such as temperature and logit_bias, for more details visit https://platform.openai.com/docs/api-reference/completions/create. Don’t set any values if you don’t know what these parameters do or OpenAI may stop working.

Logit bias
The Logit bias parameter must be JSON string and the keys must be strings, for example: {“2435”:-100, “640”:-100}.

Generate user questions
When this option is active, OpenAI will generate new user questions in real time when you open the chatbot training window.

Use conversations for training
Masi Chat continuously improves its chatbot performance by automatically training with real conversations. This ensures the chatbot becomes smarter and more effective over time without any manual intervention. You will find the training data in Chatbot > Training > Conversations.

Data Source: Training is based on conversations between users and agents.
Chatbot messages are excluded from the training process.
Training Frequency: Masi Chat automatically trains the chatbot every 24 hours.
Data Management: The first 5 user expressions are used to generate new expressions, avoiding duplicates and improving chatbot accuracy.
Training
With this feature, your chatbot can undergo training using your website, texts, PDF documents and more. Once the training is successfully completed, the chatbot will be able to answer questions related to your contents.

Training using files
This training process enables you to train the chatbot using PDF and TEXT files.

To access this feature, navigate to Chatbot > Training > Files.
Select your files, click the Train chatbot button and await completion of the training process.
It is only possible to upload files in PDF and TXT formats.
As soon as the training is completed, the uploaded files are removed.
Training using a website
This training process enables you to train the chatbot using websites.

To access this feature, navigate to Chatbot > Training > Website.
Enter the website URLs, click the Train chatbot button and await completion of the training process.
If you retrain the same website, you must first delete the previous website’s training data; otherwise, only new URLs will be trained.
You can provide the website URL and all child URLs will be included and crawled, but with large websites, it is more efficient and less prone to errors and infinite link loops to utilize an XML sitemap instead of relying on the website URL. You can create it with a service like https://www.xml-sitemaps.com.
If you want to train your chatbot using specific pages from your website instead of all of them, you can make use of an XML sitemap. Create one using a tool like https://www.xml-sitemaps.com, and then remove the pages you do not wish to include by editing the file in a text editor. To use the XML sitemap, you need to upload it onto an external online location service like https://tmpfiles.org. Afterward, add the URL of the sitemap in Chatbot > Training > Website. If your sitemap contains more than 1000 URLs, it’s advisable to split it into multiple files and train the chatbot with one file at a time.
Training via questions and answers
This training process enables you to train the chatbot by adding questions and answers individually.

To access this feature, navigate to Chatbot > Training > Q&A.
For more details click here.
Training using articles
This training process enables you to train the chatbot using the articles.

To train the chatbot add you articles and than click the Train chatbot button. The articles are used as training sources automatically.
For more details click here.
Training using flows
This training process enables you to train the chatbot using the flows.

To access this feature, navigate to Chatbot > Flows.
For more details click here.
Training using conversations
This training process enables you to train the chatbot using the conversations from the users and agents.

To enable this feature, check the Settings > Artificial Intelligence > OpenAI > Use conversations for training option.
The training data will begin to be generated in the coming days and will be available in the Chatbot > Training > Conversations area.
While this method is powerful and automated, it can produce low-quality training data if the responses from your human agents or the user messages are unhelpful or incorrect. We strongly recommend reviewing the training data regularly. For most chatbots, we recommend avoiding this method.
Training using the chatbot training window
Once you have finished training your chatbot, you may want to continue enhancing it. This feature allows you to select conversation messages and use them to add new questions and answers or improve existing ones.

For more details click here.
Training using real-time information
This feature allow the chatbot to answers to questions that require real-time information, such as, “What is the temperature in London today?”.

For more details click here.
Information
To add new training sources, simply train the chatbot again. The previous training sources will not be lost, and only the new sources will be added.
There are character limits for training the chatbot. You can view the character limits here.
The embedding model is essential for training your chatbot and handling all user messages. We currently use the text-embedding-3-small model. It is necessary for these scenarios and cannot be disabled or changed. You can find pricing information at https://openai.com/pricing. Check out the pricing for the text-embedding-3-small model in the Embedding models section.
Go to Chatbot > Training > Information, and click the Delete all training data button to remove all previous training data for the chatbot.
Run the training via cron job
Masi Chat automatically handles the AI training process, enabling the training option through Settings > Artificial Intelligence > OpenAI > Training. The training process runs automatically at regular intervals, with no need for manual intervention.

The cron job runs automatically, up to once every 7 days.
We recommend using an XML sitemap instead of a website URL for improved performance.
Automatic training applies only to websites; files, Q&A, and articles are excluded.
Re-training will overwrite the previous website training data..
Multilingual training
If your user base is multilingual, you can train the chatbot with content in multiple languages and limit the chatbot to retrieve answers only from the sources in the user’s language. To activate this feature, check the Settings > Artificial Intelligence > OpenAI > Multilingual Training Sources option.

Files Files currently support only one language.
Websites The language of the website is detected automatically. For Masi Chat to comprehend the language of your web pages, the <html> must contain the attribute lang.
Q&A Q&A currently support only one language.
Articles All article languages are used automatically.
Conversations If automatic translation is active, only messages in the agent’s language will be used.
Q&A
The information below is related to the Question and Answers section of the chatbot training area. Add questions and answers to the chatbot to improve its performance. The chatbot will use this information to respond to user inquiries.

Question
Enter the user messages that will trigger the answer. Add as many question variations as necessary. For example the questions to the answer I’m a chatbot! could be Who are you?, What are you?, Are you a bot?.

Answer
Enter the text that will be used to answer the user question.

Function calling
Function Calling enables the chatbot to connect to external tools and systems by fetching real-time data through API integrations.

Retrieve Data: Allow users to check order statuses, fetch information, or query external systems directly through the chatbot.

Automation: Automate responses by connecting to CRMs, ERPs, or custom APIs

Parameters
URL: Enter the URL of the API endpoint that will supply the necessary values for the function.
Headers: Enter key-value header parameters, separated by commas. E.g. apikey:123345, json:true.
Properties: Enter values that the user must provide to the chatbot. For example a city, a tracking number, etc. The chatbot will ask the user for these values. If you already know all the possible values, you can enter them in the Allowed values field.
Response from Masi Chat’s Server
Masi Chat’s server will return a JSON array with the values required by the chatbot. For example: {"order_status": "Delivered 2 hours and 25 minutes ago"}.

Important Note on the mc Key
When using the Rest API block within flows, Masi Chat automatically includes the user details under the mc key in the JSON body. These details may include user information like their name, email, and custom fields. Make sure your server can handle this structure when processing requests.

Set data and actions
Set the specified user values when the question is asked. You will see such values in the user details panel. You can use the following merge fields to assign values extracted from the user messages. Include these fields in the answer and they will be replaced with the actual values: {language}. You can also use this feature to perform actions like assigning departments, agents, and tags to the conversation.

Flows
The information below is related to the chatbot flows area. Flows allows you to easily create conversation flows powered by the chatbot. Use them to guide the user toward a specific goal with a series of pre-defined messages.

Flow blocks
Start: Use the start block to set when the flows should start. It can be every time the user start a new conversation, when it sends a specific message, or on page load. If you set conditions, the flow will only start when all conditions are met.
Send message: Send a message to the user.
Send button list: Send a list of buttons to the user.
Send video: Send a video to the user.
Get user details: Get the user details and store them in Masi Chat. You will see such values in the user details panel. You can leave the description field empty for default details, but it’s required if using custom user fields.
Set data: Set the specified user values when the block is executed. You will see such values in the user details panel.
Actions: Execute the specified actions when the block is executed.
Conditions: Use it to create different branches in the flow. If the conditions are met, the flow will follow the branch set as true, otherwise the one set as false.
Rest API:

The REST API block allows you to send data to external servers via API requests directly from Masi Chat flows. This feature is particularly useful for integrating with external systems and retrieving dynamic data.

Request Structure

The request body must be a JSON array.
Masi Chat user details are automatically included under the mc key, providing a convenient way to pass user information to external APIs.
Example Request Payload

{
    "mc": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "user_id": "12345"
    },
    "custom_data": "example_value"
}
How to Use It

You can access Masi Chat user details in the API request using JSON dot notation, e.g., mc.name, mc.email.
In the API headers, include key-value pairs as needed for authentication or data formatting.
When to Use the mc Key

✅Required: When your external API needs user-specific information for personalized responses or data retrieval.
❌Optional: When user details are not needed, you can ignore the mc key.
Best Practices

Ensure your API endpoint can handle the mc key correctly.
Review and test API responses to verify user data integration.
This feature offers powerful integration options but requires a solid understanding of API handling and Masi Chat’s flow builder.
Google
The settings below are related to the Artificial Intelligence app.

Synchronization
To start using the Google AI services and Dialogflow follow the steps below.

Automatic sync mode
Click Synchronize now and complete the procedure.

If you want to activate the Dialogflow chatbot, check Settings > Artificial Intelligence > Google > Dialogflow chatbot. Also, you have to enter your chatbot Project ID or Agent Name, to get it follow the steps below.
You need to purchase credits to use this option.
Manual sync mode
Enable the Masi Chat > Settings > Artificial Intelligence > Google > Dialogflow Chatbot option and save the changes.
Go to console.cloud.google.com and sign in.
Select or create a project by clicking the Select a project button on top-left.  Name the project as you want. 
Activate the Dialogflow API by entering Dialogflow API on the top search bar. Click Dialogflow API and then click Enable. 
Select the project and the go to Left menu > APIs and services > OAuth consent screen. Select External and click Create.  In App name enter what you want, in User support email and Developer contact information enter your email. Click Save and continue. 
In the scopes area, click Add or remove scopes, scroll bottom and into the Manually add scopes area enter https://www.googleapis.com/auth/dialogflow,https://www.googleapis.com/auth/cloud-language,https://www.googleapis.com/auth/cloud-translation. Click Add to table and then Update and Save and continue. 
In the test users area, click Add users and add your Google email, use the same email as the currently logged in account. Click Save and continue. 
Go to Left menu > APIs and services > Credentials and click Create credentials, select OAuth client ID.  As Application type select Web application. Enter any name you want. In Authorised redirect URI enter the redirect URL, get it from Masi Chat > Settings > Artificial Intelligence > Google > Authorised redirect URI. Click Create. 
Copy Client ID and Client Secret and paste them into Masi Chat > Settings > Artificial Intelligence > Dialogflow. Save the settings. 
In Settings > APIs & Services > OAuth Consent Screen click PUBLISH APP. There is no need to complete the review process, leave it in the pending review state.
Click Masi Chat > Settings > Artificial Intelligence > Google > Synchronize. Login with the same Google account you have used till now. On the next screen click Continue.  On the next screen select all scopes and click Continue. 
Copy the Refresh token and paste it into Masi Chat > Settings > Artificial Intelligence > Google > Refresh token. 
You are done! If you want to activate the Dialogflow chatbot, check Settings > Artificial Intelligence > Google > Dialogflow chatbot. Also, you have to enter your chatbot Project ID or Agent Name, to get it follow the steps below. Note that the app does not need to be approved by Google.
If you are a Google Workspace user, go to https://admin.google.com/ and click Google Cloud Session control. Set Re-authentication policy to Never require re-authentication.
Dialogflow
The information provided below is relevant to Dialogflow. Warning! We will stop supporting Dialogflow by the end of 2025. All its features will be available in Masi Chat through OpenAI. Please use OpenAI instead of Dialogflow.

Get Project ID
Log in to the Dialogflow ES console by going to dialogflow.cloud.google.com. You must sign in with the same Google account used during the synchronization.
Click the gear icon at the top left, near the chatbot name, and open the settings area. . If you haven’t created a chatbot yet, follow the instructions below to create your first bot.
Copy the Project ID .
Make sure to choose US / GLOBAL on the top left of the Dialogflow dashboard. Please note that our system does not support mega agents. .
Get Dialogflow CX Agent Name
Log in to the Dialogflow CX console by going to https://dialogflow.cloud.google.com/cx/.
Select the project of the desidered agent and go to the agents page. You can enter the agents page by selecting the agent and by clicking the top button Agents > View all agents .
Click the options menu for the desiderate agent in click Copy name .
More details at https://cloud.google.com/dialogflow/cx/docs/quick/api#detect-intent-drest.
Location
Set the location or region of your Dialogflow agent. This setting is optional if your agent location is set to global. 

Welcome Intent
Trigger the Dialogflow Welcome Intent for new visitors. The option Settings > Messages > Welcome message must be active.

Send the user details
Send the user details of the registration form and email rich messages to Dialogflow.

Add Intents to saved replies
Include the Dialogflow Intents into the saved replies. To access the saved replies option, go to Settings > Admin > Saved replies.

Create a basic chatbot
If you haven’t created a chatbot yet, follow the instructions below to create your first one. The creation and management of your Dialogflow chatbot is handled entirely by Dialogflow. There are a lot of tutorials online that can help you create and configure your Dialogflow bot.

To create your first chatbot enter the Dialogflow console and create an agent. As Google project select the same project used during the synchronization.
Add a new Intent from the left menu and open it.
In the Training phrases area adds the user’s question you want the chatbot to reply to, add as many variants are you can. For example, if you want to the chatbot to reply to users asking for your business address, add variants like “what is your address”, “address”, “where are you”.
In the Responses area adds the chatbot answer as a text response. If you want to add buttons, cards, etc., you can use the rich messages.
You have created your first question and answer! Test if from the right area or from the Masi Chat chat. Add new Intents to populate your chatbot with the questions and answers you want. You can include basic pre-built questions and answers by enabling the Small Talk feature from the left menu.
Here are some great resources to help you build a more complex chatbot:
Actions
The following actions give the chatbot the ability to interact with the website autonomously on behalf of the user. To use an action go to Dialogflow, edit an Intent, and add a new Custom Payload response with the following syntax: { “ACTION-NAME”: ACTION-VALUE }.

Action code	Description
{ “human-takeover”: true }	Start human takeover and disable the chatbot.
{ “redirect”: “URL” }	Redirect the user to the given URL. Add the value “new-window”: true to open the URL in a new window.
{ “open-article”: ID }	Open the article with the given ID.
{ “transcript”: true }	Generate the conversation transcript as a text file and download it. Set it to email to send the transcript to the user’s email, add the value message: “Your message” to include a message in the email.
{ “tags”: [“Tag 1”, “Tag 2”] }	Assign tags to a conversation.
{ “department”: ID }	Change or set the conversation department and notify the agents.
{ “agent”: ID }	Change or set the agent assigned to the conversation and notify the agent.
{ “send-email”: { “recipient”: “active_user”, “message”: “”, “attachments”: [] } }	Send an email to the active user or agents. Attachments syntax: [[“name”, “link”], [“name”, “link”],…]}. Recipient value can be active_user or agents.
{ “update-user”: true }	Tells the admin area to update the user of the active conversation. Use this action in combination with other actions to update the user details of the admin area in real-time.
{ “archive-chat”: true }	Archive the chat and send the close message if active.
{ “update-user-details”: { “email”: “”, “last_name”: “”, “first_name”: “”, “extra”: { “phone”: [“+*********”, “Phone”] }}}	Update the details of the active user. You can update all details, including first_name, last_name, email, user_type, password You can update the user extra details, like the phone number, by entering the values into the extra key, the values must use the following syntax: “slug”: [value, “label”]. Download an example here. To upload an Intent go to the Intents area and click the 3-dots menu icon on the top-right, then click Upload Intent. Start the conversation by sending the message “start”.
{ “update-user-language”: “$language” }	Update the user language, and the chatbot language if multilingual chatbots feature is active, to match the language requested by the user. The Dialogflow Entity value for the language is represented by the parameter $language. 

Show more
Dialogflow fulfillment
The fulfillment data sent to your webhook URL is like below:

{
	"responseId": "4a58fc4f...",
	"queryResult": {
		"queryText": "fullfilment",
		"parameters": [],
		"allRequiredParamsPresent": true,
		"fulfillmentText": "Example",
		"fulfillmentMessages": [{
			"text": {
				"text": ["Response"]
			}
		}],
		"outputContexts": [{
			"name": "projects/masi...",
			"parameters": {
				"no-input": 0,
				"no-match": 0
			}
		}],
		"intent": {
			"name": "projects/masi...",
			"displayName": "Fullfilment"
		},
		"intentDetectionConfidence": 1,
		"languageCode": "en"
	},
	"originalDetectIntentRequest": {
		"payload": {
			"masi_chat": {
				"conversation_id": "3002",
				"user_id": "3777"
			}
		}
	},
	"session": "projects/example/agent/sessions/3777-3002"
}
The payload and session fields contain the Masi Chat user ID and conversation ID.

Dialogflow Information
OpenAI
If OpenAI is enabled, Dialogflow takes priority over it. The OpenAI API will only be used if Dialogflow cannot answer the user’s question.

Chatbot training and optimization
It will require some time for your chatbot to consistently provide correct answers to all questions. To improve its performance, you and your human agents should continuously train the chatbot by incorporating new question variations and Intents. This approach will effectively enhance the capabilities of your chatbot.
Agents can add new Intents and chatbot responses, and add new training phrases to existing intents on the fly from the admin area by moving the mouse cursor over a message and clicking the Dialogflow Intent icon. Enable Settings > Artificial Intelligence > OpenAI > Generate user questions to automatically add variations of the question and to rewrite the answer. New Intents will contain also the responses, while the update of existing intents will add new training phrases only, but not new chatbot responses. If you’re using Dialogflow CX, the chatbot responses will be added to the latest flow used in the conversation if any, otherwise to the start flow.
If the chatbot is replying with the wrong intents, go to Dialogflow Console > chatbot Settings > ML Settings, and set the ML CLASSIFICATION THRESHOLD to a larger number, such as 0.6. Also check the Intent detection confidence.
Dialogflow Intent detection confidence
When searching for a matching intent, Dialogflow scores potential matches with an intent detection confidence, also known as the confidence score. These values range from 0.0 (completely uncertain) to 1.0 (completely certain). Specify a value ranging from 0.1 to 1.0. Any answer provided by Dialogflow that is less than this value will not be considered. If you are utilizing Dialogflow alongside your OpenAI chatbot that is trained using your resources, it is recommended to configure the value as 0.81.

Knowledge Base
Knowledge Base are automatically enabled. Knowledge Base is a feature that gives your chatbot the ability to search within documents (such as a PDF) or web pages to find an answer. To create your first Knowledge Base, go to cloud.google.com/dialogflow/docs/knowledge-connectors

Ignore an Intent if it doesn’t fit the provided keywords
Sometimes Dialogflow gives incorrect answers due to similar questions with different subjects. To solve this, you can instruct Masi Chat to ignore an Intent if specific keywords are not present in the user’s message. Here’s how you can do it:

Enter Dialogflow and edit the Intent.
For each training phrase, select the keywords you want to be required and link them to a new Entity, or an existing one.
Under Actions and parameters check Required and add a new Prompt with value skip-intent
Save the Intent. 
User attachments
User attachments are sent to Dialogflow by appending the attachment URLs to the message.

Chatbot attachments
To allow the chatbot to send attachments, add a Custom Payload response and insert this JSON code:

{ "attachments": [["name", "YOUR-LINK"], ["name", "YOUR-LINK"], ["name", "YOUR-LINK"]]}
Replace “name” with the actual name of the attachment to display and replace “YOUR-LINK” with the actual URL of the file. Images attachments are displayed automatically as images.

Rich messages
To allow Dialogflow to send rich messages, simply enter the rich message shortcode into the TEXT RESPONSE field or add a new Custom Payload response and insert this JSON code: { “rich-message”: “shortcode” }. Replace “shortcode” with the rich message shortcode, to have a valid JSON code you need to replace all “ chars with \”. To obtain the shortcodes or to learn how to create a rich message, please click here.

Rich message response
When the user interacts with rich messages via the Masi Chat chat (e.g. by clicking a button), the rich message response is sent to Dialogflow in the following format: ID|response, or ID if the rich message type is registration, email, follow up, in this case the rich message values are sent as array in the queryParams[‘payload’] key. ID is the rich message ID, which can be set by adding the attribute id=”YOUR-ID” to the shortcode. If no ID has been set, a random ID will be used instead. response is the input or selection of the user. To block Dialogflow from replying to a rich message add a new intent with ID as the only user expression and no response.

If the user is interacting with the chatbot via a messaging app (e.g. WhatsApp), the response of the rich message does not contain the rich message ID and Dialogflow contexts must be used to allow Dialogflow to understand which Intent to activate for a specific rich message response.

Get rich message response for registration, email, follow up
Check the option Masi Chat > Settings > Artificial Intelligence > Google > Send user details.
From Left menu > Fulfillment enable Webhooks, you only need to enter the URL of the file that will receive the Dialogflow webhook data.
Create an Intent with the ID of the rich message, for the registration form, enter registration, for the follow up form enter mc-follow-up-form, for email forms enter email.
Enable the Fulfillment for the Intent.
You’re done! The file of the webhook URL will receive the Masi Chat rich message user details.
Sequential survey
To create a sequential survey like the one of the demo you need to enter the Rich message ID in the Training phrases, check the example below.

Go to Dialogflow and create a new intent. In the Training phrases area enter the user expression survey example. In the Responses area enter the code [buttons id=”test-survey-1″ options=”Software,Physical products,Services” title=”What is your type of business?” message=”Please choose the type that best suits your company.” success=”Your company type is “]
Create a new intent. Enter the user expression test-survey-1 and as response enter [select id=”seq-survey-2″ options=”Priority post, Express courier, International courier” title=”Shipping methods” message=”Choose the preferred shipping method of your customers” success=”Your customers preferred shipping method is”]
Create a new intent. Enter the user expression test-survey-2 and as response enter Thank you for completing our survey!.
You’re done.
Department linking
Get the department IDs from Settings > Miscellaneous > Departments.
Get the project IDs from the Dialogflow settings area of your agents.
More information
Dialogflow supports the use of voice messages through speech recognition. WhatsApp audio messages and .ogg audio files are not natively supported. To support WhatsApp audio messages and .ogg audio files, activate Settings > Artificial Intelligence > OpenAI > Speech recognition.
You can activate the chatbot via API but sending a message with no text and payload { “event”: “activate-bot” }.
The following details are sent to Dialogflow in the queryParams parameter when detecting an intent: conversation_id, user_id.
To trigger the welcome event in Dialogflow CX, create a Event Handler and insert Welcome as Custom Event.
Masi Chat articles are synchronized automatically with the Dialogflow knowledge base.
Use the JS variable MC_DIALOGFLOW_AGENT = “AGENT ID” to change the default Dialogflow agent, replace “AGENT ID” with the project ID.
If the user sends the same message again, triggering the same Intent in Dialogflow, and OpenAI is active, Masi Chat will attempt to send a message from OpenAI and will ignore the response from Dialogflow.
You can access the user_id and conversation_id of the current user and conversation in Dialogflow CX using the $session.params.user_id and $session.params.conversation_id codes, which are located in the Condition area of the route. 
Multilingual chatbot
The feature Artificial Intelligence > Google > Multilingual checks if there is a Dialogflow agent in the user’s language and activate it.

For this feature to work the user language must be know. The user language is based on the language user detail of the user if set, otherwise on the user browser language. The language can also be detected with the language detection feature.
Make sure to activate this setting even if your OpenAI sources consist of multiple languages.
Automatic translation
The automatic translation feature automatically translates user messages into agent language and agent messages into user language. To enable it check the option Settings > Artificial Intelligence > Automatic translation. If Artificial Intelligence > Google > Sync mode is set to Automatic, the multilingual features will work automatically. Otherwise you have to complete the synchronization and the setup below. The multilingual via translation and language detection features also require these steps.

Setup
Log in to https://console.cloud.google.com with your Google account and select the project of your Dialogflow Agent.
On the top search bar type cloud translation, select the Cloud Translation API service and enable it. Please note that this is a paid service with a free tier, additional charges may occur.
Enable the billing by clicking Left menu > Billing and by enabling a billing account with a valid payment method.
Information
The user messages in the admin area are translated automatically in real-time to match the agent language.
The agent messages are translated automatically in real-time to match the user language.
Agents can view the original message by opening the message menu and by clicking View original message.
The agent language is based on the language user detail of the agent if set, otherwise on the browser language, or admin area language.
The user language is based on the language user detail, if set, otherwise on chat language, if set, otherwise on the browser language. The language can also be detected with the language detection feature.
The notifications are also translated.
To avoid translating a string, enclose it with the characters ` or “`.
Multilingual via translation
The feature at Settings > Artificial Intelligence > Multilingual via translation automatically translates user messages into the default language of Dialogflow or OpenAI, and translates Dialogflow or OpenAI messages into the language spoken by the user. Additionally, this feature translates all text displayed within the chat, such as the chatbot’s rich messages, articles, registration forms, and pop-up notifications. To enhance performance and minimize translation costs, the translations are automatically integrated into the translation files. Combine this feature with the language detection feature for optimal results.

The original texts must be in English.
Language detection
Detect the language of the user’ messages and change the user language and Dialogflow agent language accordingly, if available, otherwise, show a fallback message. You can use the following merge fields in the message: {language_name}.

The user message must be at least 2 words long.
Language detection is executed only for the first 2 user messages of a conversation.
As long as the OpenAI chatbot is operational and programmed to respond to generic inquiries, the fallback message will never be dispatched, since OpenAI will consistently provide answers to any use message.
Google search
The Google search feature at Settings > Artificial Intelligence > Google search gives your chatbot the ability to search for answers on Google. This feature is helpful for providing answers to questions that require real-time information.

Register at https://programmablesearchengine.google.com/.
Create a search and setup it is as you want. We recommend to add only your website and Wikipedia if you want your chatbot to reply to general questions, leave disabled the option Search the entire web. Use the Entities option to exclude invalid results.
Go to Edit search engine > Setup > Basic and copy the Search engine ID value, paste it in Masi Chat.
To get the API key visit https://developers.google.com/custom-search/v1/overview and click Get a key.
Spelling correction
In the case where Dialogflow is unable to find the appropriate response to the user’s message, this function verifies if there are any spelling errors. If any such errors exist, Dialogflow is prompted again with the correctly spelled version of the message for an accurate response. Although it has a lower priority, this feature can still work together with the OpenAI Dialogflow spelling correction feature.

Entities
The Entities setting analyze the user’s message and recognize and extract entities like cities, events, dates, and more. Use it to exclude invalid results returned by Google search. To enable this setting follow the steps below.

Log in to https://console.cloud.google.com with your Google account and select the project of your Dialogflow Agent.
Enter Cloud Natural Language API in the search bar at the top, select Cloud Natural Language API and enable the API .
Enable the billing by clicking Left menu > Billing and by enabling a billing account with a valid payment method. Enable billing for the project of your Dialogflow agent.
Information
The Google search is performed only if the chatbot does not know the answer to the user’s question and if the length of the user’s message is greater than 4 characters.
If the Google search returns a result, the Dialogflow context google-search is automatically activated. The context contains the attribute link which is the website’s link of the Google search result. Use the context to create a new intent that provides the link, if the user sends a message like tell me more or I want to know more. 
Google search is not required to activate the spelling correction. You can disable Google search and activate only spelling correction.
Google search is compatible with the OpenAI chatbot.
Tickets
The settings below are related to the Tickets app. The Tickets app allows users to create conversations and send messages via a UI different from the chat.

Installation
From Settings > Apps, click Tickets and click Activate.
Display the tickets area
To display the tickets area include the chat embed code into your page and add the attribute &mode=tickets to the script URL, e.g. <script id=”chat-init” src=”https://app.masichat.com/account/js/init.js?id=********&mode=tickets”></script>.

You can show the tickets area also by inserting the code <script>MC_TICKETS = true;</script> into any page showing the chat.

Information
If the tickets area is not visible, make sure to uncheck the option Tickets > Manual initialization. Additionally, you can explore other potential reasons for this issue here.
You can also use the tickets area to display an inline or full-width chat panel.
Tickets are the same of chat conversations on the admin-side, the only difference from chat conversations is the front-end UI.
Most of the settings of the chat are compatible with the Tickets App but not all of them. The dashboard settings, the pop-up message, and more are not compatible.
Dedicated APIs for the Tickets App are available in the API section.
To remove the mandatory ‘New ticket’ form for new users, activate the welcome message of Settings > Messages > Welcome message. The welcome message delay is ignored in the tickets area, the message is sent immediately..
To manually disable the mandatory registration only on a single page use the JavaScript code var MC_REGISTRATION_REQUIRED = true. Set it to true to force the registration instead.
WhatsApp
The settings below are related to the WhatsApp app.

Installation
From Settings > Apps, click WhatsApp and click Activate.
WhatsApp Cloud API Setup – Automatic sync mode
Click Synchronize now and complete the procedure.
To add new numbers, visit https://business.facebook.com/wa/manage/phone-numbers/. If you add new numbers after the sync process, you will need to sync them again. All numbers will be automatically synchronized. If you wish to disable specific numbers, you can delete them from Settings > WhatsApp > Cloud API numbers.
If you sync again with the same phone number and do not receive the verification SMS or call, you can enter the latest PIN you received and it will work.
If you do not receive the messages sent to your WhatsApp number in Masi Chat, please check the following:

Click Reconnect and complete the procedure.
Go to the Meta Business Suite and add a payment method.
WhatsApp Cloud API Setup – Manual sync mode
Create a new account at https://developers.facebook.com or login with your existing account.
Create a new app and choose Other as the app type. Then select Business. Enter a name for the app and select the Business Account used for WhatsApp. 
In Masi Chat > Settings > WhatsApp > Cloud API settings > Secret key enter a random string then go to https://developers.facebook.com/apps and select your app. Click Add product and add WhatsApp, then go to WhatsApp > Configuration and in Webhook URL enter the URL you get from Masi Chat > Settings > WhatsApp > Cloud API > Configuration URL. In Verify token enter the secret key you previously entered in Masi Chat. Click Verify and save, click Webhook fields > Manage, enable the following Webhook fields: messages. 
To verify the integration, simply go to https://developers.facebook.com and select your app. From there, click on “WhatsApp” in the left menu and then select “API Setup”. Copy the Phone number ID and paste it into Masi Chat > Settings > WhatsApp > Cloud API numbers > Phone number ID. Enter the desired phone number in the “To” field, such as your personal WhatsApp number, and send a test message. Check your WhatsApp account and send a reply, which should then appear in Masi Chat. To reply to the test number from Masi Chat, copy the “Temporary access token” and paste it in Masi Chat > Settings > WhatsApp > Cloud API numbers > Token. 
To activate the WhatsApp integration for all phone numbers and add a live phone number, refer to the following guidelines.
In Masi Chat > Settings > WhatsApp > Cloud API numbers > Token enter the permanent access token, follow the instructions below for getting it.
Visit https://business.facebook.com and go to Left menu > Settings > Business settings, then go to Users > System Users to view your admin system user, or create a new one. Open the user and click Add Assets, then select the app used for the WhatsApp API integration and check Develop App, or Full control. The system user needs to be an admin. If you do not see the option, click Business settings.
Click Left menu > Account > Apps. Select your app or add it. Make sure the system user is there and has full control. If not, click Add user, select the system user, click Full control, and click Assign.
Click Left menu > Apps and under Select Assets and choose your app, enable Develop App, or Full control and save.
From Users > System Users select the user you just creted and click Generate New Token, click Apps and select the app used for the WhatsApp API integration, set the Token expiration to Never, enable the following permissions: whatsapp_business_management, whatsapp_business_messaging, business_management. Click Generate Token and save. Paste the token in Masi Chat > Settings > WhatsApp > Cloud API numbers > Token. 
To add additional phone numbers, you can do so by visiting https://developers.facebook.com, selecting your app, and navigating to Left menu > WhatsApp > API Setup. To get started, click on Add phone number at the bottom and follow the instructions provided.
After activating the number, copy the Phone number ID and paste it into Masi Chat > Settings > WhatsApp > Cloud API numbers > Phone number ID.
If the number is in pending status, you need to enter your app dashboard and then from Left menu > WhatsApp > API Setup and click Generate access token.
Please keep in mind that if you use your current WhatsApp business number in Masi Chat, it will no longer be usable with your WhatsApp Business app, and you will need to migrate it following these instructions.
360dialog Account Setup
Go to https://www.360dialog.com/ and create a new account.
Enter your dashboard and from Left menu > WhatsApp Accounts generate the API key and copy and paste it in Masi Chat > Settings > WhatsApp > 360dialog settings.
Click Masi Chat > Settings > WhatsApp > 360dialog settings > Synchronize now.
Done! Masi Chat should start receiving the WhatsApp messages sent to your number, and you can reply to those messages from Masi Chat.
Note that you can also use the free sandbox account for testing, more details at https://docs.360dialog.com/whatsapp-api/whatsapp-api/sandbox. The sandbox account has limitations and some features, such as media attachments, will not work.
Twilio Account Setup
Go to https://www.twilio.com and create a new account.
Verify your phone number. 
Complete the form and choose WhatsApp, Alerts & Notifications, With no code at all, 3rd party integrations. 
From the Twilio console copy ACCOUNT SID and AUTH TOKEN and paste them into Masi Chat > Settings > WhatsApp > Twilio settings, save the changes. 
You will now set up a free test account to run some tests and make sure the integration works with Masi Chat. From the left menu click Messaging > Settings > WhatsApp sandbox settings and enter into WHEN A MESSAGE COMES IN and STATUS CALLBACK URL the URL of Masi Chat , get it from Masi Chat > Settings > WhatsApp > Twilio settings > Get configuration URL.
From the left menu click Messaging > Try it out > Send a WhatsApp message. Follow the instructions and send the message with the code to the WhatApp number provided. Click the next buttons until the configuration is complete. 
Done! Masi Chat should start receiving the WhatsApp messages sent to the sandbox account, and you can reply to those messages from the Masi Chat.
To publicly use the WhatsApp integration with your customers you need also to complete the steps below:
Update your account and enable billing, you can do that here.
Purchase a Twilio number, which will be the phone number of your official WhatsApp Business account. More details here. You cannot use the phone number of your existing WhatsApp Business account, you must use a Twilio number. More details here.
From the Twilio console go to Messaging > Services and create a new Messaging Service. Click Add Senders, select WhatsApp Number as the sender type, and add the Twilio number you purchased. Copy the Service SID and paste it into Masi Chat > Settings > WhatsApp > Twilio settings > Sender.
Templates
As for WhatsApp Business Policy, you cannot send outbound marketing and solicitation messages to end users. End user users must reach out to you first. You have 24 hours from when the end user’s message was sent from WhatsApp to reply to the message. To communicate with a user who has not contacted you before or has not been in touch for more than 24 hours, you must opt for the text message fallback or the WhatsApp message template.

To send message templates you have to add a payment method to your WhatsApp Business Account. You can do it from https://business.facebook.com/billing_hub/.
To send a specific message template to a group of users, use the direct messages feature.
Text message fallback
To enable the text message fallback you must set up the SMS in Settings > Notifications > Text message notifications.
More details here.

WhatsApp message templates
A WhatsApp message template is a message format that you can use over and over again to message users once they have opted-in and given your app permission to send them messages. You can not Enter the original message into the template, you must use it to notify the user of a new message and instruct him on how to view it, for example by providing a link to your website where the chat is shown.

WhatsApp Cloud API
To get the Template name and manage the templates visit https://business.facebook.com and go to Left menu > Settings > More system settings, then go to Accounts > WhatsApp accounts > Settings and click WhatsApp Manager.
In Template languages enter all the language codes supported by your template, separated by commas. Language codes list here (copy only the language code, e.g. it, es, en_US). If you use a template that supports multiple languages, the matching language for the user will be automatically selected. Otherwise, the default template language will be used.
In Header variables and Body variables enter the values separated by commas, you can use the following merge fields: {recipient_name}, {recipient_email}. The number of parameters entered here must match the number of parameters of the template. Use this feature if your template uses dynamic values. Order is important, the first merge field will be used as the first template parameter.
In Button variables enter the link of your custom buttons, or the text of the quick replies with prefix quick_reply_, e.g. quick_reply_abcde. Each value must be separated by a comma.
Template fallback
Set the WhatsApp template sent as fallback from Settings > WhatsApp > Cloud API template fallback.

Send template messages to a user who has not contacted you before
In Settings > WhatsApp > Cloud API settings > Business Account ID enter you Business Account ID. Provide your Business Account ID, which can be obtained from https://developers.facebook.com. Choose your app and go to Left menu > WhatsApp > API Setup.
To send the template to specific users, go to the Masi Chat Users section and choose the intended recipients. Then, click on the WhatsApp icon located at the top right corner.
If you are using the WhatsApp Cloud API with a template that supports multiple languages, the matching language for the user will be automatically selected. Otherwise, the default template language will be used.
If you are using Twilio and have parameters in your template, input the parameter values separated by commas in the Body section.
For more details click here.
Twilio
Enter the Template SID and the template attributes separated by commas into Masi Chat > Settings > Twilio template.

360dialog
Get the Namespace value from Left menu > WhatsApp accounts > Details.
Get Template name and Template default language from your 360dialog templates area.
In Custom parameters Enter the values separated by commas, you can use the following merge fields: {recipient_name}, {recipient_email}. Use this feature if your template uses dynamic values. Order is important, the first merge field will be used as the first template dynamic value.
Masi Chat will try to use the template with the same language as the user, if it is not available, it will use the template with the default language. The following template languages are not compatible: en_GB(use en_US), pt_PT(use pt_BR), zh_HK and zh_TW(use zh_CN), es_AR and es_MX(use es_ES).
WhatsApp flows
For more details about the WhatsApp Flows click here.

Built-in flows
The Masi Chat automatically generates and sends the following flow. To regenerate a flow, click the Settings > WhatsApp > Clear flows button.

Registraton – This flow is sent when a new user sends their first message to the WhatsApp number, and the Settings > Users > Require registration option is enabled.
Follow-up – This flow is sent if the Settings > Messages > Follow-up message option is active and the user does not have an email address.
Send custom flows
To send custom flows use the merge field {wa_flow id=”123″ header=”” body=”” button=””}. Replace 123 with the flow ID and enter a text for the attributes header, body, and button.

More information
You cannot send a WhatsApp message to a user who has sent you a message more than 24 hours ago or has never messaged you before. WhatsApp prohibits this action. Instead, you must use a WhatsApp template or send an text message. If you encounter an “Error message: Re-engagement,” it indicates this situation.
If you does not receive WhatsApp messages make sure you are not assigning the WhatsApp conversations to a department and that the WhatsApp number used for testing is not a phone number of a Masi Chat admin or agent.
If you can not send messages, an error should appear in the admin area when you try to send a message to the user.
We cannot provide support for Twilio or 360dialog configuration, including all related issues.
We cannot provide support in getting your WhatsApp account or WhatsApp message template approved.
WhatsApp conversations and messages are compatible with queue and routing.
If you are testing with the sandbox and after 72 hours you can no longer send messages to your phone number you must link again your phone number to your sandbox.
You can send rich messages to WhatsApp. If you send chips, buttons or select rich messages, with more than 3 options, you can use the whatsapp=”Your menu text” shortcode attribute to set the text of the WhatsApp message menu.
The follow-up message is supported, but the message is always sent, also if an agent replies.
The offline message is supported, but the timetable is not sent.
The chatbot is supported. The human takeover feature is also supported. To enable the Dialogflow chatbot support for audio messages, activate Settings > Artificial Intelligence > OpenAI > Speech recognition
The supported AI features include language detetction, spelling correction, multilingual via translation, Google search.
Twilio and 360dialog has many limitations. For example, Twilio does not support messages longer than 1600 characters. We strongly recommend to use the Official WhatsApp API.
Messenger
The settings below are related to the Messenger app.

Installation
From Settings > Apps, click Messenger and click Activate.
Automatic sync mode (coming soon)
Complete the synchronization by choosing at least 1 Facebook page and enter the returned information in Settings > Messenger > Facebook pages.
You’re done. All messages sent to the Facebook pages and Instagram accounts you selected will appear in the conversation admin area of Masi Chat. Mind that only new messages will be synchronized, the old ones will not be imported.
Instagram (coming soon)
To link Instagram to your Facebook page and Masi Chat follow the steps below.

Enter the Settings area of your Facebook Page and click Left Menu > Instagram (https://www.facebook.com/YOUR-PAGE-SLUG/settings/).
Click Connect account and complete the setup.
Sync Messenger with Masi Chat again and you’re done. Mind that only new messages will be synchronized, the old ones will not be imported.
More information
If you don’t receive Instagram messages:
Make sure to enable Settings > Privacy > Messages > Connected tools – Allow access from your Instagram mobile app.
Make sure your Instagram account is not setup as a professional account, it must be a business account.
Go to Meta Business Suite, select your account, and go to Users > People. Under Instagram account click Manage and make sure to enable all the permissions. 
If you don’t receive Facebook Messenger messages, make sure that the Facebook page does not send automated replies, such as the welcome message.
In case you encounter duplicated messages or an ongoing chat cycle between the chatbot and Instagram messages, you are facing a problem due to the presence of two Masi Chat installations or Masi Chat Cloud accounts that are both synchronized with the same Instagram account. To resolve this, access the second Masi Chat admin area, navigate to Settings > Messenger > Facebook pages, remove the Instagram ID value and save the changes. Try also to click the Unsubscribe button.
The Unsubscribe button remove the webhook subscription from all of your Facebook pages, it is useful if you want to stop receiving messages from a Facebook page.
Every Masi Chat user has only 1 Facebook conversation and 1 Instagram conversation.
Masi Chat rich messages are automatically converted to Facebook rich messages when possible, some part of the rich message could be removed or changed.
Only private Facebook messages will get sent to your team inbox. If someone posts a Facebook message on your wall it won’t appear in your team inbox.
When someone sends a message to your company Facebook page or Instagram account they will get designated as a lead in Masi Chat. You’ll only be able to see the user’s Facebook or Instagram name and profile picture.
Messenger conversations and messages are compatible with queue and routing.
The chatbot is supported. The human takeover feature is supported.
The supported AI features include language detetction, spelling correction, multilingual via translation, Google search.
If the chatbot is enabled, it is necessary to deactivate any automatic replies on Facebook Messenger, such as the welcome message.
The follow-up message is supported, but the message is always sent, also if an agent replies.
The offline message is supported, but the timetable is not sent.
Only 1 Facebook account can be synchronized, to link pages from multiple Facebook accounts, the account synchronized in Masi Chat must be an admin of all Facebook pages of the other Facebook accounts.
Using this integration of Instagram and Facebook Messenger comes at no additional cost.
If the message exceeds 1000 characters, only the initial 1000 characters will be transmitted due to the character limit.
Only new messages, sent after the synchronization, will be synchronized, the old ones will not be imported.
Twitter
The settings below are related to the Twitter app.

Installation
Register at https://developer.twitter.com. Make sure to verify your phone at https://twitter.com/settings/phone or the registration will fail.
Create your first app by entering the app name and clickGet keys, copy API Key (Consumer key) and API Key Secret (Consumer secret) and paste them in Masi Chat > Settings > Twitter.
Request the Elevated access from https://developer.twitter.com/en/portal/products/elevated. Click Apply for Elevated and complete the form as follow: In the first area In your words and in Will your app use Tweet, Retweet, Like, Follow, or Direct Message functionality? enter I need access to the Account Activity API to start receiving Twitter Direct Messages to my chat software(Masi Chat ) and to reply to them directly from Masi Chat , details at https://masichat.com/twitter. Disable all the other fields by clicking No: Are you planning to analyze Twitter data?, Do you plan to display Tweets or aggregate data about Twitter content outside Twitter?, Will your product, service, or analysis make Twitter content or derived information available to a government entity? 
Wait a few days for Twitter to review and approve the Elevated access, you will receive an email from Twitter.
Once you have Elevated access, enter the developers dashboard (https://developer.twitter.com/en/portal/dashboard) and from the left menu click Products > Premium > Dev environments and under Account Activity API / Sandbox click Set up dev environment, in Dev environment label enter mc or the same value entered in Settings > Twitter > Synchronization > Dev environment label.
Enter your app Settings area from Left menu > Projects & Apps > Your project > Your app and under User authentication settings click Set up and activate OAuth 1.0a. In App permissions check Read and write and Direct message, in Callback URI / Redirect URL enter the URL you get from Masi Chat > Settings > Twitter > Get callback URL, in Website URL enter your website URL. 
Enter your app Keys and tokens area from Left menu > Projects & Apps > Your project > Your app > Keys and tokens and under Authentication Tokens generate Access Token and Secret, copy and paste them in Masi Chat > Settings > Twitter.
Enter your Twitter profile username in Masi Chat > Settings > Twitter > Your username. Get it from your Twitter profile page, copy the name starting with @ or the URL part containing your username. Ex. https://twitter.com/MasiChat. 
Save the Masi Chat settings and click the button Masi Chat > Settings > Twitter > Subscribe and you’re done. All messages sent to your Twitter account will be received by Masi Chat.
More information
If you receive duplicate messages, the Twitter account you are using for testing may be the same as the one you synced. Try sending a message from another Twitter account.
When a message is received from a Twitter user you may send up to 5 messages in response within a 24 hour window. No messages can be sent after 24 hours of receiving the Twitter message.
You can send maximum 3 or 4 attachments depending by the media type.
The following Masi Chat rich messages are not supported: images slider, slider, card.
The chatbot is supported. The human takeover feature is supported.
The supported AI features include language detetction, spelling correction, multilingual via translation, Google search.
Telegram
The settings below are related to the Telegram app.

Installation
From Settings > Apps, click Telegram and click Activate.
Once the app is installed visit https://t.me/botfather.
If you have already created a Telegram bot in the past, type the command /mybots and open your bot, then click API token.
If you never created a Telegram bot before, type the command /newbot to create a new bot. The BotFather will ask you for a name and username, enter them and generate the authentication token. More details at https://core.telegram.org/bots.
Copy the token and paste it into Masi Chat > Telegram > Token, then click Synchronize now.
You’re done. All messages sent to your Telegram bot will appear in the conversation admin area of Masi Chat.
More information
Masi Chat rich messages are automatically converted to Telegram rich messages when possible, otherwise they are removed from the message.
Telegram conversations and messages are compatible with queue and routing.
The chatbot is supported. The human takeover feature is supported.
The supported AI features include language detetction, spelling correction, multilingual via translation, Google search.
The follow-up message is supported, but the message is always sent, also if an agent replies.
The offline message is supported, but the timetable is not sent.
You can verify the webhook status from https://api.telegram.org/bot><your_bot_token>/getWebhookInfo. Replace <your_bot_token> with your token.
Viber
The settings below are related to the Viber app.

Installation
From Settings > Apps, click Viber and click Activate.
Create the bot at https://partners.viber.com/account/create-bot-account.
Copy the token and paste it into Masi Chat > Viber > Token, then click Synchronize now.
You’re done. All messages sent to your Viber bot will appear in the conversation admin area of Masi Chat.
More information
Masi Chat rich messages are automatically converted to Viber rich messages when possible, otherwise they are removed from the message.
Viber conversations and messages are compatible with queue and routing.
The chatbot is supported. The human takeover feature is supported.
The supported AI features include language detetction, spelling correction, multilingual via translation, Google search.
The follow-up message is supported, but the message is always sent, also if an agent replies.
The offline message is supported, but the timetable is not sent.
Slack
The settings below are related to the Slack App.

Installation
From Settings > Apps, click Slack and click Activate.
Once the app is installed go to Settings > Slack, click the Synchronize now button, and follow the instructions.
Having Problems?
The synchronization of Slack may not be successful for a number of reasons; below are the most common ones:

Problem description	Solution
You can receive messages on Slack, but you cannot send messages from Slack to Masi Chat	Navigate to Workspace Settings > Permissions, you must allow anyone to create public channels.  If you have further issues please contact the Slack support team at https://api.slack.com/support.
Slack sync not working	Double check whether or not you chose a public Slack channel when you attempted to synchronize Slack. If you did not, try syncing Slack once again, this time choosing a public channel instead. The general channel is a good option.
Manually archive channels
To archive a channel in Slack, follow these steps:

Open the Slack channel you want to archive.
On the top right click the gear icon and select Additional options. 
Click the info icon in the top right area of the screen, then click the More icon, and then click Additional options…. Click Archive this channel. 
Department linking
If the Settngs > Slack > Department linking option is active, when a conversation in the Masi Chat is assigned to a department, a new message is sent to the linked Slack channel, informing Slack users that a new conversation has started and inviting them to join the dedicated user Slack channel. However, the full conversation can only be accessed on the dedicated Slack channel of the specific user.

Get the department IDs from Settings > Miscellaneous > departments.
Get the channel IDs by clicking the button Get channel IDs.
User fields
The Settings > Slack > User fields option allows you to choose which user details to include in the message sent to the main channel when a new user sends the first message. You can include the slug of your custom user details or the following slugs: browser, browser_language, location, email, user_type, token, creation_time, country_code, current_url, os, city, address, postal_code, phone, birthdate, company, timezone, website. Default: email, browser, browser_language, location.

Information
When a new user sends the first message, a Slack message with the user details and a button to join the user’s channel is sent to the main channel selected during the synchronization, or to the channel linked to the conversation’s department. Only the Slack account used during the synchronization will automatically join the user’s channel, other Slack members will have to join it manually via the button.
When Settings > Slack > Agent linking is set, if the conversation is assigned to a department, only agents assigned to that department will receive the message, if the conversation is assigned to a single agent only that agent will receive the message.
Slack is free to use. Only large companies may need a paid subscription plan, more details here.
Push notifications are compatible with Slack, when agents send messages via Slack, Push notifications are sent to users.
If you’re using Dialogflow, no messages will be received in Slack if the chatbot knows the answer. Once the human takeover is active all conversation’s messages are sent to Slack.
The Slack app is compatible with email piping and the messaging apps.
Deleting or leaving a channel is not supported, only archiving a channel is supported.
When a conversation is archived in Masi Chat , the linked Slack channel is also archived.
When replying via Slack to a user, if the user is notified by email or text message, a Slack message is sent to notify you.
Use the Slack command /archive to archive a conversation from Slack.
Zendesk
The settings below are related to the Zendesk App.

Installation
From Settings > Apps, click Zendesk and click Activate.
Get the domain from the URL of your Zendesk admin area, copy the first part of the URL: https://domain.zendesk.com/. For example, the domain of https://masichat.zendesk.com/agent/get-started/ticketing-system/ticketing-intro is masichat.
Get the API key from Left menu > Admin > Channels > API > Settings. Click Add API token. 
The email is your Zendesk account email.
More information
Tickets converted by Masi Chat are automatically synchronized when new messages are sent and received in Masi Chat , and they are linked to an existing Zendesk user if any, otherwise a new Zendesk user is created.
Masi Chat links Zendesk users to Masi Chat users via email or phone number.
LINE
The settings below are related to the LINE app.

Installation
From Settings > Apps, click LINE and click Activate.
Login at https://developers.line.biz/console/ or create a new account.
Create a new Provider and then a new Messaging API channel, select Messaging API as channel type
From the channel Basic settings area copy Channel secret and paste it into Masi Chat > Settings > Line > Synchronization > Channel secret.
From the channel Messaging API area generate a Channel access token (long-lived), copy it and paste it into Masi Chat > Settings > Line > Synchronization > Access token.
Enter your Webhook URL into Webhook URL and click Verify. Get the Webhook URL from Masi Chat > Settings > Line > Synchronization > Webhook URL.
Enable Use webhook.
Scan the QR code with your LINE mobile app to start writing to your channel.
Disable the Auto-reply messages setting.
You’re done. All messages sent to your LINE bot account will appear in the conversation admin area of Masi Chat.
More information
You have 7 days from when the end user’s message was sent from WhatsApp to reply to the message.
Stickers are not supported.
Masi Chat rich messages are automatically converted to LINE rich messages when possible, otherwise they are removed from the message.
LINE conversations and messages are compatible with queue and routing.
The chatbot is supported. The human takeover feature is supported.
The supported AI features include language detetction, spelling correction, multilingual via translation, Google search.
The follow-up message is supported, but the message is always sent, also if an agent replies.
The offline message is supported, but the timetable is not sent.
WeChat
The settings below are related to the WeChat app.

Installation
From Settings > Apps, click WeChat and click Active.
Visit https://mp.weixin.qq.com/cgi-bin/readtemplate?t=register/step1_tmpl&lang=en_US and register a Service account.
After the registration enter Official accounts area and from the left menu click Settings and development > WeChat verification. You must complete the verification, it takes a few weeks and costs USD 99, or CNY 300 for chinese entities.
From the left menu click Settings and development > Basic configuration and copy Developer ID(App ID), Developer Password(App Secret). Paste the information into Masi Chat > Settings > WeChat.
From the left menu click Settings and development > Basic configuration and complete the server configuration (服务器配置(已启用). In Server Address(URL) enter the URL you get from Masi Chat > Settings > WeChat > Synchronization > Get configuration URL. In Token insert any value you want, the same value must be entered in Masi Chat > Settings > WeChat > Token.
You’re done. All messages sent to your WeChat account will appear in the conversation admin area of Masi Chat.
More information
If you receive an error like {"errcode":41001,"errmsg":"access_token missing rid: 631111-470b3b22-********"}, you need to whitelist our server IP address: **************. You can do this from your WeChat Official Account under Settings and Development > Basic Configuration > IP Whitelist.
If you continue to experience issues, please contact our support team for assistance.
WeChat files and location attachments are not supported and are not received by Masi Chat.
Links are not supported in WeChat, they are converted to texts.
Masi Chat rich messages are automatically converted to WeChat rich messages when possible, otherwise they are removed from the message.
WeChat conversations and messages are compatible with queue and routing.
The chatbot is supported. The human takeover feature is supported.
The supported AI features include language detetction, spelling correction, multilingual via translation, Google search.
The follow-up message is supported, but the message is always sent, also if an agent replies.
The offline message is supported, but the timetable is not sent.
Zalo
The settings below are related to the Zalo app.

Installation
From Settings > Apps, click Zalo and click Activate.
Register at https://developers.zalo.me/ and create a new App. Enter the required information and save.
Copy the Application ID and Application secret key from the app you just created and paste them into Masi Chat > Settings > Synchronization > Zalo.
From the left menu click Webhooks and set the URL, get the URL from Masi Chat > Settings > Zalo > Synchronization > Webhook URL.
From the left menu click Webhooks copy OA Secret Key and paste it into Masi Chat > Settings > Zalo > Synchronization > OA secret key.
From the left menu click Webhooks and enable the following webhook events: user_send_location, user_send_image, user_send_link, user_send_text, user_send_sticker, user_send_gif, user_received_message, user_seen_message, oa_send_text, oa_send_image, oa_send_list, oa_send_gif, user_send_audio, user_send_video, user_send_file, user_reacted_message, user_received_message.
From the left menu click Official Account > OA Management and link your Official Account.
From the top menu click Tools > API Explorer or go to https://developers.zalo.me/tools/explorer/. As Access token type select OA Access Token, click Get Access Token and select your Official Account. Copy the Refresh token and paste it into Masi Chat > Settings > Zalo > Synchronization > Refresh token. More details here.
From the left menu click Role and a test user as admin. You will use this user to send message to your Zalo Official Account and test the integration.
From the left menu click Sign up to use API > Official AccountAPI enable User Management and click Submit for review.
You’re done. All messages sent to your Zalo Official account will appear in the conversation admin area of Masi Chat.
More information
You need a Zalo Official Account to use this integration. Masi Chat will receive messages sent to your Zalo Official Account.
The slider rich message only send the first element to Zalo.
Masi Chat rich messages are automatically converted to Zalo rich messages when possible.
Zalo conversations and messages are compatible with queue and routing.
The chatbot is supported. The human takeover feature is supported.
The supported AI features include language detetction, spelling correction, multilingual via translation, Google search.
The follow-up message is supported, but the message is always sent, also if an agent replies.
The offline message is supported, but the timetable is not sent.
Credits
Credits are used by the following functions, only in Automatic sync mode. The Manual sync mode doesn’t use credits. If you do not want to use credits, you can use the Manual sync mode and your API keys.

Artificial Intelligence > Google > Dialogflow chatbot and Automatic Translations, Language detection, Multilingual via translation.
Artificial Intelligence > OpenAI > Chatbot and Spelling correction, Message rewrite button, Speech recognition.
Pricing
Credits must be purchased from https://app.masichat.com/account/?tab=installation. Here are the costs of functions that use credits.

Dialogflow ES $0.004 per request.
Dialogflow CX $0.014 per request.
Dialogflow ES Audio Message $0.000866 per second of audio.
Dialogflow CX Audio Message $0.002 per second of audio.
Google Translations $0.00004 per char.
OpenAI gpt-3.5-turbo-instruct $0.000004 per token.
OpenAI gpt-3.5-turbo $0.000004 per token.
OpenAI gpt-4 $0.00012 per token.
OpenAI gpt-4-32k $0.00024 per token.
OpenAI text-embedding-3-small $0.******** per token.
OpenAI audio-to-text whisper $0.0002 per second of audio.
Information
When your credits run out, if the automatic recharge is not enabled, you will be notified via email. You will receive a maximum of two email notifications. Activate the automatic recharge to prevent running out of credits.
When automatic recharge is enabled, the credit will be recharged once it falls below 1.
API token
You can find your API token at https://app.masichat.com/account/?tab=installation.
You can switch between accounts without having to sign out and in by adding the accounts in Settings > Admin > Switch accounts.
If you are experiencing a white screen while accessing https://app.masichat.com/account/?tab=installation, add the URL attribute reset-login to the URL and open it again. E.g. https://app.masichat.com/?reset-login.
Miscellaneous
This section contains help for other features not listed above.

Progressive Web App
The Masi Chat admin area is a Progressive Web App – PWA, which means that you can install it on desktop, Mac, iPhone, or mobile devices and use it like a fully-functional app. Note: This feature is optimized for Google Chrome and Safari. The PWA is supported on all versions of Masi Chat.

Desktop installation
Enter in your admin area, and click the + icon on the top right of the URL bar of your Chrome browser. If you are using the WordPress version see the information below. 

Mobile installation – Android and Windows – All versions
Enter in your admin area with Google Chrome
Open your browser’s settings.
Scroll down and tap Add to Home screen.
Confirm by tapping Add.
Mobile installation – iPhone or Mac – All versions
Enter in your admin area with Safari
Press the Share button and select Add to Home Screen from the popup.
Tap Add in the top right corner to finish installing the PWA.
Change PWA Icon and Name
Prepare Your Manifest File:
Download the default manifest.json file and update it with your brand information:
Replace YOUR NAME with your brand name.
Replace YOUR NAME DESCRIPTION with your desired description.
Replace example.png with the URL of your 512x512px icon.
Host the Manifest File:
Upload the customized manifest.json file to a publicly accessible URL (e.g., your website or a file hosting service).
Set the Manifest File URL:
Go to Settings > PWA Settings in Masi Chat and enter the URL of your customized manifest.json file in the Manifest File URL field.
Note:
This customization is not covered by our support. If you need help, our consulting services are available.
Keyboard shortcuts
Admin area keyboard shortcuts are enabled on both PC and MAC and work as follows:

Shortcut	Description
ENTERorSPACE	Confirm or close a dialog alert; the same as clicking OK.
ESCorCANCEL	Decline a dialog alert and close it.
SHIFT + ENTER orCTRL + ENTER	Add a line break to a message. This only works for the admin editor.
ESC	Close a lightbox.
CANCEL	In the admin conversations area, archive a conversation, or delete it.
CTRL + UP/DOWN ARROW	In the admin conversations area, navigate between the conversations of the list.
CTRL + RIGHT/LEFT ARROW	In the admin users area, navigate between the users.
CTRL + V	Paste an image from the clipboard and send it as message.
CTRL + Left mouse click	To perform actions like archiving, deleting, marking as read, or marking as unread on conversations in the admin area, press and hold the CTRL button while selecting them from the list on the left.

Show more
Cron Jobs
Cron jobs in Masi Chat are executed automatically by the first user visiting the website at any given hour of the day. They run every 60 minutes (and every 60 seconds for email piping) based on website traffic. As long as there is at least one active user, the execution frequency is maintained.

URL parameters
Front-end chat
URL parameters allow the chat to perform specific actions on page load. To use them, append the URL parameters below to any URL on your website that displays the chat. Ex. https://app.masichat.com/?conversation=174

URL parameter	Description
?token=TOKEN	Login an existing user. Replace TOKEN with the user’s token.
?conversation=ID	Open a conversation. Replace ID with the conversation ID. The attribute token is required for not logged in users.
?chat=open	Open the chat.
Admin area
URL parameters allow the administration area to perform specific actions on page load. To use them, append the URL parameters below to your admin URL. Ex. https://app.masichat.com/admin.php?conversation=1234 or https://app.masichat.com/?conversation=1234.

URL parameter	Description
?conversation=ID	Open a conversation of the conversations area. Replace ID with the conversation ID.
?user=ID	Open the profile box of a user. Replace ID with the user ID.
?setting=ID	Open a setting of the settings area. Replace ID with the setting ID.
?report=ID	Open a report of the reports area. Replace ID with the report ID.
?area=name	Open an area of the admin. Replace name with: conversations, users, settings, reports.
?login_email=email&login_password=password	Login an existing agent or user automatically. Replace email with the account email and password with the account password.

Show more
Calendly
Follow the steps below to send a Calendly booking invitation.

Create an event type from https://calendly.com/event_types/user/me.
Click share and copy the URL. 
To send the Calendly invitation, utilize the rich message button type. Ensure to enter the Calendly URL you copied in the previous step as the URL. Additionally, include the success attribute to customize the message that will be sent after the booking is successfully completed. E.g. [button link=”https://calendly.com/masichat/mc” name=”Schedule a meeting” success=”Thank you! Your meeting has been scheduled.”]
Zapier (coming soon)
Security
Security is critical here at Masi Chat. We work with security experts who perform periodic security audits. We continually implement the latest security technologies and stay up-to-date on the latest security threats. Find below some of the security measures that have been implemented in Masi Chat.

IP ban
Masi Chat permits a maximum of 10 unsuccessful login attempts within an hour. In case the limit is exceeded by a user, admin, or agent, they will be unable to access their account or the admin area for a period of 1 hour. This blocking mechanism is based on the user’s IP address and serves to deter brute force attacks aimed at uncovering login credentials. If you encounter the “Too many login attempts. Please retry again in a few hours.” error, you have one option: wait for a few hours.

XSS injection protection
To prevent XSS injection protection attacks, all sensitive user inputs undergo sanitization.

Cross-Site Request Forgery (CSRF) protection
To prevent CSRF attacks, all requests are validated by checking the login cookie and the client-side login string.

Active sessions termination on user update
Whenever the password or the details for an admin or agent is changed, all active sessions are automatically logged out when sensitive operations are executed or after 1 hour.

AES-256-bit encryption
We use 256-bit AES encryption to encrypt sensitive data such as active session login data.

Password by filename protection
Masi Chat ensures that all uploads are renamed with a random alphanumeric string prepended to the original file name to prevent discovery of the URL and unauthorized download of the uploaded file.

