"use strict";!function(t){let s,a,i,r,o,n,l,c=document.location.href,d=c,u=t(window).width()<465,p="razorpay"==PAYMENT_PROVIDER,m="stripe"==PAYMENT_PROVIDER,f="rapyd"==PAYMENT_PROVIDER,h="paystack"==PAYMENT_PROVIDER,b="messages-agents"==MEMBERSHIP_TYPE,g={password_length:L("The password must be at least 8 characters long."),password_match:L("The passwords do not match."),email:L("The email address is not valid.")},v={stripe:"stripe-create-session",paystack:"paystack-create-session",rapyd:"rapyd-checkout",verifone:"verifone-checkout",razorpay:"razorpay-create-subscription",yoomoney:"yoomoney-create-subscription"};function y(e,s={},a=!1){t.extend(s,{function:e}),console.log(`AJAX Request: ${e}`);t.ajax({method:"POST",url:"ajax.php",data:s,timeout:3e4,beforeSend:function(t){t.setRequestHeader("X-Request-Function",e)}}).done(((t,s,i)=>{if(a){let s;if(null==t)s={status:"error",message:"The server returned an empty response. Please try again later.",error_type:"empty-response"};else if(!1===t)s={status:"error",message:"Authentication failed. Please check your credentials and try again.",error_type:"auth-error"};else if("ip-ban"===t)s={status:"error",message:"For security reasons, your account has been temporarily locked due to too many failed attempts. Please try again later or contact support.",error_type:"ip-ban"};else if("string"==typeof t)if(t.trim().startsWith("{")||t.trim().startsWith("["))try{s=JSON.parse(t)}catch(e){console.warn("JSON parsing failed for response:",t.substring(0,100)+(t.length>100?"...":"")),s=t.includes("500")||t.includes("Internal Server Error")?{status:"error",message:"The server encountered an internal error. Please try again later or contact support if the problem persists.",error_type:"server-error"}:t.includes("404")||t.includes("Not Found")?{status:"error",message:"The requested resource was not found. Please check your request and try again.",error_type:"not-found"}:t.includes("timeout")||t.includes("timed out")?{status:"error",message:"The request timed out. Please try again later.",error_type:"timeout"}:{status:"error",message:"We encountered an issue processing the server response. Please try again or contact support if the problem persists.",error_type:"parse-error"}}else s=t.includes("error")||t.includes("failed")||t.includes("invalid")?{status:"error",message:t,error_type:"string-error"}:t;else"object"==typeof t?(s=t,"error"!==s.status||s.error_type||(s.error_type="api-error")):s=t.toString();s&&"error"===s.status&&console.warn(`AJAX Error (${e}):`,s.error_type||"unknown",s.message),a(s)}})).fail(((t,s,i)=>{console.warn(`AJAX Request Failed (${e}):`,s,i);let r="We couldn't connect to the server. Please check your internet connection and try again.",o="connection-error";"timeout"===s?(r="The request timed out. The server might be experiencing high load. Please try again later.",o="timeout-error"):"parsererror"===s?(r="We encountered an issue processing the server response. Please try again or contact support if the problem persists.",o="parse-error"):"abort"===s?(r="The request was aborted. Please try again.",o="abort-error"):404===t.status?(r="The requested resource was not found. Please check your request and try again.",o="not-found"):403===t.status?(r="You don't have permission to access this resource. Please log in again or contact support.",o="permission-error"):500===t.status?(r="The server encountered an internal error. Please try again later or contact support if the problem persists.",o="server-error"):0===t.status&&""===i&&(r="We couldn't connect to the server. Please check your internet connection and try again.",o="network-error"),a&&a({status:"error",message:r,error_type:o,http_status:t.status})}))}function w(e,t,s){let a=new Date,i="";s?(a.setTime(a.getTime()+24*s*60*60*1e3),i="; expires="+a.toUTCString()):0===s&&(i="; expires=Thu, 01 Jan 1970 00:00:01 GMT"),document.cookie=e+"="+(t||"")+i+"; path=/; SameSite=None; Secure"}function _(e,t){w("mc-cloud",e,3650),w("mc-login",t,3650)}function P(e){let s=t(e);return!!s.hasClass("mc-loading")||(s.addClass("mc-loading"),!1)}function k(e,a,i="",o=!1,n=!1,l=!1){let c=t(".mc-tab > .mc-content > .mc-active");c.length||(c=r.find(".mc-admin-tab.mc-active"),c.length||(c=s.find(".mc-tab > .mc-content").first(),c.length||(c=s)));let d="banner-"+function(e){if("string"!=typeof e)return"";e=e.trim().toLowerCase();const t="àáâãäåāçćčèéêëēęîïíīìłñńôöòóõøōřßşśšșțûüúùūůŵýÿŷžźż·/_,:;",s="aaaaaaaacceeeeeeiíiiilnnooooooorsssssttuuuuuuwyyyzzz------";for(let a=0,i=t.length;a<i;a++)e=e.replace(new RegExp(t.charAt(a),"g"),s.charAt(a));return e.replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,"")}(String(e||"banner-"+I()));s.find(`#${d}`).remove(),c.prepend(`<div id="${d}" class="banner${o?" banner-img":""}${n?" banner-error":""}${l?" banner-success":""}">\n                ${o?`<img src="${o}" alt="Notification Image"/>`:""}\n                <h2>${L(e)}</h2>\n                <p>${L(a)}</p>\n                <div>${i}</div>\n                <i class="mc-btn-icon mc-icon mc-icon-close"></i>\n            </div>`),M(c)}function A(e){k("Success",e,"",!1,!1,!0)}function S(e){e.includes("Error")||e.includes("error")||e.includes("failed")||e.includes("invalid")?e="We encountered an issue while processing your request. Please try again or contact support if the problem persists.":e.includes("duplicate-email")?e="This email address is already registered. Please use a different email address or try to log in if you already have an account.":e.includes("duplicate-phone")?e="This phone number is already registered. Please use a different phone number or try to log in if you already have an account.":e.includes("invalid-password")?e='The password you entered is incorrect. Please try again or use the "Forgot Password" link if you need to reset your password.':e.includes("invalid-login")?e="The email or password you entered is incorrect. Please check your credentials and try again.":e.includes("ip-ban")&&(e="For security reasons, your account has been temporarily locked due to too many failed login attempts. Please try again later."),k("Error",e,"",!1,!0,!1)}function E(e){if("undefined"!=typeof membership&&null!==membership)switch(e){case"suspended":let e=b&&membership&&"object"==typeof membership&&"quota_agents"in membership,t=membership&&"object"==typeof membership&&"count_agents"in membership,s=membership&&"object"==typeof membership&&"quota"in membership,i=membership&&"object"==typeof membership&&"count"in membership,r=e&&t&&membership.count_agents>membership.quota_agents;if(s&&i&&membership.count>membership.quota||membership&&membership.expired||r){k("undefined"!=typeof SETTINGS&&SETTINGS&&SETTINGS.text_suspended_title||"Your account needs attention",("undefined"!=typeof SETTINGS&&SETTINGS&&SETTINGS.text_suspended||L("Your website visitors can still use the chat, but you cannot view messages or reply to visitors because your account requires attention. Please renew your subscription below or upgrade to a higher plan to restore full access to your account."))+(r?" "+L("You have exceeded your agent limit. You can manage your team members and restore your account by clicking {R}.").replace("{R}",'<a href="#" id="delete-agents-quota">'+L("here")+"</a>"):""),"",!1,!0,!1)}break;case"verify":if(!a||!a.length)return;let o=a.find(".btn-verify-email").length,n=a.find(".btn-verify-phone").length,l=o&&n?"email and phone number":o?"email":"phone number";!o&&!n||c.includes("welcome")||k(`Verify your ${l}`,`Please verify your ${l} from the profile area.`,"",!1,!0)}else console.warn("Membership data not available for banner check:",e)}function x(e){return!0===e||1==e||"true"===String(e).toLowerCase()}function L(e){return"undefined"!=typeof MC_TRANSLATIONS&&null!==MC_TRANSLATIONS&&"object"==typeof MC_TRANSLATIONS&&e in MC_TRANSLATIONS?MC_TRANSLATIONS[e]:e}function C(e){if("undefined"==typeof MEMBERSHIPS||!Array.isArray(MEMBERSHIPS))return console.error("MEMBERSHIPS data is missing or invalid."),{id:e||"unknown",name:"Unknown Plan"};for(var t=0;t<MEMBERSHIPS.length;t++)if(MEMBERSHIPS[t].id==e)return MEMBERSHIPS[t];return MEMBERSHIPS.find((e=>"free"===e.id))||MEMBERSHIPS[0]||{id:e||"unknown",name:"Unknown Plan"}}function T(e){try{let t=JSON.parse(e.value);if(!Array.isArray(t)||t.length<6)return console.error("Invalid invoice data structure:",e.value),`<div class="mc-invoice-row" data-id="${e.id||""}"><span>Error loading invoice</span></div>`;let s="Invalid Date",a=parseInt(t[5]);isNaN(a)||(s=new Date(1e3*a).toLocaleString());let i=CLOUD_CURRENCY||"",r=t[0]||0,o=t[1]?R(t[1]):"Unknown Plan";return`<div class="mc-invoice-row" data-id="${e.id||""}"><span>INV-${t[5]}-${CLOUD_USER_ID||""}</span><span>${i} ${r}</span><span>${o}</span><span>${s}</span></div>`}catch(t){return console.error("Error parsing invoice data:",t,e.value),`<div class="mc-invoice-row" data-id="${e.id||""}"><span>Error loading invoice</span></div>`}}function $(e){if(!e||"object"!=typeof e)return"Invalid invoice item<br>";if((m||p||h)&&e.created&&e.currency){let t=e.amount_paid||e.amount,s=e.currency.toUpperCase(),a=(t/(["BIF","CLP","DJF","GNF","JPY","KMF","KRW","MGA","PYG","RWF","UGX","VND","VUV","XAF","XOF","XPF"].includes(s)?1:100)).toFixed(2);return`${new Date(1e3*e.created).toISOString().slice(0,10)} | ${s} ${a} | ${e.number||e.id||e.reference||"N/A"}<br>`}if(f&&e.paid_at&&e.currency_code){let t=new Date(1e3*e.paid_at).toISOString().slice(0,10),s=e.payment_method_type?e.payment_method_type.replace(/_/g," ").toUpperCase():"N/A";return`${e.currency_code.toUpperCase()} ${e.amount||0} | ${t} | ${s}<br>`}return e.OrderDate&&e.Currency&&e.NetPrice?`${e.Currency.toUpperCase()} ${e.NetPrice} | ${e.OrderDate} | ${e.RefNo||"N/A"}<br>`:`Invoice | ${e.id||"N/A"}<br>`}function R(e){return"string"!=typeof e?"":(e=e.replace(/_/g," ").replace(/-/g," ")).charAt(0).toUpperCase()+e.slice(1)}function I(){let e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",t="";for(var s=5;s>0;--s)t+=e[Math.floor(62*Math.random())];return t}function N(e,s){let a=t(e);return!1===s||!0===s?(a.setClass("active",s),a):a.hasClass("active")}function M(e=null){let t=null;e&&e instanceof jQuery&&e.length?t=e[0]:r&&r.length?t=r.find(".mc-admin-tab.mc-active .mc-scroll-area")[0]||r.find(".mc-scroll-area")[0]:a&&a.length&&(t=a.find("> .mc-tab > .mc-content.mc-active")[0]||a.find("> .mc-tab > .mc-content")[0]),t?t.scrollTop=0:window.scrollTo(0,0)}t(document).ready((function(){if(s=t("body"),a=s.find(".mc-account-box"),i=!a.length&&s.find(".mc-registration-box"),r=s.find(".mc-super-box"),n=s.find(".mc-profile-edit-box"),o=s.find(".mc-loading-global"),s.removeClass("on-load"),function(){if("undefined"==typeof FB_APP_ID||!FB_APP_ID)return;if("undefined"!=typeof FB)return void console.log("Facebook SDK already initialized");window.fbAsyncInit=function(){FB.init({appId:FB_APP_ID,cookie:!0,xfbml:!0,version:"v18.0",status:!0,autoLogAppEvents:!0}),FB.AppEvents.logPageView()},e=document,a="script",i="facebook-jssdk",o=e.getElementsByTagName(a)[0],e.getElementById(i)||((r=e.createElement(a)).id=i,r.src="https://connect.facebook.net/en_US/sdk.js",o.parentNode.insertBefore(r,o)),s.on("click",".mc-btn-facebook",(function(){let e=t(this);if(!P(e))return"undefined"==typeof FB?(console.error("Facebook SDK not loaded"),S("Facebook login is not available at the moment. Please try again later or use regular login."),void e.stopLoading()):void FB.login((function(t){if(t.authResponse){const s=t.authResponse.accessToken;FB.api("/me",{fields:"name,email,id",access_token:s},(function(t){if(!t)return k("Facebook Error","Could not get information from Facebook. Please try again or use regular login.","",!1,!1,!0),void e.stopLoading();!t.email&&t.id&&(console.log("Facebook did not provide email, but we have ID: "+t.id),t.email="fb_"+t.id+"@facebook.com"),t.access_token=s,y("facebook-login",{fb_data:t},(function(t){if(t&&"error"===t.status)return k("Login Error",t.message||"Facebook login failed","",!1,!1,!0),void e.stopLoading();Array.isArray(t)&&t.length>=2?(_(t[0],t[1]),t.length>3&&"welcome"===t[3]?setTimeout((function(){document.location=CLOUD_URL+"/account?welcome"}),300):setTimeout((function(){document.location=CLOUD_URL}),300)):(k("Login Error","Login failed. Please try again.","",!1,!1,!0),e.stopLoading())}))}))}else e.stopLoading()}),{scope:"email,public_profile",auth_type:"rerequest",display:"popup",return_scopes:!0,enable_profile_selector:!0,locale:"en_US"})}));var e,a,i,r,o}(),s.on("click",".mc-nav li",(function(){t(this).siblings().mcActive(!1),t(this).mcActive(!0),t(this).closest(".mc-tab").find("> .mc-content > div").mcActive(!1).eq(t(this).index()).mcActive(!0)})),s.on("click",".mc-lightbox .mc-close",(function(){t(this).closest(".mc-lightbox").mcActive(!1),s.find(".mc-lightbox-overlay").mcActive(!1)})),s.on("click",".mc-lightbox .mc-info",(function(){t(this).mcActive(!1)})),s.on("click",".banner > i",(function(){t(this).parent().remove()})),c.includes("?")&&(d=c.substring(0,c.indexOf("?"))),c.includes("reload=true"))return a.startLoading(),void setTimeout((()=>{document.location=c.replace("&reload=true","").replace("?reload=true","")}),2e3);if(u&&(s.on("click",".mc-nav,.mc-menu-wide",(function(){t(this).setClass("mc-active",!t(this).hasClass("mc-active"))})),s.on("click",".mc-nav li,.mc-menu-wide li",(function(){t(this).parents().eq(1).find(" > div").html(t(this).html())}))),a.length){let s,i,r=a.find("#chart-usage"),o=["installation","membership","invoices","profile"],n=a.find("#tab-profile .mc-input:not(#password)").map((function(){return t(this).attr("id")})).get(),u=a.find(".plans-box-menu");if((c.includes("payment_status=success")||c.includes("payment_status=success_pending_webhook"))&&(t(".banner").remove(),console.log("Payment success URL:",c),console.log("Contains payment_type=credits:",c.includes("payment_type=credits")),console.log("Contains #credits:",c.includes("#credits")),c.includes("payment_type=credits")||c.includes("#credits")?(console.log("Detected credits purchase success from URL parameters"),window.isCreditsPayment=!0,setTimeout((()=>{t(".banner").remove(),A("Credits purchase successful! Your credits have been added to your account.")}),500)):y("account-membership-details",{},(e=>{e&&e.membership&&"credits"===e.membership.last_payment_type?(console.log("Detected credits purchase from last_payment_type"),window.isCreditsPayment=!0,t(".banner").remove(),A("Credits purchase successful! Your credits have been added to your account.")):(console.log("Detected plan purchase success"),window.isCreditsPayment=!1,t(".banner").remove(),A("Your plan has been updated successfully!"))})),setTimeout((()=>{y("account-membership-details",{},(e=>{e&&!e.status&&e.membership&&(t(".membership-quota").first().text(9999==e.membership.quota?"∞":e.membership.quota),t(".membership-name").text(e.membership.name),void 0!==e.membership.credits&&t("#credits .credits-amount").text(e.membership.credits),t("#plans > div").removeAttr("data-active-membership").removeAttr("data-expired"),t('#plans > div[data-id="'+e.membership.id+'"]').attr("data-active-membership","true"),t(".active-membership-info").remove(),t('#plans > div[data-id="'+e.membership.id+'"]').prepend('<div class="active-membership-info">'+L("Active Membership")+"</div>"),console.log("Membership data updated successfully"))}))}),1e3)),c.includes("#credits")&&setTimeout((()=>{a.find("#credits")[0].scrollIntoView()}),500),y("account-user-details",{},(e=>{l=e,a.find("#embed-code").val(`\x3c!-- ${BRAND_NAME} --\x3e\n<script id="chat-init" src="${CLOUD_URL}/account/js/init.js?id=${e.chat_id}"><\/script>`),a.stopLoading();for(var t=0;t<n.length;t++)n[t]in e&&a.find(`#${n[t]} input`).val(e[n[t]]);for(t=0;t<2;t++){let s=t?"email":"phone";1!=e[s+"_confirmed"]&&(t||TWILIO_SMS)||a.find("#"+s).removeClass("mc-type-input-button").find(".mc-btn").remove()}E("verify")})),u.length){let e=u.find("li").eq(0).mcActive(!0);a.find("#plans > div").removeClass("mc-visible"),a.find(`#plans > [data-menu="${e.attr("data-type")}"]`).addClass("mc-visible"),t(u).on("click","li",(function(){u.find("li").mcActive(!1),a.find("#plans > div").removeClass("mc-visible"),a.find(`#plans > [data-menu="${t(this).attr("data-type")}"]`).addClass("mc-visible"),t(this).mcActive(!0)})),a.find("#membership-appsumo").length&&u.find("ul").append('<li><a href="https://appsumo.com/account/products/" target="_blank" style="color:#028be5;text-decoration:none">AppSumo</a></li>')}else a.find("#plans > div").addClass("mc-visible");if(a.on("click"," > .mc-tab > .mc-nav li",(function(){let e=t(this).attr("id").replace("nav-","");"membership"==e&&(r.isLoading()&&t.getScript(CLOUD_URL+"/script/vendor/chart.min.js",(()=>{r.stopLoading(),s=new Chart(r,{type:"bar",data:{labels:[L("January"),L("February"),L("March"),L("April"),L("May"),L("June"),L("July"),L("August"),L("September"),L("October"),L("November"),L("December")],datasets:[{data:messages_volume,backgroundColor:"#009BFC"}]},options:{legend:{display:!1}}})})),setTimeout((()=>{E("suspended")}),300)),"profile"==e&&setTimeout((()=>{E("verify")}),1e3),"logout"==e&&(MCF.logout(!1),setTimeout((()=>{document.location=location.href.substring(0,location.href.indexOf("/account"))+"?login"}),300)),window.history.replaceState(null,null,"?tab="+e+(location.href.includes("debug")?"&debug":""))})),a.on("click",".btn-verify-email,.btn-verify-phone",(function(){if(!t(this).parent().find("input").val()||P(this))return;let e={},s=t(this).hasClass("btn-verify-email");e[s?"email":"phone"]=t(this).parent().find("input").val(),y("verify",e,(e=>{if(e&&"object"==typeof e&&"error"===e.status)return S(e.message||"We couldn't send your verification code. Please check your contact information and try again in a few minutes."),void t(this).stopLoading();e&&"object"==typeof e&&"success"===e.status&&e.message?(console.log("Received verification code in new format"),i=e.message):i=e,console.log("Verification code sent successfully"),k("We sent you a secret code","We sent you a secret code, please enter it below to verify your "+(s?"email address":"phone number"),`<div data-type="text" class="mc-input mc-type-input-button"><input type="text"><a id="btn-verify-code" class="mc-btn">${L("Complete verification")}</a></div>`),t(this).stopLoading()}))})),a.on("click",".banner #btn-verify-code",(function(){let e=t(this).parent().find("input").val();if(e&&!P(this)){if(console.log("Sending verification code:",{encrypted_code_type:typeof i,code_type:typeof e}),"string"!=typeof i)return S("There was a problem with your verification request. Please try requesting a new code by clicking the verify button again."),void t(this).stopLoading();y("verify",{code_pairs:[i,e]},(e=>{if(console.log("Verification response:",e),e&&"object"==typeof e&&"error"===e.status)return S(e.message||"The verification code you entered is incorrect or has expired. Please check the code and try again, or request a new code."),void t(this).stopLoading();if(e&&Array.isArray(e)&&e.length>=1){let s="email"===e[0];if(Array.isArray(e[1])&&e[1].length>=2){_(e[1][0],e[1][1]),t(a).find(s?"#email":"#phone").removeClass("mc-type-input-button").find(".mc-btn").remove(),a.find(".banner").remove(),A(`Thank you! Your ${s?"email address":"phone number"} has been verified.`)}else S("We encountered an issue while verifying your information. Please try again or contact our support team if the problem persists.")}else S("We couldn't complete your verification at this time. Please try again later or contact support if the problem persists.");t(this).stopLoading()}))}})),a.on("click","#save-profile",(function(){if(P(this))return;let e={},s=!1;a.find("#tab-profile .mc-input input").each(((a,i)=>{let r=t(i).parent().attr("id"),o=t.trim(t(i).val());o||"password"===r||(S("All fields except password are required."),s=!0),"password"==r&&o&&o.length<8&&(S(g.password_length),s=!0),"email"!=r||o.includes("@")&&o.includes(".")||(S(g.email),s=!0),e[r]=o})),s?t(this).stopLoading():y("account-save",{details:e},(s=>{if(s&&"error"===s.status)return S(s.message||"We couldn't save your profile information. Please check your entries and try again. If the problem persists, contact support."),void t(this).stopLoading();Array.isArray(s)?(_(s[0],s[1]),A("Your profile information has been updated successfully."),e.password&&a.find("#password input").val("")):S(s),t(this).stopLoading()}))})),a.on("click","#nav-invoices",(function(){let e=a.find("#tab-invoices");e.isLoading()&&y("get-payments",{},(t=>{if(t&&"error"===t.status)return e.append(`<p>${L("We couldn't load your payment history at this time. Please refresh the page or try again later.")}</p>`),void e.stopLoading();let s="";for(var a=0;a<t.length;a++)s+=`<tr><td><i class="mc-icon-file"></i>${T(t[a])}</td></tr>`;s?e.find("tbody").html(s):e.append(`<p>${L(`There are no ${m||h?"invoices":"payments"} yet.`)}</p>`),e.stopLoading()}))})),a.on("click",".mc-invoice-row",(function(){let e=t(this);e.isLoading()||(e.startLoading(),y("get-invoice",{payment_id:t(this).attr("data-id")},(t=>{if(e.stopLoading(),!t||"object"!=typeof t||"error"===t.status)return console.error("Error getting invoice:",t?t.message:"Invalid response received"),void S(t&&t.message||"Failed to retrieve invoice details.");let s=t.message;"string"==typeof s&&(s=s.trim().replace(/^"|"$/g,"")),s&&"string"==typeof s&&!s.includes("/")&&!s.includes("..")&&s.endsWith(".pdf")?(window.open(CLOUD_URL+"/script/uploads/invoices/"+s),setTimeout((()=>{y("delete-invoice",{file_name:s},(e=>{e&&"error"===e.status?console.warn("Failed to delete temporary invoice file:",s,e.message):console.log("Temporary invoice delete request sent for:",s)}))}),1500)):S("We couldn't generate your invoice. Please try again or contact our support team for assistance.")})))})),a.on("click","#plans > div",(function(e){if(e.preventDefault(),!(t(this).attr("data-active-membership")&&!t(this).attr("data-expired")||P(this)))return"manual"==PAYMENT_PROVIDER?(t(this).stopLoading(),document.location=PAYMENT_MANUAL_LINK):void y("shopify"===external_integration?"shopify-subscription":v[PAYMENT_PROVIDER],{price_id:t(this).attr("data-id"),cloud_user_id:CLOUD_USER_ID},(e=>{if(e&&"error"===e.status)return S(e.message||"We couldn't process your payment request. Please check your payment information and try again, or contact support if the issue persists."),void t(this).stopLoading();e&&e.url?document.location=e.url:(S("We couldn't redirect you to the payment page. Please try again or contact support if the problem continues."),t(this).stopLoading())}))})),a.on("click","#purchase-white-label",(function(e){if(e.preventDefault(),!t(this).hasClass("mc-plan-active")&&!P(this))return"manual"==PAYMENT_PROVIDER?(t(this).stopLoading(),document.location=PAYMENT_MANUAL_LINK):void y("purchase-white-label",{external_integration:external_integration},(e=>{if(e&&"error"===e.status)return S(e.message||"We couldn't process your white-label purchase request. Please try again later or contact support for assistance."),void t(this).stopLoading();e&&e.url?document.location=e.url:(S("We couldn't redirect you to the white-label payment page. Please try again or contact our support team for help."),t(this).stopLoading())}))})),a.on("change","#add-credits select",(function(e){let s=t(this).val();if(e.preventDefault(),s&&!P(t(this).parent()))return"manual"==PAYMENT_PROVIDER?(t(this).parent().stopLoading(),document.location=PAYMENT_MANUAL_LINK):void y("purchase-credits",{amount:s,external_integration:external_integration},(e=>{if(e&&"error"===e.status)return S(e.message||"We couldn't process your credit purchase. Please verify the amount and try again, or contact support if the issue persists."),void t(this).parent().stopLoading();e.error?(S("We couldn't complete your credit purchase. Please try again later or contact our support team for assistance."),t(this).parent().stopLoading()):e.url?document.location=e.url:(S("We encountered an issue while processing your credit purchase. Please try again or contact our support team for assistance with your purchase."),t(this).parent().stopLoading())}))})),a.on("click",".mc-custom-addon",(function(e){P(this)||y("purchase-addon",{index:t(this).attr("data-index")},(e=>{if(e&&"error"===e.status)return S(e.message||"We couldn't process your addon purchase. Please try again later or contact our support team for assistance."),void t(this).stopLoading();e&&e.url?document.location=e.url:(S("We couldn't redirect you to the addon payment page. Please try again in a few minutes or contact our support team for assistance with your purchase."),t(this).stopLoading())}))})),a.on("click","#credits-recharge input",(function(e){y("set-auto-recharge-credits",{enabled:t(this).is(":checked")},(e=>{if(e&&"error"===e.status)return console.error("Error setting auto-recharge:",e.message),void S(e.message||"Failed to save auto-recharge setting.");A("Settings saved.")}))})),a.on("click","#cancel-subscription",(function(){if("paystack"===PAYMENT_PROVIDER){var s=t(this).data("subscription-code"),a=t(this).data("email-token");if(s){if(confirm(L("Are you sure you want to cancel your subscription?"))){let e=t(this);e.addClass("mc-loading").html('<i class="mc-icon-loading"></i> '+L("Cancelling...")),t.ajax({url:"ajax.php",method:"POST",data:{function:"cancel_paystack_subscription",subscription_code:s,email_token:a},dataType:"json"}).done((function(t){console.log("Cancellation response:",t),t&&"manage_link_generated"===t.status&&t.link?(alert(t.message||L("Redirecting to Paystack to manage your subscription...")),window.location.href=t.link):!t||"success"!==t.status&&"canceled_locally"!==t.status?(alert(t.message||L("Subscription cancellation failed. Please try again or contact support.")),e.removeClass("mc-loading").html('<i class="mc-icon-close"></i>'+L("Cancel subscription"))):(alert(t.message||L("Subscription cancellation requested. Please reload the page to see the changes.")),e.replaceWith('<a href="#" id="mc-refresh-after-cancel" class="mc-btn-text mc-icon"><i class="mc-icon-refresh"></i> '+L("Refresh to update status")+"</a>"))})).fail((function(t,s,a){console.error("Cancellation AJAX failed:",s,a,t.responseText),alert(L("Unable to process subscription cancellation. Please check console (F12) for details.")),e.removeClass("mc-loading").html('<i class="mc-icon-close"></i>'+L("Cancel subscription"))}))}}else console.error("Missing subscription code for cancellation.",{code:s}),alert(L("Subscription details not found. Cannot proceed with cancellation."))}else if(e.preventDefault(),confirm(L("Are you sure?"))){if(P(this))return;let e=t(this);y((external_integration||PAYMENT_PROVIDER)+"-cancel-subscription",{},(t=>{if(t&&"error"===t.status)return console.error("Error cancelling subscription:",t.message),S(t.message||"Failed to cancel subscription."),void e.stopLoading();if("no-subscriptions"==t)k("Subscription already cancelled","You do not have any active subscription.","",!1,!1,!0),e.remove();else if(!t||"canceled"!=t.status&&!0!==t){k("Error","Cancellation failed: "+("object"==typeof t?JSON.stringify(t):t),"",!1,!0)}else k("Subscription cancelled","The subscription has ben cancelled sucessfully.","",!1,!1,!0),e.replaceWith('<a href="#" id="mc-refresh-after-cancel" class="mc-btn-text mc-icon"><i class="mc-icon-refresh"></i> '+L("Refresh to update status")+"</a>");e.stopLoading()}))}})),console.log("Attaching refresh handler to box_account"),a.on("click","#mc-refresh-after-cancel",(function(e){e.preventDefault(),t(this).html('<i class="mc-icon-loading"></i> Refreshing...'),location.reload()})),c.includes("welcome")&&SETTINGS.text_welcome_title&&(k(SETTINGS.text_welcome_title,SETTINGS.text_welcome,"",SETTINGS.text_welcome_image),window.history.replaceState({},document.title,d),a.find(".mc-btn-dashboard").addClass("animation-button").attr("href","../?welcome")),c.includes("payment_status=")){let e=MCF.getURL("payment_status"),s=MCF.getURL("psk_error");"success_pending_webhook"===e?(a.startLoading(),y("membership",{},(e=>{window.history.replaceState({},document.title,d+"?tab=membership"),e&&"object"==typeof e&&e.id?(membership=e,function(e){if(!e||!a)return!1;try{let i=a.find(".membership-quota");i.length&&i.each((function(){let s=t(this).closest("div").text().toLowerCase().includes("agent");s&&"quota_agents"in e?t(this).text(9999==e.quota_agents?"∞":e.quota_agents):!s&&"quota"in e&&t(this).text(e.quota)}));let r=a.find(".membership-name");r.length&&"name"in e&&r.text(L(e.name));let o=a.find(".membership-price");if(o.length&&"price"in e&&"currency"in e&&"period"in e){let t="";e.price>0&&(t=((s=e.currency)?s.toUpperCase():"")+" "+e.price+" "+function(e){if(!e)return"";switch(e.toLowerCase()){case"month":return L("per month");case"year":return L("per year");default:return e}}(e.period)),o.text(t),e.currency&&o.attr("data-currency",e.currency)}if("id"in e){a.find("#plans > div").removeClass("mc-plan-active").removeAttr("data-active-membership data-expired");let t=a.find('#plans > div[data-id="'+e.id+'"]');if(t.length&&(t.attr("data-active-membership","true"),e.expired&&t.attr("data-expired","true"),0===t.find(".active-membership-info").length)){let s=L("Active Membership");e.expired&&(s+=" "+L("Expired")),t.prepend('<div class="active-membership-info">'+s+"</div>")}}if("credits"in e){let t=a.find(".maso-box-credits .box-black:first-child div");t.length&&t.text(e.credits||"0")}return!0}catch(e){return console.error("Error updating membership UI:",e),!1}var s}(e),console.log("Webhook success - URL:",c),console.log("Webhook success - isCreditsPayment flag:",window.isCreditsPayment),console.log("Webhook success - Contains payment_type=credits:",c.includes("payment_type=credits")),console.log("Webhook success - Contains #credits:",c.includes("#credits")),!0===window.isCreditsPayment||c.includes("payment_type=credits")||c.includes("#credits")?(t(".banner").remove(),console.log("Webhook showing CREDITS success message"),A("Credits purchase successful! Your credits have been added to your account.")):(t(".banner").remove(),console.log("Webhook showing PLAN success message"),A("Your plan has been updated successfully!")),a.stopLoading()):(console.log("Fallback - URL:",c),console.log("Fallback - isCreditsPayment flag:",window.isCreditsPayment),console.log("Fallback - Contains payment_type=credits:",c.includes("payment_type=credits")),console.log("Fallback - Contains #credits:",c.includes("#credits")),!0===window.isCreditsPayment||c.includes("payment_type=credits")||c.includes("#credits")?(t(".banner").remove(),console.log("Fallback showing CREDITS success message"),A("Credits purchase successful! Updating your account...")):(t(".banner").remove(),console.log("Fallback showing PLAN success message"),A("Payment successful! Updating your plan...")),setTimeout((()=>{location.reload()}),500))}))):"failed"===e?(window.history.replaceState({},document.title,d+"?tab=membership"),S("Payment verification failed: "+(s||"Unknown error"))):"error"===e&&(window.history.replaceState({},document.title,d+"?tab=membership"),S("Error processing payment: "+(s||"Unknown error")))}if(c.includes("tab="))for(var f=0;f<o.length;f++)if(c.includes("tab="+o[f])){let e=a.find(" > .mc-tab > .mc-nav");e.find("li").eq(f).click(),e.mcActive(!1);break}a.on("click","#delete-account",(function(e){if(e.preventDefault(),confirm(L("Are you sure? Your account, along with all its users and conversations, will be deleted permanently."))){if(P(this))return;y("account-delete",{},(e=>{if(e&&"error"===e.status)return console.error("Error deleting account:",e.message),S(e.message||"We couldn't delete your account. Please try again later or contact our support team for assistance."),void t(this).stopLoading();MCF.cookie("mc-login","","",!1),MCF.cookie("mc-cloud","","",!1),MCF.storage("open-conversation",""),MCF.storage("login",""),setTimeout((()=>{location.reload()}),500)}))}})),a.on("click","#delete-agents-quota",(function(){confirm(L("Are you sure?"))&&y("account-delete-agents-quota",{},(e=>{if(e&&"error"===e.status)return console.error("Error deleting agents quota:",e.message),void S(e.message||"We couldn't update your agent quota. Please try again later or contact our support team for assistance.");location.href=CLOUD_URL}))})),a.on("click","#save-payment-information",(function(){y("save-referral-payment-information",{method:a.find("#payment_method").val(),details:a.find("#payment_information").val()},(e=>{if(e&&"error"===e.status)return console.error("Error saving payment info:",e.message),void S(e.message||"Failed to save payment information.");A("Settings saved."),M()}))})),a.on("click","#nav-referral",(function(){let e=a.find("#payment_method");e.attr("data-loaded")||y("get-referral-payment-information",{},(t=>{t&&"error"===t.status?console.error("Error getting payment info:",t.message):(t=t?t.split("|"):["",""],e.attr("data-loaded","true"),e.val(t[0]),a.find("#payment_information").val(t[1]),a.find("#payment_information_label").html(L("bank"==t[0]?"Bank details":"PayPal email")))}))})),a.on("change","#payment_method",(function(){a.find("#payment_information_label").html(L("bank"==t(this).val()?"Bank details":"PayPal email"))})),E("suspended")}else console.warn("box_account not defined or found, using document delegation for #mc-refresh-after-cancel."),t(document).on("click","#mc-refresh-after-cancel",(function(e){e.preventDefault(),t(this).html('<i class="mc-icon-loading"></i> Refreshing...'),location.reload()}));if(i.length){let e=s.find(".mc-login-box"),a=s.find(".mc-reset-password-box");if(MCF.getURL("login_email")&&setTimeout((()=>{e.find("#email input").val(MCF.getURL("login_email")),e.find("#password input").val(MCF.getURL("login_password")),N(e,!0),N(i,!1),e.find(".btn-login").click()}),300),t(i).on("click",".btn-register",(function(e){if(e.preventDefault(),P(this))return;let s={},a=!1,r=i.find(".mc-errors-area");r.html(""),i.find("[id].mc-input").each((function(){let e=t(this).find("input");t.trim(e.val())?e.removeClass("mc-error"):(e.addClass("mc-error"),a=!0),s[t(this).attr("id")]=t.trim(e.val())})),a?r.html(L("Please fill in all required fields to complete your registration. All fields are necessary to create your account.")):s.password.length<8?(r.html(L("Your password must be at least 8 characters long. Please choose a stronger password for better security.")),a=!0):s.password!=s.password_2?(r.html(L("The passwords you entered do not match. Please make sure both passwords are identical.")),a=!0):s.email.includes("@")&&s.email.includes(".")?(_("",""),c.includes("ref=")&&w("mc-referral",MCF.getURL("ref"),180),y("registration",{details:s},(e=>{if(e&&"error"===e.status)return r.html(e.message||"We couldn't complete your registration. Please check that all your information is correct and try again. If the problem persists, please contact our support team for assistance."),void t(this).stopLoading();"duplicate-email"==e?r.html(L("This email address is already registered. Please use a different email address or try to log in if you already have an account. If you forgot your password, you can reset it from the login page.")):Array.isArray(e)?(_(e[0],e[1]),y("account-welcome"),setTimeout((()=>{document.location=MCF.getURL("redirect")?MCF.getURL("redirect"):CLOUD_URL+"/account?welcome"}),300)):r.html("This email address is already registered. Please use a different email address or try to log in if you already have an account. If you forgot your password, you can reset it from the login page."),t(this).stopLoading()}))):(r.html(L("Please enter a valid email address. This will be used for account verification and important notifications.")),a=!0),a&&t(this).stopLoading()})),t(e).on("click",".btn-login",(function(s){s.preventDefault();let a=e.find("#email input").val(),i=e.find("#password input").val(),r=e.find(".mc-errors-area");a&&i&&!P(this)?(r.html(""),y("login",{email:a,password:i},(e=>{if(e&&"error"===e.status)return r.html(e.message||"We couldn't log you in. Please check your email and password and try again."),void t(this).stopLoading();!1===e?r.html(L('The email or password you entered is incorrect. Please check your credentials and try again, or use the "Forgot Password" link if you need to reset your password.')):"ip-ban"===e?r.html(L("For security reasons, your account has been temporarily locked due to too many failed login attempts. Please wait a few hours before trying again, or contact our support team for immediate assistance.")):Array.isArray(e)?(_(e[0],e[1]),document.location=MCF.getURL("redirect")?MCF.getURL("redirect"):CLOUD_URL):r.html("Your login was processed, but we encountered an issue. Please try again or contact our support team for assistance."),t(this).stopLoading()}))):a&&i||r.html(L("Email and password are required."))})),t(e).on("click",".btn-registration-box",(function(){N(e,!1),N(i,!0)})),t(i).on("click",".mc-btn-login-box",(function(){N(i,!1),N(e,!0)})),t(a).on("click",".btn-reset-password",(function(){let e=t.trim(a.find("#reset-password-email").val());e&&e.includes("@")&&e.includes(".")?y("account-reset-password",{email:e},(e=>{e&&"error"===e.status&&console.error("Error requesting password reset:",e.message),a.html(`<div class="mc-top-bar"><div class="mc-title">${L("Check your email")}</div><div class="mc-text">${L("If an account linked to the email provided exists you will receive an email with a link to reset your password.")}</div></div>`)})):a.find(".mc-errors-area").html(g.email).mcActive(!0)})),t(a).on("click",".btn-cancel-reset-password",(function(){N(e,!0),N(a,!1)})),t(e).on("click",".btn-forgot-password",(function(){N(i,!1),N(e,!1),N(a,!0)})),c.includes("reset=")){let a=s.find(".mc-reset-password-box-2"),i=a.find(".mc-info");t(a).on("click",".btn-reset-password-2",(function(){let s=a.find("#reset-password-1").val();i.html("").mcActive(!1),s?s==a.find("#reset-password-2").val()?s.length<8?i.html(L("Your password must be at least 8 characters long. Please choose a stronger password for better security.")).mcActive(!0):P(this)||y("account-reset-password",{email:MCF.getURL("email"),token:MCF.getURL("reset"),password:s},(s=>{if(s&&"error"===s.status)return i.html(s.message||"We couldn't reset your password. The reset link may have expired. Please request a new password reset link and try again.").mcActive(!0),void t(this).stopLoading();N(e,!0),N(a,!1),t(this).stopLoading()})):i.html(L("The passwords you entered do not match. Please make sure both passwords are identical.")).mcActive(!0):i.html(L("Please enter a new password to complete the reset process. Your password should be at least 8 characters long for better security.")).mcActive(!0)}))}t(window).keydown((function(t){13==t.which&&(e.mcActive()?e.find(".btn-login").click():i.mcActive()&&i.find(".btn-register").click())}))}r.length&&(r.find(".table-customers").length&&y("super-get-customers",{},(e=>{if(e&&"error"===e.status)return console.log("Could not load customer list"),void r.find("#tab-customers").html(`<p>${L("We couldn't load the customer list. Please refresh the page and try again.")}</p>`).stopLoading();let t="";for(var s=0;s<e.length;s++){let a=e[s],i=C(a.membership)||{name:"Unknown"};t+=`<tr data-customer-id="${a.id}"><td data-id="id">${a.id}</td><td data-id="name">${a.first_name||""} ${a.last_name||""}</td><td data-id="email">${a.email||""}</td><td data-id="phone">${a.phone||""}</td><td data-id="membership">${i.name}</td><td data-id="token">${a.token||""}</td><td data-id="creation_time">${a.creation_time||""}</td></tr>`}r.find(".table-customers tbody").html(t),r.find("#tab-customers").stopLoading()})),t(r).on("click",".btn-login",(function(e){e.preventDefault();let s=r.find("#email input").val(),a=r.find("#password input").val(),i=r.find(".mc-errors-area");s&&a&&!P(this)?(i.html(""),t.ajax({method:"POST",url:"ajax.php",data:{function:"super-login",email:s,password:a},dataType:"json"}).done((function(e){if(console.log("Super Login response:",e),e&&"object"==typeof e&&"success"===e.status&&"string"==typeof e.message&&e.message.length>10){w("mc-super",e.message,3650),document.location=d+"?login=success"}else e&&"object"==typeof e&&"error"===e.status?i.html(e.message||"Invalid email or password."):"ip-ban"===e?i.html("Too many login attempts. Please retry again in a few hours."):!1===e?i.html("Invalid email or password."):i.html('The email or password you entered is incorrect. Please check your credentials and try again, or use the "Forgot Password" link if you need to reset your password.')})).fail((function(e,t,s){i.html("We couldn't connect to the server. Please check your internet connection and try again. If the problem persists, please contact our support team for assistance.")})).always((function(){r.find(".btn-login").stopLoading()}))):s&&a||i.html(L("Email and password are required."))})),t(r).on("click",".table-customers td",(function(e){e.preventDefault(),o.mcActive(!0),y("super-get-customer",{customer_id:t(this).parent().attr("data-customer-id")},(e=>{if(e&&"error"===e.status)return S(e.message||"We couldn't load the customer details. Please try again or contact support if the problem persists."),void o.mcActive(!1);let t=["first_name","last_name","email","phone","password","credits"],s=["id","lifetime_value","token","creation_time","customer_id","database","count_users","count_agents","membership_expiration"],a="";if("object"!=typeof e||null===e)return S("We couldn't load the customer information. Please refresh the page and try again. If the problem persists, please contact our support team for assistance."),void o.mcActive(!1);for(var i=0;i<t.length;i++){let s=t[i],r="password"===s?"":e[s]||"";a+=`<div data-type="text" class="mc-input"><span>${R(s)}</span><input id="${s}" type="${"password"===s?"password":"text"}" value="${r}" placeholder="${"password"===s?"Enter new password or leave blank":""}" ${"phone"!==s&&"password"!==s?"required":""} /></div>`}if(e.extra_fields&&Array.isArray(e.extra_fields)){for(i=0;i<e.extra_fields.length;i++){let t=e.extra_fields[i];if(t&&t.slug&&!["payment","active_membership_cache","notifications_credits_count","marketing_email_30","marketing_email_7","email_limit"].includes(t.slug)){let e="white-label"===t.slug||"white_label"===t.slug;if(a+=`<div data-type="${e?"select":"text"}" class="mc-input"><span>${R(t.slug)}</span>`,e){let e=t.value||"";a+='<select id="white_label" data-extra="true">',e&&!["renew","disable","activate"].includes(e)?a+=`<option value="${e}" selected>${R(e)}</option>`:a+=`<option value="" ${""===e?"selected":""}>Not Active</option>`,a+=`<option value="activate" ${"activate"===e?"selected":""}>Activate</option>`,a+=`<option value="renew" ${"renew"===e?"selected":""}>Manual renewal</option>`,a+=`<option value="disable" ${"disable"===e?"selected":""}>Disable</option>`,a+="</select></div>"}else a+=`<input id="${t.slug}" type="text" value="${t.value||""}" data-extra="true" /></div>`}}n.find("#white_label").length||a.includes('id="white_label"')||(a+='<div data-type="select" class="mc-input"><span>White label</span><select id="white_label" data-extra="true"><option value="" selected>Not Active</option><option value="activate">Activate</option></select></div>')}else a+='<div data-type="select" class="mc-input"><span>White label</span><select id="white_label" data-extra="true"><option value="" selected>Not Active</option><option value="activate">Activate</option></select></div>';a+='<div data-type="text" class="mc-input"><span>Membership</span><select id="membership" required>';for(i=0;i<MEMBERSHIPS.length;i++)a+=`<option value="${MEMBERSHIPS[i].id}"${MEMBERSHIPS[i].id==e.membership?" selected":""}>${MEMBERSHIPS[i].name}${MEMBERSHIPS[i].period?" | "+R(MEMBERSHIPS[i].period):""}</option>`;a+='<option value="manual_membership_renewal">Manual membership renewal</option></select></div>',n.find(".mc-edit-box").html(a),a="";for(i=0;i<s.length;i++){let t=e[s[i]]||"";if(["creation_time","membership_expiration"].includes(s[i])&&t){let e=parseInt(t);!isNaN(e)&&e>1e6&&(t=new Date(1e3*e).toLocaleString())}a+=`<div data-type="readonly" class="mc-input"><span>${R(s[i])}</span><input id="${s[i]}" type="text" value="${t}" readonly /></div>`}if(n.find(".mc-readonly-box").html(a),a="",e.invoices&&Array.isArray(e.invoices))for(i=0;i<e.invoices.length;i++)a+=$(e.invoices[i]);if(n.find(".mc-sales-box").html(a||"<div>No payment data available</div>"),a="",e.monthly_volume&&Array.isArray(e.monthly_volume))for(i=0;i<e.monthly_volume.length;i++){let t=e.monthly_volume[i];a+=`<div>${t.date||"N/A"} | ${t.count||0} messages</div>`}n.find(".mc-volume-box").html(a||"<div>No volume data available</div>"),n.find(".mc-name").html(`${e.first_name||""} ${e.last_name||""}`),n.find(".mc-delete-box input").val(""),n.attr("data-customer-id",e.id||""),n.lightbox()}))})),t(n).on("click",".mc-save",(function(e){if(e.preventDefault(),P(this))return;let a={},i={},o=!1,l=t(n).find(".mc-info");if(l.html("").mcActive(!1),n.find(".mc-edit-box input:not([readonly]), .mc-edit-box select").each(((e,s)=>{let r=t(s),n=t.trim(r.val()),c=r.attr("id");c&&(!n&&r.attr("required")&&"password"!==c?(l.html(L("Please fill in all required fields.")).mcActive(!0),r.addClass("mc-error"),o=!0):r.removeClass("mc-error"),"password"===c&&n&&n.length<8&&(l.html(g.password_length).mcActive(!0),r.addClass("mc-error"),o=!0),r.attr("data-extra")?i[c]=n:a[c]=n)})),o)return void t(this).stopLoading();let c=t(this).closest("[data-customer-id]").attr("data-customer-id");y("super-save-customer",{customer_id:c,details:a,extra_details:i},(e=>{if(e&&"error"===e.status)return console.error("Error saving customer:",e.message),l.html(e.message||"We couldn't save the customer details. Please check all fields and try again.").mcActive(!0),void t(this).stopLoading();if("duplicate-phone-or-email"===e)return l.html("This email or phone number is already in use. Please use a different email or phone number.").mcActive(!0),void t(this).stopLoading();!0!==e&&1!==e&&"true"!==e&&console.warn("Unexpected success response from super-save-customer:",e);let i=r.find(`.table-customers [data-customer-id="${c}"]`);if(i.length){let e=["name","email","phone","membership"];a.name=a.first_name+" "+a.last_name;let t=MEMBERSHIPS.find((e=>e.id==a.membership));a.membership=t?t.name:a.membership;for(var o=0;o<e.length;o++)i.find(`[data-id="${e[o]}"]`).html(a[e[o]]||"")}A("Settings saved."),s.find(".mc-lightbox,.mc-lightbox-overlay").mcActive(!1),t(this).stopLoading()}))})),t(n).on("click",".mc-delete-box .mc-btn-text",(function(){if("DELETE"===t(this).parent().find("input").val().toUpperCase()){let e=t(this).closest("[data-customer-id]").attr("data-customer-id");y("super-delete-customer",{customer_id:e},(t=>{if(t&&"error"===t.status)return console.error("Error deleting customer:",t.message),void n.lightboxError(t.message||"Failed to delete customer.");r.find(`.table-customers [data-customer-id="${e}"]`).remove(),s.find(".mc-lightbox,.mc-lightbox-overlay").mcActive(!1)}))}else t(this).parent().find("input").addClass("mc-error"),n.lightboxError("Please type DELETE to confirm.")})),t(r).on("click","#save-emails, #save-settings",(function(e){if(e.preventDefault(),P(this))return;let s={},a="save-emails"==t(this).attr("id");r.find(a?"#tab-emails":"#tab-settings").find(" .mc-setting textarea,.mc-setting input,.mc-setting select").each(((e,a)=>{a=t(a),s[a.attr("id")]=a.is(":checkbox")?a.is(":checked"):t.trim(a.val())})),y(a?"super-save-emails":"super-save-settings",{settings:s},(e=>{x(e)||e&&"object"==typeof e&&"success"===e.status?A("Settings saved successfully."):e&&"error"===e.status?S(e.message||`We couldn't save your ${a?"emails":"settings"}. Please try again or contact support if the problem persists.`):S(`We couldn't save your ${a?"emails":"settings"}. Please try again or contact support if the problem persists.`),t(this).stopLoading()}))})),t((function(){t("body").on("click",'.mc-nav li[id^="nav-"]',(function(e){e.preventDefault();let s=t(this),a=s.attr("id"),i=a.replace("nav","tab"),r=t("#"+i);console.log(`Nav click: ${a}, Target area: #${i}`),s.hasClass("mc-active")?console.log("Clicked already active tab."):(console.log("Switching active tab..."),t(".mc-nav li").removeClass("mc-active"),t(".mc-content > div").removeClass("mc-active"),s.addClass("mc-active"),r.addClass("mc-active"));let o="nav-emails"===a,n="nav-settings"===a,l="nav-customers"===a,c="nav-affiliates"===a;if("nav-membership-plans"===a){console.log("Membership tab selected.");let e=r.find("#membership-plans-container");if(0===e.length)return console.error("CRITICAL: #membership-plans-container not found!"),void r.html('<p style="color:red;">Error: Container div missing in HTML.</p>');e.hasClass("content-loaded")?console.log("#membership-plans-container already loaded. Skipping AJAX."):(console.log("#membership-plans-container needs loading..."),r.addClass("mc-loading"),t.ajax({method:"POST",url:"ajax.php",data:{function:"super-membership-plans"},dataType:"html",timeout:15e3}).done((function(s){if(console.log("AJAX success for super-membership-plans."),s&&s.trim().startsWith("<")){e.html(s),e.addClass("content-loaded"),console.log("Membership content loaded and marked.");var a="undefined"!=typeof PAYMENT_PROVIDER&&"paystack"===PAYMENT_PROVIDER,i="undefined"!=typeof PAYMENT_PROVIDER&&"stripe"===PAYMENT_PROVIDER,r="undefined"!=typeof PAYMENT_PROVIDER&&"razorpay"===PAYMENT_PROVIDER;i||r||a||e.find(".mc-input [data-period]").each((function(){let e=t(this).closest("[data-period]").attr("data-period");t(this).find("select.period").val(e)}))}else console.error("Received invalid HTML for membership plans:",s),e.html('<div class="mc-msg mc-error"><h2>Loading Error</h2><p>Invalid HTML received.</p></div>')})).fail((function(t,s,a){e.html('<div class="mc-msg mc-error"><h2>Connection Error</h2><p>We couldn\'t load the membership plans. Please check your internet connection and try again. If the problem persists, please contact our support team for assistance.</p></div>')})).always((function(){r.removeClass("mc-loading"),console.log("Membership AJAX complete.")})))}else o||n?(console.log((o?"Emails":"Settings")+" tab selected."),r.hasClass("data-loaded")?(console.log(`#${i} already marked as data-loaded. Skipping AJAX.`),r.removeClass("mc-loading")):(console.log(`#${i} needs data loading...`),r.addClass("mc-loading"),y(o?"super-get-emails":"super-get-settings",{},(e=>{if(e&&"object"==typeof e&&"error"!==e.status){for(var t in e)if(e.hasOwnProperty(t)){let s=r.find("#"+t);s.length&&(s.is(":checkbox")?s.prop("checked",!1!==e[t]&&"false"!==e[t]&&0!==e[t]&&"0"!==e[t]):s.val(e[t]))}r.addClass("data-loaded"),console.log((o?"Emails":"Settings")+" data loaded and marked."),r.find(".mc-msg.mc-transient").remove()}else console.log("Could not load "+(o?"emails":"settings")),r.prepend(`<div class="mc-msg mc-error mc-transient"><h2>Loading Error</h2><p>We couldn't load the ${o?"email":"settings"} information. Please refresh the page and try again. If the problem persists, please contact our support team.</p></div>`);r.removeClass("mc-loading"),console.log((o?"Emails":"Settings")+" AJAX complete.")})))):(l||c)&&console.log(`Tab ${a} selected. Assuming specific JS (e.g., MCF.admin.get) handles data loading.`)}))})),t(r).on("click","#save-membership-plans",(function(){if(P(this))return;let e=t(this),s=[],a=!1,i="";if(r.find("#membership-plans > div").each((function(){let e=t(this),r={id:e.attr("data-id"),price:m||p||h?e.attr("data-price"):e.find(".price").val(),currency:m||p||h?e.attr("data-currency"):CURRENCY,period:m||p||h?e.attr("data-period"):e.find(".period").val(),name:e.find(".name").val().trim(),quota:e.find(".quota").val().trim()};r.currency||(r.currency="usd"),b&&(r.quota_agents=e.find(".quota-agents").val().trim());let o=r.name||`Plan ID ${r.id}`;if(!r.name)return i=`Plan name is required for ${o}.`,a=!0,!1;if(""===r.quota||!t.isNumeric(r.quota))return i=`Quota must be a valid number (can be 0 or negative) for ${o}.`,a=!0,!1;if(b&&(""===r.quota_agents||!t.isNumeric(r.quota_agents)))return i=`Agent Quota must be a valid number (can be 0 or negative) for ${o}.`,a=!0,!1;if("free"!==r.id){if(!r.price||!t.isNumeric(r.price)||parseFloat(r.price)<0)return i=`Price must be a non-negative number for ${o}.`,a=!0,!1;if(!r.period)return i=`Period (monthly/yearly) is required for ${o}.`,a=!0,!1}s.push(r)})),a)return S(i||"Error in plan configuration. Please check all fields."),void e.stopLoading();window.confirm("Are you sure to update the membership plans? The changes will be live instantaneously.")?y("super-save-membership-plans",{plans:s},(t=>{if(e.stopLoading(),t&&"object"==typeof t&&"error"===t.status)console.error("Error saving membership plans:",t.message),S(t.message||"Failed to save membership plans.");else if(t&&"object"==typeof t&&"success"===t.status)A("Membership plans saved successfully.");else if(x(t))A("Membership plans saved successfully.");else{let e="object"==typeof t?JSON.stringify(t):String(t);console.warn("Unexpected response saving membership plans:",t),S("Failed to save membership plans. Unexpected response: "+e)}e.stopLoading()})):e.stopLoading()})),t(r).on("click","#save-white-label",(function(){P(this)||y("super-save-white-label",{price:r.find(".super-white-label input").val()},(e=>{e&&"error"===e.status?(console.error("Error saving white label:",e.message),S(e.message||"Failed to save white label price.")):x(e)?A("White label price saved successfully."):S("Error saving white label price: "+e),t(this).stopLoading()}))})),t(r).on("click","#logout",(function(){w("mc-super","",0),document.location=d+"?logout=true"})),t(r).on("click","#membership-plans > div > i.mc-icon-close",(function(){confirm("Are you sure you want to remove this membership plan?")&&t(this).parent().remove()})),t(r).on("click","#add-membership",(function(){let e=I(),s=`\n                    <div data-id="${e}" data-price="" data-period="" data-currency="${CURRENCY||"usd"}">\n                        <i class="mc-icon-close"></i> \x3c!-- Close icon at top --\x3e\n                        <div class="mc-input">\n                             <h5>ID</h5>\n                             <input type="text" value="${e}" readonly style="background:#eee;"> \x3c!-- Display ID --\x3e\n                            <h5>Name</h5>\n                            <input class="name" type="text" value="" placeholder="Insert plan name..." required>\n                            <h5>Quota (Messages/Month)</h5>\n                            <input type="number" class="quota" placeholder="0" value="" required min="0">\n                            ${b?'<h5>Quota (Agents)</h5><input type="number" class="quota-agents" placeholder="0" value="" required min="0">':""}\n                            <h5>Price</h5>\n                            <input type="number" class="price" placeholder="0.00" value="" required min="0" step="0.01">\n                             <h5>Currency</h5>\n                             <input type="text" class="currency" value="${CURRENCY||"usd"}" readonly style="background:#eee;"> \x3c!-- Display Currency --\x3e\n                            <h5>Period</h5>\n                            <select class="period" required>\n                                 <option value="" disabled selected>Select period...</option> \x3c!-- Added default option --\x3e\n                                <option value="month">Monthly</option>\n                                <option value="year">Yearly</option>\n                            </select>\n                        </div>\n                    </div>`,a=t(s);t(r).find("#membership-plans").append(a),a[0].scrollIntoView({behavior:"smooth",block:"nearest"})})),t(r).on("click","#nav-affiliates",(function(){let e=t(s).find("#tab-affiliates");e.isLoading()&&y("super-get-affiliates",{},(t=>{if(t&&"error"===t.status)return console.error("Error getting affiliates:",t.message),void e.html(`<p>${L("Error loading affiliate list.")}</p>`).stopLoading();let s="";if(Array.isArray(t))for(var a=0;a<t.length;a++){let e=t[a];s+=`<tr><td>${e.id||"N/A"}</td><td>${e.first_name||""} ${e.last_name||""}</td ><td>${e.email||""}</td><td>${(CURRENCY||"").toUpperCase()} <span>${e.value||0}</span></td><td><div class="mc-btn mc-btn-payment-details" data-id="${e.id}">Details</div></td><td><div class="mc-btn mc-btn-reset-affiliate" data-id="${e.id}">Reset to zero</div></td></tr>`}e.find("tbody").html(s||`<tr><td colspan="6">${L("No affiliates found.")}</td></tr>`),e.stopLoading()}))})),t(r).on("click",".table-affiliates .mc-btn",(function(){let e=t(this).attr("data-id");if(!e)return;let a={affiliate_id:e};t(this).hasClass("mc-btn-payment-details")?(o.mcActive(!0),y("super-get-affiliate-details",a,(e=>{if(e&&"error"===e.status)return console.error("Error getting affiliate details:",e.message),S(e.message||"Failed to load affiliate details."),void o.mcActive(!1);let a=s.find(".mc-generic-box");0===a.length&&(t("body").append('<div class="mc-lightbox mc-generic-box"><div class="mc-info"></div><div class="mc-top-bar"><div></div><i class="mc-close mc-icon-close"></i></div><div class="mc-main mc-scroll-area"></div></div>'),a=s.find(".mc-generic-box")),a.find(".mc-top-bar > div:first-child").html("Payment details of "+t(this).closest("tr").find("td").eq(1).html());let i="The user has not provided the payment details yet.";Array.isArray(e)&&e.length>=2&&e[0]&&(i=`<b>Payment method</b><br>${e[0]?e[0].toUpperCase():""}<br><br><b>Payment details</b><br>${e[1]?e[1].replace(/\n/g,"<br>"):""}`),a.find(".mc-main").html(i),a.lightbox()}))):t(this).hasClass("mc-btn-reset-affiliate")&&confirm("Are you sure you want to reset this affiliate's balance to zero?")&&y("super-reset-affiliate",a,(e=>{if(e&&"error"===e.status)return console.error("Error resetting affiliate:",e.message),void S(e.message||"Failed to reset affiliate balance.");t(this).closest("tr").find("td").eq(3).find("span").text("0"),A("Affiliate balance reset successfully.")}))})),t(r).on("click","#mc-get-emails",(function(){let e="",s=r.find(".table-customers");s.length&&s.find("tbody [data-customer-id]").each((function(){let s=t(this).find('[data-id="membership"]'),a=t(this).find('[data-id="email"]');s.length&&a.length&&"free"!==s.html().trim().toLowerCase()&&(e+=a.html().trim()+", ")}));let a=t("#mc-response-area");0===a.length&&(t(this).after('<div id="mc-response-area" style="margin-top: 10px; word-break: break-all;"></div>'),a=t("#mc-response-area")),a.html(e?e.substring(0,e.length-2):"No paying customer emails found.")})))})),t.fn.stopLoading=function(){return t(this).removeClass("mc-loading"),this},t.fn.startLoading=function(){return t(this).addClass("mc-loading"),this},t.fn.isLoading=function(){return t(this).hasClass("mc-loading")},t.fn.lightbox=function(){let e=t(this);return e.mcActive(!0),s.find(".mc-lightbox-overlay").mcActive(!0),o.mcActive(!1),setTimeout((()=>{e.css({"margin-top":e.outerHeight()/-2+"px","margin-left":e.outerWidth()/-2+"px",opacity:1})}),50),this},t.fn.lightboxError=function(e){let s=t(this),a=s.find(" > .mc-info");return 0===a.length&&(s.prepend('<div class="mc-info"></div>'),a=s.find(" > .mc-info")),a.html(e).mcActive(!0),setTimeout((()=>{a.mcActive(!1)}),1e4),this},t.fn.setClass=function(e,s=!0){return"string"==typeof e&&""!==e.trim()&&(s?t(this).addClass(e):t(this).removeClass(e)),this},t.fn.mcActive=function(e=-1){let s=t(this);return-1===e?s.hasClass("mc-active"):(s.setClass("mc-active",e),e?s.attr("aria-hidden","false"):s.attr("aria-hidden","true"),s)}}(jQuery);