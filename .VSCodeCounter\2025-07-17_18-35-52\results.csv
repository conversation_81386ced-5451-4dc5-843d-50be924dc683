"filename", "language", "JavaScript", "HTML", "JSON", "XML", "PHP", "Markdown", "Ini", "Shell Script", "Makefile", "YAML", "JSON with Comments", "SCSS", "PostCSS", "comment", "blank", "total"
"c:\Users\<USER>\Downloads\sb removal\script\admin.php", "PHP", 0, 0, 0, 0, 94, 0, 0, 0, 0, 0, 0, 0, 0, 8, 5, 107
"c:\Users\<USER>\Downloads\sb removal\script\apps\aecommerce\admin.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 9, 15, 76
"c:\Users\<USER>\Downloads\sb removal\script\apps\aecommerce\functions.php", "PHP", 0, 0, 0, 0, 162, 0, 0, 0, 0, 0, 0, 0, 0, 55, 25, 242
"c:\Users\<USER>\Downloads\sb removal\script\apps\aecommerce\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 0, 0, 0, 0, 78
"c:\Users\<USER>\Downloads\sb removal\script\apps\armember\countries.json", "JSON", 0, 0, 214, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 214
"c:\Users\<USER>\Downloads\sb removal\script\apps\armember\functions.php", "PHP", 0, 0, 0, 0, 138, 0, 0, 0, 0, 0, 0, 0, 0, 48, 18, 204
"c:\Users\<USER>\Downloads\sb removal\script\apps\armember\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 0, 0, 0, 0, 38
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\components.php", "PHP", 0, 0, 0, 0, 353, 0, 0, 0, 0, 0, 0, 0, 0, 8, 4, 365
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\data.json", "JSON", 0, 0, 96, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 96
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\dialogflow_languages.json", "JSON", 0, 0, 124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 124
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\functions.php", "PHP", 0, 0, 0, 0, 4237, 0, 0, 0, 0, 0, 0, 0, 0, 228, 226, 4691
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Config.php", "PHP", 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 63, 24, 155
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Document.php", "PHP", 0, 0, 0, 0, 164, 0, 0, 0, 0, 0, 0, 0, 0, 94, 49, 307
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element.php", "PHP", 0, 0, 0, 0, 96, 0, 0, 0, 0, 0, 0, 0, 0, 36, 19, 151
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementArray.php", "PHP", 0, 0, 0, 0, 77, 0, 0, 0, 0, 0, 0, 0, 0, 43, 20, 140
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementBoolean.php", "PHP", 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 38, 10, 76
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementDate.php", "PHP", 0, 0, 0, 0, 73, 0, 0, 0, 0, 0, 0, 0, 0, 47, 20, 140
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementHexa.php", "PHP", 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 36, 12, 86
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementMissing.php", "PHP", 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 32, 9, 67
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementName.php", "PHP", 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 35, 9, 70
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementNull.php", "PHP", 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 35, 10, 72
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementNumeric.php", "PHP", 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 35, 8, 63
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementString.php", "PHP", 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 38, 12, 94
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementStruct.php", "PHP", 0, 0, 0, 0, 29, 0, 0, 0, 0, 0, 0, 0, 0, 36, 11, 76
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Element\ElementXRef.php", "PHP", 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 45, 13, 99
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Encoding.php", "PHP", 0, 0, 0, 0, 77, 0, 0, 0, 0, 0, 0, 0, 0, 56, 25, 158
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Encoding\AbstractEncoding.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 9
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Encoding\EncodingLocator.php", "PHP", 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 18
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Encoding\ISOLatin1Encoding.php", "PHP", 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 33, 6, 77
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Encoding\ISOLatin9Encoding.php", "PHP", 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 33, 6, 77
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Encoding\MacRomanEncoding.php", "PHP", 0, 0, 0, 0, 42, 0, 0, 0, 0, 0, 0, 0, 0, 33, 6, 81
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Encoding\PostScriptGlyphs.php", "PHP", 0, 0, 0, 0, 1057, 0, 0, 0, 0, 0, 0, 0, 0, 36, 7, 1100
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Encoding\StandardEncoding.php", "PHP", 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 33, 6, 77
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Encoding\WinAnsiEncoding.php", "PHP", 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 33, 6, 77
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Exception\EncodingNotFoundException.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Font.php", "PHP", 0, 0, 0, 0, 396, 0, 0, 0, 0, 0, 0, 0, 0, 163, 106, 665
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Font\FontCIDFontType0.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 32, 5, 43
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Font\FontCIDFontType2.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 32, 5, 43
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Font\FontTrueType.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 32, 5, 43
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Font\FontType0.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 32, 5, 43
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Font\FontType1.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 32, 5, 43
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Font\FontType3.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 32, 5, 43
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Header.php", "PHP", 0, 0, 0, 0, 96, 0, 0, 0, 0, 0, 0, 0, 0, 72, 27, 195
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\PDFObject.php", "PHP", 0, 0, 0, 0, 580, 0, 0, 0, 0, 0, 0, 0, 0, 110, 131, 821
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Page.php", "PHP", 0, 0, 0, 0, 504, 0, 0, 0, 0, 0, 0, 0, 0, 343, 107, 954
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Pages.php", "PHP", 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 38, 10, 74
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\Parser.php", "PHP", 0, 0, 0, 0, 195, 0, 0, 0, 0, 0, 0, 0, 0, 73, 60, 328
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\RawData\FilterHelper.php", "PHP", 0, 0, 0, 0, 208, 0, 0, 0, 0, 0, 0, 0, 0, 162, 27, 397
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\RawData\RawDataParser.php", "PHP", 0, 0, 0, 0, 599, 0, 0, 0, 0, 0, 0, 0, 0, 279, 74, 952
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\XObject\Form.php", "PHP", 0, 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 32, 6, 52
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\XObject\Image.php", "PHP", 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 32, 5, 48
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\pdf\autoload.php", "PHP", 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 49, 7, 76
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 549, 0, 0, 0, 0, 549
"c:\Users\<USER>\Downloads\sb removal\script\apps\dialogflow\sitemap-generator.php", "PHP", 0, 0, 0, 0, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 114
"c:\Users\<USER>\Downloads\sb removal\script\apps\line\functions.php", "PHP", 0, 0, 0, 0, 158, 0, 0, 0, 0, 0, 0, 0, 0, 14, 8, 180
"c:\Users\<USER>\Downloads\sb removal\script\apps\line\post.php", "PHP", 0, 0, 0, 0, 94, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 118
"c:\Users\<USER>\Downloads\sb removal\script\apps\line\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 0, 0, 0, 0, 44
"c:\Users\<USER>\Downloads\sb removal\script\apps\martfury\admin.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 9, 15, 76
"c:\Users\<USER>\Downloads\sb removal\script\apps\martfury\functions.php", "PHP", 0, 0, 0, 0, 131, 0, 0, 0, 0, 0, 0, 0, 0, 42, 19, 192
"c:\Users\<USER>\Downloads\sb removal\script\apps\martfury\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 103, 0, 0, 0, 0, 103
"c:\Users\<USER>\Downloads\sb removal\script\apps\messenger\functions.php", "PHP", 0, 0, 0, 0, 276, 0, 0, 0, 0, 0, 0, 0, 0, 18, 11, 305
"c:\Users\<USER>\Downloads\sb removal\script\apps\messenger\post.php", "PHP", 0, 0, 0, 0, 146, 0, 0, 0, 0, 0, 0, 0, 0, 18, 13, 177
"c:\Users\<USER>\Downloads\sb removal\script\apps\messenger\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 90, 0, 0, 0, 0, 90
"c:\Users\<USER>\Downloads\sb removal\script\apps\opencart\functions.php", "PHP", 0, 0, 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 0, 8, 7, 107
"c:\Users\<USER>\Downloads\sb removal\script\apps\opencart\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 0, 0, 0, 0, 38
"c:\Users\<USER>\Downloads\sb removal\script\apps\perfex\admin.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 9, 13, 69
"c:\Users\<USER>\Downloads\sb removal\script\apps\perfex\functions.php", "PHP", 0, 0, 0, 0, 103, 0, 0, 0, 0, 0, 0, 0, 0, 35, 16, 154
"c:\Users\<USER>\Downloads\sb removal\script\apps\perfex\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 57, 0, 0, 0, 0, 57
"c:\Users\<USER>\Downloads\sb removal\script\apps\slack\emoji.json", "JSON", 0, 0, 1356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1356
"c:\Users\<USER>\Downloads\sb removal\script\apps\slack\functions.php", "PHP", 0, 0, 0, 0, 427, 0, 0, 0, 0, 0, 0, 0, 0, 66, 34, 527
"c:\Users\<USER>\Downloads\sb removal\script\apps\slack\post.php", "PHP", 0, 0, 0, 0, 80, 0, 0, 0, 0, 0, 0, 0, 0, 21, 15, 116
"c:\Users\<USER>\Downloads\sb removal\script\apps\slack\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 103, 0, 0, 0, 0, 103
"c:\Users\<USER>\Downloads\sb removal\script\apps\telegram\functions.php", "PHP", 0, 0, 0, 0, 177, 0, 0, 0, 0, 0, 0, 0, 0, 17, 12, 206
"c:\Users\<USER>\Downloads\sb removal\script\apps\telegram\post.php", "PHP", 0, 0, 0, 0, 93, 0, 0, 0, 0, 0, 0, 0, 0, 15, 10, 118
"c:\Users\<USER>\Downloads\sb removal\script\apps\telegram\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 0, 0, 0, 0, 39
"c:\Users\<USER>\Downloads\sb removal\script\apps\tickets\functions.php", "PHP", 0, 0, 0, 0, 208, 0, 0, 0, 0, 0, 0, 0, 0, 12, 8, 228
"c:\Users\<USER>\Downloads\sb removal\script\apps\tickets\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 0, 0, 0, 1, 281
"c:\Users\<USER>\Downloads\sb removal\script\apps\tickets\tickets.js", "JavaScript", 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 52, 670
"c:\Users\<USER>\Downloads\sb removal\script\apps\tickets\tickets.min.js", "JavaScript", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\apps\twitter\functions.php", "PHP", 0, 0, 0, 0, 308, 0, 0, 0, 0, 0, 0, 0, 0, 14, 22, 344
"c:\Users\<USER>\Downloads\sb removal\script\apps\twitter\post.php", "PHP", 0, 0, 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 0, 15, 14, 121
"c:\Users\<USER>\Downloads\sb removal\script\apps\twitter\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 67
"c:\Users\<USER>\Downloads\sb removal\script\apps\viber\functions.php", "PHP", 0, 0, 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 151
"c:\Users\<USER>\Downloads\sb removal\script\apps\viber\post.php", "PHP", 0, 0, 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 15, 10, 90
"c:\Users\<USER>\Downloads\sb removal\script\apps\viber\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 0, 0, 0, 0, 35
"c:\Users\<USER>\Downloads\sb removal\script\apps\wechat\emoji.json", "JSON", 0, 0, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101
"c:\Users\<USER>\Downloads\sb removal\script\apps\wechat\functions.php", "PHP", 0, 0, 0, 0, 138, 0, 0, 0, 0, 0, 0, 0, 0, 16, 11, 165
"c:\Users\<USER>\Downloads\sb removal\script\apps\wechat\post.php", "PHP", 0, 0, 0, 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 14, 13, 129
"c:\Users\<USER>\Downloads\sb removal\script\apps\wechat\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 0, 0, 0, 0, 45
"c:\Users\<USER>\Downloads\sb removal\script\apps\whatsapp\functions.php", "PHP", 0, 0, 0, 0, 901, 0, 0, 0, 0, 0, 0, 0, 0, 10, 31, 942
"c:\Users\<USER>\Downloads\sb removal\script\apps\whatsapp\post.php", "PHP", 0, 0, 0, 0, 267, 0, 0, 0, 0, 0, 0, 0, 0, 17, 15, 299
"c:\Users\<USER>\Downloads\sb removal\script\apps\whatsapp\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 0, 0, 0, 0, 288
"c:\Users\<USER>\Downloads\sb removal\script\apps\whmcs\admin.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 9, 15, 76
"c:\Users\<USER>\Downloads\sb removal\script\apps\whmcs\functions.php", "PHP", 0, 0, 0, 0, 108, 0, 0, 0, 0, 0, 0, 0, 0, 45, 21, 174
"c:\Users\<USER>\Downloads\sb removal\script\apps\whmcs\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0, 58
"c:\Users\<USER>\Downloads\sb removal\script\apps\zalo\functions.php", "PHP", 0, 0, 0, 0, 224, 0, 0, 0, 0, 0, 0, 0, 0, 14, 9, 247
"c:\Users\<USER>\Downloads\sb removal\script\apps\zalo\post.php", "PHP", 0, 0, 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 16, 12, 111
"c:\Users\<USER>\Downloads\sb removal\script\apps\zalo\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 54
"c:\Users\<USER>\Downloads\sb removal\script\apps\zendesk\functions.php", "PHP", 0, 0, 0, 0, 142, 0, 0, 0, 0, 0, 0, 0, 0, 14, 12, 168
"c:\Users\<USER>\Downloads\sb removal\script\apps\zendesk\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 0, 0, 0, 0, 32
"c:\Users\<USER>\Downloads\sb removal\script\config.php", "PHP", 0, 0, 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 33, 24, 126
"c:\Users\<USER>\Downloads\sb removal\script\config\config_010847917b08f2ee7462934228ebbb4166c40fb6.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_17fa2101211b483ccda37882f3dd9a690e390b0c.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_1fa56c7f0aaa5b7e3a024a8dfbcde290431d610d.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_47e075ea9d619cb0792d72f02ff124206931bd8d.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_5345658deb3c57b34e2419ea7241468960c6fc9f.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_5c0de8c713bbf0cd4aaab1511fca1d7a4ddd62d8.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_78288ad2af64ae8c79c8ad43ca52cf8c6ce362b6.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_7a91e79d90dd2dc51c4a72505ad3d9c4e074eaa9.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_9ba9f45af33527f978e0659e087d19fcbdeb0313.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_9fb62da2a36d410a356618bb9d8a2c8b13a03f6e.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_c60ee72f332688b58c716cfbe60c1f6526193e06.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_ccda4b1e25c3632202119e74f9dd75549026a482.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_d9fa87be44ca5d2c75aa3f07df8218f8fa064125.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_dbdc14e5f248ba3eb60e69ae4917a86df6398a65.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\config\config_e411e7dbd3cea062c333ac3e1da193cc12afeca0.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\css\admin-custom.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 1, 3, 16
"c:\Users\<USER>\Downloads\sb removal\script\css\admin.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 2
"c:\Users\<USER>\Downloads\sb removal\script\css\admin.scss", "SCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5961, 0, 86, 1267, 7314
"c:\Users\<USER>\Downloads\sb removal\script\css\articles.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 2
"c:\Users\<USER>\Downloads\sb removal\script\css\articles.scss", "SCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 21, 55, 341
"c:\Users\<USER>\Downloads\sb removal\script\css\main.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 2
"c:\Users\<USER>\Downloads\sb removal\script\css\main.scss", "SCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1217, 0, 27, 259, 1503
"c:\Users\<USER>\Downloads\sb removal\script\css\responsive-admin.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 3
"c:\Users\<USER>\Downloads\sb removal\script\css\responsive-admin.scss", "SCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1461, 0, 75, 351, 1887
"c:\Users\<USER>\Downloads\sb removal\script\css\responsive.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 3
"c:\Users\<USER>\Downloads\sb removal\script\css\responsive.scss", "SCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 277, 0, 9, 67, 353
"c:\Users\<USER>\Downloads\sb removal\script\css\rtl-admin.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 3
"c:\Users\<USER>\Downloads\sb removal\script\css\rtl-admin.scss", "SCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1067, 0, 9, 262, 1338
"c:\Users\<USER>\Downloads\sb removal\script\css\rtl.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 3
"c:\Users\<USER>\Downloads\sb removal\script\css\rtl.scss", "SCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 337, 0, 9, 90, 436
"c:\Users\<USER>\Downloads\sb removal\script\css\shared.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 2
"c:\Users\<USER>\Downloads\sb removal\script\css\shared.scss", "SCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2752, 0, 117, 539, 3408
"c:\Users\<USER>\Downloads\sb removal\script\css\tickets.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 3
"c:\Users\<USER>\Downloads\sb removal\script\css\tickets.scss", "SCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1134, 0, 57, 260, 1451
"c:\Users\<USER>\Downloads\sb removal\script\include\admin-detection.php", "PHP", 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 21, 17, 100
"c:\Users\<USER>\Downloads\sb removal\script\include\ajax.php", "PHP", 0, 0, 0, 0, 639, 0, 0, 0, 0, 0, 0, 0, 0, 14, 16, 669
"c:\Users\<USER>\Downloads\sb removal\script\include\api.php", "PHP", 0, 0, 0, 0, 479, 0, 0, 0, 0, 0, 0, 0, 0, 28, 21, 528
"c:\Users\<USER>\Downloads\sb removal\script\include\articles.php", "PHP", 0, 0, 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 9, 6, 171
"c:\Users\<USER>\Downloads\sb removal\script\include\components.php", "PHP", 0, 0, 0, 0, 1480, 0, 0, 0, 0, 0, 0, 0, 0, 30, 13, 1523
"c:\Users\<USER>\Downloads\sb removal\script\include\functions.php", "PHP", 0, 0, 0, 0, 2821, 0, 0, 0, 0, 0, 0, 0, 0, 201, 168, 3190
"c:\Users\<USER>\Downloads\sb removal\script\include\functions_email.php", "PHP", 0, 0, 0, 0, 1228, 0, 0, 0, 0, 0, 0, 0, 0, 155, 168, 1551
"c:\Users\<USER>\Downloads\sb removal\script\include\functions_messages.php", "PHP", 0, 0, 0, 0, 1460, 0, 0, 0, 0, 0, 0, 0, 0, 98, 76, 1634
"c:\Users\<USER>\Downloads\sb removal\script\include\functions_settings.php", "PHP", 0, 0, 0, 0, 1197, 0, 0, 0, 0, 0, 0, 0, 0, 73, 58, 1328
"c:\Users\<USER>\Downloads\sb removal\script\include\functions_users.php", "PHP", 0, 0, 0, 0, 1366, 0, 0, 0, 0, 0, 0, 0, 0, 112, 84, 1562
"c:\Users\<USER>\Downloads\sb removal\script\include\google.php", "PHP", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 9, 5, 35
"c:\Users\<USER>\Downloads\sb removal\script\include\init.php", "PHP", 0, 0, 0, 0, 228, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 244
"c:\Users\<USER>\Downloads\sb removal\script\include\messenger.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 9, 3, 20
"c:\Users\<USER>\Downloads\sb removal\script\include\pusher.php", "PHP", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 9, 3, 33
"c:\Users\<USER>\Downloads\sb removal\script\include\slack.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 9, 3, 20
"c:\Users\<USER>\Downloads\sb removal\script\include\upload.php", "PHP", 0, 0, 0, 0, 52, 0, 0, 0, 0, 0, 0, 0, 0, 8, 3, 63
"c:\Users\<USER>\Downloads\sb removal\script\js\admin.js", "JavaScript", 9135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 437, 769, 10341
"c:\Users\<USER>\Downloads\sb removal\script\js\enhanced-admin.js", "JavaScript", 369, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 80, 504
"c:\Users\<USER>\Downloads\sb removal\script\js\enhanced-editor.js", "JavaScript", 203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 64, 312
"c:\Users\<USER>\Downloads\sb removal\script\js\main.js", "JavaScript", 6364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 344, 435, 7143
"c:\Users\<USER>\Downloads\sb removal\script\js\min\admin.min.js", "JavaScript", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\js\min\jquery.min.js", "JavaScript", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 3
"c:\Users\<USER>\Downloads\sb removal\script\js\min\main.min.js", "JavaScript", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\manifest.json", "JSON", 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\academy.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\aecommerce.svg", "XML", 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 39
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\armember.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\business messages.svg", "XML", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\dialogflow.svg", "XML", 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 20
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\email.svg", "XML", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\facebook.svg", "XML", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 42
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\gbm.svg", "XML", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\instagram.svg", "XML", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\line.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\martfury.svg", "XML", 0, 0, 0, 189, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 191
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\messenger.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\opencart.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\perfex.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\phone.svg", "XML", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 17
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\shopify.svg", "XML", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\slack.svg", "XML", 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 23
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\telegram.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\text message.svg", "XML", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\ticket.svg", "XML", 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 36
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\tickets.svg", "XML", 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 36
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\twitter.svg", "XML", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 17
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\ump.svg", "XML", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 15
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\viber.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\wechat.svg", "XML", 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 59
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\whatsapp.svg", "XML", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 16
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\whmcs.svg", "XML", 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 26
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\woocommerce.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\zalo.svg", "XML", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 11
"c:\Users\<USER>\Downloads\sb removal\script\media\apps\zendesk.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\button-chat-2.svg", "XML", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 14
"c:\Users\<USER>\Downloads\sb removal\script\media\button-chat-3.svg", "XML", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 16
"c:\Users\<USER>\Downloads\sb removal\script\media\button-chat-4.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\button-chat-5.svg", "XML", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 18
"c:\Users\<USER>\Downloads\sb removal\script\media\button-chat-custom.svg", "XML", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 1, 19
"c:\Users\<USER>\Downloads\sb removal\script\media\button-chat.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\button-close-custom.svg", "XML", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 1, 20
"c:\Users\<USER>\Downloads\sb removal\script\media\button-close.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\close.svg", "XML", 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 38
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\android.svg", "XML", 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 55
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\apple.svg", "XML", 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 47
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\chrome.svg", "XML", 0, 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 59
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\edge.svg", "XML", 0, 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 45
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\facebook.svg", "XML", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 42
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\firefox.svg", "XML", 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 64
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\instagram.svg", "XML", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\internet-explorer.svg", "XML", 0, 0, 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 58
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\linux.svg", "XML", 0, 0, 0, 1532, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1533
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\opera.svg", "XML", 0, 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 45
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\safari.svg", "XML", 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 39
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\telegram.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\text message.svg", "XML", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\ubuntu.svg", "XML", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\whatsapp.svg", "XML", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 16
"c:\Users\<USER>\Downloads\sb removal\script\media\devices\windows.svg", "XML", 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 48
"c:\Users\<USER>\Downloads\sb removal\script\media\icon-18x18.svg", "XML", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 22
"c:\Users\<USER>\Downloads\sb removal\script\media\icon.svg", "XML", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 22
"c:\Users\<USER>\Downloads\sb removal\script\media\icons\icons.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 127, 1, 56, 184
"c:\Users\<USER>\Downloads\sb removal\script\media\icons\icons.scss", "SCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 218, 0, 0, 69, 287
"c:\Users\<USER>\Downloads\sb removal\script\media\icons\masi-chat.svg", "XML", 0, 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 75
"c:\Users\<USER>\Downloads\sb removal\script\media\lines.svg", "XML", 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 18
"c:\Users\<USER>\Downloads\sb removal\script\media\loader.svg", "XML", 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12
"c:\Users\<USER>\Downloads\sb removal\script\media\logo.svg", "XML", 0, 0, 0, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 103
"c:\Users\<USER>\Downloads\sb removal\script\media\masi-button-chat.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\masi-button-close.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\media\thumb.svg", "XML", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 16
"c:\Users\<USER>\Downloads\sb removal\script\media\triangle.svg", "XML", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 12
"c:\Users\<USER>\Downloads\sb removal\script\media\user.svg", "XML", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 14
"c:\Users\<USER>\Downloads\sb removal\script\resources\config-source.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 32
"c:\Users\<USER>\Downloads\sb removal\script\resources\json\countries.json", "JSON", 0, 0, 239, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 239
"c:\Users\<USER>\Downloads\sb removal\script\resources\json\country_codes.json", "JSON", 0, 0, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 252
"c:\Users\<USER>\Downloads\sb removal\script\resources\json\emoji-slack.json", "JSON", 0, 0, 1356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1356
"c:\Users\<USER>\Downloads\sb removal\script\resources\json\emoji.json", "JSON", 0, 0, 18884, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 18885
"c:\Users\<USER>\Downloads\sb removal\script\resources\json\phone.json", "JSON", 0, 0, 233, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 233
"c:\Users\<USER>\Downloads\sb removal\script\resources\json\settings.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2469, 0, 0, 0, 4, 2473
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\.vs\VSWorkspaceState.json", "JSON", 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\am.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\ar.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\bg.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\br.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\cs.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\da.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\de.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\el.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\es.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\et.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\fa.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\fi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\fr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\he.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\hi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\hr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\hu.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\id.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\is.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\it.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\ja.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\am.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\ar.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\bg.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\br.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\cs.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\da.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\de.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\el.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\es.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\et.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\fa.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\fi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\fr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\he.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\hi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\hr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\hu.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\id.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\is.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\it.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\ja.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\ka.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\ko.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\lt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\mk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\mn.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\ms.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\my.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\nl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\no.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\pa.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\pl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\pt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\ro.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\ru.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\sk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\sl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\source.json", "JSON", 0, 0, 408, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 408
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\sq.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\sr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\su.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\sv.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\th.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\tl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\tr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\tw.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\uk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\vi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\zh.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\js\zt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\ka.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\ko.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\lt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\mk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\mn.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\ms.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\my.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\nl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\no.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\pa.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\pl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\pt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\ro.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\ru.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\am.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\ar.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\bg.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\br.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\cs.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\da.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\de.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\el.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\es.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\et.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\fa.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\fi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\fr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\he.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\hi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\hr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\hu.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\id.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\is.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\it.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\ja.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\ka.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\ko.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\lt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\mk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\mn.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\ms.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\my.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\nl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\no.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\pa.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\pl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\pt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\ro.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\ru.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\sk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\sl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\source.json", "JSON", 0, 0, 834, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 834
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\sq.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\sr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\su.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\sv.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\th.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\tl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\tr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\tw.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\uk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\vi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\zh.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\settings\zt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\sk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\sl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\source.json", "JSON", 0, 0, 434, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 434
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\sq.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\sr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\su.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\sv.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\th.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\tl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\tr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\tw.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\uk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\vi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\zh.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\admin\zt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\am.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\ar.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\bg.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\br.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\cs.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\da.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\de.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\el.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\en.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\es.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\et.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\fa.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\fi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\fr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\he.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\hi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\hr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\hu.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\id.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\is.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\it.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\ja.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\ka.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\ko.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\lt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\mk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\mn.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\ms.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\my.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\nl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\no.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\pa.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\pl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\pt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\ro.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\ru.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\sk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\sl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\source.json", "JSON", 0, 0, 227, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 227
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\sq.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\sr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\su.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\sv.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\th.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\tl.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\tr.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\tw.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\uk.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\vi.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\zh-TW.json", "JSON", 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 127
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\zh.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\front\zt.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\resources\languages\language-codes.json", "JSON", 0, 0, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51
"c:\Users\<USER>\Downloads\sb removal\script\resources\pwa\offline.html", "HTML", 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 35
"c:\Users\<USER>\Downloads\sb removal\script\resources\sb.html", "HTML", 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 73
"c:\Users\<USER>\Downloads\sb removal\script\resources\sw.js", "JavaScript", 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 92
"c:\Users\<USER>\Downloads\sb removal\script\sw.js", "JavaScript", 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 8, 99
"c:\Users\<USER>\Downloads\sb removal\script\uploads\01-05-25\98760_user.svg", "XML", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 14
"c:\Users\<USER>\Downloads\sb removal\script\uploads\30-04-25\27297_user.svg", "XML", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 14
"c:\Users\<USER>\Downloads\sb removal\script\uploads\30-04-25\33274_user.svg", "XML", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 14
"c:\Users\<USER>\Downloads\sb removal\script\uploads\30-04-25\45217_user.svg", "XML", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 14
"c:\Users\<USER>\Downloads\sb removal\script\uploads\30-04-25\75540_user.svg", "XML", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 14
"c:\Users\<USER>\Downloads\sb removal\script\uploads\30-04-25\9689_user.svg", "XML", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 14
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\55\embeddings-05ffc5ed8c7e3b6bec25.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\55\embeddings-4ef5dbb244294121092c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\55\embeddings-ce69145d0bc6e521b373.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\55\embeddings-d38749688aee7deeec2e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\55\embeddings-de4abc5b492f2040cbb0.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\55\embeddings-f364879699005d119041.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\55\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\59\embeddings-f1ecd80fb5a25938a008.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\59\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-07c64b552109d572178a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-0dea28a49f92f1b206c4.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-145d036dbf3c69ad744c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-19173ddf9aa627dbad9f.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-24bce6a714406de3a0f1.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-387696452eae51a97ed0.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-3ca9813f9204efeb16a5.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-3fcb119c070b960bc3b4.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-4167afa9ba2ce5798b99.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-567852611323df1e3892.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-5980705de805d44cc29b.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-614f1b410c4bd1765c69.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-640a5085768b2fdbde29.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-6ccc53644d677eb3a56a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-7278f741400995e0b27d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-7ddc8343419ef513bef2.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-8b734ed5fe310a2b368f.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-9fa5f5bb8f3430af26f4.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-a31eb7e8ad0e806c937e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-ab2f4a9e6cff42238729.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-ad97071a9ff0dc7b9875.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-b97a91026d64ddb70e43.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-c03a48cf3146566a61a0.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-c6536846b868a49fb7fe.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-ce889b4b7546f4ad9d75.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-d21dbd22a531e3d14cb5.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-ddf8d0ba97f6012094a8.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-e23092f0c5515a9683e1.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-ee4e13686c66c03b6a4c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-ef904ec6601278437fe5.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-f41fb1e7f76d9af6f5fb.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-f50d2e3478ba1ff4f1a6.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-f615b1292e2cb2df2743.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-f825b3287753251ff1e1.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-fbf23f7bb03d421426de.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-fc370bc7c9f92aa0d72d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\embeddings-fc40379f534f1182d540.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\61\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\62\embeddings-dc74037ee640b588f359.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\62\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-06cbc0a6c64e93d510d8.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-0af40318ed97ad10b1d9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-1a078b215effda5c187a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-1eca215b6fcb3af35124.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-1f09b39836fe029cec0f.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-2137f1723dc95c98d73d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-2830e452c2ae6e6668cb.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-3513059ab95d602e8a78.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-3c5388113447ea351378.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-463567d35f94de6b4828.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-61a53168d75f65240b7d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-63bdccc88739858eaf6d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-6bda9dbc1151c95c93b3.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-6dbabb1da926b6f4b068.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-6f69e0259d1b65c12ea0.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-87382f607bdcb440d8f9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-96fe096b39b489a3f186.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-9844c2619f9ec4190389.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-9f3b39aec6bff41ac88c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-b67b62b8a3197ed7c986.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-c3829242d37132d90a68.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-c6b5dc53f43339db0adc.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-c75ea3138bcdbf31f8ef.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-c7e0efc9e3a217009e8e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-cced382040fb5bb54637.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-cdc60f52a231c29ddfae.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-d99892e1f0be5ad3dbd2.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-dbe988110db23f7a5e50.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-dc0e821b2e391ebd00c9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-ecdbf28779d9a6250902.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-ed06294e79cb48369c8b.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-f08771bd3081b853f8de.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-f281cf31bbbfccb67081.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-f42f3a5ddc50d46cfbb7.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-f553af662f4ed89437bf.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-fad1a684b03ccb67054d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\embeddings-fefa9dbea11c9f31db33.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\63\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\64\embeddings-36a15dc66196317ab989.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\64\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\65\embeddings-156f3eedcb31550ea39c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\65\embeddings-2a87adbc7eef8a52eb08.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\65\embeddings-326bdab2f09629970b4f.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\65\embeddings-54a42721b338a32ac8d2.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\65\embeddings-6207e3ca71a391ef8af5.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\65\embeddings-ccae8ed3bb51a238b725.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\65\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-01777fb6f8ac8a75160e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-0280b0b55612edbe14c6.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-03903060fa6dfe3a8e0c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-0426a1974df93b521c74.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-04ed41acf85b14e90f6a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-05f9f9bc7cccffa6e0de.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-0a67a9575c5f0a337ab6.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-0c32be9f44a48a7ae8b3.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-0df3ddd6a8b8cd395ddf.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-0e7ab4f7aca6c2506777.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-11a79855636838f13388.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-11f2563de23c3f251a68.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-17ca5c94cc659b1715cd.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-20eb0ff5228fbcdb9056.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-22496a88837c11e3010a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-2308ff4577d492e3ecb8.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-2b2d20332c381bcc8c9e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-2f1146fbac2d6f9f178f.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-3474ad0772a7ed5bf34a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-449b621b68dd0ad9d8d8.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-47d2c6f86b50c911cc9a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-4c897864dc7ab1c8a906.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-4f599689a3c4462f5098.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-51e94fed33ba0b6ed1cc.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-5401ace9ec76f2f884eb.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-5ab4c632e9a0f10ee856.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-60f644252e41bab27f47.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-637d30b440fddb2e10e7.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-6c06e3d1d2f14736381b.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-6e9e79da90baa4116110.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-7242e3d9e97f5f00586b.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-74f2e03d6a74430693c9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-776083831c2be134f5fd.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-7a1be334c809a49c2ac8.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-7a328c8ef1bc1972211d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-7ee8c38f7751cb5b549e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-7f62505387a8e4145ab7.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-87af6026fba89881acd1.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-967f153e9ef3ee905f36.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-97001bb0ed8114cee073.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-98ea53794fc112d244ce.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-9917346e2ef17973e9f4.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-9c88d831fd26d9b7a8db.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-9db89ba7ec2f1601bac9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-9ec928803a4f127c038a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-a2941f57a4edc1e64bee.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-a94b6e728c0b15627ef8.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-a95c6f3b17e10d798eed.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-aa5863c41cb75150524d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-bbc3c930097b12d3e645.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-c46f63565cd9263039c9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-cba8e5b8f363e5b8a687.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-cd30f62f922a5b11c9a8.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-cd68c70351af8bfaa10c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-d0b4f9261b3d2970ff26.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-dc3d44a7bb30d169f56a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-dc53d0344cb1dc6bf328.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-df9407ff5df611ea9299.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-e2beba9cf1fd39138c26.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-eaf22902e29c7e47cab4.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-f3be943e09931cf05032.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-f8e84e9b6f524f240e5f.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-f9a3fe8d079df202af41.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-fad29fb65f748fe16dd3.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-fdea8c572d3499881c82.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\embeddings-fffaf52654a15fca3450.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\66\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\69\embeddings-90af7d7074b6dc9369b9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\69\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\72\embeddings-1373cc6e198f80beb735.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\72\embeddings-f538827274870d34f6f5.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\72\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-031a59591c294a23470e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-064b98de40b12a765741.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-080993c024921bf5c257.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-094fbb5718bd2da81c07.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-0bca7ef7091fc2a8d011.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-0cda7e6316bebeec8643.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-17820e4b83791da9f1eb.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-198565570483e7275370.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-1cde4f616c3fe2ee6e55.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-1e0f8dd03c33686d02b1.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-1f0b1571200af3549394.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-215f46d60cc0b80eff90.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-21679d1c5c987bd8f227.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-2aa8a0389950e5224ae9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-31d0e2ad626f337932b9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-32154d7d8aa61c02893e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-3c1aecafd754faf1eb73.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-408b2e1586c905cdc3c0.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-43054260b32d591e9377.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-4f9d146a832ebb7c3a72.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-506ba3fd5053c0b7c0a3.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-51dd564f5acb1930fee0.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-5294d8f8916645981db4.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-5bab02a643c1335ccedd.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-5e5e098baf5aa7f1f3ee.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-651963963a1f4bec7ee9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-6bd6bbb2cd6a1a083754.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-6c6fb12cc5592c3c3770.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-6ee8959e426afc57deee.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-7ff276dd320f2061f904.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-81901d3644e4a06019c9.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-8477a3715df41426c6b1.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-894c6fdfda712d7207de.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-8c0a8fd1a56d933140d8.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-928480e7d9430c25d43c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-94d2ebaa1c14dcbdf37e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-9918981c15b6e16a123a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-993c7c6708026be4097a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-9b406229d298ba1d5b30.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-9f752285273a115d7359.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-a163c134170ca440b5c6.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-a1b47f9285f6354f63be.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-a9fdf88f404185486394.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-b04333a09bf54ad9d98d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-b3aa2b012f617327125a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-b44b2f93b768468692f5.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-b6910ac4f482196aa42c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-b7cf011f0c2f68fa48a5.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-b90cd265caae1accd4e2.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-c05806c9d69947d97e09.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-c1652a79afe4db9ae88c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-c83ba112ff6123c0b316.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-cd27f339402dcd98a10d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-d0508abec4ff3520bf9e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-d06f3e90bfd565341fcc.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-d10b621cce1983b24479.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-d2a385072302874ef00b.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-d3b832138d807a338715.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-d6912e194c1ceef0928c.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-d9fab44d5d4647aed6f0.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-e00e7d91cee4032a442a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-e97f5c9ba8d23d980a9b.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-eb62e5f04c0a54dbbce4.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-eb7b6898ccba15b475da.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-ee8843ab1746a0f8eb89.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-eefabaeae70223d3649a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-f02473627baae525a3e3.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-f0bea847ab35cf052ab8.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-f165376f97d318a7fafe.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-f25f8eb475fe0a2363fa.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-f702e86b98c3e7822c24.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-f7faa4709fea4e9c2e37.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-f8f114950a3c527e8b0d.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-fe74b6fa9764df6e8769.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\embeddings-ff240bc171c3a1599375.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\75\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\76\embeddings-0c2f896c7ea88787340a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\76\embeddings-7e7d124da5386dd45986.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\76\embeddings-87a1dcd43a67aa652a1a.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\76\embeddings-97ee7fae193e1fd360a5.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\76\embeddings-9be639e486c541205b3e.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\76\embeddings-aa4535faeb132181ba29.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\embeddings\76\index.html", "HTML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\settings_918089239.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\settings_test.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\settings_test2.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\settings_test3.json", "JSON", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\uploads\sitemap.xml", "XML", 0, 0, 0, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 94
"c:\Users\<USER>\Downloads\sb removal\script\vendor\OneSignalSDK.sw.js", "JavaScript", 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\chart.min.js", "JavaScript", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 1, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\daterangepicker.min.js", "JavaScript", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\vendor\editorjs.js", "JavaScript", 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 8, 23
"c:\Users\<USER>\Downloads\sb removal\script\vendor\lame.min.js", "JavaScript", 353, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 356
"c:\Users\<USER>\Downloads\sb removal\script\vendor\moment.min.js", "JavaScript", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Downloads\sb removal\script\vendor\phpmailer\Exception.php", "PHP", 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 28, 4, 41
"c:\Users\<USER>\Downloads\sb removal\script\vendor\phpmailer\PHPMailer.php", "PHP", 0, 0, 0, 0, 2926, 0, 0, 0, 0, 0, 0, 0, 0, 1929, 394, 5249
"c:\Users\<USER>\Downloads\sb removal\script\vendor\phpmailer\PHPMailerAutoload.php", "PHP", 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 23, 3, 41
"c:\Users\<USER>\Downloads\sb removal\script\vendor\phpmailer\SMTP.php", "PHP", 0, 0, 0, 0, 747, 0, 0, 0, 0, 0, 0, 0, 0, 619, 144, 1510
"c:\Users\<USER>\Downloads\sb removal\script\vendor\phpmailer\class.phpmailer.php", "PHP", 0, 0, 0, 0, 2319, 0, 0, 0, 0, 0, 0, 0, 0, 1456, 262, 4037
"c:\Users\<USER>\Downloads\sb removal\script\vendor\phpmailer\class.smtp.php", "PHP", 0, 0, 0, 0, 653, 0, 0, 0, 0, 0, 0, 0, 0, 511, 93, 1257
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\autoload.php", "PHP", 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\ClassLoader.php", "PHP", 0, 0, 0, 0, 253, 0, 0, 0, 0, 0, 0, 0, 0, 148, 45, 446
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\InstalledVersions.php", "PHP", 0, 0, 0, 0, 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 114, 256
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\autoload_classmap.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 11
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\autoload_files.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 11
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\autoload_namespaces.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 10
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\autoload_psr4.php", "PHP", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 12
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\autoload_real.php", "PHP", 0, 0, 0, 0, 56, 0, 0, 0, 0, 0, 0, 0, 0, 4, 16, 76
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\autoload_static.php", "PHP", 0, 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 1, 9, 46
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\installed.json", "JSON", 0, 0, 262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 263
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\installed.php", "PHP", 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 61
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\composer\platform_check.php", "PHP", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5, 27
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\CHANGELOG.md", "Markdown", 0, 0, 0, 0, 0, 1301, 0, 0, 0, 0, 0, 0, 0, 0, 364, 1665
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\README.md", "Markdown", 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 28, 95
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\UPGRADING.md", "Markdown", 0, 0, 0, 0, 0, 1008, 0, 0, 0, 0, 0, 0, 0, 1, 245, 1254
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\composer.json", "JSON", 0, 0, 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 132
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\BodySummarizer.php", "PHP", 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 6, 6, 29
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\BodySummarizerInterface.php", "PHP", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 3, 4, 14
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Client.php", "PHP", 0, 0, 0, 0, 274, 0, 0, 0, 0, 0, 0, 0, 0, 154, 56, 484
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\ClientInterface.php", "PHP", 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 60, 9, 85
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\ClientTrait.php", "PHP", 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 166, 17, 242
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Cookie\CookieJar.php", "PHP", 0, 0, 0, 0, 210, 0, 0, 0, 0, 0, 0, 0, 0, 62, 36, 308
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Cookie\CookieJarInterface.php", "PHP", 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 59, 9, 81
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Cookie\FileCookieJar.php", "PHP", 0, 0, 0, 0, 52, 0, 0, 0, 0, 0, 0, 0, 0, 38, 12, 102
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Cookie\SessionCookieJar.php", "PHP", 0, 0, 0, 0, 42, 0, 0, 0, 0, 0, 0, 0, 0, 27, 9, 78
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Cookie\SetCookie.php", "PHP", 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 157, 67, 489
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Exception\BadResponseException.php", "PHP", 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 10, 6, 40
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Exception\ClientException.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 11
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Exception\ConnectException.php", "PHP", 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 22, 8, 57
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Exception\GuzzleException.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 10
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Exception\InvalidArgumentException.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Exception\RequestException.php", "PHP", 0, 0, 0, 0, 88, 0, 0, 0, 0, 0, 0, 0, 0, 44, 19, 151
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Exception\ServerException.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 11
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Exception\TooManyRedirectsException.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Exception\TransferException.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\HandlerStack.php", "PHP", 0, 0, 0, 0, 143, 0, 0, 0, 0, 0, 0, 0, 0, 98, 35, 276
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Handler\CurlFactory.php", "PHP", 0, 0, 0, 0, 553, 0, 0, 0, 0, 0, 0, 0, 0, 87, 97, 737
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Handler\CurlFactoryInterface.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 13, 5, 26
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Handler\CurlHandler.php", "PHP", 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 19, 8, 50
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Handler\CurlMultiHandler.php", "PHP", 0, 0, 0, 0, 163, 0, 0, 0, 0, 0, 0, 0, 0, 78, 44, 285
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Handler\EasyHandle.php", "PHP", 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 46, 18, 113
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Handler\HeaderProcessor.php", "PHP", 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 12, 9, 43
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Handler\MockHandler.php", "PHP", 0, 0, 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 56, 28, 213
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Handler\Proxy.php", "PHP", 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 27, 5, 52
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Handler\StreamHandler.php", "PHP", 0, 0, 0, 0, 445, 0, 0, 0, 0, 0, 0, 0, 0, 88, 89, 622
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\MessageFormatter.php", "PHP", 0, 0, 0, 0, 130, 0, 0, 0, 0, 0, 0, 0, 0, 54, 16, 200
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\MessageFormatterInterface.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 7, 4, 19
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Middleware.php", "PHP", 0, 0, 0, 0, 154, 0, 0, 0, 0, 0, 0, 0, 0, 92, 23, 269
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Pool.php", "PHP", 0, 0, 0, 0, 64, 0, 0, 0, 0, 0, 0, 0, 0, 50, 12, 126
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\PrepareBodyMiddleware.php", "PHP", 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 25, 20, 106
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\RedirectMiddleware.php", "PHP", 0, 0, 0, 0, 149, 0, 0, 0, 0, 0, 0, 0, 0, 45, 35, 229
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\RequestOptions.php", "PHP", 0, 0, 0, 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 206, 33, 275
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\RetryMiddleware.php", "PHP", 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 36, 17, 120
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\TransferStats.php", "PHP", 0, 0, 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 0, 59, 17, 134
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\Utils.php", "PHP", 0, 0, 0, 0, 207, 0, 0, 0, 0, 0, 0, 0, 0, 133, 45, 385
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\functions.php", "PHP", 0, 0, 0, 0, 42, 0, 0, 0, 0, 0, 0, 0, 0, 114, 12, 168
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\guzzle\src\functions_include.php", "PHP", 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 7
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\CHANGELOG.md", "Markdown", 0, 0, 0, 0, 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 86, 167
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\README.md", "Markdown", 0, 0, 0, 0, 0, 410, 0, 0, 0, 0, 0, 0, 0, 0, 150, 560
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\composer.json", "JSON", 0, 0, 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 59
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\AggregateException.php", "PHP", 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 3, 4, 20
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\CancellationException.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 3, 4, 13
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\Coroutine.php", "PHP", 0, 0, 0, 0, 89, 0, 0, 0, 0, 0, 0, 0, 0, 55, 19, 163
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\Create.php", "PHP", 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 22, 14, 80
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\Each.php", "PHP", 0, 0, 0, 0, 42, 0, 0, 0, 0, 0, 0, 0, 0, 34, 6, 82
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\EachPromise.php", "PHP", 0, 0, 0, 0, 150, 0, 0, 0, 0, 0, 0, 0, 0, 59, 40, 249
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\FulfilledPromise.php", "PHP", 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 13, 15, 90
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\Is.php", "PHP", 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 12, 7, 41
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\Promise.php", "PHP", 0, 0, 0, 0, 198, 0, 0, 0, 0, 0, 0, 0, 0, 48, 36, 282
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\PromiseInterface.php", "PHP", 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 62, 11, 92
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\PromisorInterface.php", "PHP", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 6, 4, 17
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\RejectedPromise.php", "PHP", 0, 0, 0, 0, 65, 0, 0, 0, 0, 0, 0, 0, 0, 15, 16, 96
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\RejectionException.php", "PHP", 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 15, 9, 50
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\TaskQueue.php", "PHP", 0, 0, 0, 0, 39, 0, 0, 0, 0, 0, 0, 0, 0, 24, 9, 72
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\TaskQueueInterface.php", "PHP", 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 10, 6, 25
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\promises\src\Utils.php", "PHP", 0, 0, 0, 0, 142, 0, 0, 0, 0, 0, 0, 0, 0, 95, 25, 262
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\CHANGELOG.md", "Markdown", 0, 0, 0, 0, 0, 278, 0, 0, 0, 0, 0, 0, 0, 0, 188, 466
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\README.md", "Markdown", 0, 0, 0, 0, 0, 545, 0, 0, 0, 0, 0, 0, 0, 0, 343, 888
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\composer.json", "JSON", 0, 0, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 94
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\AppendStream.php", "PHP", 0, 0, 0, 0, 157, 0, 0, 0, 0, 0, 0, 0, 0, 47, 45, 249
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\BufferStream.php", "PHP", 0, 0, 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 28, 29, 148
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\CachingStream.php", "PHP", 0, 0, 0, 0, 96, 0, 0, 0, 0, 0, 0, 0, 0, 31, 27, 154
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\DroppingStream.php", "PHP", 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 12, 12, 50
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\Exception\MalformedUriException.php", "PHP", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 3, 5, 15
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\FnStream.php", "PHP", 0, 0, 0, 0, 112, 0, 0, 0, 0, 0, 0, 0, 0, 40, 29, 181
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\Header.php", "PHP", 0, 0, 0, 0, 86, 0, 0, 0, 0, 0, 0, 0, 0, 29, 20, 135
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\HttpFactory.php", "PHP", 0, 0, 0, 0, 73, 0, 0, 0, 0, 0, 0, 0, 0, 6, 16, 95
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\InflateStream.php", "PHP", 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 16, 7, 38
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\LazyOpenStream.php", "PHP", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 18, 11, 50
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\LimitStream.php", "PHP", 0, 0, 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 0, 43, 25, 158
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\Message.php", "PHP", 0, 0, 0, 0, 147, 0, 0, 0, 0, 0, 0, 0, 0, 59, 41, 247
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\MessageTrait.php", "PHP", 0, 0, 0, 0, 164, 0, 0, 0, 0, 0, 0, 0, 0, 56, 46, 266
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\MimeType.php", "PHP", 0, 0, 0, 0, 1244, 0, 0, 0, 0, 0, 0, 0, 0, 10, 6, 1260
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\MultipartStream.php", "PHP", 0, 0, 0, 0, 101, 0, 0, 0, 0, 0, 0, 0, 0, 38, 27, 166
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\NoSeekStream.php", "PHP", 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 4, 8, 29
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\PumpStream.php", "PHP", 0, 0, 0, 0, 119, 0, 0, 0, 0, 0, 0, 0, 0, 28, 33, 180
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\Query.php", "PHP", 0, 0, 0, 0, 80, 0, 0, 0, 0, 0, 0, 0, 0, 26, 13, 119
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\Request.php", "PHP", 0, 0, 0, 0, 111, 0, 0, 0, 0, 0, 0, 0, 0, 18, 31, 160
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\Response.php", "PHP", 0, 0, 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 0, 16, 20, 162
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\Rfc7230.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 12, 4, 24
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\ServerRequest.php", "PHP", 0, 0, 0, 0, 204, 0, 0, 0, 0, 0, 0, 0, 0, 82, 55, 341
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\Stream.php", "PHP", 0, 0, 0, 0, 198, 0, 0, 0, 0, 0, 0, 0, 0, 36, 50, 284
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\StreamDecoratorTrait.php", "PHP", 0, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 29, 28, 157
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\StreamWrapper.php", "PHP", 0, 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 0, 62, 28, 208
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\UploadedFile.php", "PHP", 0, 0, 0, 0, 136, 0, 0, 0, 0, 0, 0, 0, 0, 41, 35, 212
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\Uri.php", "PHP", 0, 0, 0, 0, 423, 0, 0, 0, 0, 0, 0, 0, 0, 206, 115, 744
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\UriComparator.php", "PHP", 0, 0, 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 0, 10, 12, 53
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\UriNormalizer.php", "PHP", 0, 0, 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 0, 104, 30, 221
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\UriResolver.php", "PHP", 0, 0, 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 57, 26, 212
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\guzzlehttp\psr7\src\Utils.php", "PHP", 0, 0, 0, 0, 271, 0, 0, 0, 0, 0, 0, 0, 0, 146, 61, 478
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\random_compat\build-phar.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 1, 2, 5
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\random_compat\composer.json", "JSON", 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 35
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\random_compat\lib\random.php", "PHP", 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 30, 2, 33
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\random_compat\other\build_phar.php", "PHP", 0, 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 9, 4, 58
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\random_compat\psalm-autoload.php", "PHP", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\random_compat\psalm.xml", "XML", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 20
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\README.md", "Markdown", 0, 0, 0, 0, 0, 271, 0, 0, 0, 0, 0, 0, 0, 0, 77, 348
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\appveyor.yml", "YAML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 0, 0, 0, 3, 28
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\autoload-fast.php", "PHP", 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\autoload-pedantic.php", "PHP", 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 7
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\autoload-php7.php", "PHP", 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 10, 4, 32
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\autoload-phpunit.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\autoload.php", "PHP", 0, 0, 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 17, 6, 66
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\build-phar.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 2, 3, 9
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\composer-php52.json", "JSON", 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 88
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\composer.json", "JSON", 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 64
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\dist\Makefile", "Makefile", 0, 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 4, 10, 40
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\dist\box.json", "JSON", 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 33
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\lib\constants.php", "PHP", 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 53
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\lib\namespaced.php", "PHP", 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 21, 6, 49
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\lib\php72compat.php", "PHP", 0, 0, 0, 0, 703, 0, 0, 0, 0, 0, 0, 0, 0, 659, 5, 1367
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\lib\php72compat_const.php", "PHP", 0, 0, 0, 0, 88, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 91
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\lib\php84compat.php", "PHP", 0, 0, 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 0, 45, 3, 132
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\lib\php84compat_const.php", "PHP", 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 11
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\lib\ristretto255.php", "PHP", 0, 0, 0, 0, 167, 0, 0, 0, 0, 0, 0, 0, 0, 109, 3, 279
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\lib\sodium_compat.php", "PHP", 0, 0, 0, 0, 384, 0, 0, 0, 0, 0, 0, 0, 0, 436, 7, 827
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\lib\stream-xchacha20.php", "PHP", 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 33, 2, 76
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Compat.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\BLAKE2b.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\ChaCha20.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\ChaCha20\Ctx.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\ChaCha20\IetfCtx.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Curve25519.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Curve25519\Fe.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Curve25519\Ge\Cached.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Curve25519\Ge\P1p1.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Curve25519\Ge\P2.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Curve25519\Ge\P3.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Curve25519\Ge\Precomp.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Curve25519\H.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Ed25519.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\HChaCha20.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\HSalsa20.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Poly1305.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Poly1305\State.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Salsa20.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\SipHash.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Util.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\X25519.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\XChaCha20.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Core\Xsalsa20.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\Crypto.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\namespaced\File.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\psalm-above-3.xml", "XML", 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 9, 55
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\psalm-below-3.xml", "XML", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 7, 44
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Compat.php", "PHP", 0, 0, 0, 0, 2077, 0, 0, 0, 0, 0, 0, 0, 0, 1246, 238, 3561
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\BLAKE2b.php", "PHP", 0, 0, 0, 0, 379, 0, 0, 0, 0, 0, 0, 0, 0, 269, 71, 719
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\ChaCha20.php", "PHP", 0, 0, 0, 0, 183, 0, 0, 0, 0, 0, 0, 0, 0, 186, 32, 401
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\ChaCha20\Ctx.php", "PHP", 0, 0, 0, 0, 64, 0, 0, 0, 0, 0, 0, 0, 0, 52, 11, 127
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\ChaCha20\IetfCtx.php", "PHP", 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 16, 4, 40
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Curve25519.php", "PHP", 0, 0, 0, 0, 2310, 0, 0, 0, 0, 0, 0, 0, 0, 659, 228, 3197
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Curve25519\Fe.php", "PHP", 0, 0, 0, 0, 106, 0, 0, 0, 0, 0, 0, 0, 0, 67, 13, 186
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Curve25519\Ge\Cached.php", "PHP", 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 25, 7, 66
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Curve25519\Ge\P1p1.php", "PHP", 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 28, 6, 68
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Curve25519\Ge\P2.php", "PHP", 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 21, 6, 55
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Curve25519\Ge\P3.php", "PHP", 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 25, 7, 66
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Curve25519\Ge\Precomp.php", "PHP", 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 23, 6, 57
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Curve25519\H.php", "PHP", 0, 0, 0, 0, 1431, 0, 0, 0, 0, 0, 0, 0, 0, 30, 7, 1468
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Curve25519\README.md", "Markdown", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Ed25519.php", "PHP", 0, 0, 0, 0, 304, 0, 0, 0, 0, 0, 0, 0, 0, 136, 42, 482
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\HChaCha20.php", "PHP", 0, 0, 0, 0, 80, 0, 0, 0, 0, 0, 0, 0, 0, 33, 15, 128
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\HSalsa20.php", "PHP", 0, 0, 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 0, 52, 12, 142
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Int32.php", "PHP", 0, 0, 0, 0, 536, 0, 0, 0, 0, 0, 0, 0, 0, 247, 89, 872
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Int64.php", "PHP", 0, 0, 0, 0, 705, 0, 0, 0, 0, 0, 0, 0, 0, 265, 97, 1067
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Poly1305.php", "PHP", 0, 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 22, 5, 64
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Poly1305\State.php", "PHP", 0, 0, 0, 0, 290, 0, 0, 0, 0, 0, 0, 0, 0, 113, 49, 452
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Salsa20.php", "PHP", 0, 0, 0, 0, 203, 0, 0, 0, 0, 0, 0, 0, 0, 82, 22, 307
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\SecretStream\State.php", "PHP", 0, 0, 0, 0, 99, 0, 0, 0, 0, 0, 0, 0, 0, 48, 17, 164
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\SipHash.php", "PHP", 0, 0, 0, 0, 184, 0, 0, 0, 0, 0, 0, 0, 0, 21, 34, 239
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\Util.php", "PHP", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 3, 4, 14
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\X25519.php", "PHP", 0, 0, 0, 0, 228, 0, 0, 0, 0, 0, 0, 0, 0, 69, 49, 346
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\XChaCha20.php", "PHP", 0, 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 24, 4, 65
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core32\XSalsa20.php", "PHP", 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 27, 4, 58
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\AEGIS128L.php", "PHP", 0, 0, 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 0, 34, 7, 133
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\AEGIS256.php", "PHP", 0, 0, 0, 0, 95, 0, 0, 0, 0, 0, 0, 0, 0, 31, 9, 135
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\AEGIS\State128L.php", "PHP", 0, 0, 0, 0, 160, 0, 0, 0, 0, 0, 0, 0, 0, 102, 33, 295
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\AEGIS\State256.php", "PHP", 0, 0, 0, 0, 143, 0, 0, 0, 0, 0, 0, 0, 0, 84, 25, 252
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\AES.php", "PHP", 0, 0, 0, 0, 385, 0, 0, 0, 0, 0, 0, 0, 0, 117, 31, 533
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\AES\Block.php", "PHP", 0, 0, 0, 0, 211, 0, 0, 0, 0, 0, 0, 0, 0, 116, 28, 355
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\AES\Expanded.php", "PHP", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 13
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\AES\KeySchedule.php", "PHP", 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 28, 10, 84
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\BLAKE2b.php", "PHP", 0, 0, 0, 0, 449, 0, 0, 0, 0, 0, 0, 0, 0, 266, 75, 790
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Base64\Common.php", "PHP", 0, 0, 0, 0, 128, 0, 0, 0, 0, 0, 0, 0, 0, 74, 12, 214
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Base64\Original.php", "PHP", 0, 0, 0, 0, 145, 0, 0, 0, 0, 0, 0, 0, 0, 81, 23, 249
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Base64\UrlSafe.php", "PHP", 0, 0, 0, 0, 145, 0, 0, 0, 0, 0, 0, 0, 0, 81, 22, 248
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\ChaCha20.php", "PHP", 0, 0, 0, 0, 191, 0, 0, 0, 0, 0, 0, 0, 0, 176, 29, 396
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\ChaCha20\Ctx.php", "PHP", 0, 0, 0, 0, 63, 0, 0, 0, 0, 0, 0, 0, 0, 47, 10, 120
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\ChaCha20\IetfCtx.php", "PHP", 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 15, 4, 39
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Curve25519.php", "PHP", 0, 0, 0, 0, 2098, 0, 0, 0, 0, 0, 0, 0, 0, 724, 181, 3003
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Curve25519\Fe.php", "PHP", 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 0, 0, 0, 53, 11, 124
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Curve25519\Ge\Cached.php", "PHP", 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 25, 7, 66
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Curve25519\Ge\P1p1.php", "PHP", 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 25, 6, 65
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Curve25519\Ge\P2.php", "PHP", 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 21, 6, 55
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Curve25519\Ge\P3.php", "PHP", 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 25, 7, 66
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Curve25519\Ge\Precomp.php", "PHP", 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 21, 6, 55
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Curve25519\H.php", "PHP", 0, 0, 0, 0, 1431, 0, 0, 0, 0, 0, 0, 0, 0, 30, 7, 1468
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Curve25519\README.md", "Markdown", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 2, 4
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Ed25519.php", "PHP", 0, 0, 0, 0, 304, 0, 0, 0, 0, 0, 0, 0, 0, 136, 41, 481
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\HChaCha20.php", "PHP", 0, 0, 0, 0, 80, 0, 0, 0, 0, 0, 0, 0, 0, 15, 14, 109
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\HSalsa20.php", "PHP", 0, 0, 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 0, 17, 5, 97
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Poly1305.php", "PHP", 0, 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 22, 5, 64
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Poly1305\State.php", "PHP", 0, 0, 0, 0, 277, 0, 0, 0, 0, 0, 0, 0, 0, 120, 49, 446
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Ristretto255.php", "PHP", 0, 0, 0, 0, 520, 0, 0, 0, 0, 0, 0, 0, 0, 167, 78, 765
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Salsa20.php", "PHP", 0, 0, 0, 0, 198, 0, 0, 0, 0, 0, 0, 0, 0, 54, 22, 274
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\SecretStream\State.php", "PHP", 0, 0, 0, 0, 99, 0, 0, 0, 0, 0, 0, 0, 0, 48, 17, 164
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\SipHash.php", "PHP", 0, 0, 0, 0, 213, 0, 0, 0, 0, 0, 0, 0, 0, 58, 36, 307
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\Util.php", "PHP", 0, 0, 0, 0, 490, 0, 0, 0, 0, 0, 0, 0, 0, 379, 58, 927
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\X25519.php", "PHP", 0, 0, 0, 0, 235, 0, 0, 0, 0, 0, 0, 0, 0, 58, 35, 328
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\XChaCha20.php", "PHP", 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 45, 6, 118
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Core\XSalsa20.php", "PHP", 0, 0, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 27, 4, 58
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Crypto.php", "PHP", 0, 0, 0, 0, 943, 0, 0, 0, 0, 0, 0, 0, 0, 529, 184, 1656
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\Crypto32.php", "PHP", 0, 0, 0, 0, 942, 0, 0, 0, 0, 0, 0, 0, 0, 529, 184, 1655
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\File.php", "PHP", 0, 0, 0, 0, 952, 0, 0, 0, 0, 0, 0, 0, 0, 438, 178, 1568
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\PHP52\SplFixedArray.php", "PHP", 0, 0, 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 0, 78, 19, 189
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\paragonie\sodium_compat\src\SodiumException.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 12
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-client\CHANGELOG.md", "Markdown", 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 16, 32
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-client\README.md", "Markdown", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 5, 13
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-client\composer.json", "JSON", 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 31
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-client\src\ClientExceptionInterface.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 11
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-client\src\ClientInterface.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 9, 4, 21
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-client\src\NetworkExceptionInterface.php", "PHP", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 14, 4, 25
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-client\src\RequestExceptionInterface.php", "PHP", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 14, 4, 25
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-factory\README.md", "Markdown", 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 5, 13
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-factory\composer.json", "JSON", 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 39
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-factory\src\RequestFactoryInterface.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 10, 3, 19
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-factory\src\ResponseFactoryInterface.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 10, 3, 19
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-factory\src\ServerRequestFactoryInterface.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 16, 3, 25
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-factory\src\StreamFactoryInterface.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 33, 5, 46
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-factory\src\UploadedFileFactoryInterface.php", "PHP", 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 20, 3, 35
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-factory\src\UriFactoryInterface.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 9, 3, 18
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\CHANGELOG.md", "Markdown", 0, 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 13, 37
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\README.md", "Markdown", 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 5, 16
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\composer.json", "JSON", 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\docs\PSR7-Interfaces.md", "Markdown", 0, 0, 0, 0, 0, 106, 0, 0, 0, 0, 0, 0, 0, 0, 25, 131
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\docs\PSR7-Usage.md", "Markdown", 0, 0, 0, 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 0, 42, 160
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\src\MessageInterface.php", "PHP", 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 159, 13, 188
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\src\RequestInterface.php", "PHP", 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 111, 9, 131
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\src\ResponseInterface.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 56, 5, 69
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\src\ServerRequestInterface.php", "PHP", 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 229, 15, 262
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\src\StreamInterface.php", "PHP", 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 122, 17, 159
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\src\UploadedFileInterface.php", "PHP", 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 105, 8, 124
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\http-message\src\UriInterface.php", "PHP", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 286, 18, 325
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\AbstractLogger.php", "PHP", 0, 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 82, 10, 129
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\InvalidArgumentException.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\LogLevel.php", "PHP", 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 19
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\LoggerAwareInterface.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 10, 3, 19
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\LoggerAwareTrait.php", "PHP", 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 13, 4, 27
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\LoggerInterface.php", "PHP", 0, 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 101, 11, 126
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\LoggerTrait.php", "PHP", 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 94, 11, 143
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\NullLogger.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 20, 3, 31
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\Test\DummyTest.php", "PHP", 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 7, 3, 19
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\Test\LoggerInterfaceTest.php", "PHP", 0, 0, 0, 0, 94, 0, 0, 0, 0, 0, 0, 0, 0, 24, 21, 139
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\Psr\Log\Test\TestLogger.php", "PHP", 0, 0, 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 0, 56, 14, 148
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\README.md", "Markdown", 0, 0, 0, 0, 0, 43, 0, 0, 0, 0, 0, 0, 0, 0, 16, 59
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\composer.json", "JSON", 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\src\AbstractLogger.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 7, 3, 16
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\src\InvalidArgumentException.php", "PHP", 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\src\LogLevel.php", "PHP", 0, 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 19
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\src\LoggerAwareInterface.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 6, 3, 15
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\src\LoggerAwareTrait.php", "PHP", 0, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 9, 4, 23
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\src\LoggerInterface.php", "PHP", 0, 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 74, 11, 99
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\src\LoggerTrait.php", "PHP", 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 50, 11, 99
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\psr\log\src\NullLogger.php", "PHP", 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 16, 3, 27
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\CHANGELOG.md", "Markdown", 0, 0, 0, 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 88, 179
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\composer.json", "JSON", 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 34
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\encryption.ini", "Ini", 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 2
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\scripts\travis-install.sh", "Shell Script", 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 1, 4, 13
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\src\ApiErrorException.php", "PHP", 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 10, 3, 22
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\src\Pusher.php", "PHP", 0, 0, 0, 0, 480, 0, 0, 0, 0, 0, 0, 0, 0, 281, 141, 902
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\src\PusherCrypto.php", "PHP", 0, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 63, 27, 190
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\src\PusherException.php", "PHP", 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 10
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\src\PusherInstance.php", "PHP", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 32
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\src\PusherInterface.php", "PHP", 0, 0, 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 208, 24, 258
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\pusher\pusher-php-server\src\Webhook.php", "PHP", 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\ralouphie\getallheaders\README.md", "Markdown", 0, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 10, 28
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\ralouphie\getallheaders\composer.json", "JSON", 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 27
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\ralouphie\getallheaders\src\getallheaders.php", "PHP", 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 5, 8, 47
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\symfony\deprecation-contracts\CHANGELOG.md", "Markdown", 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 2, 6
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\symfony\deprecation-contracts\README.md", "Markdown", 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 8, 27
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\symfony\deprecation-contracts\composer.json", "JSON", 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 36
"c:\Users\<USER>\Downloads\sb removal\script\vendor\pusher\symfony\deprecation-contracts\function.php", "PHP", 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 18, 3, 28
"Total", "-", 17169, 114, 26403, 3230, 66517, 4431, 1, 14, 26, 25, 4467, 14689, 351, 26935, 15204, 179576