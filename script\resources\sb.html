<div class="mc-main mc-admin-box mc-hide">
    <div class="mc-input">
        <span>Enter your Envato Purchase Code</span>
        <input type="text" />
    </div>
    <p>
        Please enter your Envato Purchase Code to enter the administration area. Restricted to one live domain, a testing secondary domain, or localhost.
        Save the code in Settings > Miscellaneous > Envato Purchase Code to to hide this message permanently.
        <a class="btn-text" href="https://help.market.envato.com/hc/en-us/articles/202822600-Where-Is-My-Purchase-Code-" rel="nofollow" target="_blank">Where is my purchase code?</a>
    </p>
    <a href="#" class="mc-btn">Activate</a>
</div>
<style>
    .mc-input {
        display: block;
    }

    .mc-input > span {
        display: block;
        width: auto;
        font-size: 17px;
        margin-bottom: 15px;
    }

    p {
        color: #566069;
        font-size: 15px;
        line-height: 25px;
    }

    p a {
        color: #566069;
    }
</style>
<script>
    var MC_DISABLED = true;
    (function ($) {
        let button;
        mcvpc('auto');
        $(document).ready(function () {
            button = $('.mc-btn');
            $(button).on('click', function () {
                mcvpc($.trim($('.mc-input input').val()));
            });
        });

        function mcvpc(code) {
            if (!code || $(button).hasClass('mc-loading')) {
                return;
            }
            $(button).addClass('mc-loading');
            $.ajax({
                method: 'POST',
                url: MC_AJAX_URL,
                data: { function: 'ver' + 'ification-cookie', code: code, domain: MC_URL }
            }).done((response) => {
                if (typeof response === 'string' || response instanceof String) {
                    response = JSON.parse(response);
                }
                if (response[1][0]) {
                    MCF.cookie('SA_' + 'VGCKMENS', response[1][1], 60, 'set');
                    location.reload();
                } else {
                    if (response[1][1]) {
                        alert(response[1][1]);
                    }
                    $(button).removeClass('mc-loading');
                    $('.mc-hide').removeClass('mc-hide');
                }
            });
        }
    }(jQuery));
</script>