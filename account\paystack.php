<?php

/**
 * Paystack Webhook Handler
 */

header('Content-Type: application/json');
$raw = file_get_contents('php://input');
$response = json_decode($raw, true);
$event_processed = false; // Flag to track if we handled the event

// Load functions ONLY if the request looks potentially valid
if ($response && isset($response['event'])) {
    require_once('functions.php'); // Ensure functions are loaded for signature check and processing

    // --- 1. Verify Signature ---
    $signature = $_SERVER['HTTP_X_PAYSTACK_SIGNATURE'] ?? '';
    if (!defined('PAYSTACK_SECRET_KEY') || !$signature || $signature !== hash_hmac('sha512', $raw, PAYSTACK_SECRET_KEY)) {
        mc_cloud_debug('Paystack webhook signature validation failed.');
        http_response_code(401);
        echo json_encode(['status' => 'error', 'message' => 'Invalid signature']);
        exit();
    }

    // --- 2. Process Verified Event ---
    $event_type = $response['event'];
    $data = $response['data'];
    $reference = $data['reference'] ?? null; // Common transaction reference

    mc_cloud_debug('Paystack webhook received: Event=' . $event_type . ', Reference=' . $reference);

    // --- Helper Function/Logic to Check if Processed ---
    function cloud_get_user_if_processed($reference) {
        if (!$reference) return false;
        $query = 'SELECT user_id FROM users_data WHERE slug = "payment" AND JSON_EXTRACT(value, "$[2]") = "' . db_escape($reference) . '" LIMIT 1';
        $result = db_get($query);
        if ($result && isset($result['user_id'])) {
             mc_cloud_debug('Paystack webhook: Reference ' . $reference . ' already processed for user ' . $result['user_id']);
            return $result['user_id'];
        }
        return false;
    }

    // --- 3. Handle Specific Events ---
    try {
        switch ($event_type) {
                        // === Successful One-Time Charge or Initial Subscription Payment ===
            case 'charge.success':
                if (!$reference) {
                    mc_cloud_debug('Paystack charge.success: Missing reference.');
                    break;
                }
                if (cloud_get_user_if_processed($reference)) {
                     $event_processed = true; // Already handled, acknowledge event
                     break;
                }

                $metadata = $data['metadata'] ?? [];
                $client_reference_id = $metadata['client_reference_id'] ?? null;
                $payment_type = $metadata['payment_type'] ?? null; // Get payment type from metadata

                if ($client_reference_id && strpos($client_reference_id, '|mc') !== false) {
                    $ref_parts = explode('|', rtrim($client_reference_id, '|mc'));
                    $ref_parts_count = count($ref_parts);
                    $purchase_type = mc_isset($ref_parts, 0);
                    $cloud_user_id = mc_isset($ref_parts, 1);
                    $amount_paid = ($data['amount'] ?? 0) / 100; // Amount in main currency unit (e.g., ZAR, NGN)
                    $currency = $data['currency'] ?? PAYSTACK_CURRENCY;

                    // Validate User ID and Amount
                    if (!$cloud_user_id || !is_numeric($cloud_user_id) || $amount_paid <= 0) {
                         mc_cloud_debug('Paystack charge.success webhook: Invalid user ID or amount in client_reference_id.', ['ref' => $client_reference_id, 'amount_kobo' => $data['amount'] ?? 0, 'cloud_user_id' => $cloud_user_id]);
                         $event_processed = true; // Acknowledge but don't process
                         break;
                    }
                     $cloud_user_id = intval($cloud_user_id); // Ensure integer

                    // --- Process based on purchase type ---
                    if ($purchase_type == 'white_label') {
                        mc_cloud_debug('Paystack charge.success webhook: Processing White Label for user ' . $cloud_user_id);
                         if (membership_save_white_label($cloud_user_id)) {
                              cloud_add_to_payment_history($cloud_user_id, $amount_paid, 'white-label', $reference);
                              membership_add_reseller_sale(false, 'white-label', $amount_paid);
                              $event_processed = true;
                         } else { mc_cloud_debug('Paystack charge.success webhook: membership_save_white_label failed.'); }

                    } elseif ($purchase_type == 'credits' || $payment_type == 'credits') {
                         mc_cloud_debug('Paystack charge.success webhook: Processing Credits for user ' . $cloud_user_id);

                         // --- Save Paystack Authorization Code for Credits Auto-Recharge ---
                         $authorization = $data['authorization'] ?? null;
                         if ($authorization && isset($authorization['authorization_code']) && !empty($authorization['authorization_code']) && $authorization['reusable'] == true) { // Check if reusable
                             $auth_code = $authorization['authorization_code'];
                             mc_cloud_debug('Paystack charge.success webhook: Saving reusable authorization code ' . $auth_code . ' for user ' . $cloud_user_id);
                             super_delete_user_data($cloud_user_id, 'paystack_authorization_code', true); // Remove old one if exists
                             $save_auth_result = super_insert_user_data('(' . $cloud_user_id . ', "paystack_authorization_code", "' . db_escape($auth_code) . '")');
                             if ($save_auth_result !== true) {
                                  mc_cloud_debug('Paystack charge.success webhook: Failed to save authorization code.', ['error' => $save_auth_result]);
                             }
                              // Optionally save other details like card type, last4 if needed for display
                              super_delete_user_data($cloud_user_id, 'paystack_auth_details', true);
                              super_insert_user_data('(' . $cloud_user_id . ', "paystack_auth_details", "' . db_escape(json_encode($authorization)) . '")');
                         } else {
                              mc_cloud_debug('Paystack charge.success webhook: Authorization code not found or not reusable in event data for credits purchase.');
                         }
                         // --- End Save Authorization Code ---

                         // Update credits (use the function that also handles history/reseller)
                         if (membership_set_purchased_credits($amount_paid, $currency, $cloud_user_id, $reference, 'webhook_credits')) {
                             $event_processed = true;

                             // Set a flag in the user's data to indicate this was a credits purchase
                             // This will be used by the frontend to show the correct success message
                             super_delete_user_data($cloud_user_id, 'last_payment_type', true);
                             super_insert_user_data('(' . $cloud_user_id . ', "last_payment_type", "credits")');
                         } else {
                             mc_cloud_debug('Paystack charge.success webhook: membership_set_purchased_credits failed.');
                         }

                    } else { // Assume membership subscription
                        $plan_id = $purchase_type; // The first part of ref should be the plan ID/Code
                        $period = mc_isset($ref_parts, 2);
                        $referral_code = ($ref_parts_count > 3 && isset($ref_parts[3])) ? $ref_parts[3] : false;

                        // Get customer identifier (code preferred, fallback to email)
                        $paystack_customer_code = $data['customer']['customer_code'] ?? null;
                        $customer_email = $data['customer']['email'] ?? null;
                        $customer_identifier_for_db = $paystack_customer_code ?: $customer_email;

                        if (!$period) {
                            mc_cloud_debug('Paystack charge.success webhook: Missing period in client_reference_id for subscription.', ['ref' => $client_reference_id]);
                            $event_processed = true; // Acknowledge but don't process
                            break;
                        }
                        if (!$customer_identifier_for_db) {
                            mc_cloud_debug('Paystack charge.success webhook: Missing customer identifier for subscription.', ['ref' => $client_reference_id]);
                            $event_processed = true; // Acknowledge but don't process
                            break;
                        }

                        mc_cloud_debug('Paystack charge.success webhook: Processing Membership ' . $plan_id . ' for user ' . $cloud_user_id);
                        if (membership_update($plan_id, $period, $cloud_user_id, $customer_identifier_for_db, $referral_code)) {
                          // ---> ADD THIS <---
    $invoice_description = 'Membership: ' . $plan_id; // Or fetch plan name
    $invoice_filename = cloud_invoice($cloud_user_id, $amount_paid, $invoice_description, time());
    // ---> END ADD <---
                            if ($invoice_filename) { // Check if invoice was created
        // ---> FIX THIS LINE <---
        // Remove quotes from $invoice_filename, pass plan_id as extra_2
        cloud_add_to_payment_history($cloud_user_id, $amount_paid, 'membership', $reference, $invoice_filename, $plan_id);
    } else {
        // Handle invoice creation failure (optional)
        cloud_add_to_payment_history($cloud_user_id, $amount_paid, 'membership', $reference, '', $plan_id); // Save history without invoice link
        mc_cloud_debug('Paystack charge.success webhook: Failed to create invoice for membership ' . $plan_id);
    }
    // ---> END FIX <---
                            // --- Save Subscription Code if available in charge data ---
                            $paystack_subscription_code = $data['subscription']['subscription_code'] ?? null;
                            if ($paystack_subscription_code) {
                                mc_cloud_debug('Paystack charge.success webhook: Saving subscription code ' . $paystack_subscription_code . ' for user ' . $cloud_user_id);
                                super_delete_user_data($cloud_user_id, 'paystack_subscription_code', true);
                                super_insert_user_data('(' . $cloud_user_id . ', "paystack_subscription_code", "' . db_escape($paystack_subscription_code) . '")');

                                // Also save email token if available here
                                $email_token = $data['subscription']['email_token'] ?? null;
                                if ($email_token) {
                                     mc_cloud_debug('Paystack charge.success webhook: Saving email token ' . $email_token . ' for user ' . $cloud_user_id);
                                     super_delete_user_data($cloud_user_id, 'paystack_email_token', true);
                                     super_insert_user_data('(' . $cloud_user_id . ', "paystack_email_token", "' . db_escape($email_token) . '")');
                                }

                            } else {
                                mc_cloud_debug('Paystack charge.success webhook: Subscription code not found in charge data.');
                            }
                            // --- End Save Subscription Code ---

                            $event_processed = true;
                        } else {
                            mc_cloud_debug('Paystack charge.success webhook: membership_update failed for subscription.');
                        }
                    } // End purchase type check

                } else {
                     mc_cloud_debug('Paystack charge.success webhook: client_reference_id missing or invalid format.', ['metadata' => $metadata, 'ref' => $reference]);
                     $event_processed = true; // Acknowledge event but don't process
                }
                break; // End charge.success

            // === Successful Recurring Subscription Payment ===
            case 'invoice.payment_succeeded':
                 $invoice_reference = $data['reference'] ?? null;
                 $subscription_code = $data['subscription']['subscription_code'] ?? null;
                 $paystack_customer_code = $data['customer']['customer_code'] ?? null;
                 $plan_code = $data['subscription']['plan']['plan_code'] ?? null;
                 $plan_interval = $data['subscription']['plan']['interval'] ?? null;
                 $amount_paid = ($data['amount_paid'] ?? 0) / 100;

                 mc_cloud_debug('Paystack invoice.payment_succeeded processing:', ['sub' => $subscription_code, 'customer' => $paystack_customer_code, 'plan' => $plan_code, 'ref' => $invoice_reference]);
                 if (!$invoice_reference || !$paystack_customer_code || !$plan_code || !$plan_interval || $amount_paid <= 0) { /* ... error handling ... */ break; }
                 if (cloud_get_user_if_processed($invoice_reference)) { $event_processed = true; break; }

                 $user_id_result = db_get('SELECT id FROM users WHERE customer_id = "' . db_escape($paystack_customer_code) . '" LIMIT 1');
                 $cloud_user_id = mc_isset($user_id_result, 'id');

                 if ($cloud_user_id) {
                     $period = ($plan_interval == 'monthly') ? 'month' : (($plan_interval == 'annually') ? 'year' : null);
                     if (!$period) { /* ... error handling ... */ break; }

                     mc_cloud_debug('Paystack invoice.payment_succeeded: Updating membership for user ' . $cloud_user_id . ', Plan=' . $plan_code);
                    if (membership_update($plan_code, $period, $cloud_user_id, $paystack_customer_code)) {
                        // ---> ADD THIS <---
    $invoice_description = 'Membership Renewal: ' . $plan_code; // Or fetch plan name
    $invoice_filename = cloud_invoice($cloud_user_id, $amount_paid, $invoice_description, time());
    // ---> END ADD <---

    if ($invoice_filename) { // Check if invoice was created
        // ---> FIX THIS LINE <---
        // Remove quotes from $invoice_filename, pass plan_code as extra_2
        cloud_add_to_payment_history($cloud_user_id, $amount_paid, 'membership-renewal', $invoice_reference, $invoice_filename, $plan_code);
    } else {
        // Handle invoice creation failure (optional)
        cloud_add_to_payment_history($cloud_user_id, $amount_paid, 'membership-renewal', $invoice_reference, '', $plan_code); // Save history without invoice link
        mc_cloud_debug('Paystack invoice.payment_succeeded webhook: Failed to create invoice for renewal ' . $plan_code);
    }
    // ---> END FIX <---

                        // --- ADDED: Save Subscription Code from invoice data ---
                        if ($subscription_code) {
                             mc_cloud_debug('Paystack invoice.payment_succeeded webhook: Saving subscription code ' . $subscription_code . ' for user ' . $cloud_user_id);
                             super_delete_user_data($cloud_user_id, 'paystack_subscription_code', true);
                             super_insert_user_data('(' . $cloud_user_id . ', "paystack_subscription_code", "' . db_escape($subscription_code) . '")');
                        } else { /* ... debug log ... */ }
                        // --- END ADDED ---
                        $event_processed = true;
                    } else { /* ... error handling ... */ }
                 } else { /* ... error handling ... */ }
                 break; // End invoice.payment_succeeded

           // === Subscription Created ===
           case 'subscription.create':
                $subscription_code = $data['subscription_code'] ?? null;
                $paystack_customer_code = $data['customer']['customer_code'] ?? null;
                $customer_email_from_event = $data['customer']['email'] ?? null;
                $plan_code = $data['plan']['plan_code'] ?? null;

                mc_cloud_debug('Paystack subscription.create event received. Attempting processing...', ['sub' => $subscription_code, 'customer_code' => $paystack_customer_code, 'customer_email' => $customer_email_from_event, 'plan' => $plan_code]);

                if ($subscription_code && ($paystack_customer_code || $customer_email_from_event)) {
                    mc_cloud_debug('Subscription code and customer identifier found. Looking up user...');
                    $user_id_result = null;
                    if ($paystack_customer_code) {
                         mc_cloud_debug('Attempting user lookup via customer_code: ' . $paystack_customer_code);
                         $user_id_result = db_get('SELECT id FROM users WHERE customer_id = "' . db_escape($paystack_customer_code) . '" LIMIT 1');
                    }
                    if (!$user_id_result && $customer_email_from_event) {
                         mc_cloud_debug('Attempting user lookup via email: ' . $customer_email_from_event);
                         $user_id_result = db_get('SELECT id FROM users WHERE email = "' . db_escape($customer_email_from_event) . '" LIMIT 1');
                    }
                    $cloud_user_id = mc_isset($user_id_result, 'id');

                    if ($cloud_user_id) {
                        mc_cloud_debug('User found: ID ' . $cloud_user_id . '. Proceeding to save subscription details.');
                        mc_cloud_debug('Paystack subscription.create webhook: Saving subscription code ' . $subscription_code . ' for user ' . $cloud_user_id);
                        super_delete_user_data($cloud_user_id, 'paystack_subscription_code', true);
                        $save_result = super_insert_user_data('(' . $cloud_user_id . ', "paystack_subscription_code", "' . db_escape($subscription_code) . '")');
                        if ($save_result !== true) { mc_cloud_debug('Paystack subscription.create webhook: Failed to save subscription code for user ' . $cloud_user_id, ['error' => $save_result]); }

                        // --- Save Email Token ---
                        $email_token = $data['email_token'] ?? null;
                        if ($email_token) {
                            mc_cloud_debug('Paystack subscription.create webhook: Saving email token ' . $email_token . ' for user ' . $cloud_user_id);
                            super_delete_user_data($cloud_user_id, 'paystack_email_token', true); // Remove old one if exists
                            $save_token_result = super_insert_user_data('(' . $cloud_user_id . ', "paystack_email_token", "' . db_escape($email_token) . '")');
                            if ($save_token_result !== true) { mc_cloud_debug('Paystack subscription.create webhook: Failed to save email token for user ' . $cloud_user_id, ['error' => $save_token_result]); }
                        } else {
                            mc_cloud_debug('Paystack subscription.create webhook: Email token not found in event data.');
                        }
                        // --- END Save Email Token ---

                        $event_processed = true;
                        mc_cloud_debug('Subscription details saved for user ' . $cloud_user_id);
                    } else {
                         mc_cloud_debug('Paystack subscription.create webhook: User not found for customer_code [' . $paystack_customer_code . '] or email [' . $customer_email_from_event . ']. Cannot save details.');
                         $event_processed = true; // Acknowledge event even if user not found to prevent retries
                    }
                } else {
                     mc_cloud_debug('Paystack subscription.create webhook: Missing subscription_code or customer identifier. Cannot process.');
                     $event_processed = true; // Acknowledge event
                }
                break; // End subscription.create

            // ... (other event cases) ...

            default:
                 mc_cloud_debug('Paystack webhook: Unhandled event type: ' . $event_type);
                 $event_processed = true;
                 break;
        }

        // --- 4. Send Response to Paystack ---
        if ($event_processed) {
            http_response_code(200);
            echo json_encode(['status' => 'success', 'message' => 'Webhook processed successfully.']);
        } else {
            mc_cloud_debug('Paystack webhook: Event ' . $event_type . ' processing failed internally.', ['ref' => $reference]);
            http_response_code(200);
            echo json_encode(['status' => 'acknowledged_with_error', 'message' => 'Event processing failed.']);
        }

    } catch (Exception $e) {
        mc_cloud_debug('Paystack webhook exception: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => 'Internal server error during webhook processing.']);
    }

} else {
    mc_cloud_debug('Paystack webhook received invalid data: ' . $raw);
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Invalid webhook data received']);
}
exit();
?>