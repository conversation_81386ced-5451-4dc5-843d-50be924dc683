<?php

/*
 * ==========================================================
 * INIT.PHP
 * ==========================================================
 *
 * This file loads the chat code and initilize the chat
 *
 */

header('Access-Control-Allow-Headers: *');
$_POST['init.php'] = true;

if (!file_exists('../config.php')) {
    die();
}
if (!defined('MC_PATH')) {
    define('MC_PATH', dirname(dirname(__FILE__)));
}
require('../config.php');
if (defined('MC_CROSS_DOMAIN') && MC_CROSS_DOMAIN) {
    header('Access-Control-Allow-Origin: *');
}
require('functions.php');
if (mc_is_cloud()) {
    if (defined('MC_BAN') && in_array(mc_isset($_SERVER, 'HTTP_REFERER'), MC_BAN)) {
        die('ip-banned');
    }
    $load = mc_cloud_load();
    if ($load !== true) {
        if ($load == 'config-file-missing') {
            die('<script>document.cookie="mc-login=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;";document.cookie="mc-cloud=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;";location.reload();</script>');
        }
        die('cloud-load-error');
    }
}
if (!empty($_GET['lang'])) {
    $_POST['language'] = [$_GET['lang']];
} else if (!empty($_GET['lang_optional']) && mc_get_setting('front-auto-translations') == 'auto') {
    $_POST['language'] = [$_GET['lang_optional']];
}
if (mc_get_setting('ip-ban')) {
    $ip = isset($_SERVER['HTTP_CF_CONNECTING_IP']) && substr_count($_SERVER['HTTP_CF_CONNECTING_IP'], '.') == 3 ? $_SERVER['HTTP_CF_CONNECTING_IP'] : $_SERVER['REMOTE_ADDR'];
    if (strpos(mc_get_setting('ip-ban'), $ip) !== false) {
        die('ip-banned');
    }
}
mc_init_translations();
if (mc_isset($_GET, 'mode') == 'tickets') {
    mc_component_tickets();
} else {
    mc_component_chat();
}
echo mc_is_cloud() ? '<!-- ' . MC_CLOUD_BRAND_NAME . ' - ' . CLOUD_URL . ' -->' : '<!-- Masi Chat - https://app.masichat.com -->';
die();

function mc_component_chat() {
    mc_js_global();
    mc_css();
    $header_headline = mc_get_setting('header-headline');
    $header_message = mc_get_setting('header-msg');
    $background = mc_get_setting('header-img');
    $icon = mc_get_setting('chat-icon');
    $header_type = mc_get_setting('header-type', 'agents');
    $disable_dashboard = mc_get_setting('disable-dashboard');
    $texture = mc_get_setting('chat-background');
    $css = '';
    $departments_menu = mc_get_multi_setting('departments-settings', 'departments-dashboard');
    $agents_menu = mc_get_multi_setting('agents-menu', 'agents-menu-active');
    mc_cross_site_init();
    if (mc_get_setting('rtl') || in_array(mc_get_user_language(), ['ar', 'he', 'ku', 'fa', 'ur'])) {
        $css .= ' mc-rtl';
    }
    if (mc_get_setting('chat-position') == 'left') {
        $css .= ' mc-chat-left';
    }
    if ($disable_dashboard) {
        $css .= ' mc-dashboard-disabled';
    }
    if (mc_is_cloud()) {
        $css .= ' mc-cloud';
        if (defined('MC_CLOUD_BRAND_LOGO')) {
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            if (membership_is_white_label(mc_cloud_account()['user_id']))
                $css .= ' mc-cloud-white-label';
        }
        cloud_css_js_front();
    }
    if (empty($icon)) {
        $icon = mc_get_setting('chat-mc-icons');
        if (!empty($icon)) {
            $icon = MC_URL . '/media/' . $icon;
        }
    }
    ?>
    <div class="mc-main mc-chat mc-no-conversations<?php echo $css ?>" style="display: none; transition: none;">
        <div class="mc-body">
            <div class="mc-scroll-area<?php echo $texture ? ' mc-texture-' . substr($texture, -5, 1) : '' ?>">
                <div class="mc-header mc-header-main mc-header-type-<?php echo $header_type ?>" <?php echo $background ? 'style="background-image: url(' . $background . ')"' : '' ?>>
                    <i class="mc-icon-close <?php echo $disable_dashboard ? 'mc-responsive-close-btn' : 'mc-dashboard-btn' ?>"></i>
                    <div class="mc-content">
                        <?php
                        if ($header_type == 'brand') {
                            echo '<div class="mc-brand"><img src="' . mc_get_setting('brand-img') . '" loading="lazy" alt="" /></div>';
                        }
                        ?>
                        <div class="mc-title">
                            <?php echo mc_($header_headline ? $header_headline : 'Welcome') ?>
                        </div>
                        <div class="mc-text">
                            <?php echo mc_($header_message ? $header_message : 'We are an experienced team that provides fast and accurate answers to your questions.') ?>
                        </div>
                        <?php
                        if ($header_type == 'agents') {
                            $agents = mc_db_get('SELECT first_name, profile_image FROM mc_users WHERE user_type = "agent" OR user_type = "admin" LIMIT 3', false);
                            $code = '';
                            for ($i = 0; $i < count($agents); $i++) {
                                $code .= '<div><span>' . $agents[$i]['first_name'] . '</span><img src="' . $agents[$i]['profile_image'] . '" loading="lazy" alt="" /></div>';
                            }
                            echo '<div class="mc-profiles">' . $code . '</div>';
                        }
                        ?>
                    </div>
                    <div class="mc-label-date-top"></div>
                </div>
                <div class="mc-list mc-active"></div>
                <div class="mc-dashboard">
                    <div class="mc-dashboard-conversations">
                        <div class="mc-title">
                            <?php mc_e('Conversations') ?>
                        </div>
                        <ul class="mc-user-conversations<?php echo mc_get_setting('force-one-conversation') ? ' mc-one-conversation' : '' ?>"></ul>
                        <?php
                        if (!$agents_menu && !$disable_dashboard) {
                            echo (!$departments_menu ? '<div class="mc-btn mc-btn-new-conversation">' . mc_('New conversation') . '</div>' : '') . '<div class="mc-btn mc-btn-all-conversations">' . mc_('View all') . '</div>';
                        }
                        ?>
                    </div>
                    <?php
                    if ($departments_menu) {
                        mc_departments('dashboard');
                    }
                    if ($agents_menu) {
                        mc_agents_menu();
                    }
                    if (mc_get_multi_setting('messaging-channels', 'messaging-channels-active')) {
                        mc_messaging_channels();
                    }
                    if (mc_get_setting('articles-active')) {
                        echo mc_get_rich_message('articles');
                    }
                    ?>
                </div>
                <div class="mc-panel mc-panel-articles"></div>
            </div>
            <?php
            mc_component_editor();
            if (defined('MC_CLOUD_BRAND_LOGO')) {
                echo '<a href="' . MC_CLOUD_BRAND_LOGO_LINK . '" target="_blank" class="mc-cloud-brand"><img src="' . MC_CLOUD_BRAND_LOGO . '" loading="lazy" /></a>';
            }
            ?>
        </div>
        <div class="mc-chat-btn">
            <span data-count="0"></span>
            <img class="mc-icon" alt="" src="<?php echo $icon ? $icon : MC_URL . '/media/masi-button-chat.svg' ?>" />
            <img class="mc-close" alt="" src="<?php echo MC_URL ?>/media/masi-button-close.svg" />
        </div>
        <i class="mc-icon mc-icon-close mc-responsive-close-btn"></i>
        <audio id="mc-audio" preload="auto"><source src="<?php echo MC_URL ?>/media/sound.mp3" type="audio/mpeg"></audio>
        <div class="mc-lightbox-media">
            <div></div>
            <i class="mc-icon-close"></i>
        </div>
        <div class="mc-lightbox-overlay"></div>
    </div>
<?php }

function mc_cross_site_init() {
    if (defined('MC_CROSS_DOMAIN') && defined('MC_CROSS_DOMAIN_URL') && MC_CROSS_DOMAIN) {
        $domains = [];
        $current_domain = false;
        if (is_string(MC_CROSS_DOMAIN_URL)) {
            $domains = [MC_CROSS_DOMAIN_URL];
        } else {
            $domains = MC_CROSS_DOMAIN_URL;
            $current_domain = str_replace(['https://', 'http://'], '', $_SERVER['HTTP_REFERER']);
            if (strpos($current_domain, '/')) {
                $current_domain = substr($current_domain, 0, strpos($current_domain, '/'));
            }
        }
        for ($i = 0; $i < count($domains); $i++) {
            $domain = $domains[$i];
            if (!$current_domain || strpos($domain, $current_domain) !== false) {
                echo '<style>@font-face {
                    font-family: "Masi Chat Font";
                    src: url("' . $domain . '/fonts/regular.woff2") format("woff2");
                    font-weight: 400;
                    font-style: normal;
                }
                @font-face {
                    font-family: "Masi Chat Font";
                    src: url("' . $domain . '/fonts/medium.woff2") format("woff2");
                    font-weight: 500;
                    font-style: normal;
                }
                @font-face {
                    font-family: "Masi Chat Icons";
                    src: url("' . $domain . '/icons/masi-chat.eot?v=2");
                    src: url("' . $domain . '/icons/masi-chat.eot?#iefix") format("embedded-opentype"), url("' . $domain . '/icons/masi-chat.woff?v=2") format("woff"), url("' . $domain . '/icons/masi-chat.ttf?v=2") format("truetype"), url("' . $domain . '/icons/masi-chat.svg#masi-chat?v=2") format("svg");
                    font-weight: normal;
                    font-style: normal;
                }
                </style>';
            }
        }
    }
}

function mc_agents_menu() {
    $online_agent_ids = mc_get_multi_setting('agents-menu', 'agents-menu-online-only') ? mc_get_online_user_ids(true) : false;
    $agents = mc_db_get('SELECT id, first_name, last_name, profile_image FROM mc_users WHERE ' . ($online_agent_ids !== false ? 'id IN (' . (count($online_agent_ids) ? implode(', ', $online_agent_ids) : '""') . ')' : 'user_type = "agent"'), false);
    $code = '<div class="mc-dashboard-agents"><div class="mc-title">' . mc_(mc_get_multi_setting('agents-menu', 'agents-menu-title', 'Agents')) . '</div><div class="mc-agents-list"' . (mc_get_multi_setting('agents-menu', 'agents-menu-force-one') ? ' data-force-one="true"' : '') . '>';
    $count = count($agents);
    for ($i = 0; $i < $count; $i++) {
        $code .= '<div data-id="' . $agents[$i]['id'] . '"><img src="' . $agents[$i]['profile_image'] . '" loading="lazy"><span>' . $agents[$i]['first_name'] . ' ' . $agents[$i]['last_name'] . '</span></div>';
    }
    echo $code . ($count ? '' : '<span class="mc-no-results">' . mc_('No online agents available.') . '</span>') . '</div></div>';
}

function mc_messaging_channels() {
    $channels = [['wa', 'WhatsApp'], ['fb', 'Messenger'], ['ig', 'Instagram'], ['tw', 'Twitter'], ['tg', 'Telegram'], ['vb', 'Viber'], ['ln', 'LINE'], ['wc', 'WeChat'], ['za', 'Zalo'], ['em', 'Email'], ['tk', 'Ticket'], ['tm', 'Phone']];
    $code = '<div class="mc-messaging-channels"><div class="mc-title">' . mc_(mc_get_multi_setting('messaging-channels', 'messaging-channels-title', 'Channels')) . '</div><div class="mc-channels-list">';
    for ($i = 0; $i < count($channels); $i++) {
        $channel = $channels[$i][0];
        $link = mc_get_multi_setting('messaging-channels', 'messaging-channels-' . $channel);
        if ($link) {
            $code .= '<div onclick="window.open(\'' . $link . '\')" data-channel="' . $channel . '"><img src="' . MC_URL . '\media\apps\\' . strtolower($channels[$i][1]) . '.svg" loading="lazy"><span>' . mc_($channels[$i][1]) . '</span></div>';
        }
    }
    echo $code . '</div></div>';
}

?>