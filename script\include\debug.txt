﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿<PERSON><PERSON><PERSON>[smcopen_ai_message][no-credits]

<PERSON><PERSON>[mc_open_ai_message][no-credits]

<PERSON><PERSON>[shopify_get_products][shopify-get-products-error]: {"errors":[{"message":"Invalid global id 'gid:\/\/shopify\/Collection\/Collezione A'","locations":[{"line":1,"column":9}],"path":["query","collection","id"],"extensions":{"code":"argumentLiteralsIncompatible","typeName":"CoercionError"}}]}

Ma<PERSON>t[shopify_get_products][shopify-get-products-error]: {"errors":[{"message":"Invalid global id 'gid:\/\/shopify\/Collection\/Collezione A'","locations":[{"line":1,"column":9}],"path":["query","collection","id"],"extensions":{"code":"argumentLiteralsIncompatible","typeName":"CoercionError"}}]}

<PERSON><PERSON>[shopify_curl][Cloud user ID not found]

<PERSON><PERSON>[shopify_curl][Cloud user ID not found]

<PERSON><PERSON>[shopify_curl][Cloud user ID not found]

<PERSON><PERSON><PERSON>[shopify_curl][Cloud user ID not found]

Masi Chat[shopify_curl][Cloud user ID not found]

Masi Chat[shopify_curl][Cloud user ID not found]

Masi Chat[shopify_curl][Cloud user ID not found]

Masi Chat[shopify_curl][Cloud user ID not found]

Masi Chat[shopify_get_variants][shopify-get-variants-error]: {"errors":[{"message":"Throttled","extensions":{"code":"THROTTLED","documentation":"https:\/\/shopify.dev\/api\/usage\/rate-limits"}}],"extensions":{"cost":{"requestedQueryCost":233,"actualQueryCost":null,"throttleStatus":{"maximumAvailable":2000,"currentlyAvailable":132,"restoreRate":100}}}}

Masi Chat[shopify_get_variants][shopify-get-variants-error]: {"errors":[{"message":"Throttled","extensions":{"code":"THROTTLED","documentation":"https:\/\/shopify.dev\/api\/usage\/rate-limits"}}],"extensions":{"cost":{"requestedQueryCost":233,"actualQueryCost":null,"throttleStatus":{"maximumAvailable":2000,"currentlyAvailable":155,"restoreRate":100}}}}

Masi Chat[shopify_get_variants][shopify-get-variants-error]: {"errors":[{"message":"Throttled","extensions":{"code":"THROTTLED","documentation":"https:\/\/shopify.dev\/api\/usage\/rate-limits"}}],"extensions":{"cost":{"requestedQueryCost":233,"actualQueryCost":null,"throttleStatus":{"maximumAvailable":2000,"currentlyAvailable":195,"restoreRate":100}}}}

Masi Chat[shopify_get_variants][shopify-get-variants-error]: {"errors":[{"message":"Throttled","extensions":{"code":"THROTTLED","documentation":"https:\/\/shopify.dev\/api\/usage\/rate-limits"}}],"extensions":{"cost":{"requestedQueryCost":233,"actualQueryCost":null,"throttleStatus":{"maximumAvailable":2000,"currentlyAvailable":212,"restoreRate":100}}}}

Masi Chat[shopify_get_variants][shopify-get-variants-error]: {"errors":[{"message":"Throttled","extensions":{"code":"THROTTLED","documentation":"https:\/\/shopify.dev\/api\/usage\/rate-limits"}}],"extensions":{"cost":{"requestedQueryCost":233,"actualQueryCost":null,"throttleStatus":{"maximumAvailable":2000,"currentlyAvailable":204,"restoreRate":100}}}}

Masi Chat[shopify_get_variants][shopify-get-variants-error]: {"errors":[{"message":"Throttled","extensions":{"code":"THROTTLED","documentation":"https:\/\/shopify.dev\/api\/usage\/rate-limits"}}],"extensions":{"cost":{"requestedQueryCost":233,"actualQueryCost":null,"throttleStatus":{"maximumAvailable":2000,"currentlyAvailable":145,"restoreRate":100}}}}

Masi Chat[shopify_get_variants][shopify-get-variants-error]: {"errors":[{"message":"syntax error, unexpected COLON (\":\") at [1, 40]","locations":[{"line":1,"column":40}]}]}

Masi Chat[shopify_get_variants][shopify-get-variants-error]: {"errors":[{"message":"syntax error, unexpected COLON (\":\") at [1, 42]","locations":[{"line":1,"column":42}]}]}

Masi Chat[shopify_curl][Cloud user ID not found]

Masi Chat[shopify_get_tags][shopify-get-tags-error]: {"errors":[{"message":"syntax error, unexpected invalid token (\"\\\"\") at [1, 37]","locations":[{"line":1,"column":37}]}]}

Masi Chat[shopify_get_variants][shopify-get-variants-error]: Bad Request

Masi Chat[shopify_get_variants][shopify-get-variants-error]: {"errors":[{"message":"Throttled","extensions":{"code":"THROTTLED","documentation":"https:\/\/shopify.dev\/api\/usage\/rate-limits"}}],"extensions":{"cost":{"requestedQueryCost":233,"actualQueryCost":null,"throttleStatus":{"maximumAvailable":2000,"currentlyAvailable":102,"restoreRate":100}}}}

Masi Chat[shopify_curl][Cloud user ID not found]

Masi Chat[shopify_get_products][shopify-get-products-error]: {"errors":"[API] Invalid API key or access token (unrecognized login or wrong password)"}

Pusher error:

{}

Pusher error:

{}

Pusher error:

{}

Pusher error:

{}