<?php

/*
 * ==========================================================
 * TELEGRAM POST.PHP
 * ==========================================================
 *
 * Telegram response listener. This file receive the messages sent to the Telegram bot. This file requires the Telegram App.
 * © 2017-2025 app.masichat.com. All rights reserved.
 *
 */

$raw = file_get_contents('php://input');
flush();
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
}
$response = json_decode($raw, true);
if ($response) {
    require('../../include/functions.php');
    $response_message = mc_isset($response, 'message');
    if (!$response_message) {
        $response_message = mc_isset($response, 'business_message');
    }
    if ($response_message) {
        $GLOBALS['MC_FORCE_ADMIN'] = true;
        mc_cloud_load_by_url();
        $from = $response_message['from'];
        $chat_id = $response_message['chat']['id'];
        $telegram_message_id = mc_isset($response_message, 'message_id', '');
        $message = isset($response_message['text']) ? $response_message['text'] : $response_message['caption'];
        $attachments = [];
        $get_token = mc_isset($_GET, 'tg_token');
        $token = $get_token ? $get_token : mc_get_multi_setting('telegram', 'telegram-token'); // Deprecated
        $user_id = false;
        $department = false;

        // User and conversation
        $username = isset($from['username']) ? $from['username'] : $from['id'];
        $user = mc_get_user_by('telegram-id', $username);
        if (!$user) {
            $extra = ['telegram-id' => [$username, 'Telegram ID']];
            $profile_image = mc_get('https://api.telegram.org/bot' . $token . '/getUserProfilePhotos?user_id=' . $from['id'], true);
            $business_connection_id = mc_isset($response_message, 'business_connection_id');
            if (!empty($profile_image['ok']) && count($profile_image['result']['photos'])) {
                $photos = $profile_image['result']['photos'][0];
                $profile_image = mc_telegram_download_file($photos[count($photos) - 1]['file_id'], $token);
            } else {
                $profile_image = '';
            }
            if (isset($from['language_code'])) {
                $extra['language'] = [$from['language_code'], 'Language'];
            } else {
                if (defined('MC_DIALOGFLOW')) {
                    $extra['language'] = mc_google_language_detection_get_user_extra($message);
                }
            }
            if ($business_connection_id) {
                $extra['telegram_bcid'] = [$business_connection_id, 'Telegram BCID'];
            }
            $user_id = mc_add_user(['first_name' => mc_isset($from, 'first_name', ''), 'last_name' => mc_isset($from, 'last_name', ''), 'profile_image' => mc_is_error($profile_image) || empty($profile_image) ? '' : $profile_image, 'user_type' => 'lead'], $extra);
            $user = mc_get_user($user_id);
        } else {
            $user_id = $user['id'];
            $conversation_id = mc_isset(mc_db_get('SELECT id FROM mc_conversations WHERE source = "tg" AND user_id = ' . $user_id . ' AND (extra_2 = "' . $token . '" OR extra_3 = "' . $token . '") ORDER BY id DESC LIMIT 1'), 'id'); // Deprecated. Remove extra_2. Use only extra_3 = "' . $token . '"
        }
        $GLOBALS['MC_LOGIN'] = $user;
        if (!$conversation_id) {
            $tags = false;
            $numbers = mc_get_setting('telegram-numbers');
            if (is_array($numbers)) {
                for ($i = 0; $i < count($numbers); $i++) {
                    if ($numbers[$i]['telegram-numbers-token'] == $token) {
                        $department = $numbers[$i]['telegram-numbers-department-id'];
                        $tags = $numbers[$i]['telegram-numbers-tags'];
                    }
                }
            }
            $conversation_id = mc_isset(mc_new_conversation($user_id, 2, '', $department, false, 'tg', $chat_id, false, $token, $tags), 'details', [])['id'];
        } else if ($telegram_message_id && mc_isset(mc_db_get('SELECT COUNT(*) AS `count` FROM mc_messages A, mc_conversations B WHERE A.conversation_id =  ' . $conversation_id . ' AND A.payload LIKE "%' . mc_db_escape($telegram_message_id) . '%" AND B.id = A.conversation_id AND (B.extra_2 = "' . $token . '" OR B.extra_3 = "' . $token . '")'), 'count') != 0) { // Deprecated. Use only extra_3 = "' . $token . '"
            die();
        }

        // Attachments
        $document = mc_isset($response_message, 'document');
        $photos = mc_isset($response_message, 'photo');
        $voice = mc_isset($response_message, 'voice');
        if ($document) {
            array_push($attachments, [$document['file_name'], mc_telegram_download_file($document['file_id'], $token)]);
        }
        if ($voice) {
            array_push($attachments, [mc_('Audio'), mc_telegram_download_file($voice['file_id'], $token)]);
        }
        if ($photos) {
            $url = mc_telegram_download_file($photos[count($photos) - 1]['file_id'], $token);
            array_push($attachments, [substr($url, strripos($url, '/') + 1), $url]);
        }

        // Send message
        $response = mc_send_message($user_id, $conversation_id, $message, $attachments, false, $telegram_message_id ? json_encode(['tgid' => $telegram_message_id]) : '');

        // Dialogflow, Notifications, Bot messages
        $response_external = mc_messaging_platforms_functions($conversation_id, $message, $attachments, $user, ['source' => 'tg', 'platform_value' => $chat_id]);

        // Queue
        if (mc_get_multi_setting('queue', 'queue-active')) {
            mc_queue($conversation_id, $department);
        }

        // Online status
        mc_update_users_last_activity($user_id);

        $GLOBALS['MC_FORCE_ADMIN'] = false;
    }
}
die();

?>