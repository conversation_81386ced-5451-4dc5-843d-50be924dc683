<?php

/*
 * ==========================================================
 * TICKETS APP
 * ==========================================================
 *
 * Tickets app. � 2017-2025 app.masichat.com. All rights reserved.
 *
 * 1. The tickets main block that render the whole tickets panel code.
 * 2. Generate the CSS for the ticketswith values setted in the settings area
 * 3. Send ticket confirmation email
 *
 */

define('MC_TICKETS', '1.2.5');

function mc_component_tickets() {
    mc_js_global();
    mc_css();
    mc_tickets_css();
    mc_cross_site_init();
    $css = '';
    $disable_fields = mc_get_setting('tickets-disable-features', []);
    $disable_arrows = mc_isset($disable_fields, 'tickets-arrows');
    $custom_fields = mc_get_setting('tickets-custom-fields');
    $button_name = mc_get_multi_setting('tickets-names', 'tickets-names-button');
    if ($disable_arrows) {
        $css .= ' mc-no-arrows';
    }
    if (mc_get_setting('rtl') || in_array(mc_get_user_language(), ['ar', 'he', 'ku', 'fa', 'ur'])) {
        $css .= ' mc-rtl';
    }
    ?>
    <div class="mc-main mc-tickets mc-loading mc-load<?php echo $css ?>" data-height="<?php echo mc_get_setting('tickets-height') ?>" data-offset="<?php echo mc_get_setting('tickets-height-offset') ?>">
        <div class="mc-tickets-area" style="visibility: hidden; opacity: 0;">
            <?php if (!mc_isset($disable_fields, 'tickets-left-panel')) { ?>
                <div class="mc-panel-left">
                    <div class="mc-top">
                        <div>
                            <?php if (!mc_isset($disable_fields, 'tickets-button'))
                                echo '<div class="mc-btn mc-icon mc-new-ticket"><i class="mc-icon-plus"></i>' . mc_($button_name ? $button_name : 'Create Ticket') . '</div>';
                            else
                                echo '<div class="mc-title">' . mc_($button_name ? $button_name : 'Tickets') . '</div>'; ?>
                        </div>
                        <div class="mc-search-btn">
                            <i class="mc-icon mc-icon-search"></i>
                            <input type="text" autocomplete="false" placeholder="<?php mc_e('Search for keywords or users...') ?>" />
                        </div>
                    </div>
                    <ul class="mc-user-conversations mc-scroll-area" data-profile-image="<?php echo mc_isset($disable_fields, 'tickets-profile-image') ? 'false' : 'true' ?>">
                        <p>
                            <?php mc_e('No results found.') ?>
                        </p>
                    </ul>
                </div>
            <?php } ?>
            <div class="mc-panel-main">
                <div class="mc-top<?php echo mc_isset($disable_fields, 'tickets-top-bar') ? ' mc-top-hide' : '' ?>">
                    <?php
                    if (mc_isset($disable_fields, 'tickets-right-panel') && !mc_isset($disable_fields, 'tickets-agent')) {
                        echo '<div class="mc-profile mc-profile-agent mc-profile-empty"><img src="" /><div><span class="mc-name"></span><span class="mc-status"></span></div></div>';
                    }
                    ?>
                    <div class="mc-title"></div>
                    <a class="mc-close mc-btn-icon mc-btn-red">
                        <i class="mc-icon-close"></i>
                    </a>
                    <div class="mc-label-date-top"></div>
                </div>
                <div class="mc-conversation">
                    <div class="mc-list"></div>
                    <?php mc_component_editor(); ?>
                    <div class="mc-no-conversation-message">
                        <div>
                            <label>
                                <?php mc_e('Select a ticket or create a new one') ?>
                            </label>
                            <p>
                                <?php mc_e('Select a ticket from the left area or create a new one.') ?>
                            </p>
                        </div>
                    </div>
                    <audio id="mc-audio" preload="auto">
                        <source src="<?php echo MC_URL ?>/media/sound.mp3" type="audio/mpeg">
                    </audio>
                </div>
                <div class="mc-panel mc-scroll-area"></div>
            </div>
            <?php if (!mc_isset($disable_fields, 'tickets-right-panel')) { ?>
                <div class="mc-panel-right">
                    <div class="mc-top">
                        <?php if (mc_get_setting('tickets-registration-required')) { ?>
                            <div class="mc-profile-menu">
                                <div class="mc-profile<?php echo !mc_get_setting('registration-profile-img') || mc_get_setting('tickets-registration-required') ? ' mc-no-profile-image' : '' ?>">
                                    <img src="" />
                                    <span class="mc-name"></span>
                                </div>
                                <div>
                                    <ul class="mc-menu">
                                        <?php
                                        if (!mc_isset($disable_fields, 'tickets-edit-profile')) {
                                            echo '<li data-value="edit-profile">' . mc_('Edit profile') . '</li>';
                                        }
                                        if (!mc_get_setting('tickets-registration-disable-password')) {
                                            echo '<li data-value="logout">' . mc_('Logout') . '</li>';
                                        }
                                        ?>
                                    </ul>
                                </div>
                            </div>
                            <?php
                        } else {
                            echo '<div class="mc-title">' . mc_('Details') . '</div>';
                        }
                        ?>
                    </div>
                    <div class="mc-scroll-area">
                        <?php
                        $code = '';
                        if (!mc_isset($disable_fields, 'tickets-agent')) {
                            echo '<div class="mc-profile mc-profile-agent mc-profile-empty"><img src="" /><div><span class="mc-name"></span><span class="mc-status"></span></div></div>' . (mc_isset($disable_fields, 'tickets-agent-details') ? '' : '<div class="mc-agent-label"></div>');
                        }
                        $code .= '<div class="mc-ticket-details"></div>';
                        if (!mc_isset($disable_fields, 'tickets-department')) {
                            $code .= '<div class="mc-department" data-label="' . mc_(mc_isset(mc_get_setting('departments-settings'), 'departments-single-label', 'Department')) . '"></div>';
                        }
                        $code .= '<div class="mc-conversation-attachments"></div>';
                        if (mc_get_setting('tickets-articles')) {
                            $code .= mc_get_rich_message('articles');
                        }
                        echo $code;

                        ?>
                    </div>
                    <div class="mc-no-conversation-message"></div>
                </div>
                <?php
            }
            if (!mc_isset($disable_fields, 'tickets-left-panel') && !$disable_arrows) {
                echo '<i class="mc-btn-collapse mc-left mc-icon-arrow-left"></i>';
            }
            if (!mc_isset($disable_fields, 'tickets-right-panel') && !$disable_arrows) {
                echo '<i class="mc-btn-collapse mc-right mc-icon-arrow-right"></i>';
            }
            ?>
        </div>
        <div class="mc-lightbox mc-lightbox-media">
            <div></div>
            <i class="mc-icon-close"></i>
        </div>
        <div class="mc-lightbox-overlay"></div>
        <div class="mc-ticket-fields">
            <?php
            $code = '';
            if (mc_get_multi_setting('tickets-fields', 'tickets-field-departments')) {
                $departments = mc_get_departments();
                $code .= '<div id="department" class="mc-input mc-input-select"><span>' . mc_(mc_isset(mc_get_setting('departments-settings'), 'departments-label', 'Department')) . '</span><div class="mc-select"><p data-value="" data-required="true">' . mc_('Select a value') . '</p><ul>';
                foreach ($departments as $key => $value) {
                    $code .= '<li data-value="' . $key . '">' . mc_($value['name']) . '</li>';
                }
                $code .= '</ul></div></div>';
            }
            if (mc_get_multi_setting('tickets-fields', 'tickets-field-priority')) {
                $code .= '<div id="priority" class="mc-input mc-input-select"><span>' . mc_('Priority') . '</span><div class="mc-select"><p data-value="" data-required="true">' . mc_('Select a value') . '</p><ul><li data-value="' . mc_('General issue') . '">' . mc_('General issue') . '</li><li data-value="' . mc_('Medium') . '">' . mc_('Medium') . '</li><li data-value="' . mc_('Critical') . '">' . mc_('Critical') . '</li></ul></div></div>';
            }
            if (mc_get_multi_setting('wc-tickets-products', 'wc-tickets-products-active')) {
                $products = mc_woocommerce_get_products([], false, mc_get_user_language());
                $code .= '<div id="products" class="mc-input mc-input-select"><span>' . mc_(mc_get_multi_setting('wc-tickets-products', 'wc-tickets-products-label', 'Related product')) . '</span><div class="mc-select"><p data-value="" data-required="true">' . mc_('Select a product') . '</p><ul class="mc-scroll-area">';
                $exclude = explode(',', mc_get_multi_setting('wc-tickets-products', 'wc-tickets-products-exclude'));
                for ($i = 0; $i < count($products); $i++) {
                    if (!in_array($products[$i]['id'], $exclude)) {
                        $name = $products[$i]['name'];
                        $code .= '<li data-value="' . $name . '">' . $name . '</li>';
                    }
                }
                $code .= '</ul></div></div>';
            }
            if ($custom_fields && is_array($custom_fields)) {
                for ($i = 0; $i < count($custom_fields); $i++) {
                    $value = $custom_fields[$i];
                    if ($value['tickets-extra-field-name']) {
                        $code .= '<div id="' . mc_string_slug($value['tickets-extra-field-name']) . '" class="mc-input mc-input-text"><span>' . mc_($value['tickets-extra-field-name']) . '</span><input type="text"' . (mc_isset($value, 'tickets-extra-field-required') ? ' required' : '') . '></div>';
                    }
                }
            }
            echo $code;
            ?>
        </div>
    </div>
<?php }

function mc_tickets_css() {
    $css = '';
    $color_1 = mc_get_setting('color-1');
    if ($color_1 != '') {
        $css .= '.mc-tickets .mc-panel-right .mc-input.mc-input-btn>div:hover, .mc-tickets .mc-panel-right .mc-input.mc-input-btn input:focus+div,.mc-tickets .mc-top .mc-btn:hover, .mc-tickets .mc-create-ticket:hover, .mc-tickets .mc-panel-right .mc-btn:hover { background-color: ' . $color_1 . '; border-color: ' . $color_1 . '; }';
        $css .= '.mc-tickets .mc-ticket-details>div .mc-icon,.mc-btn-collapse:hover,.mc-profile-menu:hover .mc-name,.mc-tickets .mc-conversation-attachments a i { color: ' . $color_1 . '; }';
        $css .= '.mc-user-conversations>li.mc-active{ border-left-color: ' . $color_1 . '; }';
        $css .= '.mc-search-btn>input:focus,[data-panel="new-ticket"] .mc-editor.mc-focus { border-color: ' . $color_1 . '; }';
        $css .= '.mc-btn-icon:hover { border-color: ' . $color_1 . '; color: ' . $color_1 . '; }';
    }
    if ($css != '') {
        echo '<style>' . $css . '</style>';
    }
}

function mc_tickets_email($user, $message = false, $attachments = false, $conversation_id = false) {
    if (empty($message) && empty($attachments)) {
        return false;
    }
    $user_email = mc_isset($user, 'email');
    $email = mc_get_multilingual_setting('emails', 'tickets-email', mc_get_user_language($user['id']));
    if ($user_email && !empty($email['tickets-email-subject'])) {
        $body = str_replace(['{user_name}', '{message}', '{attachments}', '{conversation_id}'], [mc_get_user_name($user), $message, mc_email_attachments_code($attachments), $conversation_id], $email['tickets-email-content']);
        if ($conversation_id && $user['token']) {
            $body = str_replace('{conversation_url_parameter}', 'https://app.masichat.com/?conversation=' . $conversation_id . '&token=' . $user['token'], $body);
        }
        return mc_email_send($user_email, str_replace('{conversation_id}', $conversation_id, mc_merge_fields($email['tickets-email-subject'])), $body, mc_email_piping_suffix($conversation_id));
    }
    return false;
}

function mc_tickets_recaptcha($token) {
    return mc_isset(mc_curl('https://www.google.com/recaptcha/api/siteverify', ['response' => $token, 'secret' => mc_get_multi_setting('tickets-recaptcha', 'tickets-recaptcha-secret')]), 'success');
}

?>