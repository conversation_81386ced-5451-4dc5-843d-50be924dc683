<?php

/*
 * ==========================================================
 * COMPONENTS.PHP
 * ==========================================================
 *
 * Library of static html components for the Artificial Intelligence area. This file must not be executed directly. © 2017-2025 app.masichat.com. All rights reserved.
 *
 */

function mc_dialogflow_chatbot_area() { ?>
    <div class="mc-area-chatbot">
        <div class="mc-top-bar">
            <div>
                <h2>
                    <?php mc_e('Chatbot') ?>
                </h2>
                <div class="mc-menu-wide mc-menu-chatbot">
                    <div>
                        <?php mc_e('Chatbot') ?>
                    </div>
                    <ul>
                        <li data-type="training" class="mc-active">
                            <?php mc_e('Training') ?>
                        </li>
                        <li data-type="flows">
                            <?php mc_e('Flows') ?>
                        </li>
                        <li data-type="playground">
                            <?php mc_e('Playground') ?>
                        </li>
                        <li data-type="settings">
                            <?php mc_e('Settings') ?>
                        </li>
                        <?php mc_docs_link('#open-ai') ?>
                    </ul>
                </div>
            </div>
            <div>
                <a id="mc-train-chatbot" class="mc-btn mc-icon">
                    <i class="mc-icon-automation"></i>
                    <?php mc_e('Train chatbot') ?>
                </a>
            </div>
        </div>
        <div data-id="training" class="mc-tab mc-inner-tab mc-active">
            <div class="mc-nav">
                <ul>
                    <li data-value="files" class="mc-active">
                        <?php mc_e('Files') ?>
                    </li>
                    <li data-value="website">
                        <?php mc_e('Website') ?>
                    </li>
                    <li data-value="qea">
                        <?php mc_e('Q&A') ?>
                    </li>
                    <?php
                    if (mc_get_multi_setting('open-ai', 'open-ai-user-train-conversations')) {
                        echo '<li data-value="conversations">' . mc_('Conversations') . '</li>';
                    }
                    ?>
                    <li data-value="info">
                        <?php mc_e('Information') ?>
                    </li>
                </ul>
            </div>
            <div class="mc-content mc-scroll-area">
                <div class="mc-active">
                    <table id="mc-table-chatbot-files" class="mc-table mc-loading">
                        <tbody></tbody>
                    </table>
                    <div class="mc-flex">
                        <div id="mc-chatbot-add-files" class="mc-btn mc-icon mc-btn-white">
                            <i class="mc-icon-plus"></i>
                            <?php mc_e('Add new files') ?>
                        </div>
                        <div id="mc-chatbot-delete-files" class="mc-btn-icon mc-btn-red">
                            <i class="mc-icon-delete"></i>
                        </div>
                    </div>
                </div>
                <div>
                    <table id="mc-table-chatbot-website" class="mc-table mc-loading">
                        <tbody></tbody>
                    </table>
                    <hr />
                    <div id="mc-repeater-chatbot-website" data-type="repeater" class="mc-setting mc-type-repeater">
                        <div class="input">
                            <div class="mc-repeater">
                                <div class="repeater-item">
                                    <div>
                                        <label>
                                            <?php mc_e('URL') ?>
                                        </label>
                                        <input data-id="open-ai-sources-url" type="url" />
                                    </div>
                                    <div>
                                        <label>
                                            <?php mc_e('Extract URLs') ?>
                                        </label>
                                        <input data-id="open-ai-sources-extract-url" type="checkbox" />
                                    </div>
                                    <i class="mc-icon-close"></i>
                                </div>
                            </div>
                            <div class="mc-btn mc-repeater-add mc-btn-white mc-icon">
                                <i class="mc-icon-plus"></i>
                                <?php mc_e('Add new item') ?>
                            </div>
                            <div id="mc-chatbot-delete-website" class="mc-btn-icon mc-btn-red">
                                <i class="mc-icon-delete"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <div id="mc-chatbot-qea" data-type="repeater" class="mc-setting mc-type-repeater">
                        <div class="input">
                            <div class="mc-repeater">
                                <div class="repeater-item">
                                    <div>
                                        <label>
                                            <?php mc_e('Question') ?>
                                        </label>
                                        <div>
                                            <div data-id="open-ai-faq-questions" class="mc-repeater">
                                                <div class="repeater-item">
                                                    <input data-id="question" placeholder="<?php mc_e('Add question') ?>" type="text" />
                                                    <i class="mc-icon-close"></i>
                                                </div>
                                            </div>
                                            <div class="mc-btn mc-btn-white mc-repeater-add mc-icon">
                                                <i class="mc-icon-plus"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mc-qea-repeater-answer">
                                        <label>
                                            <?php mc_e('Answer') ?>
                                            <i class="mc-btn-open-ai mc-icon-openai mc-btn-icon" data-mc-tooltip="<?php mc_e('Rewrite') ?>" data-mc-tooltip-init="true"></i>
                                        </label>
                                        <textarea data-id="open-ai-faq-answer"></textarea>
                                    </div>
                                    <div>
                                        <label>
                                            <?php mc_e('Function calling') ?>
                                        </label>
                                        <div class="mc-enlarger mc-enlarger-function-calling">
                                            <div>
                                                <label>
                                                    <?php mc_e('URL') ?>
                                                </label>
                                                <input data-id="open-ai-faq-function-calling-url" type="text" />
                                            </div>
                                            <div>
                                                <label>
                                                    <?php mc_e('Method') ?>
                                                </label>
                                                <select data-id="open-ai-faq-function-calling-method">
                                                    <option>POST</option>
                                                    <option value="GET">GET</option>
                                                    <option value="PUT">PUT</option>
                                                    <option value="PATH">PATH</option>
                                                    <option value="DELETE">DELETE</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label>
                                                    <?php mc_e('Headers') ?>
                                                </label>
                                                <input data-id="open-ai-faq-function-calling-headers" type="text" />
                                            </div>
                                            <div>
                                                <label>
                                                    <?php mc_e('Properties') ?>
                                                </label>
                                                <div>
                                                    <div data-id="open-ai-faq-function-calling-properties" class="mc-repeater">
                                                        <div class="repeater-item">
                                                            <div>
                                                                <input data-id="name" type="text" placeholder="<?php mc_e('Name') ?>" />
                                                            </div>
                                                            <div>
                                                                <input data-id="description" type="text" placeholder="<?php mc_e('Description') ?>" />
                                                            </div>
                                                            <div>
                                                                <input data-id="allowed" type="text" placeholder="<?php mc_e('Allowed values separated by commas') ?>" />
                                                            </div>
                                                            <i class="mc-icon-close"></i>
                                                        </div>
                                                    </div>
                                                    <div class="mc-btn mc-btn-white mc-repeater-add mc-icon">
                                                        <i class="mc-icon-plus"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <label>
                                            <?php mc_e('Set data and actions') ?>
                                        </label>
                                        <div class="mc-enlarger">
                                            <div class="mc-repeater" data-id="open-ai-faq-set-data">
                                                <div class="repeater-item">
                                                    <div>
                                                        <div class="mc-setting">
                                                            <select data-id="id">
                                                                <?php
                                                                $code = '';
                                                                $fields = array_merge([['name' => 'Name', 'id' => 'full_name'], ['name' => 'Email', 'id' => 'email'], ['name' => 'Password', 'id' => 'password']], mc_users_get_fields(), [['name' => 'Assign tags', 'id' => 'tags'], ['name' => 'Assign a department', 'id' => 'department'], ['name' => 'Assign an agent', 'id' => 'agent'], ['name' => 'Go to URL', 'id' => 'redirect'], ['name' => 'Show an article', 'id' => 'open_article'], ['name' => 'Download transcript', 'id' => 'transcript'], ['name' => 'Email transcript', 'id' => 'transcript_email'], ['name' => 'Send email to user', 'id' => 'send_email'], ['name' => 'Send email to agents', 'id' => 'send_email_agents'], ['name' => 'Archive the conversation', 'id' => 'archive_conversation'], ['name' => 'Human takeover', 'id' => 'human_takeover']]);
                                                                for ($i = 0; $i < count($fields); $i++) {
                                                                    $code .= '<option value="' . $fields[$i]['id'] . '">' . $fields[$i]['name'] . '</option>';
                                                                }
                                                                echo $code;
                                                                ?>
                                                            </select>
                                                        </div>
                                                        <div class="mc-setting">
                                                            <input data-id="value" type="text" placeholder="<?php mc_e('Enter the value') ?>" />
                                                        </div>
                                                    </div>
                                                    <i class="mc-icon-close"></i>
                                                </div>
                                            </div>
                                            <div class="mc-btn mc-btn-white mc-repeater-add mc-icon">
                                                <i class="mc-icon-plus"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <i class="mc-icon-close"></i>
                                </div>
                            </div>
                            <div class="mc-btn mc-btn-white mc-repeater-add mc-icon">
                                <i class="mc-icon-plus"></i>
                                <?php mc_e('Add new item') ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php if (mc_get_multi_setting('open-ai', 'open-ai-user-train-conversations')) { ?>
                    <div>
                        <div id="mc-chatbot-conversations">
                            <div data-type="repeater" class="mc-setting mc-type-repeater">
                                <div class="input">
                                    <div class="mc-repeater"></div>
                                </div>
                            </div>
                            <hr />
                            <div id="mc-chatbot-delete-all-training-conversations" class="mc-btn mc-btn-white">Delete all training data</div>
                        </div>
                    </div>
                <?php } ?>
                <div id="mc-chatbot-info" class="mc-active"></div>
            </div>
        </div>
        <div data-id="flows" class="mc-tab mc-inner-tab mc-loading">
            <div class="mc-nav mc-nav-only mc-scroll-area">
                <ul id="mc-flows-nav"></ul>
                <div id="mc-flow-add" class="mc-btn mc-icon mc-btn-white">
                    <i class="mc-icon-plus"></i>
                    <?php mc_e('Add new flow') ?>
                </div>
            </div>
            <div class="mc-content mc-scroll-area"></div>
            <i class="mc-flow-scroll mc-btn mc-btn-white mc-icon-arrow-left"></i>
            <i class="mc-flow-scroll mc-btn mc-btn-white mc-icon-arrow-right"></i>
        </div>
        <div data-id="playground">
            <div class="mc-flex">
                <div class="mc-playground">
                    <div class="mc-scroll-area mc-list"></div>
                    <div class="mc-no-results">
                        <?php mc_e('Send a message') ?>
                    </div>
                    <div class="mc-playground-editor">
                        <div class="mc-setting">
                            <textarea placeholder="<?php mc_e('Write a message...') ?>"></textarea>
                        </div>
                        <div class="mc-flex">
                            <div class="mc-flex">
                                <div data-value="user" class="mc-btn mc-btn-white mc-icon">
                                    <i class="mc-icon-reload"></i>
                                    <?php mc_e('User') ?>
                                </div>
                                <i data-value="clear" class="mc-icon-close mc-btn-icon mc-btn-red"></i>
                            </div>
                            <div class="mc-flex">
                                <div data-value="add" class="mc-btn mc-btn-white">
                                    <?php mc_e('Add') ?>
                                </div>
                                <div data-value="send" class="mc-btn mc-btn-white mc-icon" data-mc-tooltip="<?php mc_e('Send message') ?>">
                                    <i class="mc-icon-send"></i>
                                    <?php mc_e('Send') ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mc-playground-info"></div>
            </div>
        </div>
    </div>
<?php } ?>
<?php
function mc_dialogflow_intent_box() {
    $is_dialogflow = mc_chatbot_active(true, false);
    ?>
    <div class="mc-lightbox mc-dialogflow-intent-box<?php echo $is_dialogflow ? '' : ' mc-dialogflow-disabled' ?>">
        <div class="mc-info"></div>
        <div class="mc-top-bar">
            <div>
                <?php mc_e('Chatbot Training') ?><a href="<?php echo mc_is_cloud() ? mc_defined('MC_CLOUD_DOCS', '') : 'https://app.masichat.com/docs' ?>#chatbot-training-window" target="_blank">
                    <i class="mc-icon-help"></i>
                </a>
            </div>
            <div>
                <a class="mc-send mc-btn mc-icon">
                    <i class="mc-icon-check"></i>
                    <?php mc_e('Train chatbot') ?>
                </a>
                <a class="mc-close mc-btn-icon mc-btn-red">
                    <i class="mc-icon-close"></i>
                </a>
            </div>
        </div>
        <div class="mc-main mc-scroll-area">
            <div class="mc-title mc-intent-add">
                <?php echo mc_('Question') . '<i data-value="add" data-mc-tooltip="' . mc_('Add question') . '" class="mc-btn-icon mc-icon-plus"></i><i data-value="previous" class="mc-btn-icon mc-icon-arrow-up"></i><i data-value="next" class="mc-btn-icon mc-icon-arrow-down"></i>' ?>
            </div>
            <div class="mc-setting mc-type-text mc-first">
                <input type="text" />
            </div>
            <div class="mc-title mc-bot-response">
                <?php
                mc_e('Answer');
                if (mc_get_multi_setting('open-ai', 'open-ai-rewrite')) {
                    echo '<i class="mc-btn-open-ai mc-btn-icon mc-icon-openai" data-mc-tooltip="' . mc_('Rewrite') . '"></i>';
                }
                ?>
            </div>
            <div class="mc-setting mc-type-textarea mc-bot-response">
                <textarea></textarea>
            </div>
            <div class="mc-title">
                <?php mc_e('Language') ?>
            </div>
            <?php
            echo mc_dialogflow_languages_list();
            if ($is_dialogflow) {
                echo '<div class="mc-title mc-title-search">' . mc_('Intent') . '<div class="mc-search-btn"><i class="mc-icon mc-icon-search"></i><input type="text" autocomplete="false" placeholder="' . mc_('Search for Intents...') . '" /></div><i id="mc-intent-preview" data-mc-tooltip="' . mc_('Preview') . '" class="mc-icon-help"></i></div><div class="mc-setting mc-type-select"><select id="mc-intents-select"></select></div>';
                if (mc_chatbot_active(false, true)) {
                    echo '<div class="mc-title">' . mc_('Services to update') . '</div><div class="mc-setting mc-type-select"><select id="mc-train-chatbots"><option value="">' . mc_('All') . '</option><option value="open-ai">OpenAI</option><option value="dialogflow">Dialogflow</option></select></div>';
                }
            } else {
                echo '<div class="mc-title mc-title-search">' . mc_('Q&A') . '<div class="mc-search-btn"><i class="mc-icon mc-icon-search"></i><input type="text" autocomplete="false" placeholder="' . mc_('Search for Q&A...') . '" /></div><i id="mc-qea-preview" data-mc-tooltip="' . mc_('Preview') . '" class="mc-icon-help"></i></div><div class="mc-setting mc-type-select"><select id="mc-qea-select"></select></div>';
            }
            ?>
        </div>
    </div>
<?php } ?>

