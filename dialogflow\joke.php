12c12
< define('SB_DIALOGFLOW', '1.5.0');
---
> define('SB_DIALOGFLOW', '1.5.3');
190c190
<     if ($human_takeover && sb_dialogflow_is_human_takeover($conversation_id) && sb_isset($human_takeover, 'disable_chatbot')) {
---
>     if ($human_takeover && $conversation_id && sb_dialogflow_is_human_takeover($conversation_id) && sb_isset($human_takeover, 'disable_chatbot')) {
861,864c861,867
<  * 14. Support Board database embedding
<  * 15. Check if manual or automatic sync mode
<  * 16. Data scraping
<  * 17. Sitemap generation
---
>  * 14. JSON to text
>  * 15. CSV to text
>  * 16. Support Board database embedding
>  * 17. Check if manual or automatic sync mode
>  * 18. Data scraping
>  * 19. Sitemap generation
>  * 20. Check if a string terminates with a dot or similar character
1119,1135c1122,1124
<         try {
<             $parser = new \Smalot\PdfParser\Parser();
<             $pdf = $parser->parseFile($path);
<             $text = $pdf->getText();
< 
<             // Check if the PDF has actual content
<             if (empty(trim($text)) || strlen(trim($text)) < 10) {
<                 // This is likely an image-only PDF or has no readable text
<                 return 'empty-pdf-data';
<             }
< 
<             return $text;
<         } catch (\Exception $e) {
<             // Log the error for debugging
<             error_log('PDF parsing error: ' . $e->getMessage() . ' - File: ' . $path);
<             return 'empty-pdf-data';
<         }
---
>         $parser = new \Smalot\PdfParser\Parser();
>         $pdf = $parser->parseFile($path);
>         return $pdf->getText();
1139a1129,1155
> function sb_json_to_text($path_or_data, $indent = 0) {
>     $text = '';
>     $prefix = str_repeat('  ', $indent);
>     if ($indent === 0) {
>         $path_or_data = json_decode(file_get_contents($path_or_data), true);
>     }
>     foreach ($path_or_data as $key => $value) {
>         $text .= is_array($value) ? $prefix . ucfirst($key) . ':' . PHP_EOL . sb_json_to_text($value, $indent + 1) : $prefix . ucfirst($key) . ': ' . $value . PHP_EOL;
>     }
>     return $text;
> }
> 
> function sb_csv_to_text($url) {
>     $data = sb_csv_read($url);
>     $text = '';
>     if ($data) {
>         $keys = array_keys($data[0]);
>         foreach ($data as $row) {
>             foreach ($keys as $key) {
>                 $text .= ucfirst($key) . ': ' . (isset($row[$key]) ? $row[$key] : '') . PHP_EOL;
>             }
>             $text .= PHP_EOL;
>         }
>     }
>     return $text;
> }
> 
1144c1160
<         if (!strpos($url->loc, 'mailto:')) {
---
>         if (!strpos($url->loc, 'mailto:') && !strpos($url->loc, 'javascript:')) {
1164a1181,1184
> function sb_is_string_ends($string) {
>     return in_array(substr(trim($string), -1), ['.', ',', ':', '!', '?', ';', '።', '।', '。', '။']);
> }
> 
1330d1349
< 
1336c1355
<             $model = sb_isset($post_fields, 'model', 'gpt-4o-mini');
---
>             $model = sb_isset($post_fields, 'model', 'gpt-4.1-mini');
1338c1357
<                 $tokens = sb_open_ai_get_max_tokens($model);
---
>                 return sb_error('no-usage', 'sb_open_ai_curl');
1344c1363
<     return sb_error('no-credits', 'sb_open_ai_message');
---
>     return sb_error('no-credits', 'sb_open_ai_curl');
1351c1370
< 
---
>     global $SB_OPEN_AI_RECURSION_CHECK_3;
1354,1356c1373,1379
<     if ($audio) {
<         $message = sb_open_ai_audio_to_text($audio, $language, sb_isset($extra, 'user_id'), false, $conversation_id);
<     }
---
>     $is_google_search = $extra == 'embeddings-search';
>     $is_scraping = $extra == 'scraping';
>     $is_embeddings = sb_isset($extra, 'embeddings') || $is_google_search;
>     $is_rewrite = $extra == 'rewrite';
>     $is_smart_reply = sb_isset($extra, 'smart_reply');
>     $message = $audio ? sb_open_ai_audio_to_text($audio, $language, sb_isset($extra, 'user_id'), false, $conversation_id) : (is_string($message) ? trim($message) : $message);
>     $message_ = '';
1358,1360c1381,1388
<     for ($i = 0; $i < count($attachments); $i++) {
<         if (strpos($attachments[$i][0], 'voice_message') === false) {
<             $message .= ' ' . $attachments[$i][1];
---
>     if (!$is_embeddings) {
>         for ($i = 0; $i < count($attachments); $i++) {
>             if (strpos($attachments[$i][0], 'voice_message') === false) {
>                 $message_ .= $attachments[$i][1] . ', ';
>             }
>         }
>         if ($message_) {
>             $message = trim(str_replace('..', '.', $message . ($message ? '.' : '') . ' ' . substr($message_, 0, -2)));
1381,1385d1408
<     $is_google_search = $extra == 'embeddings-search';
<     $is_scraping = $extra == 'scraping';
<     $is_embeddings = sb_isset($extra, 'embeddings') || $is_google_search;
<     $is_rewrite = $extra == 'rewrite';
<     $is_smart_reply = sb_isset($extra, 'smart_reply');
1398c1421
<     $model = $model ? $model : sb_isset($settings, 'open-ai-custom-model', sb_isset($settings, 'open-ai-model', 'gpt-4o-mini'));
---
>     $model = $model ? $model : sb_isset($settings, 'open-ai-custom-model', sb_isset($settings, 'open-ai-model', 'gpt-4.1-mini'));
1399a1423
>     $url_part = $chat_model ? 'chat/completions' : 'completions';
1405c1429
<         $is_human_takeover = !$is_rewrite && !$is_scraping && !$is_smart_reply && $conversation_id && $SB_OPEN_AI_PLAYGROUND === null && (sb_is_agent(false, true) || sb_dialogflow_is_human_takeover($conversation_id));
---
>         $is_human_takeover = !$is_rewrite && !$is_scraping && !$is_smart_reply && $conversation_id && $SB_OPEN_AI_PLAYGROUND === null && sb_dialogflow_is_human_takeover($conversation_id);
1407a1432
>             $message_fallback = $human_takeover_active ? $human_takeover_settings['message_fallback'] : false;
1409,1424c1434,1442
<                 if (sb_is_agent($messages[$i]['user_type'], true)) {
<                     if (sb_is_user_online($messages[$i]['user_id'])) {
<                         $message_fallback = $human_takeover_active ? $human_takeover_settings['message_fallback'] : false;
<                         if ($message_fallback) {
<                             for ($j = $count - 1; $j > -1; $j--) {
<                                 if (strpos($messages[$j]['payload'], 'human-takeover-message-fallback')) {
<                                     if (strtotime($messages[$j]['creation_time']) > $time) {
<                                         $message_fallback = false;
<                                     }
<                                     break;
<                                 }
<                             }
<                             if ($message_fallback) {
<                                 sb_send_message(sb_get_bot_id(), $conversation_id, $message_fallback, $attachments_response, false, ['human-takeover-message-fallback' => true]);
<                                 sb_messaging_platforms_send_message($message_fallback, $conversation_id, false, $attachments_response);
<                             }
---
>                 if (sb_is_agent($messages[$i]['user_type'], true) && sb_is_user_online($messages[$i]['user_id'])) {
>                     return [true, false];
>                 }
>             }
>             if ($message_fallback) {
>                 for ($i = $count - 1; $i > -1; $i--) {
>                     if (strpos($messages[$i]['payload'], 'human-takeover-message-fallback')) {
>                         if (strtotime($messages[$i]['creation_time']) > $time) {
>                             $message_fallback = false;
1426c1444
<                         return [true, false];
---
>                         break;
1428a1447,1450
>                 if ($message_fallback) {
>                     sb_send_message(sb_get_bot_id(), $conversation_id, $message_fallback, $attachments_response, false, ['human-takeover-message-fallback' => true]);
>                     return [true, $message_fallback, $token, true];
>                 }
1436,1438c1458,1460
<         if ($extra == 'messaging-app' && $human_takeover_active && !$is_smart_reply) {
<             $button_confirm = sb_rich_value($human_takeover_settings['confirm'], false) == $message;
<             if ($button_confirm || sb_rich_value($human_takeover_settings['cancel'], false) == $message) {
---
>         if (sb_isset($extra, 'messaging-app') && $human_takeover_active && !$is_smart_reply) {
>             $is_button_confirm = sb_rich_value($human_takeover_settings['confirm'], false) == $message;
>             if ($is_button_confirm) {
1441c1463
<                     return [true, $button_confirm ? $is_human_takeover : false, false, $button_confirm];
---
>                     return [true, sb_dialogflow_human_takeover($conversation_id), false, $is_button_confirm];
1442a1465,1466
>             } else if (sb_rich_value($human_takeover_settings['cancel'], false) == $message) {
>                 return [true, false, false, false];
1534c1558,1560
<                 $messages[$i]['message'] .= ' ' . $payload_temp['action'];
---
>                 if (!empty($payload_temp['action'])) {
>                     $messages[$i]['message'] .= ' ' . $payload_temp['action'];
>                 }
1541c1567
<         $extra_embeddings = ['conversation_id' => $conversation_id, 'user_id' => $user_id];
---
>         $extra_embeddings = ['conversation_id' => $conversation_id, 'user_id' => $user_id, 'attachments' => $attachments];
1585c1611
<                     $response_json = sb_open_ai_curl($chat_model ? 'chat/completions' : 'completions', $query_);
---
>                     $response_json = sb_open_ai_curl($url_part, $query_);
1611c1637
<                     sb_open_ai_execute_set_data($embedding_extra_set_data, $conversation_id);
---
>                     sb_open_ai_execute_set_data($embedding_extra_set_data);
1627c1653,1654
<     if ($is_chips_response || $flows_structured_output || (!$response && (in_array($open_ai_mode, ['general', 'all', '']) || $is_embeddings || $is_rewrite || $is_scraping))) { // Deprecated. Remove All
---
>     if ($is_chips_response || $flows_structured_output || !$response) {
>         $is_function_calling_only = !$is_chips_response && !$flows_structured_output && !$response && !$is_embeddings && !$is_rewrite && !$is_scraping && $open_ai_mode == 'sources';
1636c1663
<         $prompt = $is_scraping ? $message : sb_isset($settings, 'open-ai-prompt', $is_embeddings ? 'Provide extensive answers to the user message from the context below. If the answer is not included, write exactly "I don\'t know." in English language and stop after that. Refuse to answer any user message not about the info. Never break character.' . $prompt_language . $prompt_real_time : $prompt_language);
---
>         $prompt = $is_scraping ? $message : sb_isset($settings, 'open-ai-prompt', $is_embeddings ? 'Provide extensive answers to the user message from the context below. If the answer is not included, write exactly "I don\'t know." in English language and stop after that. Do not provide external knowledge. Do not answer unrelated questions. Never break character.' . $prompt_language . $prompt_real_time : $prompt_language);
1670c1697
<                 if (sb_open_ai_is_valid($message_text, false) || $is_scraping) {
---
>                 if (sb_open_ai_is_valid($message_text) || $is_scraping) {
1684a1712,1724
> 
>         // Vision
>         if (!$is_smart_reply && !$is_rewrite && !$is_google_search && !$is_scraping && in_array($model, ['gpt-4.1-nano', 'gpt-4.1-mini', 'gpt-4.1', 'gpt-4o-mini', 'gpt-4o', 'gpt-4', 'gpt-4-32k']) && sb_isset($settings, 'open-ai-vision')) {
>             foreach ($attachments as $attachment) {
>                 if (preg_match('/\.(jpe?g|png|webp|gif|bmp|tiff?)$/i', $attachment[1])) {
>                     array_push($query_messages, ['role' => 'user', 'content' => [['type' => 'image_url', 'image_url' => ['url' => $attachment[1]]]]]);
>                     if (isset($message['user_prompt'])) {
>                         $message['user_prompt'] = str_replace($attachment[1], '', $message['user_prompt']);
>                     }
>                 }
>             }
>         }
> 
1706c1746
<                     $query['response_format'] = ['type' => 'json_schema', 'json_schema' => ['name' => 'flow-' . sb_string_slug($flows_structured_output_string), 'schema' => ['type' => 'object', 'properties' => $properties, 'required' => $required, 'additionalProperties' => false], 'strict' => true]];
---
>                     $query['response_format'] = ['type' => 'json_schema', 'json_schema' => ['name' => 'flow-' . sb_string_slug($flows_structured_output_string, 'slug', true), 'schema' => ['type' => 'object', 'properties' => $properties, 'required' => $required]]];
1714c1754
<             if (!$is_smart_reply && !$is_rewrite && !$is_google_search && !$is_scraping && in_array($model, ['o3-mini', 'o1', 'gpt-4o-mini', 'gpt-4o', 'gpt-4', 'gpt-4-32k'])) {
---
>             if (!$is_smart_reply && !$is_rewrite && !$is_google_search && !$is_scraping && in_array($model, ['gpt-4.1-nano', 'gpt-4.1-mini', 'gpt-4.1', 'o3-mini', 'o4-mini', 'o1', 'gpt-4o-mini', 'gpt-4o', 'gpt-4', 'gpt-4-32k'])) {
1725c1765
<                                 $property_slug = sb_string_slug($value[0]);
---
>                                 $property_slug = sb_string_slug($value[0], 'slug', true);
1733c1773
<                                 'name' => substr(sb_string_slug(is_string($qea[$i][0]) ? $qea[$i][0] : $qea[$i][0][0]), 0, 20) . '-' . $i, // Deprecated Replace is_string($qea[$i][0]) ? $qea[$i][0] : $qea[$i][0][0] with $qea[$i][0][0]
---
>                                 'name' => substr(sb_string_slug(is_string($qea[$i][0]) ? $qea[$i][0] : $qea[$i][0][0], 'slug', true), 0, 20) . '-' . $i, // Deprecated Replace is_string($qea[$i][0]) ? $qea[$i][0] : $qea[$i][0][0] with $qea[$i][0][0]
1738c1778
<                                     'properties' => $properties,
---
>                                     'properties' => count($properties) ? $properties : (object) [],
1751c1791
<                     if (defined('SB_WOOCOMMERCE')) {
---
>                     if (defined('SB_WOOCOMMERCE') && !sb_get_setting('wc-disable-bot-integration')) {
1756c1796
<             if (isset($message['user_prompt'])) {
---
>             if (!empty($message['user_prompt']) && is_string($query_messages[count($query_messages) - 1]['content'])) {
1774,1783c1814,1857
<             if (empty($SB_OPEN_AI_RECURSION_CHECK_2) && !empty($query_messages) && sb_isset($query_messages[0], 'role') == 'developer' && !empty($query['tools']) && (count($query['tools']) > 1 || $query['tools'][0]['function']['name'] != 'sb-human-takeover')) {
<                 $SB_OPEN_AI_RECURSION_CHECK_2 = true;
<                 $human_takeover_tool = $query['tools'][0]['function']['name'] == 'sb-human-takeover' ? $query['tools'][0] : false;
<                 $query['messages'][0]['content'] = '';
<                 if ($human_takeover_tool) {
<                     array_shift($query['tools']);
<                 }
<                 $response = sb_open_ai_curl($chat_model ? 'chat/completions' : 'completions', $query);
<                 if ($human_takeover_tool) {
<                     array_unshift($query['tools'], $human_takeover_tool);
---
>             if (!empty($query_messages) && !empty($query['tools'])) {
>                 if (empty($SB_OPEN_AI_RECURSION_CHECK_3) && !empty($query['response_format']) && !$is_function_calling_only) {
>                     $SB_OPEN_AI_RECURSION_CHECK_3 = true;
>                     $tools = $query['tools'];
>                     $query['messages'][0]['content'] = '';
>                     $query['tools'] = [];
>                     $response = sb_open_ai_curl($url_part, $query);
>                     $query['messages'][0]['content'] = $query_messages[0]['content'];
>                     $query['tools'] = $tools;
>                     $continue = empty($response['choices']) || empty($response['choices'][0]['message']);
>                 }
>                 if ($continue && empty($SB_OPEN_AI_RECURSION_CHECK_2) && (sb_isset($query_messages[0], 'role') == 'developer' || $is_function_calling_only) && (count($query['tools']) > 1 || $query['tools'][0]['function']['name'] != 'sb-human-takeover')) {
>                     $SB_OPEN_AI_RECURSION_CHECK_2 = true;
>                     $human_takeover_tool = $query['tools'][0]['function']['name'] == 'sb-human-takeover' ? $query['tools'][0] : false;
>                     if ($is_function_calling_only) {
>                         array_unshift($query['messages'], ['role' => 'system', 'content' => 'Only respond when a function call is appropriate. Do not reply to general questions or small talk. If you cannot reply, reply exactly and only "I don\'t know".']);
>                     } else {
>                         $query['messages'][0]['content'] = '';
>                     }
>                     if ($human_takeover_tool) {
>                         array_shift($query['tools']);
>                     }
>                     $response = sb_open_ai_curl($url_part, $query);
>                     $continue = empty($response['choices']);
>                     if (!$continue && empty($response['choices'][0]['message']['tool_calls'])) {
>                         $open_ai_response = $response['choices'][0]['message'];
>                         $continue = empty($open_ai_response['tool_calls']);
>                         if ($continue) {
>                             $open_ai_response_embeddings = sb_open_ai_embeddings_generate($open_ai_response['content']);
>                             if (!empty($open_ai_response_embeddings) && isset($open_ai_response_embeddings[0]['embedding'])) {
>                                 foreach ($query['tools'] as $tool) {
>                                     $tool_embeddings = sb_open_ai_embeddings_generate($tool['function']['description']);
>                                     if (!empty($tool_embeddings) && isset($tool_embeddings[0]['embedding']) && sb_open_ai_embeddings_compare($tool_embeddings[0]['embedding'], $open_ai_response_embeddings[0]['embedding'], $tool_embeddings[0]['text'], $open_ai_response_embeddings[0]['text']) > 0.4) {
>                                         $continue = false;
>                                         break;
>                                     }
>                                 }
>                             }
>                         }
>                     }
>                     if ($human_takeover_tool) {
>                         $query['tools'] = [$human_takeover_tool];
>                     }
>                     $query['messages'][0]['content'] = $query_messages[0]['content'];
1785,1786d1858
<                 $query['messages'][0]['content'] = $query_messages[0]['content'];
<                 $continue = empty($response['choices']) || empty($response['choices'][0]['message']['tool_calls']);
1788,1789c1860,1861
<             if ($continue) {
<                 $response = sb_open_ai_curl($chat_model ? 'chat/completions' : 'completions', $query);
---
>             if ($continue && !$is_function_calling_only) {
>                 $response = sb_open_ai_curl($url_part, $query);
1808c1880
<             $function_calling = sb_open_ai_function_calling($response, sb_isset($query, 'tools'));
---
>             $function_calling = sb_open_ai_function_calling($response, sb_isset($query, 'tools'), $conversation_id);
1813a1886,1888
>                 } else if ($function_calling[2] == 'sb-ignore-call') {
>                     $SB_OPEN_AI_RECURSION_CHECK_2 = true;
>                     return sb_open_ai_message(is_string($message) ? $message : sb_isset($message, 'user_prompt', $message), $max_tokens, $model, $conversation_id, $extra);
1843c1918
<                                         $message_ = substr($string, 0, -1);
---
>                                         $message_ = substr($message_, 0, -1);
1881c1956
<                         sb_update_user(sb_get_active_user_ID(), $user_data, $user_data);
---
>                         sb_update_user(sb_get_active_user_ID(), $user_data, $user_data, true, true);
1904c1979
<             if ($chat_model && strpos($response_message_content, '[action ') !== false) {
---
>             if ($chat_model && $response_message_content && strpos($response_message_content, '[action ') !== false && !$is_smart_reply && !$is_rewrite && !$is_google_search && !$is_scraping) {
1925,1927c2000
<     // Check if this is for email piping
<     $is_email_piping = isset($extra) && is_array($extra) && isset($extra['source']) && $extra['source'] == 'em';
<     $unknow_answer = !sb_open_ai_is_valid($response, $is_email_piping);
---
>     $unknow_answer = !sb_open_ai_is_valid($response);
1962c2035
<         $human_takeover = !$is_embeddings && !$dialogflow_active && $human_takeover_active && ($human_request || ($unknow_answer && strlen($message) > 3 && strpos($message, ' ')));
---
>         $human_takeover = !$is_embeddings && !$dialogflow_active && $human_takeover_active && ($human_request || ($unknow_answer && ((strlen($message) > 3 && strpos($message, ' ')) || strlen($message) > 10)));
1964a2038,2040
>                 if (sb_isset($extra, 'messaging-app') == 'em') {
>                     return [true, sb_dialogflow_human_takeover($conversation_id), $token, true];
>                 }
1969c2045,2046
<         } else if ($human_request && $is_human_takeover) {
---
>         }
>         if ($human_request && $is_human_takeover) {
1991,1997d2067
< 
<         // Log the successful response
<         file_put_contents($log_file, date('Y-m-d H:i:s') . " - Returning successful response:\n" .
<             "response: " . (is_string($response) ? substr($response, 0, 100) . (strlen($response) > 100 ? "..." : "") : "not a string") . "\n" .
<             "unknow_answer: " . ($unknow_answer ? "true" : "false") . "\n" .
<             "is_email_piping: " . (isset($extra) && is_array($extra) && isset($extra['source']) && $extra['source'] == 'em' ? "true" : "false") . "\n", FILE_APPEND);
< 
2000,2005d2069
< 
<     // Log the failed response
<     file_put_contents($log_file, date('Y-m-d H:i:s') . " - Returning failed response:\n" .
<         "is_embedding_response: " . ($is_embedding_response ? "true" : "false") . "\n" .
<         "response: " . (is_string($response) ? $response : "not a string") . "\n", FILE_APPEND);
< 
2133c2197
<             $response = sb_open_ai_is_valid($response, false) && strlen($response) > (strlen($message) * 0.5) ? $response : $message;
---
>             $response = sb_open_ai_is_valid($response) && strlen($response) > (strlen($message) * 0.5) ? $response : $message;
2152a2217,2219
>     if (empty($message)) {
>         return $message;
>     }
2295,2311c2362,2363
< function sb_open_ai_is_valid($message, $for_email = false) {
<     if (!$message) {
<         return false;
<     }
< 
<     // For email responses, we want to be VERY lenient
<     if ($for_email) {
<         // For emails, only reject completely empty messages
<         // Accept ANY message that has content, even if it would normally be rejected
<         $is_valid = !empty(trim($message));
<         return $is_valid;
<     }
< 
<     // Standard validation for non-email responses
<     $is_valid = ($message == 'I don\'t know.' || $message == 'I don\'t know' || substr($message, -9) == 'don\'t know' || substr($message, -10) == 'don\'t know.' ? false : preg_match('/(Non lo so.|Bilmiyorum.|не знаю|Tôi không biết.|我不知道。|Jeg vet ikke.|Nie wiem.|Nu știu.|ne vem|nuk e di.|не знам.|Abdi henteu terang.|jag vet inte.|ฉันไม่รู้.|hindi ko alam.|Я не знаю.|ja neviem.|わからない。|არ ვიცი.|모르겠습니다.|aš nežinau.|не знам.|Би мэдэхгүй.|saya tak tahu.|ကျွန်တော်မသိပါ။|Ik weet het niet.|Je ne sais pas.|չգիտեմ։|لا أعرف.|аз не знам|Não sei.|Nevím.|Jeg ved det ikke.|Δεν ξέρω.|No sé.|ma ei tea.|من نمی دانم.|En tiedä.|אני לא יודע.|मुझें नहीं पता।|ne znam|Nem tudom.|Aku tidak tahu.|Ég veit það ekki.|Ich weiß nicht.|I can\'t assist with that|provide real-time|Sorry, as an AI model|don\'t have access to real-time|don\'t have real-time|can\'t access the internet or real-time|provide real-time information|access to real-time|don\'t have access to real-time|not included in the context|sb-human-takeover|provide more context|provide a valid text|What was that|I didn\'t get that|I don\'t understand|no text provided|provide the text|I cannot provide|I don\'t have access|I don\'t have any|As a language model|I do not have the capability|I do not have access|modelo de lenguaje de IA|no tengo acceso|no tinc accés|En tant qu\'IA|je n\'ai pas d\'accès|en tant qu\'intelligence artificielle|je n\'ai pas accès|programme d\'IA|স্মার্ট AI কম্পিউটার প্রোগ্রাম|আমি একটি AI|আমি জানি না|我無法回答未來的活動|AI 語言模型|我無法提供|作為AI|我無法得知|作為一名AI|我無法預測|作为AI|我没有未来预测的功能|作為一個AI|我無法預測未來|作为一个AI|我无法预测|我不具备预测|我作为一个人工智能|Как виртуальный помощник|я не могу предоставить|как AI-ассистента|Как ИИ|Как искусственный интеллект|я не имею доступа|я не могу ответить|я не могу предсказать|como um modelo de linguagem|eu não tenho informações|sou um assistente de linguagem|Não tenho acesso|modelo de idioma de AI|não é capaz de fornecer|não tenho a capacidade|como modelo de linguagem de IA|como uma AI|não tenho um|como modelo de linguagem de inteligência artificial|como modelo de linguagem AI|não sou capaz|poiché sono un modello linguistico|non posso fornire informazioni|in quanto intelligenza artificiale|non ho la capacità|non sono in grado|non ho la possibilità|non posso dare|non posso fare previsioni|non posso predire|in quanto sono un\'Intelligenza Artificiale|Come assistente digitale|come assistente virtuale|Si një AI|nuk mund të parashikoj|Si inteligjencë artificiale|nuk kam informacion|Nuk mund të jap parashikime|nuk mund të parashikoj|لا يمكنني توفير|نموذجًا لغة|لا يمكنني التنبؤ|AI भाषा मॉडल हूँ|मैं एक AI|मुझे इसकी जानकारी नहीं है|मैं आपको बता नहीं सकती|AI सहायक|मेरे पास भविष्य के बारे में कोई जानकारी नहीं है|का पता नहीं है|не мога да|Като AI|не разполагам с|нямам достъп|ne mogu pratiti|Nisam u mogućnosti|nisam sposoban|ne mogu prikazivati|ne mogu ti dati|ne mogu pružiti|nemam pristup|nemam sposobnosti|nemam trenutne informacije|nemam sposobnost|ne mogu s preciznošću|nemůžu předpovídat|nemohu s jistotou|Jako AI|nemohu předpovídat|nemohu s jistotou znát|Jako umělá inteligence|nemám informace|nemohu predikovat|Jako NLP AI|nemohu předvídat|nedokážu předvídat|nemám schopnost|som AI|som en AI|har jeg ikke adgang|Jeg kan desværre ikke besvare|jeg ikke har adgang|kan jeg ikke give|jeg har ikke|har jeg ikke mulighed|Jeg er en AI og har ikke|har jeg ikke evnen|Jeg kan desværre ikke hjælpe med|jeg kan ikke svare|Som sprog AI|jeg ikke i stand)/i', $message) !== 1);
< 
<     return $is_valid;
---
> function sb_open_ai_is_valid($message) {
>     return $message ? ($message == 'I don\'t know.' || $message == 'I don\'t know' || substr($message, -9) == 'don\'t know' || substr($message, -10) == 'don\'t know.' ? false : preg_match('/(Non lo so.|Bilmiyorum.|не знаю|Tôi không biết.|我不知道。|Jeg vet ikke.|Nie wiem.|Nu știu.|ne vem|nuk e di.|не знам.|Abdi henteu terang.|jag vet inte.|ฉันไม่รู้.|hindi ko alam.|Я не знаю.|ja neviem.|わからない。|არ ვიცი.|모르겠습니다.|aš nežinau.|не знам.|Би мэдэхгүй.|saya tak tahu.|ကျွန်တော်မသိပါ။|Ik weet het niet.|Je ne sais pas.|չգիտեմ։|لا أعرف.|аз не знам|Não sei.|Nevím.|Jeg ved det ikke.|Δεν ξέρω.|No sé.|ma ei tea.|من نمی دانم.|En tiedä.|אני לא יודע.|मुझें नहीं पता।|ne znam|Nem tudom.|Aku tidak tahu.|Ég veit það ekki.|Ich weiß nicht.|I can\'t help|I can\'t assist with that|provide real-time|Sorry, as an AI model|don\'t have access to real-time|don\'t have real-time|can\'t access the internet or real-time|provide real-time information|access to real-time|don\'t have access to real-time|not included in the context|sb-human-takeover|provide more context|provide a valid text|What was that|I didn\'t get that|I don\'t understand|no text provided|provide the text|I cannot provide|I don\'t have access|I don\'t have any|As a language model|I do not have the capability|I do not have access|modelo de lenguaje de IA|no tengo acceso|no tinc accés|En tant qu\'IA|je n\'ai pas d\'accès|en tant qu\'intelligence artificielle|je n\'ai pas accès|programme d\'IA|স্মার্ট AI কম্পিউটার প্রোগ্রাম|আমি একটি AI|আমি জানি না|我無法回答未來的活動|AI 語言模型|我無法提供|作為AI|我無法得知|作為一名AI|我無法預測|作为AI|我没有未来预测的功能|作為一個AI|我無法預測未來|作为一个AI|我无法预测|我不具备预测|我作为一个人工智能|Как виртуальный помощник|я не могу предоставить|как AI-ассистента|Как ИИ|Как искусственный интеллект|я не имею доступа|я не могу ответить|я не могу предсказать|como um modelo de linguagem|eu não tenho informações|sou um assistente de linguagem|Não tenho acesso|modelo de idioma de AI|não é capaz de fornecer|não tenho a capacidade|como modelo de linguagem de IA|como uma AI|não tenho um|como modelo de linguagem de inteligência artificial|como modelo de linguagem AI|não sou capaz|poiché sono un modello linguistico|non posso fornire informazioni|in quanto intelligenza artificiale|non ho la capacità|non sono in grado|non ho la possibilità|non posso dare|non posso fare previsioni|non posso predire|in quanto sono un\'Intelligenza Artificiale|Come assistente digitale|come assistente virtuale|Si një AI|nuk mund të parashikoj|Si inteligjencë artificiale|nuk kam informacion|Nuk mund të jap parashikime|nuk mund të parashikoj|لا يمكنني توفير|نموذجًا لغة|لا يمكنني التنبؤ|AI भाषा मॉडल हूँ|मैं एक AI|मुझे इसकी जानकारी नहीं है|मैं आपको बता नहीं सकती|AI सहायक|मेरे पास भविष्य के बारे में कोई जानकारी नहीं है|का पता नहीं है|не мога да|Като AI|не разполагам с|нямам достъп|ne mogu pratiti|Nisam u mogućnosti|nisam sposoban|ne mogu prikazivati|ne mogu ti dati|ne mogu pružiti|nemam pristup|nemam sposobnosti|nemam trenutne informacije|nemam sposobnost|ne mogu s preciznošću|nemůžu předpovídat|nemohu s jistotou|Jako AI|nemohu předpovídat|nemohu s jistotou znát|Jako umělá inteligence|nemám informace|nemohu predikovat|Jako NLP AI|nemohu předvídat|nedokážu předvídat|nemám schopnost|som AI|som en AI|har jeg ikke adgang|Jeg kan desværre ikke besvare|jeg ikke har adgang|kan jeg ikke give|jeg har ikke|har jeg ikke mulighed|Jeg er en AI og har ikke|har jeg ikke evnen|Jeg kan desværre ikke hjælpe med|jeg kan ikke svare|Som sprog AI|jeg ikke i stand)/i', $message) !== 1) : false;
2319,2324d2370
<     // Check if OpenAI API key is set
<     $open_ai_key = sb_open_ai_key();
<     if (empty($open_ai_key)) {
<         return [false, 'api-key-missing', 'OpenAI API key is missing. Please set a Sync Mode in Settings > Artificial Intelligence > OpenAI > Sync mode.'];
<     }
< 
2326,2345d2371
<     $file_name = basename($url);
< 
<     // Check for empty or unreadable PDF content
<     if ($response === 'empty-pdf-data') {
<         sb_file_delete($url);
<         return [false, 'empty-pdf-data', $file_name];
<     }
< 
<     // Check for other file processing errors
<     if (!is_array($response)) {
<         sb_file_delete($url);
<         return [false, $response, $file_name];
<     }
< 
<     // Check if the PDF has actual content (not just empty or with only a few characters)
<     if (is_array($response) && count($response) === 1 && strlen($response[0][0]) < 20) {
<         sb_file_delete($url);
<         return [false, 'unreadable-pdf', $file_name];
<     }
< 
2347c2373
<     return sb_open_ai_embeddings_generate($response, $url);
---
>     return is_array($response) ? sb_open_ai_embeddings_generate($response, $url) : $response;
2351,2356d2376
<     // Check if OpenAI API key is set
<     $open_ai_key = sb_open_ai_key();
<     if (empty($open_ai_key)) {
<         return [false, 'api-key-missing', 'OpenAI API key is missing. Please set a Sync Mode in Settings > Artificial Intelligence > OpenAI > Sync mode.'];
<     }
< 
2364a2385,2387
>     if (empty($response[0])) {
>         return [false, 'The page at ' . $url . ' has not content or is not accessible.'];
>     }
2369,2374d2391
<     // Check if OpenAI API key is set
<     $open_ai_key = sb_open_ai_key();
<     if (empty($open_ai_key)) {
<         return [false, 'api-key-missing', 'OpenAI API key is missing. Please set a Sync Mode in Settings > Artificial Intelligence > OpenAI > Sync mode.'];
<     }
< 
2385c2402
<     if ($update_index) {
---
>     if ($update_index !== false && $update_index !== '') {
2401,2403d2417
<             if (is_string($questions_answers[$i][0])) { // Deprecated
<                 $questions_answers[$i][0] = [$questions_answers[$i][0]];// Deprecated
<             }// Deprecated
2415,2417d2428
<         if (is_string($db_embeddings[$i][0])) { // Deprecated
<             $db_embeddings[$i][0] = [$db_embeddings[$i][0]];// Deprecated
<         }// Deprecated
2480,2481d2490
<     if (count($last_check) == 1) // Deprecated
<         array_push($last_check, 0); // Deprecated
2497d2505
<             $conversation = '';
2503c2511
<                 if (($user_id == $bot_id) || (!$is_agent && $j < ($count - 2) && $messages[$j + 1]['user_id'] == $bot_id && !in_array($messages[$j + 2]['user_id'], $agent_ids)) && (!isset($messages[$j + 3]) || !strpos($messages[$j + 3]['payload'], 'sb-human-takeover'))) {
---
>                 if (strlen($messages[$j]['message']) < 3 || !strpos($messages[$j]['message'], ' ') || ($user_id == $bot_id) || (!$is_agent && $j < ($count - 2) && $messages[$j + 1]['user_id'] == $bot_id && !in_array($messages[$j + 2]['user_id'], $agent_ids)) && (!isset($messages[$j + 3]) || !strpos($messages[$j + 3]['payload'], 'sb-human-takeover'))) {
2515,2516c2523,2524
<             $is_agent_in_conversation = false;
<             $is_question_in_conversation = false;
---
>             $question = '';
>             $answer = '';
2520,2526c2528,2530
<                     $label = '';
<                     if (isset($messages_final[$j - 1]) && $messages_final[$j - 1]) {
<                         if ($messages_final[$j - 1][0] != $is_agent || !$conversation) {
<                             $label = PHP_EOL . PHP_EOL . ($is_agent ? 'Answer: ' : 'Question: ');
<                         } else {
<                             $conversation .= (in_array(substr($conversation, -1), ['.', '!', '?', ';']) ? '' : '.') . ' ';
<                         }
---
>                     $message = strip_tags(sb_google_get_message_translation($messages_final[$j][1], $language)['message']);
>                     if ($is_agent) {
>                         $answer .= (!$answer || sb_is_string_ends($answer) ? '' : '.') . ' ' . $message;
2528c2532
<                         $label = $is_agent ? 'Answer: ' : 'Question: ';
---
>                         $question .= (!$question || sb_is_string_ends($question) ? '' : '.') . ' ' . $message;
2530,2539c2534,2537
<                     $message = strip_tags(sb_google_get_message_translation($messages_final[$j][1], $language)['message']);
<                     if (strlen($message) > 2 && strpos($message, ' ')) {
<                         if ($is_agent) {
<                             $is_agent_in_conversation = true;
<                         } else {
<                             $is_question_in_conversation = true;
<                         }
<                         if (!$is_agent || $conversation) {
<                             $conversation .= $label . strip_tags($message);
<                         }
---
>                     if ($answer && $question && $is_agent && (empty($messages_final[$j + 1]) || !$messages_final[$j + 1][0])) {
>                         array_push($paragraphs, [[trim($question), trim($answer)], '', 'conversation-' . $conversation_id]);
>                         $answer = '';
>                         $question = '';
2543,2547c2541,2542
<             if ($is_agent_in_conversation && $is_question_in_conversation) {
<                 array_push($paragraphs, [$conversation, '', 'conversation-' . $conversation_id]);
<                 if (strtotime($conversations[$i]['creation_time']) > $last_check_unix) {
<                     $count_conversations++;
<                 }
---
>             if (strtotime($conversations[$i]['creation_time']) > $last_check_unix) {
>                 $count_conversations++;
2615,2620d2609
<     // Check if OpenAI API key is set
<     $open_ai_key = sb_open_ai_key();
<     if (empty($open_ai_key)) {
<         return [false, 'api-key-missing', 'OpenAI API key is missing. Please set a Sync Mode in Settings > Artificial Intelligence > OpenAI > Sync mode.'];
<     }
< 
2680,2681c2669,2670
<             if (isset($paragraphs_or_string[$i][2]) && $paragraphs_or_string[$i][2] != 'qea' && strpos($paragraphs_or_string[$i][2], 'article-') === false && strpos($paragraphs_or_string[$i][2], 'conversation-') === false && strpos($paragraphs_or_string[$i][2], 'flow-') === false) {
<                 $paragraphs_or_string[$i][0] .= ' More details at ' . $paragraphs_or_string[$i][2] . '.';
---
>             if (isset($paragraphs_or_string[$i][2]) && $paragraphs_or_string[$i][2] != 'qea' && strpos($paragraphs_or_string[$i][2], 'article-') === false && strpos($paragraphs_or_string[$i][2], 'conversation-') === false && strpos($paragraphs_or_string[$i][2], 'flow-') === false && !strpos($paragraphs_or_string[$i][0], 'More details at ')) {
>                 $paragraphs_or_string[$i][0] .= (sb_is_string_ends($paragraphs_or_string[$i][0]) ? '' : '.') . ' More details at ' . $paragraphs_or_string[$i][2] . '.';
2892,2896c2881,2890
< function sb_open_ai_embeddings_compare($a, $b) {
<     $result = array_map(function ($x, $y) {
<         return $x * $y;
<     }, $a, $b);
<     return array_sum($result);
---
> function sb_open_ai_embeddings_compare($vector_1, $vector_2, $vector_1_text, $vector_2_text) {
>     $dot_product = 0.0;
>     $norm_query = 0.0;
>     $norm_text = 0.0;
>     for ($i = 0; $i < count($vector_1); $i++) {
>         $dot_product += $vector_1[$i] * $vector_2[$i];
>         $norm_query += $vector_1[$i] ** 2;
>         $norm_text += $vector_2[$i] ** 2;
>     }
>     return min((($norm_query > 0 && $norm_text > 0) ? ($dot_product / (sqrt($norm_query) * sqrt($norm_text))) : 0.0) + (mb_stripos($vector_2_text, $vector_1_text, 0, 'UTF-8') !== false ? 0.2 : 0.0), 1.0);
2923c2917
<                     $score = !empty($user_prompt_embeddings) && !empty($embedding['embedding']) ? sb_open_ai_embeddings_compare($user_prompt_embeddings, $embedding['embedding']) : 0;
---
>                     $score = !empty($user_prompt_embeddings) && !empty($embedding['embedding']) ? sb_open_ai_embeddings_compare($user_prompt_embeddings, $embedding['embedding'], $user_prompt, $embedding['text']) : 0;
2945c2939
<             $context_max_length = in_array($model, ['o1-mini', 'gpt-4', 'gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo']) ? 32000 : (in_array($model, ['o3-mini', 'o1']) ? 50000 : 4000);
---
>             $context_max_length = in_array($model, ['gpt-4.1-nano', 'gpt-4.1-mini', 'gpt-4.1']) ? 500000 : (in_array($model, ['o1-mini', 'gpt-4', 'gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo']) ? 32000 : (in_array($model, ['o3-mini', 'o4-mini', 'o1']) ? 50000 : 4000));
2950c2944
<                     $text = (substr($text_, 0, 9) != 'Question:' ? 'Question: ' : '') . $text_ . ($answer ? (in_array(substr($text_, -1), ['.', '?', '!']) ? '' : '.') . PHP_EOL . PHP_EOL . 'Answer: ' . $answer : '');
---
>                     $text = (substr($text_, 0, 9) != 'Question:' ? 'Question: ' : '') . $text_ . ($answer ? (sb_is_string_ends($text_) ? '' : '.') . PHP_EOL . PHP_EOL . 'Answer: ' . $answer : '');
2968c2962
<             $response = sb_open_ai_message(['context' => $context, 'user_prompt' => $user_prompt], false, false, sb_isset($extra, 'conversation_id'), $extra_, false, [], sb_isset($extra, 'context'));
---
>             $response = sb_open_ai_message(['context' => $context, 'user_prompt' => $user_prompt], false, false, sb_isset($extra, 'conversation_id'), $extra_, false, sb_isset($extra, 'attachments'), sb_isset($extra, 'context'));
2975c2969
<                     if (sb_open_ai_is_valid($response[1], false)) {
---
>                     if (sb_open_ai_is_valid($response[1])) {
3133a3128,3132
>     $answer = false;
>     if (is_array($text) && isset($text['answer'])) {
>         $answer = $text['answer'];
>         $text = $text['question'];
>     }
3153a3153,3155
>             if ($answer) {
>                 $embedding[$index]['answer'] = $answer;
>             }
3173,3174c3175,3176
<         $texts = array_column(sb_open_ai_embeddings_get_file($embeddings[$i], true), 'text');
<         for ($y = 0; $y < count($texts); $y++) {
---
>         $embedding = sb_open_ai_embeddings_get_file($embeddings[$i], true);
>         for ($y = 0; $y < count($embedding); $y++) {
3176,3180c3178
<             $qea = explode('|||', str_replace(['Question: ', 'Answer: '], '|||', $texts[$y]));
<             $count = count($qea) - 1;
<             for ($j = 1; $j < $count; $j += 2) {
<                 array_push($response, ['question' => trim($qea[$j]), 'answer' => $qea[$j + 1], 'id' => $id]);
<             }
---
>             array_push($response, ['question' => $embedding[$y]['text'], 'answer' => $embedding[$y]['answer'], 'id' => $id]);
3189,3192c3187,3188
<         $id = explode('-', $qea[$i][0]['id']);
<         $texts = array_column(sb_open_ai_embeddings_get_file($id[1], true), 'text');
<         $text = trim(str_replace(['Question: ' . htmlentities($qea[$i][0]['question']), 'Answer: ' . htmlentities($qea[$i][0]['answer'])], $qea[$i][1] && $qea[$i][1] != 'false' ? ['Question: ' . $qea[$i][1][0], 'Answer: ' . $qea[$i][1][1]] : '', $texts[$id[0]]));
<         array_push($response, sb_open_ai_embeddings_update_single($id[1], $id[0], $text));
---
>         $id = explode('-', $qea[$i]['id']);
>         array_push($response, sb_open_ai_embeddings_update_single($id[1], $id[0], $qea[$i]));
3199a3196
>     $text = false;
3204c3201
<     if ($extension == '.pdf') {
---
>     if (in_array($extension, ['pdf', 'json', 'csv'])) {
3206,3207c3203,3214
<         $file = strpos($url, $upload_url) === 0 ? sb_upload_path() . str_replace($upload_url, '', $url) : sb_download_file($url, 'sb_open_ai_source_file' . $extension, false, [], 0, true);
<         $text = sb_pdf_to_text($file);
---
>         $file = strpos($url, $upload_url) === 0 ? sb_upload_path() . str_replace($upload_url, '', $url) : sb_download_file($url, 'sb_open_ai_source_file.' . $extension, false, [], 0, true);
>         switch ($extension) {
>             case 'pdf':
>                 $text = sb_pdf_to_text($file);
>                 break;
>             case 'json':
>                 $text = sb_json_to_text($file);
>                 break;
>             case 'csv':
>                 $text = sb_csv_to_text($file);
>                 break;
>         }
3245c3252
<     $model = sb_get_multi_setting('open-ai', 'open-ai-model', 'gpt-4o-mini');
---
>     $model = sb_get_multi_setting('open-ai', 'open-ai-model', 'gpt-4.1-mini');
3342c3349
<                 $function_calling = sb_open_ai_function_calling($response);
---
>                 $function_calling = sb_open_ai_function_calling($response, false, $conversation_id);
3372,3373c3379,3391
<     $prompts = sb_open_ai_data_scraping_get_prompts();
<     $response = sb_open_ai_message($prompts[$prompt_id][0] . ' from the user messages. Do not scrape anything else, return only the scraped information separated by breaklines, do not add text. If the information is not included, write exactly "I don\'t know.', false, false, $conversation_id, 'scraping');
---
>     $prompt = sb_open_ai_data_scraping_get_prompts()[$prompt_id];
>     if ($prompt_id == 'summary') {
>         $messages = sb_get_conversation(false, $conversation_id)['messages'];
>         $text = 'Here is the conversation to summarize: ' . PHP_EOL;
>         foreach ($messages as $message) {
>             $text .= (sb_is_agent($message['user_type']) ? 'Agent' : 'User') . ': ' . $message['message'] . PHP_EOL;
>         }
>         $response = sb_open_ai_curl('chat/completions', ['model' => sb_open_ai_get_gpt_model(), 'messages' => [['role' => 'developer', 'content' => 'You are a helpful assistant that summarizes conversations between users and agents. Generate a summary of the conversation with the key user questions and agent answers. Only return the summary and nothing else. Do not add any other text. Do not ask questions.'], ['role' => 'user', 'content' => $text]]]);
>         $response = sb_isset(sb_isset(sb_isset($response, 'choices', [[]])[0], 'message'), 'content', '');
>         $response = [true, $response, false, null, empty($response) ? ['unknow_answer' => true] : []];
>     } else {
>         $response = sb_open_ai_message($prompt[0] . ' from the user messages. Do not scrape anything else, return only the scraped information separated by breaklines, do not add text. If the information is not included, write exactly "I don\'t know.', false, false, $conversation_id, 'scraping');
>     }
3379c3397
<         if (in_array('duplicate', $prompts[$prompt_id][1])) {
---
>         if (in_array('duplicate', $prompt[1])) {
3386,3387c3404,3405
<             for ($j = 0; $j < count($prompts[$prompt_id][1]); $j++) {
<                 $check = $prompts[$prompt_id][1][$j];
---
>             for ($j = 0; $j < count($prompt[1]); $j++) {
>                 $check = $prompt[1][$j];
3392c3410
<             $count = count($prompts[$prompt_id][2]);
---
>             $count = count($prompt[2]);
3395,3396c3413,3414
<                 for ($j = 0; $j < count($prompts[$prompt_id][2]); $j++) {
<                     $check = $prompts[$prompt_id][2][$j];
---
>                 for ($j = 0; $j < count($prompt[2]); $j++) {
>                     $check = $prompt[2][$j];
3416c3434
<     $prompts = ['login' => ['The login details are made up of a URL or IP address, a username or email, and a password. Scrape all login details', [], [], 'Login information'], 'links' => ['Scrape all links and URLs', ['@', 123], ['http', 'www'], 'Links and URLs'], 'contacts' => ['Scrape addresses, phone numbers and emails', ['http'], [], 'Contact information']];
---
>     $prompts = ['login' => ['The login details are made up of a URL or IP address, a username or email, and a password. Scrape all login details', [], [], 'Login information'], 'links' => ['Scrape all links and URLs', ['@', 123], ['http', 'www'], 'Links and URLs'], 'contacts' => ['Scrape addresses, phone numbers and emails', ['http'], [], 'Contact information'], 'summary' => ['Generate a summary of the conversation', [], [], 'Summary']];
3425c3443
< function sb_open_ai_function_calling($response, $query_tools = false) {
---
> function sb_open_ai_function_calling($response, $query_tools = false, $conversation_id = false) {
3456c3474
<         if (defined('SB_WOOCOMMERCE')) {
---
>         if (defined('SB_WOOCOMMERCE') && !sb_get_setting('wc-disable-bot-integration')) {
3470c3488,3490
<                 if (substr(sb_string_slug($qea[$i][0][$j]), 0, 20) . '-' . $i == $function_name) {
---
>                 if (substr(sb_string_slug($qea[$i][0][$j], 'slug', true), 0, 20) . '-' . $i == $function_name) {
>                     $function['user_id'] = sb_get_active_user_ID();
>                     $function['conversation_id'] = $conversation_id;
3529a3550
>     libxml_use_internal_errors(true);
3549a3571,3590
>             preg_match_all('/<a\s[^>]*href=["\'](#.*?)["\']/i', $body_content, $matches);
>             foreach ($matches[1] as $match) {
>                 $match = substr($match, 1);
>                 $pos = strpos($body_content, 'id="' . $match . '"');
>                 if (!$pos) {
>                     $pos = strpos($body_content, 'id=\'' . $match . '\'');
>                 }
>                 if ($pos) {
>                     $body_content = substr_replace($body_content, '<p>More details at ' . $url . '#' . $match . '.</p>', strpos($body_content, '>', $pos) + 1, 0);
>                 }
>             }
>             preg_match_all('/<a\b[^>]*>.*?<\/a>/is', $body_content, $matches);
>             $matches = array_unique($matches[0]);
>             foreach ($matches as $match) {
>                 $match_ = preg_replace('/<img\b[^>]*>/i', '', str_replace('</a>', '', preg_replace('/\s+/', ' ', str_replace(["\r\n", "\r", "\n"], '', $match))));
>                 $match_ = substr($match_, strpos($match_, '>') + 1);
>                 if (empty(trim($match_))) {
>                     $body_content = str_replace($match, '', $body_content);
>                 }
>             }
3598,3599c3639,3640
<                     $href = isset($el->getAttribute) ? $a->getAttribute('href') : false;
<                     if ($href) {
---
>                     $href = trim($a->getAttribute('href'));
>                     if ($href && $href != '#' && strpos($href, 'javascript:') === false && strpos($href, 'data:') === false) {
3622a3664,3665
>                                 } else if (strpos($href, 'http') === false && strpos($href, 'www') === false && strpos($href, 'tel:') === false && strpos($href, 'sms:') === false && strpos($href, 'file:') === false && strpos($href, 'ftp:') === false && strpos($href, 'whatsapp:') === false && strpos($href, 'mailto:') === false) {
>                                     $href = $base_protocol . $base_host . '/' . $href;
3630c3673,3674
<                         $a->nodeValue = ' ' . $href;
---
>                         $href_text = trim($a->nodeValue);
>                         $a->nodeValue = $href_text ? ' [' . $href_text . ': ' . $href . ']' : $href;
3650c3694
<                         $header->nodeValue = '--------------------------------------------------------------------------------' . $header->nodeValue . ' \n';
---
>                         $header->nodeValue = '###P###' . $header->nodeValue . ' \n';
3680c3724
<                     $text .= (in_array(substr($text, -1), ['.', ',', ':', '!', '?', ';', '።', '।', '。', '။']) ? '' : ' . ') . trim($list_2[$i + 1]);
---
>                     $text .= (sb_is_string_ends($text) ? '' : ' . ') . trim($list_2[$i + 1]);
3683c3727
<                 $text = str_replace(' .', '.', $text);
---
>                 $text = str_replace([' .', '. \n.'], '.', $text);
3689a3734
>                 $text = str_replace('. \n.', '.', $text);
3698c3743
<                             $paragraphs[] = $temp[0];
---
>                             array_push($paragraphs, $temp[0]);
3705c3750
<                     $paragraphs[] = $temp[0];
---
>                     array_push($paragraphs, $temp[0]);
3778c3823,3828
<         $response = sb_open_ai_message($messages[$count - 1][1], false, false, $conversation_id);
---
>         $message = $messages[$count - 1][1];
>         if ($count > 1 && strpos($messages[$count - 2][1], 'id="sb-human-takeover"') && $message == sb_dialogflow_get_human_takeover_settings()['confirm']) {
>             $response = [true, sb_dialogflow_get_human_takeover_settings()['message_confirmation']];
>         } else {
>             $response = sb_open_ai_message($message, false, false, $conversation_id);
>         }
3806,3807c3856,3857
<     $extension = substr($url, -4);
<     return in_array($extension, ['.pdf', '.txt']) ? $extension : false;
---
>     $extension = strtolower(pathinfo($url, PATHINFO_EXTENSION));
>     return in_array($extension, ['pdf', 'txt', 'csv', 'json']) ? $extension : false;
3810c3860,3863
< function sb_open_ai_execute_set_data($user_data, $conversation_id) {
---
> function sb_open_ai_execute_set_data($user_data) {
>     if (sb_is_agent()) {
>         return false;
>     }
3817c3870
<     return sb_update_user(sb_get_active_user_ID(), $user_data, $user_data);
---
>     return sb_update_user(sb_get_active_user_ID(), $user_data, $user_data, true, true);
3827c3880
<                 sb_tags_update($conversation_id, explode(strpos($action[1], '|') ? '|' : ',', $action[1]));
---
>                 sb_tags_update($conversation_id, explode(strpos($action[1], '|') ? '|' : ',', $action[1]), true);
3930c3983
<                                     $answer_block = sb_flows_get_block_code($blocks_next[$k], $flows[$i]['name'] . '_' . ($j + 1) . '_' . $index . '_' . $k) . ' ';
---
>                                     $answer_block = sb_flows_get_block_code($blocks_next[$k], $flows[$i]['name'] . '_' . ($j + 1) . '_' . $index . '_' . $k, false) . ' ';
4008c4061
< function sb_flows_get_block_code($block, $flow_identifier) {
---
> function sb_flows_get_block_code($block, $flow_identifier, $is_merge_fields = true) {
4015c4068
<             return '[chips id="flow_' . $flow_identifier . '" options="' . substr($options_text, 0, -1) . '" message="' . sb_merge_fields($block['message']) . '"]';
---
>             return '[chips id="flow_' . $flow_identifier . '" options="' . substr($options_text, 0, -1) . '" message="' . ($is_merge_fields ? sb_merge_fields($block['message']) : $block['message']) . '"]';
4017c4070
<             return sb_merge_fields($block['message']);
---
>             return $is_merge_fields ? sb_merge_fields($block['message']) : $block['message'];
4020c4073
<             return $matches ? sb_merge_fields($block['message']) . ' [video type="' . (strpos($block['url'], 'vimeo') ? 'vimeo' : 'youtube') . '" id="' . $matches[1] . '"]' : false;
---
>             return $matches ? ($is_merge_fields ? sb_merge_fields($block['message']) : $block['message']) . ' [video type="' . (strpos($block['url'], 'vimeo') ? 'vimeo' : 'youtube') . '" id="' . $matches[1] . '"]' : false;
4154c4207
<             sb_open_ai_execute_set_data($user_data, $conversation_id);
---
>             sb_open_ai_execute_set_data($user_data);
4172c4225,4227
<                 $response_rest_api = json_decode(sb_curl($block['url'], $body, $headers, $block['method']), true);
---
>                 $body = json_encode($body, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
>                 $response_call = sb_curl($block['url'], $body, $headers, $block['method'], false, ['Content-Type: application/json', 'Content-Length: ' . strlen($body)]);
>                 $response_rest_api = is_array($response_call) ? $response_call : json_decode($response_call, true);
4191c4246
<                             sb_update_user(sb_get_active_user_ID(), $user_data, $user_data);
---
>                             sb_update_user(sb_get_active_user_ID(), $user_data, $user_data, true, true);
4202c4257
<     if (strpos($actions_string, '[action ') === false) {
---
>     if (strpos($actions_string, '[action ') === false || strpos($action_string, '[action ') === false) {
4237c4292
<     $max_tokens_list = ['gpt-3.5-turbo' => 4097, 'gpt-3.5-turbo-1106' => 16385, 'gpt-3.5-turbo-instruct' => 4097, 'gpt-4' => 8192, 'gpt-4-turbo' => 128000, 'gpt-4o' => 128000, 'gpt-4o-mini' => 128000, 'o1' => 200000, 'o1-mini' => 128000, 'o3-mini' => 200000];
---
>     $max_tokens_list = ['gpt-3.5-turbo' => 16385, 'gpt-3.5-turbo-instruct' => 4096, 'gpt-4' => 8192, 'gpt-4-turbo' => 128000, 'gpt-4o' => 128000, 'gpt-4o-mini' => 128000, 'o1' => 200000, 'o1-mini' => 128000, 'o3-mini' => 200000, 'o4-mini' => 200000, 'gpt-4.1-nano' => 1047576, 'gpt-4.1-mini' => 1047576, 'gpt-4.1' => 1047576];
4259a4315,4316
>             } else {
>                 sb_flows_execute($message, [], $language, $conversation_id);
4260a4318,4319
>         } else {
>             $shortcode = $shortcode[0];
4262c4321
<         return sb_send_message(sb_get_bot_id(), $conversation_id, sb_merge_fields(sb_t($shortcode[0]['shortcode'], $language)), [], 3);
---
>         return sb_send_message(sb_get_bot_id(), $conversation_id, sb_merge_fields(sb_t($message, $language)), [], 3);
4319c4378
<         return $strings;
---
>         return [$strings, $token];
4366a4426
>                 $string = str_replace($shortcode_replacements[1], $shortcode_replacements[0], str_replace(['44 =', '{R}'], ['44=', '{RR}'], $string));
4373c4433
<                 $string = str_replace($shortcode_replacements[1], $shortcode_replacements[0], str_replace('44 =', '44=', $string));
---
>                 $string = str_replace('{RR}', ',', $string);
4375c4435
<                 $string = str_replace(['{R}', '{T}'], [',', ':'], $string);
---
>                 $string = str_replace(['{R}', '{T}', '\,\,', ',,'], [',', ':', '\,', ','], $string);
