<?php

/*
 * ==========================================================
 * COMPONENTS.PHP
 * ==========================================================
 *
 * Library of static html components for the admin area. This file must not be executed directly. � 2017-2025 app.masichat.com. All rights reserved.
 *
 */

function mc_profile_box() { ?>
    <div class="mc-profile-box mc-lightbox">
        <div class="mc-top-bar">
            <div class="mc-profile">
                <img src="<?php echo MC_URL ?>/media/user.svg" />
                <span class="mc-name"></span>
            </div>
            <div>
                <a data-value="custom_email" class="mc-btn-icon" data-mc-tooltip="<?php mc_e('Send email') ?>">
                    <i class="mc-icon-envelope"></i>
                </a>
                <?php
                if (mc_get_multi_setting('sms', 'sms-user')) {
                    echo '<a data-value="sms" class="mc-btn-icon" data-mc-tooltip="' . mc_('Send text message') . '"><i class="mc-icon-sms"></i></a>';
                }
                if (defined('MC_WHATSAPP') && (!function_exists('mc_whatsapp_active') || mc_whatsapp_active())) {
                    echo '<a data-value="whatsapp" class="mc-btn-icon" data-mc-tooltip="' . mc_('Send a WhatsApp message template') . '"><i class="mc-icon-social-wa"></i></a>'; // Deprecated: remove function_exists('mc_whatsapp_active')
                }
                if (((mc_is_agent(false, true, true) && !mc_supervisor()) || mc_get_multi_setting('agents', 'agents-edit-user')) || (mc_supervisor() && mc_get_multi_setting('supervisor', 'supervisor-edit-user'))) {
                    echo ' <a class="mc-edit mc-btn mc-icon" data-button="toggle" data-hide="mc-profile-area" data-show="mc-edit-area"><i class="mc-icon-user"></i>' . mc_('Edit user') . '</a>';
                }
                ?>
                <a class="mc-start-conversation mc-btn mc-icon">
                    <i class="mc-icon-message"></i>
                    <?php mc_e('Start a conversation') ?>
                </a>
                <a class="mc-close mc-btn-icon mc-btn-red" data-button="toggle" data-hide="mc-profile-area" data-show="mc-table-area">
                    <i class="mc-icon-close"></i>
                </a>
            </div>
        </div>
        <div class="mc-main mc-scroll-area">
            <div>
                <div class="mc-title">
                    <?php mc_e('Details') ?>
                </div>
                <div class="mc-profile-list"></div>
                <div class="mc-agent-area"></div>
            </div>
            <div>
                <div class="mc-title">
                    <?php mc_e('User conversations') ?>
                </div>
                <ul class="mc-user-conversations"></ul>
            </div>
        </div>
    </div>
<?php } ?>
<?php
function mc_profile_edit_box() { ?>
    <div class="mc-profile-edit-box mc-lightbox">
        <div class="mc-info"></div>
        <div class="mc-top-bar">
            <div class="mc-profile">
                <img src="<?php echo MC_URL ?>/media/user.svg" />
                <span class="mc-name"></span>
            </div>
            <div>
                <a class="mc-save mc-btn mc-icon">
                    <i class="mc-icon-check"></i>
                    <?php mc_e('Save changes') ?>
                </a>
                <a class="mc-close mc-btn-icon mc-btn-red" data-button="toggle" data-hide="mc-profile-area" data-show="mc-table-area">
                    <i class="mc-icon-close"></i>
                </a>
            </div>
        </div>
        <div class="mc-main mc-scroll-area">
            <div class="mc-details">
                <div class="mc-title">
                    <?php mc_e('Edit details') ?>
                </div>
                <div class="mc-edit-box">
                    <div id="profile_image" data-type="image" class="mc-input mc-input-image mc-profile-image">
                        <span>
                            <?php mc_e('Profile image') ?>
                        </span>
                        <div class="image">
                            <div class="mc-icon-close"></div>
                        </div>
                    </div>
                    <div id="user_type" data-type="select" class="mc-input mc-input-select">
                        <span>
                            <?php mc_e('Type') ?>
                        </span>
                        <select>
                            <option value="agent">
                                <?php mc_e('Agent') ?>
                            </option>
                            <option value="admin">
                                <?php mc_e('Admin') ?>
                            </option>
                        </select>
                    </div>
                    <?php mc_departments('select') ?>
                    <div id="first_name" data-type="text" class="mc-input">
                        <span>
                            <?php mc_e('First name') ?>
                        </span>
                        <input type="text" required />
                    </div>
                    <div id="last_name" data-type="text" class="mc-input">
                        <span>
                            <?php mc_e('Last name') ?>
                        </span>
                        <input type="text" />
                    </div>
                    <div id="password" data-type="text" class="mc-input">
                        <span>
                            <?php mc_e('Password') ?>
                        </span>
                        <input type="password" />
                    </div>
                    <div id="email" data-type="email" class="mc-input">
                        <span>
                            <?php mc_e('Email') ?>
                        </span>
                        <input type="email" />
                    </div>
                </div>
                <a class="mc-delete mc-btn-text mc-btn-red">
                    <i class="mc-icon-delete"></i>
                    <?php mc_e('Delete user') ?>
                </a>
            </div>
            <div class="mc-additional-details">
                <div class="mc-title">
                    <?php mc_e('Edit additional details') ?>
                </div>
                <div class="mc-edit-box">
                    <?php
                    $code = '';
                    $fields = mc_users_get_fields();
                    foreach ($fields as $field) {
                        $id = $field['id'];
                        $type = $id == 'country' || $id == 'language' ? 'select' : ($id == 'birthdate' ? 'date' : 'text');
                        $code .= '<div id="' . $id . '" data-type="' . $type . '" class="mc-input"><span>' . mc_($field['name']) . '</span>';
                        if ($type == 'date' || $type == 'text') {
                            $code .= '<input type="' . $type . '" />';
                        } else if ($id == 'country') {
                            $code .= mc_select_html('countries');
                        } else if ($id == 'language') {
                            $code .= mc_select_html('languages');
                        }
                        $code .= '</div>';
                    }
                    echo $code;
                    ?>
                </div>
            </div>
        </div>
    </div>
<?php } ?>
<?php
function mc_login_box() { ?>
    <form class="mc mc-rich-login mc-admin-box">
        <div class="mc-info"></div>
        <div class="mc-top-bar">
            <img src="<?php echo mc_get_setting('login-icon', MC_URL . '/media/logo.svg') ?>" />
            <div class="mc-title">
                <?php mc_e('Sign In') ?>
            </div>
            <div class="mc-text">
                <?php echo mc_sanatize_string(mc_get_setting('login-message', defined('MC_WP') ? mc_('Please insert email and password of your WordPress account') : mc_('Enter your login details below'))) ?>
            </div>
        </div>
        <div class="mc-main">
            <div id="email" class="mc-input">
                <span>
                    <?php mc_e('Email') ?>
                </span>
                <input type="text" />
            </div>
            <div id="password" class="mc-input">
                <span>
                    <?php mc_e('Password') ?>
                </span>
                <input type="password" />
            </div>
            <div class="mc-bottom">
                <div class="mc-btn mc-submit-login">
                    <?php mc_e('Login') ?>
                </div>
            </div>
        </div>
    </form>
    <img id="mc-error-check" style="display:none" src="<?php echo MC_URL . '/media/logo.svg' ?>" />
    <script>
        (function ($) {
            $(document).ready(function () {
                $('.mc-admin-start').removeAttr('style');
                $('.mc-submit-login').on('click', function () {
                    MCF.loginForm(this, false, function () {
                        location.reload();
                    });
                });
                $('#mc-error-check').one('error', function () {
                    $('.mc-info').html('It looks like the chat URL has changed. Edit the config.php file(it\'s in the Masi Chat folder) and update the MC_URL constant with the new URL.').addClass('mc-active');
                });
                MCF.serviceWorker.init();
            });
            $(window).keydown(function (e) {
                if (e.which == 13) {
                    $('.mc-submit-login').click();
                }
            });
            if (MCF.getURL('login_email')) {
                setTimeout(() => {
                    $('#email input').val(MCF.getURL('login_email'));
                    $('#password input').val(MCF.getURL('login_password'));
                    $('.mc-submit-login').click();
                }, 300);
            }
        }(jQuery));
    </script>
<?php } ?>
<?php
function mc_dialog() { ?>
    <div class="mc-dialog-box mc-lightbox">
        <div class="mc-title"></div>
        <p></p>
        <div>
            <a class="mc-confirm mc-btn">
                <?php mc_e('Confirm') ?>
            </a>
            <a class="mc-cancel mc-btn mc-btn-red">
                <?php mc_e('Cancel') ?>
            </a>
            <a class="mc-close mc-btn">
                <?php mc_e('Close') ?>
            </a>
        </div>
    </div>
<?php } ?>
<?php
function mc_updates_box() { ?>
    <div class="mc-lightbox mc-updates-box">
        <div class="mc-info"></div>
        <div class="mc-top-bar">
            <div>
                <?php mc_e('Update center') ?>
            </div>
            <div>
                <a class="mc-close mc-btn-icon mc-btn-red">
                    <i class="mc-icon-close"></i>
                </a>
            </div>
        </div>
        <div class="mc-main mc-scroll-area">
            <div class="mc-bottom">
                <a class="mc-update mc-btn mc-icon">
                    <i class="mc-icon-reload"></i>
                    <?php mc_e('Update now') ?>
                </a>
                <a href="https://app.masichat.com/changes" target="_blank" class="mc-btn-text">
                    <i class="mc-icon-clock"></i>
                    <?php mc_e('Change Log') ?>
                </a>
            </div>
        </div>
    </div>
<?php } ?>
<?php
function mc_app_box() { ?>
    <div class="mc-lightbox mc-app-box" data-app="">
        <div class="mc-info"></div>
        <div class="mc-top-bar">
            <div></div>
            <div>
                <a class="mc-close mc-btn-icon mc-btn-red">
                    <i class="mc-icon-close"></i>
                </a>
            </div>
        </div>
        <div class="mc-main">
            <p></p>
            <div class="mc-title">
                <?php mc_e('License key') ?>
            </div>
            <div class="mc-setting mc-type-text">
                <input type="text" required />
            </div>
            <div class="mc-bottom">
                <a class="mc-btn mc-icon mc-btn-app-setting">
                    <i class="mc-icon-settings"></i>
                    <?php mc_e('Settings') ?>
                </a>
                <a class="mc-activate mc-btn mc-icon">
                    <i class="mc-icon-check"></i>
                    <?php mc_e('Activate') ?>
                </a>
                <a class="mc-btn-red mc-btn mc-icon mc-btn-app-disable">
                    <i class="mc-icon-close"></i>
                    <?php mc_e('Disable') ?>
                </a>
                <a class="mc-btn mc-icon mc-btn-app-puchase" target="_blank" href="#">
                    <i class="mc-icon-plane"></i>
                    <?php mc_e('Purchase license') ?>
                </a>

            </div>
        </div>
    </div>
<?php } ?>
<?php
function mc_direct_message_box() { ?>
    <div class="mc-lightbox mc-direct-message-box">
        <div class="mc-info"></div>
        <div class="mc-top-bar">
            <div></div>
            <div>
                <a class="mc-close mc-btn-icon mc-btn-red">
                    <i class="mc-icon-close"></i>
                </a>
            </div>
        </div>
        <div class="mc-main mc-scroll-area">
            <div class="mc-title">
                <?php mc_e('User IDs') ?>
            </div>
            <div class="mc-setting mc-type-text mc-first">
                <input class="mc-direct-message-users" type="text" placeholder="<?php mc_e('User IDs separated by commas') ?>" required />
            </div>
            <div class="mc-title mc-direct-message-subject">
                <?php mc_e('Subject') ?>
            </div>
            <div class="mc-setting mc-type-text mc-direct-message-subject">
                <input type="text" placeholder="<?php mc_e('Email subject') ?>" />
            </div>
            <div class="mc-title mc-direct-message-title-subject">
                <?php mc_e('Message') ?>
            </div>
            <div class="mc-setting mc-type-textarea">
                <textarea placeholder="<?php mc_e('Write here your message...') ?>" required></textarea>
            </div>
            <div class="mc-bottom">
                <a class="mc-send-direct-message mc-btn mc-icon">
                    <i class="mc-icon-plane"></i>
                    <?php mc_e('Send message now') ?>
                </a>
                <div></div>
                <?php mc_docs_link('#direct-messages', 'mc-btn-text') ?>
            </div>
        </div>
    </div>
<?php } ?>
<?php
function mc_routing_select($exclude_id = false) {
    $agents = mc_db_get('SELECT id, first_name, last_name FROM mc_users WHERE (user_type = "agent" OR user_type = "admin")' . ($exclude_id ? (' AND id <> ' . mc_db_escape($exclude_id)) : ''), false);
    $code = '<div class="mc-inline mc-inline-agents"><h3>' . mc_('Agent') . '</h3><div id="conversation-agent" class="mc-select"><p>' . mc_('None') . '</p><ul><li data-id="" data-value="">' . mc_('None') . '</li>';
    for ($i = 0; $i < count($agents); $i++) {
        $code .= '<li data-id="' . $agents[$i]['id'] . '">' . $agents[$i]['first_name'] . ' ' . $agents[$i]['last_name'] . '</li>';
    }
    echo $code . '</ul></div></div>';
}
?>
<?php
function mc_installation_box($error = false) {
    global $MC_LANGUAGE;
    $MC_LANGUAGE = isset($_GET['lang']) ? $_GET['lang'] : strtolower(substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2));
    ?>
    <div class="mc-main mc-admin mc-admin-start">
        <form class="mc-intall mc-admin-box">
            <?php if ($error === false || $error == 'installation')
                echo '<div class="mc-info"></div>';
            else
                die('<div class="mc-info mc-active">' . mc_('We\'re having trouble connecting to your database. Please edit the file config.php and check your database connection details. Error: ') . $error . '.</div>'); ?>
            <div class="mc-top-bar">
                <img src="<?php echo (!MC_URL || MC_URL == '[url]' ? '' : MC_URL . '/') ?>media/logo.svg" />
                <div class="mc-title">
                    <?php mc_e('Installation') ?>
                </div>
                <div class="mc-text">
                    <?php mc_e('Please complete the installation process by entering your database connection details below. If you are not sure about this, contact your hosting provider for support.') ?>
                </div>
            </div>
            <div class="mc-main">
                <div id="db-name" class="mc-input">
                    <span>
                        <?php mc_e('Database Name') ?>
                    </span>
                    <input type="text" required />
                </div>
                <div id="db-user" class="mc-input">
                    <span>
                        <?php mc_e('Username') ?>
                    </span>
                    <input type="text" required />
                </div>
                <div id="db-password" class="mc-input">
                    <span>
                        <?php mc_e('Password') ?>
                    </span>
                    <input type="text" />
                </div>
                <div id="db-host" class="mc-input">
                    <span>
                        <?php mc_e('Host') ?>
                    </span>
                    <input type="text" required />
                </div>
                <div id="db-port" class="mc-input">
                    <span>
                        <?php mc_e('Port') ?>
                    </span>
                    <input type="text" placeholder="Default" />
                </div>
                <?php if ($error === false || $error == 'installation') { ?>
                    <div class="mc-text">
                        <?php mc_e('Enter the user details of the main account you will use to login into the administration area. You can update these details later.') ?>
                    </div>
                    <div id="first-name" class="mc-input">
                        <span>
                            <?php mc_e('First name') ?>
                        </span>
                        <input type="text" required />
                    </div>
                    <div id="last-name" class="mc-input">
                        <span>
                            <?php mc_e('Last name') ?>
                        </span>
                        <input type="text" required />
                    </div>
                    <div id="email" class="mc-input">
                        <span>
                            <?php mc_e('Email') ?>
                        </span>
                        <input type="email" required />
                    </div>
                    <div id="password" class="mc-input">
                        <span>
                            <?php mc_e('Password') ?>
                        </span>
                        <input type="password" required />
                    </div>
                    <div id="password-check" class="mc-input">
                        <span>
                            <?php mc_e('Repeat password') ?>
                        </span>
                        <input type="password" required />
                    </div>
                <?php } ?>
                <div class="mc-bottom">
                    <div class="mc-btn mc-submit-installation">
                        <?php mc_e('Complete installation') ?>
                    </div>
                </div>
            </div>
        </form>
    </div>
<?php } ?>
<?php
/*
 * ----------------------------------------------------------
 * ADMIN AREA
 * ----------------------------------------------------------
 *
 * Display the administration area
 *
 */

function mc_component_admin() {
    $is_cloud = mc_is_cloud();
    $cloud_active_apps = $is_cloud ? mc_get_external_setting('active_apps', []) : [];
    $mc_settings = mc_get_json_resource('json/settings.json');
    $active_user = mc_get_active_user(false, true);
    $collapse = mc_get_setting('collapse') ? ' mc-collapse' : '';
    $apps = [
        ['MC_WP', 'wordpress', 'WordPress'],
        ['MC_DIALOGFLOW', 'dialogflow', 'Artificial Intelligence', 'Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.'],
        ['MC_TICKETS', 'tickets', 'Tickets', 'Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.'],
        ['MC_MESSENGER', 'messenger', 'Messenger', 'Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.'],
        ['MC_WHATSAPP', 'whatsapp', 'WhatsApp', 'Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.'],
        ['MC_TWITTER', 'twitter', 'Twitter', 'Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.'],
        ['MC_TELEGRAM', 'telegram', 'Telegram', 'Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.'],
        ['MC_VIBER', 'viber', 'Viber', 'Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.'],
        ['MC_LINE', 'line', 'Line', 'Connect your LINE bot to {R} to read and reply to all messages sent to your LINE bot directly in {R}.'],
        ['MC_WECHAT', 'wechat', 'WeChat', 'Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.'],
        ['MC_ZALO', 'zalo', 'Zalo', 'Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.'],
        ['MC_WOOCOMMERCE', 'woocommerce', 'WooCommerce', 'Increase sales, provide better support, and faster solutions, by integrating WooCommerce with {R}.'],
        ['MC_SLACK', 'slack', 'Slack', 'Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.'],
        ['MC_ZENDESK', 'zendesk', 'Zendesk', 'Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.'],
        ['MC_UMP', 'ump', 'Ultimate Membership Pro', 'Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.'],
        ['MC_PERFEX', 'perfex', 'Perfex', 'Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.'],
        ['MC_WHMCS', 'whmcs', 'Whmcs', 'Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.'],
        ['MC_OPENCART', 'opencart', 'OpenCart', 'Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.'],
        ['MC_AECOMMERCE', 'aecommerce', 'Active eCommerce', 'Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with {R}.'],
        ['MC_ARMEMBER', 'armember', 'ARMember', 'Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.'],
        ['MC_MARTFURY', 'martfury', 'Martfury', 'Increase sales and connect you and sellers with customers in real-time by integrating Martfury with {R}.'],
    ];
    $logged = $active_user && mc_is_agent($active_user) && (!defined('MC_WP') || !mc_get_setting('wp-force-logout') || mc_wp_verify_admin_login());
    $supervisor = mc_supervisor();
    $is_admin = $active_user && mc_is_agent($active_user, true, true) && !$supervisor;
    $sms = mc_get_multi_setting('sms', 'sms-user');
    $css_class = ($logged ? 'mc-admin' : 'mc-admin-start') . (mc_get_setting('rtl-admin') || ($is_cloud && defined('MC_CLOUD_DEFAULT_RTL')) ? ' mc-rtl' : '') . ($is_cloud ? ' mc-cloud' : '') . ($supervisor ? ' mc-supervisor' : '');
    $active_areas = ['users' => $is_admin || (!$supervisor && mc_get_multi_setting('agents', 'agents-users-area')) || ($supervisor && $supervisor['supervisor-users-area']), 'settings' => $is_admin || ($supervisor && $supervisor['supervisor-settings-area']), 'reports' => ($is_admin && !mc_get_multi_setting('performance', 'performance-reports')) || ($supervisor && $supervisor['supervisor-reports-area']), 'articles' => ($is_admin && !mc_get_multi_setting('performance', 'performance-articles')) || ($supervisor && mc_isset($supervisor, 'supervisor-articles-area')) || (!$supervisor && !$is_admin && mc_get_multi_setting('agents', 'agents-articles-area')), 'chatbot' => defined('MC_DIALOGFLOW') && ($is_admin || ($supervisor && $supervisor['supervisor-settings-area'])) && (!$is_cloud || in_array('dialogflow', $cloud_active_apps))];
    $disable_translations = mc_get_setting('admin-disable-settings-translations');
    $admin_colors = [mc_get_setting('color-admin-1'), mc_get_setting('color-admin-2')];
    if ($supervisor && !$supervisor['supervisor-send-message']) {
        echo '<style>.mc-board .mc-conversation .mc-editor,#mc-start-conversation,.mc-top-bar [data-value="sms"],.mc-top-bar [data-value="email"],.mc-menu-users [data-value="message"],.mc-menu-users [data-value="sms"],.mc-menu-users [data-value="email"] { display: none !important; }</style>';
    }
    if ($is_cloud) {
        require_once(MC_CLOUD_PATH . '/account/functions.php');
        $mc_settings = mc_cloud_merge_settings($mc_settings);
        cloud_custom_code();
    } else if (!mc_box_ve()) {
        return;
    }
    if ($admin_colors[0]) {
        $css = '.mc-menu-wide ul li.mc-active, .mc-tab > .mc-nav > ul li.mc-active,.mc-table input[type="checkbox"]:checked, .mc-table input[type="checkbox"]:hover { border-color: ' . $admin_colors[0] . '; }';
        $css .= '.mc-board > .mc-admin-list .mc-scroll-area li.mc-active,.mc-user-conversations > li.mc-active { border-left-color: ' . $admin_colors[0] . '; }';
        $css .= '.mc-setting input:focus, .mc-setting select:focus, .mc-setting textarea:focus, .mc-setting input:focus, .mc-setting select:focus, .mc-setting textarea:focus,.mc-setting.mc-type-upload-image .image:hover, .mc-setting [data-type="upload-image"] .image:hover, .mc-setting.mc-type-upload-image .image:hover, .mc-setting [data-type="upload-image"] .image:hover,.mc-input > input:focus, .mc-input > input.mc-focus, .mc-input > select:focus, .mc-input > select.mc-focus, .mc-input > textarea:focus, .mc-input > textarea.mc-focus,.mc-search-btn > input,.mc-search-btn > input:focus { border-color: ' . $admin_colors[0] . '; box-shadow: 0 0 5px rgb(108 108 108 / 20%);}';
        $css .= '.mc-menu-wide ul li.mc-active, .mc-menu-wide ul li:hover, .mc-tab > .mc-nav > ul li.mc-active, .mc-tab > .mc-nav > ul li:hover,.mc-admin > .mc-header > .mc-admin-nav > div > a:hover, .mc-admin > .mc-header > .mc-admin-nav > div > a.mc-active,.mc-setting input[type="checkbox"]:checked:before, .mc-setting input[type="checkbox"]:checked:before,.mc-language-switcher > i:hover,.mc-admin > .mc-header > .mc-admin-nav-right .mc-account .mc-menu li:hover, .mc-admin > .mc-header > .mc-admin-nav-right .mc-account .mc-menu li.mc-active:hover,.mc-admin > .mc-header > .mc-admin-nav-right > div > a:hover,.mc-search-btn i:hover, .mc-search-btn.mc-active i, .mc-filter-btn i:hover, .mc-filter-btn.mc-active i,.mc-loading:before,.mc-board .mc-conversation > .mc-top a:hover i,.mc-panel-details > i:hover,.mc-board .mc-conversation > .mc-top > a:hover,.mc-btn-text:hover,.mc-table input[type="checkbox"]:checked:before,.mc-profile-list [data-id="wp-id"]:hover, .mc-profile-list [data-id="wp-id"]:hover label, .mc-profile-list [data-id="conversation-source"]:hover, .mc-profile-list [data-id="conversation-source"]:hover label, .mc-profile-list [data-id="location"]:hover, .mc-profile-list [data-id="location"]:hover label, .mc-profile-list [data-id="timezone"]:hover, .mc-profile-list [data-id="timezone"]:hover label, .mc-profile-list [data-id="current_url"]:hover, .mc-profile-list [data-id="current_url"]:hover label, .mc-profile-list [data-id="envato-purchase-code"]:hover, .mc-profile-list [data-id="envato-purchase-code"]:hover label,.mc-board > .mc-admin-list .mc-scroll-area li[data-conversation-status="2"] .mc-time,.mc-select p:hover,div ul.mc-menu li.mc-active:not(:hover), .mc-select ul li.mc-active:not(:hover),.mc-board .mc-conversation .mc-list > div .mc-menu-btn:hover { color: ' . $admin_colors[0] . '; }';
        $css .= '.mc-btn:not(.mc-btn-white), a.mc-btn:not(.mc-btn-white),.mc-area-settings .mc-tab .mc-btn:hover, .daterangepicker td.active, .daterangepicker td.active:hover, .daterangepicker .ranges li.active,div ul.mc-menu li:hover, .mc-select ul li:hover,div.mc-select.mc-select-colors > p:hover,.mc-board > .mc-admin-list .mc-scroll-area li > .mc-notification-counter { background-color: ' . $admin_colors[0] . '; }';
        $css .= '.mc-board > .mc-admin-list.mc-departments-show li.mc-active:before { background-color: ' . $admin_colors[0] . ' !important;}';
        $css .= '.mc-btn-icon:hover,.mc-tags-cnt > span:hover { border-color: ' . $admin_colors[0] . '; color: ' . $admin_colors[0] . '; }';
        $css .= '.mc-btn-icon:hover,.daterangepicker td.in-range { background-color: rgb(151 151 151 / 8%); }';
        $css .= '.mc-board .mc-user-details,.mc-admin > .mc-header,.mc-select.mc-select-colors > p:not([data-value]),.mc-table tr:hover td,.mc-board .mc-user-details .mc-user-conversations li:hover, .mc-board .mc-user-details .mc-user-conversations li.mc-active, .mc-select.mc-select-colors > p[data-value=""], .mc-select.mc-select-colors > p[data-value="-1"] {background-color: #f5f5f5  }';
        $css .= '.mc-board > .mc-admin-list .mc-scroll-area li:hover, .mc-board > .mc-admin-list .mc-scroll-area li.mc-active {background-color: #f5f5f5 !important; }';
        $css .= '.mc-profile-list > ul > li .mc-icon, .mc-profile-list > ul > li > img { color: #424242 }';
        $css .= '.mc-area-settings .mc-tab .mc-btn:hover, .mc-btn-white:hover, .mc-lightbox .mc-btn-white:hover { background-color: ' . $admin_colors[0] . '; border-color: ' . $admin_colors[0] . ';}';
        if ($admin_colors[1]) {
            $css .= '.mc-btn:hover, .mc-btn:active, a.mc-btn:hover, a.mc-btn:active { background-color: ' . $admin_colors[1] . '}';
        }
        echo '<style>' . $css . '</style>';
    }
    ?>
    <div class="mc-main <?php echo $css_class ?>" style="opacity: 0">
        <?php if ($logged) { ?>
            <div class="mc-header">
                <div class="mc-admin-nav">
                    <img src="<?php echo $is_cloud ? MC_CLOUD_BRAND_ICON : mc_get_setting('admin-icon', MC_URL . '/media/icon.svg') ?>" />
                    <div>
                        <a id="mc-conversations" class="mc-active">
                            <span>
                                <?php mc_e('Conversations') ?>
                            </span>
                        </a>
                        <?php
                        if ($active_areas['users']) {
                            echo '<a id="mc-users"><span>' . mc_('Users') . '</span></a>';
                        }
                        if ($active_areas['chatbot']) {
                            echo '<a id="mc-chatbot"><span>' . mc_('Chatbot') . '</span></a>';
                        }
                        if ($active_areas['articles']) {
                            echo '<a id="mc-articles"><span>' . mc_('Articles') . '</span></a>';
                        }
                        if ($active_areas['reports']) {
                            echo '<a id="mc-reports"><span>' . mc_('Reports') . '</span></a>';
                        }
                        if ($active_areas['settings']) {
                            echo '<a id="mc-settings"><span>' . mc_('Settings') . '</span></a>';
                        }
                        ?>
                    </div>
                </div>
                <div class="mc-admin-nav-right mc-menu-mobile">
                    <i class="mc-icon-menu"></i>
                    <div class="mc-desktop">
                        <div class="mc-account">
                            <img src="<?php echo MC_URL ?>/media/user.svg" />
                            <div>
                                <a class="mc-profile">
                                    <img src="<?php echo MC_URL ?>/media/user.svg" />
                                    <span class="mc-name"></span>
                                </a>
                                <ul class="mc-menu">
                                    <li data-value="status" class="mc-online">
                                        <?php mc_e('Online') ?>
                                    </li>
                                    <?php
                                    if ($is_admin) {
                                        echo '<li data-value="edit-profile">' . mc_('Edit profile') . '</li>' . ($is_cloud ? mc_cloud_account_menu() : '');
                                    }
                                    ?>
                                    <li data-value="logout">
                                        <?php mc_e('Logout') ?>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <?php
                        if ($is_admin) {
                            mc_docs_link();
                            echo '<a href="#" class="mc-version">' . MC_VERSION . '</a>';
                        }
                        ?>
                    </div>
                    <div class="mc-mobile">
                        <?php
                        if ($is_admin || (!$supervisor && mc_get_multi_setting('agents', 'agents-edit-user')) || ($supervisor && $supervisor['supervisor-edit-user'])) {
                            echo '<a href="#" class="edit-profile">' . mc_('Edit profile') . '</a>' . ($is_cloud ? mc_cloud_account_menu('a') : '<a href="#" class="mc-docs">' . mc_('Docs') . '</a>') . '<a href="#" class="mc-version">' . mc_('Updates') . '</a>';
                        }
                        ?>
                        <a href="#" class="mc-online" data-value="status">
                            <?php mc_e('Online') ?>
                        </a>
                        <a href="#" class="logout">
                            <?php mc_e('Logout') ?>
                        </a>
                    </div>
                </div>
            </div>
            <main>
                <div class="mc-active mc-area-conversations">
                    <div class="mc-board">
                        <div class="mc-admin-list<?php echo mc_get_multi_setting('departments-settings', 'departments-show-list') ? ' mc-departments-show' : '' ?>">
                            <div class="mc-top">
                                <div class="mc-select">
                                    <p data-value="0">
                                        <?php mc_e('Inbox') ?><span></span>
                                    </p>
                                    <ul>
                                        <li data-value="0" class="mc-active">
                                            <?php mc_e('Inbox') ?>
                                            <span></span>
                                        </li>
                                        <li data-value="3">
                                            <?php mc_e('Archive') ?>
                                        </li>
                                        <li data-value="4">
                                            <?php mc_e('Trash') ?>
                                        </li>
                                    </ul>
                                </div>
                                <div class="mc-flex">
                                    <?php mc_conversations_filter($cloud_active_apps) ?>
                                    <div class="mc-search-btn">
                                        <i class="mc-icon mc-icon-search"></i>
                                        <input type="text" autocomplete="false" placeholder="<?php mc_e('Search for keywords or users...') ?>" />
                                    </div>
                                </div>
                            </div>
                            <div class="mc-scroll-area">
                                <ul></ul>
                            </div>
                        </div>
                        <div class="mc-conversation">
                            <div class="mc-top">
                                <i class="mc-btn-back mc-icon-arrow-left"></i>
                                <a></a>
                                <div class="mc-labels"></div>
                                <div class="mc-menu-mobile">
                                    <i class="mc-icon-menu"></i>
                                    <ul>
                                        <li>
                                            <a data-value="archive" class="mc-btn-icon" data-mc-tooltip="<?php mc_e('Archive conversation') ?>">
                                                <i class="mc-icon-check"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a data-value="read" class="mc-btn-icon" data-mc-tooltip="<?php mc_e('Mark as read') ?>">
                                                <i class="mc-icon-check-circle"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a data-value="transcript" class="mc-btn-icon" data-mc-tooltip="<?php mc_e('Transcript') ?>" data-action="<?php echo mc_get_multi_setting('transcript', 'transcript-action') ?>">
                                                <i class="mc-icon-download"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a data-value="inbox" class="mc-btn-icon" data-mc-tooltip="<?php mc_e('Send to inbox') ?>">
                                                <i class="mc-icon-back"></i>
                                            </a>
                                        </li>
                                        <?php
                                        if ($is_admin || (!$supervisor && mc_get_multi_setting('agents', 'agents-delete-conversation')) || ($supervisor && $supervisor['supervisor-delete-conversation'])) {
                                            echo '<li><a data-value="delete" class="mc-btn-icon mc-btn-red" data-mc-tooltip="' . mc_('Delete conversation') . '"><i class="mc-icon-delete"></i></a></li><li><a data-value="empty-trash" class="mc-btn-icon mc-btn-red" data-mc-tooltip="' . mc_('Empty trash') . '"><i class="mc-icon-delete"></i></a></li>';
                                        }
                                        ?>
                                        <li>
                                            <a data-value="panel" class="mc-btn-icon" data-mc-tooltip="<?php mc_e('Details') ?>">
                                                <i class="mc-icon-info"></i>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="mc-label-date-top"></div>
                            </div>
                            <div class="mc-list"></div>
                            <?php mc_component_editor(true); ?>
                            <div class="mc-no-conversation-message">
                                <div>
                                    <label>
                                        <?php mc_e('Select a conversation') ?>
                                    </label>
                                    <p>
                                        <?php mc_e('Select a conversation from the left menu.') ?>
                                    </p>
                                </div>
                            </div>
                            <audio id="mc-audio" preload="auto"><source src="<?php echo mc_get_multi_setting('sound-settings', 'sound-settings-file-admin', MC_URL . '/media/sound.mp3') ?>" type="audio/mpeg"></audio>
                        </div>
                        <div class="mc-user-details">
                            <div class="mc-top">
                                <div class="mc-profile">
                                    <img src="<?php echo MC_URL ?>/media/user.svg" />
                                    <span class="mc-name"></span>
                                </div>
                            </div>
                            <div class="mc-scroll-area">
                                <a class="mc-user-details-close mc-close mc-btn-icon mc-btn-red">
                                    <i class="mc-icon-close"></i>
                                </a>
                                <div class="mc-profile-list mc-profile-list-conversation<?php echo $collapse ?>"></div>
                                <?php
                                mc_apps_panel();
                                mc_departments('custom-select');
                                if (mc_get_multi_setting('routing', 'routing-active') || (mc_get_multi_setting('agent-hide-conversations', 'agent-hide-conversations-active') && mc_get_multi_setting('agent-hide-conversations', 'agent-hide-conversations-menu'))) {
                                    mc_routing_select();
                                }
                                if (!mc_get_multi_setting('disable', 'disable-notes')) {
                                    echo '<div class="mc-panel-details mc-panel-notes' . $collapse . '"><i class="mc-icon-plus"></i><h3>' . mc_('Notes') . '</h3><div></div></div>';
                                }
                                if (!mc_get_multi_setting('disable', 'disable-tags')) {
                                    echo '<div class="mc-panel-details mc-panel-tags"><i class="mc-icon-settings"></i><h3>' . mc_('Tags') . '</h3><div></div></div>';
                                }
                                if (!mc_get_multi_setting('disable', 'disable-attachments')) {
                                    echo '<div class="mc-panel-details mc-panel-attachments mc-collapse"></div>';
                                }
                                ?>
                                <h3 class="mc-hide">
                                    <?php mc_e('User conversations') ?>
                                </h3>
                                <ul class="mc-user-conversations"></ul>
                            </div>
                            <div class="mc-no-conversation-message"></div>
                        </div>
                    </div>
                    <i class="mc-btn-collapse mc-left mc-icon-arrow-left"></i>
                    <i class="mc-btn-collapse mc-right mc-icon-arrow-right"></i>
                </div>
                <?php if ($active_areas['users']) { ?>
                    <div class="mc-area-users">
                        <div class="mc-top-bar">
                            <div>
                                <h2>
                                    <?php mc_e('Users list') ?>
                                </h2>
                                <div class="mc-menu-wide mc-menu-users">
                                    <div>
                                        <?php mc_e('All') ?>
                                        <span data-count="0"></span>
                                    </div>
                                    <ul>
                                        <li data-type="all" class="mc-active">
                                            <?php mc_e('All') ?>
                                            <span data-count="0">(0)</span>
                                        </li>
                                        <li data-type="user">
                                            <?php mc_e('Users') ?>
                                            <span data-count="0">(0)</span>
                                        </li>
                                        <li data-type="lead">
                                            <?php mc_e('Leads') ?>
                                            <span data-count="0">(0)</span>
                                        </li>
                                        <li data-type="visitor">
                                            <?php mc_e('Visitors') ?>
                                            <span data-count="0">(0)</span>
                                        </li>
                                        <li data-type="online">
                                            <?php mc_e('Online') ?>
                                        </li>
                                        <?php
                                        if ($is_admin || (!$supervisor && mc_get_multi_setting('agents', 'agents-tab')) || ($supervisor && $supervisor['supervisor-agents-tab'])) {
                                            echo '<li data-type="agent">' . mc_('Agents & Admins') . '</li>';
                                        }
                                        ?>
                                    </ul>
                                </div>
                                <div class="mc-menu-mobile">
                                    <i class="mc-icon-menu"></i>
                                    <ul>
                                        <?php
                                        if ($is_admin) {
                                            echo '<li><a data-value="csv" class="mc-btn-icon" data-mc-tooltip="' . mc_('Download CSV') . '"><i class="mc-icon-download"></i></a></li>';
                                        }
                                        ?>
                                        <li>
                                            <a data-value="message" class="mc-btn-icon" data-mc-tooltip="<?php mc_e('Send a message') ?>">
                                                <i class="mc-icon-chat"></i>
                                            </a>
                                        </li>
                                        <li>
                                            <a data-value="custom_email" class="mc-btn-icon" data-mc-tooltip="<?php mc_e('Send an email') ?>">
                                                <i class="mc-icon-envelope"></i>
                                            </a>
                                        </li>
                                        <?php
                                        if ($sms) {
                                            echo '<li><a data-value="sms" class="mc-btn-icon" data-mc-tooltip="' . mc_('Send a text message') . '"><i class="mc-icon-sms"></i></a><li>';
                                        }
                                        if (defined('MC_WHATSAPP') && (!function_exists('mc_whatsapp_active') || mc_whatsapp_active())) {
                                            echo '<li style="display:none;"><a data-value="whatsapp" class="mc-btn-icon" data-mc-tooltip="' . mc_('Send a WhatsApp message template') . '"><i class="mc-icon-social-wa"></i></a><li>'; // Initially hidden, will be shown if templates exist
                                        }
                                        if ($is_admin) {
                                            echo '<li><a data-value="delete" class="mc-btn-icon mc-btn-red" data-mc-tooltip="' . mc_('Delete users') . '" style="display: none;"><i class="mc-icon-delete"></i></a></li>';
                                        }
                                        ?>
                                    </ul>
                                </div>
                                <?php mc_conversations_filter($cloud_active_apps) ?>
                            </div>
                            <div>
                                <div class="mc-search-btn">
                                    <i class="mc-icon mc-icon-search"></i>
                                    <input type="text" autocomplete="false" placeholder="<?php mc_e('Search users ...') ?>" />
                                </div>
                                <a class="mc-btn mc-icon mc-new-user">
                                    <i class="mc-icon-user"></i>
                                    <?php mc_e('Add new user') ?>
                                </a>
                            </div>
                        </div>
                        <div class="mc-scroll-area">
                            <table class="mc-table mc-table-users">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" />
                                        </th>
                                        <th data-field="first_name">
                                            <?php mc_e('Full name') ?>
                                        </th>
                                        <?php mc_users_table_extra_fields() ?>
                                        <th data-field="email">
                                            <?php mc_e('Email') ?>
                                        </th>
                                        <th data-field="user_type">
                                            <?php mc_e('Type') ?>
                                        </th>
                                        <th data-field="last_activity">
                                            <?php mc_e('Last activity') ?>
                                        </th>
                                        <th data-field="creation_time" class="mc-active">
                                            <?php mc_e('Registration date') ?>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                <?php } ?>
                <?php
                if ($active_areas['chatbot']) {
                    require_once(MC_PATH . '/apps/dialogflow/components.php');
                    mc_dialogflow_chatbot_area();
                }
                ?>
                <?php if ($active_areas['articles']) { ?>
                    <div class="mc-area-articles mc-loading">
                        <div class="mc-top-bar">
                            <div>
                                <h2>
                                    <?php mc_e('Articles') ?>
                                </h2>
                                <div class="mc-menu-wide mc-menu-articles">
                                    <div>
                                        <?php mc_e('Articles') ?>
                                    </div>
                                    <ul>
                                        <li data-type="articles" class="mc-active">
                                            <?php mc_e('Articles') ?>
                                        </li>
                                        <li data-type="categories">
                                            <?php mc_e('Categories') ?>
                                        </li>
                                        <li data-type="settings">
                                            <?php mc_e('Settings') ?>
                                        </li>
                                        <?php
                                        if ($active_areas['reports']) {
                                            echo '<li data-type="reports">' . mc_('Reports') . '</li>';
                                        }
                                        mc_docs_link('#articles');
                                        ?>
                                    </ul>
                                </div>
                            </div>
                            <div>
                                <a class="mc-btn-icon mc-view-article" href="" target="_blank">
                                    <i class="mc-icon-next"></i>
                                </a>
                                <a class="mc-btn mc-save-articles mc-icon">
                                    <i class="mc-icon-check"></i>
                                    <?php mc_e('Save changes') ?>
                                </a>
                            </div>
                        </div>
                        <div class="mc-tab mc-inner-tab">
                            <div class="mc-nav mc-nav-only mc-scroll-area">
                                <ul class="ul-articles"></ul>
                                <ul class="ul-categories"></ul>
                                <div class="mc-add-category mc-btn mc-icon mc-btn-white">
                                    <i class="mc-icon-plus"></i>
                                    <?php mc_e('Add new category') ?>
                                </div>
                                <div class="mc-add-article mc-btn mc-icon mc-btn-white">
                                    <i class="mc-icon-plus"></i>
                                    <?php mc_e('Add new article') ?>
                                </div>
                            </div>
                            <div class="mc-content mc-content-articles mc-scroll-area mc-loading">
                                <h2 class="mc-language-switcher-cnt">
                                    <?php mc_e('Title') ?>
                                </h2>
                                <div class="mc-setting mc-type-text mc-article-title">
                                    <div>
                                        <input type="text" />
                                    </div>
                                </div>
                                <h2>
                                    <?php mc_e('Content') ?>
                                </h2>
                                <div class="mc-setting mc-type-textarea mc-article-content">
                                    <div>
                                        <?php echo mc_get_setting('disable-editor-js') ? '<textarea></textarea>' : '<div id="editorjs"></div>' ?>
                                    </div>
                                </div>
                                <h2>
                                    <?php mc_e('External link') ?>
                                </h2>
                                <div class="mc-setting mc-type-text mc-article-link">
                                    <div>
                                        <input type="text" />
                                    </div>
                                </div>
                                <div class="mc-article-categories mc-grid">
                                    <div>
                                        <h2>
                                            <?php mc_e('Parent category') ?>
                                        </h2>
                                        <div class="mc-setting mc-type-select">
                                            <div>
                                                <select id="article-parent-categories"></select>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h2>
                                            <?php mc_e('Categories') ?>
                                        </h2>
                                        <div class="mc-setting mc-type-select">
                                            <div>
                                                <select id="article-categories"></select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <h2 id="mc-article-id"></h2>
                            </div>
                            <div class="mc-content mc-content-categories mc-scroll-area mc-loading">
                                <h2 class="mc-language-switcher-cnt">
                                    <?php mc_e('Name') ?>
                                </h2>
                                <div class="mc-setting mc-type-text">
                                    <div>
                                        <input id="category-title" type="text" />
                                    </div>
                                </div>
                                <h2>
                                    <?php mc_e('Description') ?>
                                </h2>
                                <div class="mc-setting mc-type-textarea">
                                    <div>
                                        <textarea id="category-description"></textarea>
                                    </div>
                                </div>
                                <h2>
                                    <?php mc_e('Image') ?>
                                </h2>
                                <div data-type="image" class="mc-input mc-setting mc-input-image">
                                    <div id="category-image" class="image">
                                        <div class="mc-icon-close"></div>
                                    </div>
                                </div>
                                <h2 class="category-parent">
                                    <?php mc_e('Parent category') ?>
                                </h2>
                                <div data-type="checkbox" class="mc-setting mc-type-checkbox category-parent">
                                    <div class="input">
                                        <input id="category-parent" type="checkbox" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <?php if ($active_areas['reports']) { ?>
                    <div class="mc-area-reports mc-loading">
                        <div class="mc-top-bar">
                            <div>
                                <h2>
                                    <?php mc_e('Reports') ?>
                                </h2>
                            </div>
                            <div>
                                <div class="mc-setting mc-type-text">
                                    <input id="mc-date-picker" placeholder="00/00/0000 - 00/00/0000" type="text" />
                                </div>
                                <div class="mc-report-export mc-btn-icon">
                                    <i class="mc-icon-download"></i>
                                </div>
                            </div>
                        </div>
                        <div class="mc-tab">
                            <div class="mc-nav mc-nav-only mc-scroll-area">
                                <div>
                                    <?php mc_e('Reports') ?>
                                </div>
                                <ul>
                                    <li class="mc-tab-nav-title">
                                        <?php mc_e('Conversations') ?>
                                    </li>
                                    <li id="conversations" class="mc-active">
                                        <?php mc_e('Conversations') ?>
                                    </li>
                                    <li id="missed-conversations">
                                        <?php mc_e('Missed conversations') ?>
                                    </li>
                                    <li id="conversations-time">
                                        <?php mc_e('Conversations time') ?>
                                    </li>
                                    <li class="mc-tab-nav-title">
                                        <?php mc_e('Direct messages') ?>
                                    </li>
                                    <li id="direct-messages">
                                        <?php mc_e('Chat messages') ?>
                                    </li>
                                    <li id="direct-emails">
                                        <?php mc_e('Emails') ?>
                                    </li>
                                    <li id="direct-sms">
                                        <?php mc_e('Text messages') ?>
                                    </li>
                                    <li class="mc-tab-nav-title">
                                        <?php mc_e('Users and agents') ?>
                                    </li>
                                    <li id="visitors">
                                        <?php mc_e('Visitors') ?>
                                    </li>
                                    <li id="leads">
                                        <?php mc_e('Leads') ?>
                                    </li>
                                    <li id="users">
                                        <?php mc_e('Users') ?>
                                    </li>
                                    <li id="registrations">
                                        <?php mc_e('Registrations') ?>
                                    </li>
                                    <li id="agents-response-time">
                                        <?php mc_e('Agent response time') ?>
                                    </li>
                                    <li id="agents-conversations">
                                        <?php mc_e('Agent conversations') ?>
                                    </li>
                                    <li id="agents-conversations-time">
                                        <?php mc_e('Agent conversations time') ?>
                                    </li>
                                    <li id="agents-ratings">
                                        <?php mc_e('Agent ratings') ?>
                                    </li>
                                    <li id="countries">
                                        <?php mc_e('Countries') ?>
                                    </li>
                                    <li id="languages">
                                        <?php mc_e('Languages') ?>
                                    </li>
                                    <li id="browsers">
                                        <?php mc_e('Browsers') ?>
                                    </li>
                                    <li id="os">
                                        <?php mc_e('Operating systems') ?>
                                    </li>
                                    <li class="mc-tab-nav-title">
                                        <?php mc_e('Automation') ?>
                                    </li>
                                    <li id="follow-up">
                                        <?php mc_e('Follow up') ?>
                                    </li>
                                    <li id="message-automations">
                                        <?php mc_e('Message automations') ?>
                                    </li>
                                    <li id="email-automations">
                                        <?php mc_e('Email automations') ?>
                                    </li>
                                    <?php
                                    if ($sms) {
                                        echo '<li id="sms-automations">' . mc_('Text message automations') . '</li>';
                                    }
                                    ?>
                                    <li class="mc-tab-nav-title">
                                        <?php mc_e('Articles') ?>
                                    </li>
                                    <li id="articles-searches">
                                        <?php mc_e('Searches') ?>
                                    </li>
                                    <li id="articles-views">
                                        <?php mc_e('Article views') ?>
                                    </li>
                                    <li id="articles-views-single">
                                        <?php mc_e('Article views by article') ?>
                                    </li>
                                    <li id="articles-ratings">
                                        <?php mc_e('Article ratings') ?>
                                    </li>
                                </ul>
                            </div>
                            <div class="mc-content mc-scroll-area">
                                <div class="mc-reports-chart">
                                    <div class="chart-cnt">
                                        <canvas></canvas>
                                    </div>
                                </div>
                                <div class="mc-reports-sidebar">
                                    <div class="mc-title mc-reports-title"></div>
                                    <p class="mc-reports-text"></p>
                                    <div class="mc-collapse">
                                        <div>
                                            <table class="mc-table"></table>
                                        </div>
                                    </div>
                                </div>
                                <p class="mc-no-results">
                                    <?php echo mc_('No data found.') ?>
                                </p>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <?php if ($active_areas['settings']) { ?>
                    <div class="mc-area-settings">
                        <div class="mc-top-bar">
                            <div>
                                <h2>
                                    <?php mc_e('Settings') ?>
                                </h2>
                            </div>
                            <div>
                                <div class="mc-search-dropdown">
                                    <div class="mc-search-btn">
                                        <i class="mc-icon mc-icon-search"></i>
                                        <input id="mc-search-settings" type="text" autocomplete="false" placeholder="<?php mc_e('Search ...') ?>" />
                                    </div>
                                    <div class="mc-search-dropdown-items"></div>
                                </div>
                                <a class="mc-btn mc-save-changes mc-icon">
                                    <i class="mc-icon-check"></i>
                                    <?php mc_e('Save changes') ?>
                                </a>
                            </div>
                        </div>
                        <div class="mc-tab">
                            <div class="mc-nav mc-scroll-area">
                                <div>
                                    <?php mc_e('Settings') ?>
                                </div>
                                <ul>
                                    <li id="tab-chat" class="mc-active">
                                        <?php echo $disable_translations ? 'Chat' : mc_('Chat') ?>
                                    </li>
                                    <li id="tab-admin">
                                        <?php echo $disable_translations ? 'Admin' : mc_('Admin') ?>
                                    </li>
                                    <li id="tab-notifications">
                                        <?php echo $disable_translations ? 'Notifications' : mc_('Notifications') ?>
                                    </li>
                                    <li id="tab-users">
                                        <?php echo $disable_translations ? 'Users' : mc_('Users') ?>
                                    </li>
                                    <li id="tab-design">
                                        <?php echo $disable_translations ? 'Design' : mc_('Design') ?>
                                    </li>
                                    <li id="tab-messages">
                                        <?php echo $disable_translations ? 'Messages & Forms' : mc_('Messages & Forms') ?>
                                    </li>
                                    <li id="tab-various">
                                        <?php echo $disable_translations ? 'Miscellaneous' : mc_('Miscellaneous') ?>
                                    </li>
                                    <?php
                                    for ($i = 0; $i < count($apps); $i++) {
                                        if (defined($apps[$i][0]) && (!$is_cloud || in_array($apps[$i][1], $cloud_active_apps))) {
                                            echo '<li id="tab-' . $apps[$i][1] . '">' . mc_($apps[$i][2]) . '</li>';
                                        }
                                    }
                                    ?>
                                    <li id="tab-apps">
                                        <?php echo $disable_translations ? 'Apps' : mc_('Apps') ?>
                                    </li>
                                    <li id="tab-articles">
                                        <?php echo $disable_translations ? 'Articles' : mc_('Articles') ?>
                                    </li>
                                    <li id="tab-automations">
                                        <?php echo $disable_translations ? 'Automations' : mc_('Automations') ?>
                                    </li>
                                    <li id="tab-translations">
                                        <?php echo $disable_translations ? 'Translations' : mc_('Translations') ?>
                                    </li>
                                </ul>
                            </div>
                            <div class="mc-content mc-scroll-area">
                                <div class="mc-active">
                                    <?php mc_populate_settings('chat', $mc_settings) ?>
                                </div>
                                <div>
                                    <?php mc_populate_settings('admin', $mc_settings) ?>
                                </div>
                                <div>
                                    <?php mc_populate_settings('notifications', $mc_settings) ?>
                                </div>
                                <div>
                                    <?php mc_populate_settings('users', $mc_settings) ?>
                                </div>
                                <div>
                                    <?php mc_populate_settings('design', $mc_settings) ?>
                                </div>
                                <div>
                                    <?php mc_populate_settings('messages', $mc_settings) ?>
                                </div>
                                <div>
                                    <?php mc_populate_settings('miscellaneous', $mc_settings) ?>
                                </div>
                                <?php mc_apps_area($apps, $cloud_active_apps) ?>
                                <div>
                                    <?php mc_populate_settings('articles', $mc_settings) ?>
                                </div>
                                <div>
                                    <div class="mc-automations-area">
                                        <div class="mc-select">
                                            <p data-value="messages">
                                                <?php mc_e('Messages') ?>
                                            </p>
                                            <ul>
                                                <li data-value="messages" class="mc-active">
                                                    <?php mc_e('Messages') ?>
                                                </li>
                                                <li data-value="emails">
                                                    <?php mc_e('Emails') ?>
                                                </li>
                                                <?php if ($sms)
                                                    echo '<li data-value="sms">' . mc_('Text messages') . '</li>' ?>
                                                    <li data-value="popups">
                                                    <?php mc_e('Pop-ups') ?>
                                                </li>
                                                <li data-value="design">
                                                    <?php mc_e('Design') ?>
                                                </li>
                                                <li data-value="more">
                                                    <?php mc_e('More') ?>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="mc-inner-tab mc-tab">
                                            <div class="mc-nav mc-nav-only">
                                                <ul></ul>
                                                <div class="mc-add-automation mc-btn mc-icon">
                                                    <i class="mc-icon-plus"></i>
                                                    <?php mc_e('Add new automation') ?>
                                                </div>
                                            </div>
                                            <div class="mc-content mc-hide">
                                                <div class="mc-automation-values">
                                                    <h2 class="mc-language-switcher-cnt">
                                                        <?php mc_e('Name') ?>
                                                    </h2>
                                                    <div class="mc-setting mc-type-text">
                                                        <div>
                                                            <input data-id="name" type="text" />
                                                        </div>
                                                    </div>
                                                    <h2>
                                                        <?php mc_e('Message') ?>
                                                    </h2>
                                                    <div class="mc-setting mc-type-textarea">
                                                        <div>
                                                            <textarea data-id="message"></textarea>
                                                        </div>
                                                    </div>
                                                    <div class="mc-automation-extra"></div>
                                                </div>
                                                <div class="mc-automation-conditions">
                                                    <hr />
                                                    <h2>
                                                        <?php mc_e('Conditions') ?>
                                                    </h2>
                                                    <div class="mc-conditions"></div>
                                                    <div class="mc-add-condition mc-btn mc-icon">
                                                        <i class="mc-icon-plus"></i>
                                                        <?php mc_e('Add condition') ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="mc-translations mc-tab">
                                        <div class="mc-nav mc-nav-only">
                                            <div class="mc-active"></div>
                                            <ul></ul>
                                        </div>
                                        <div class="mc-content">
                                            <div class="mc-hide">
                                                <div class="mc-menu-wide">
                                                    <div>
                                                        <?php mc_e('Front End') ?>
                                                    </div>
                                                    <ul>
                                                        <li data-value="front" class="mc-active">
                                                            <?php mc_e('Front End') ?>
                                                        </li>
                                                        <li data-value="admin">
                                                            <?php mc_e('Admin') ?>
                                                        </li>
                                                        <li data-value="admin/js">
                                                            <?php mc_e('Client side admin') ?>
                                                        </li>
                                                        <li data-value="admin/settings">
                                                            <?php mc_e('Settings') ?>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <a class="mc-btn mc-icon mc-add-translation">
                                                    <i class="mc-icon-plus"></i>
                                                    <?php mc_e('New translation') ?>
                                                </a>
                                            </div>
                                            <div class="mc-translations-list"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </main>
            <?php
            mc_profile_box();
            mc_profile_edit_box();
            mc_dialog();
            mc_direct_message_box();
            mc_app_box();
            if (defined('MC_DIALOGFLOW')) {
                require_once(MC_PATH . '/apps/dialogflow/components.php');
                mc_dialogflow_intent_box();
            }
            if (defined('MC_WHATSAPP')) {
                mc_whatsapp_send_template_box();
            }
            if ($is_admin && !$is_cloud) {
                mc_updates_box();
            }
            ?>
            <div id="mc-generic-panel"></div>
            <form class="mc-upload-form-admin mc-upload-form" action="<?php echo mc_sanatize_string($_SERVER['PHP_SELF']) ?>" method="post" enctype="multipart/form-data">
                <input type="file" name="files[]" class="mc-upload-files" multiple />
            </form>
            <div class="mc-info-card"></div>
            <?php
        } else {
            if ($is_cloud) {
                mc_cloud_reset_login();
            } else {
                mc_login_box();
            }
        }
        ?>
        <div class="mc-lightbox mc-lightbox-media">
            <div></div>
            <i class="mc-icon-close"></i>
        </div>
        <div class="mc-lightbox-overlay"></div>
        <div class="mc-loading-global mc-loading mc-lightbox"></div>
        <input type="email" name="email" style="display:none" autocomplete="email" />
        <input type="password" name="hidden" style="display:none" autocomplete="new-password" />
    </div>
    <?php
    // Custom JS and CSS loading removed for SaaS version
    if ($is_cloud) {
        mc_cloud_css_js();
    }
}

/*
 * ----------------------------------------------------------
 * HTML FUNCTIONS
 * ----------------------------------------------------------
 *
 * 1. Echo the apps settings and apps area
 * 2. Echo the apps conversation panel container
 * 3. Code check
 * 4. Return the users table extra fields
 * 5. Return the Dialogflow languages list
 * 6. Return the conversations filter
 *
 */

function mc_apps_area($apps, $cloud_active_apps) {
    $apps_wp = ['MC_WP', 'MC_WOOCOMMERCE', 'MC_UMP', 'MC_ARMEMBER'];
    $apps_php = [];
    $apps_cloud_excluded = ['whmcs', 'martfury', 'aecommerce', 'perfex', 'opencart'];
    $wp = defined('MC_WP');
    $code = '';
    $is_cloud = mc_is_cloud();
    for ($i = 0; $i < count($apps); $i++) {
        if (defined($apps[$i][0]) && (!$is_cloud || in_array($apps[$i][1], $cloud_active_apps))) {
            $code .= '<div>' . mc_populate_app_settings($apps[$i][1]) . '</div>';
        }
    }
    $code .= '<div><div class="mc-apps">';
    for ($i = 1; $i < count($apps); $i++) {
        if ((($wp && !in_array($apps[$i][0], $apps_php)) || (!$wp && !in_array($apps[$i][0], $apps_wp))) && (!$is_cloud || !in_array($apps[$i][1], $apps_cloud_excluded))) {
            $code .= '<div data-app="' . $apps[$i][1] . '">' . (defined($apps[$i][0]) && (!$is_cloud || in_array($apps[$i][1], $cloud_active_apps)) ? '<i class="mc-icon-check"></i>' : '') . ' <img src="' . MC_URL . '/media/apps/' . $apps[$i][1] . '.svg" /><h2>' . $apps[$i][2] . '</h2><p>' . str_replace('{R}', $is_cloud ? MC_CLOUD_BRAND_NAME : 'Masi Chat', mc_s($apps[$i][3])) . '</p></div>';
        }
    }
    echo $code . '</div></div>';
}

function mc_apps_panel() {
    $code = '';
    $collapse = mc_get_setting('collapse') ? ' mc-collapse' : '';
    $panels = [['MC_UMP', 'ump'], ['MC_WOOCOMMERCE', 'woocommerce'], ['MC_PERFEX', 'perfex'], ['MC_WHMCS', 'whmcs'], ['MC_AECOMMERCE', 'aecommerce'], ['MC_ARMEMBER', 'armember'], ['MC_ZENDESK', 'zendesk'], ['MC_MARTFURY', 'martfury'], ['MC_OPENCART', 'opencart']];
    for ($i = 0; $i < count($panels); $i++) {
        if (defined($panels[$i][0])) {
            $code .= '<div class="mc-panel-details mc-panel-' . $panels[$i][1] . $collapse . '"></div>';
        }
    }
    if (mc_is_cloud()) {
        $code .= '<div class="mc-panel-details mc-panel-shopify' . $collapse . '"></div>';
    }
    echo $code;
}

function mc_box_ve() {
    if ((!isset($_COOKIE['SA_' . 'VGC' . 'KMENS']) && !isset($_COOKIE['_ga_' . 'VGC' . 'KMENS'])) || !password_verify('VGC' . 'KMENS', isset($_COOKIE['_ga_' . 'VGC' . 'KMENS']) ? $_COOKIE['_ga_' . 'VGC' . 'KMENS'] : $_COOKIE['SA_' . 'VGC' . 'KMENS'])) { // Deprecated. _ga will be removed
        echo file_get_contents(MC_PATH . '/resources/mc.html');
        return false;
    }
    return true;
}

function mc_users_table_extra_fields() {
    $extra_fields = mc_get_setting('user-table-extra-columns');
    $count = $extra_fields && !is_string($extra_fields) ? count($extra_fields) : false;
    if ($count) {
        $code = '';
        for ($i = 0; $i < $count; $i++) {
            $slug = $extra_fields[$i]['user-table-extra-slug'];
            $code .= '<th data-field="' . $slug . '" data-extra="true">' . mc_string_slug($slug, 'string') . '</th>';
        }
        echo $code;
    }
}

function mc_dialogflow_languages_list() {
    $languages = json_decode(file_get_contents(MC_PATH . '/apps/dialogflow/dialogflow_languages.json'), true);
    $code = '<div data-type="select" class="mc-setting mc-type-select mc-dialogflow-languages"><div class="input"><select><option value="">' . mc_('Default') . '</option>';
    for ($i = 0; $i < count($languages); $i++) {
        $code .= '<option value="' . $languages[$i][1] . '">' . $languages[$i][0] . '</option>';
    }
    return $code . '</select></div></div>';
}

function mc_conversations_filter($cloud_active_apps) {
    if (mc_get_multi_setting('disable', 'disable-filters')) {
        return;
    }
    $is_cloud = mc_is_cloud();
    $departments = mc_is_agent(false, true, true) || !mc_isset(mc_get_active_user(), 'department') ? mc_get_setting('departments', []) : [];
    $sources = [['em', 'Email', true], ['tk', 'Tickets', 'MC_TICKETS'], ['wa', 'WhatsApp', 'MC_WHATSAPP'], ['fb', 'Messenger', 'MC_MESSENGER'], ['ig', 'Instagram', 'MC_MESSENGER'], ['tg', 'Telegram', 'MC_TELEGRAM'], ['tw', 'Twitter', 'MC_TWITTER'], ['vb', 'Viber', 'MC_VIBER'], ['ln', 'LINE', 'MC_LINE'], ['wc', 'WeChat', 'MC_WECHAT'], ['za', 'Zalo', 'MC_ZALO'], ['tm', 'Text message', true]];
    $tags = mc_get_multi_setting('disable', 'disable-tags') ? [] : mc_get_setting('tags', []);
    $count = is_array($departments) ? count($departments) : 0;
    $code = (count($tags) && mc_get_multi_setting('tags-settings', 'tags-starred') ? '<i class="mc-icon mc-icon-tag-line mc-filter-star" data-color-text="' . $tags[0]['tag-color'] . '" data-value="' . $tags[0]['tag-name'] . '"></i>' : '') . '<div class="mc-filter-btn"><i class="mc-icon mc-icon-filter"></i><div><div class="mc-select' . ($count ? '' : ' mc-hide') . '"><p>' . mc_('All departments') . '</p><ul' . ($count > 8 ? ' class="mc-scroll-area"' : '') . '><li data-value="">' . mc_('All departments') . '</li>';
    for ($i = 0; $i < $count; $i++) {
        $code .= '<li data-value="' . $departments[$i]['department-id'] . '">' . ucfirst(mc_($departments[$i]['department-name'])) . '</li>';
    }
    $code .= '</ul></div>';
    if (!mc_get_multi_setting('disable', 'disable-channels-filter')) {
        $count = count($sources);
        $code .= '<div class="mc-select"><p>' . mc_('All channels') . '</p><ul' . ($count > 8 ? ' class="mc-scroll-area"' : '') . '><li data-value="false">' . mc_('All channels') . '</li><li data-value="chat">' . mc_('Chat') . '</li>';
        for ($i = 0; $i < $count; $i++) {
            if ($sources[$i][2] === true || (defined($sources[$i][2]) && (!$is_cloud || in_array(strtolower(substr($sources[$i][2], 3)), $cloud_active_apps)))) {
                $code .= '<li data-value="' . $sources[$i][0] . '">' . $sources[$i][1] . '</li>';
            }
        }
        $code .= '</ul></div>';
    } else {
        $code .= '<div class="mc-select mc-hide"></div>';
    }
    $count = count($tags);
    if ($count) {
        $code .= '<div class="mc-select"><p>' . mc_('All tags') . '</p><ul' . ($count > 8 ? ' class="mc-scroll-area"' : '') . '><li data-value="">' . mc_('All tags') . '</li>';
        for ($i = 0; $i < $count; $i++) {
            $code .= '<li data-value="' . $tags[$i]['tag-name'] . '">' . $tags[$i]['tag-name'] . '</li>';
        }
        $code .= '</ul></div>';
    } else {
        $code .= '<div class="mc-select mc-hide"></div>';
    }
    echo $code .= '</div></div>';
}

function mc_docs_link($id = '', $class = 'mc-docs') {
    if (!mc_is_cloud() || defined('MC_CLOUD_DOCS')) {
        echo '<a href="' . (mc_is_cloud() ? MC_CLOUD_DOCS : 'https://app.masichat.com/docs') . $id . '" class="' . $class . '" target="_blank"><i class="mc-icon-help"></i></a>';
    }
}

?>