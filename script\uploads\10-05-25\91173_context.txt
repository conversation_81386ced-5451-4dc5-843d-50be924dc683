i added paystack (ps) as a payment gateway copying how stripe appears on the app (masi chat -(the saas script we purchased (the project u are working on now)). 

the plan was masi chat should retrieve plans created in ps and they should appear in the super admin and user admin dashboards where users select a membership plan from their admin ("Ma<PERSON>") dashboard and it redirects them to ps they pay and then redirected to their admin dashboard and their membership automatically updated to the purchased one under Active Membership with the updated quotas and green and ps to also cancel subscriptions when a user clicks the cancel subscription button. 

these are all the files I tweaked, copying how stripe appears on the app (masi chat -(the saas script we purchased (the project u are working on now)). Ps is working perfectly. 

account/functions.php
account/paystack.php  (other gateways have their own webhooks and also included in this folder)
account/ajax.php,
account/super.php, (for super admin)
account/js/cloud.js,  
account/js/cloud.min.js,  
script/config.php 
account/index.php (user admin)

I also added fb login

When updating the code don't remove or change how things work especially if they are not part of the fix/update u r working on, be mindful of references before changing coz u might break other things

so b4 changing any code block,look at all the references of that block and make a note as to what they affect and once you are satisfied only the fix/update will be affected you can carry on and edit

if you edits fail to fix the issue twice then -Reflect on 5-7 different possible sources of the problem, distill those down to 1-2 most likely sources, and then add logs to validate your assumptions before we move onto implementing the actual code fix

if I come to you with an error from the app, always ask me if permissions and ownerships are 755/644 and admin:admin 