<?php

/*
 *
 * ===================================================================
 * PAYFAST ITN HANDLER
 * ===================================================================
 *
 */

require_once('functions.php');
require_once('../config.php');

$payfast_data = $_POST;
$pfOutput = '';

// Notify Payfast that information has been received
header('HTTP/1.0 200 OK');
flush();

// Log the PayFast data for debugging purposes
mc_cloud_debug('PayFast ITN Data: ' . json_encode($payfast_data));

// Verify the signature
$pfParamString = '';
foreach ($payfast_data as $key => $val) {
    if ($key != 'signature') {
        $pfParamString .= $key . '=' . urlencode($val) . '&';
    }
}
$pfParamString = substr($pfParamString, 0, -1);
$signature = md5($pfParamString . PAYFAST_PASSPHRASE);

if ($signature == $payfast_data['signature']) {
    // Signature is valid

    // Get the payment status
    $payment_status = $payfast_data['payment_status'];

    // Get the custom data (cloud user ID, membership ID, etc.)
    $custom_str1 = $payfast_data['m_payment_id']; // Format: cloud_user_id-membership_id-timestamp
    $custom_data = explode('-', $custom_str1);
    $cloud_user_id = $custom_data[0];
    $membership_id = $custom_data[1];

    if ($payment_status == 'COMPLETE') {
        // Payment is complete, update the user's membership
        $membership = membership_get($membership_id);
        if ($membership) {
            $response = membership_update($membership_id, $membership['period'], $cloud_user_id, $payfast_data['pf_payment_id']);
            if ($response === true) {
                // Add to payment history
                cloud_add_to_payment_history($cloud_user_id, $payfast_data['amount_gross'], 'Membership', $payfast_data['pf_payment_id']);
                mc_cloud_debug('PayFast ITN: Membership updated successfully for user ID: ' . $cloud_user_id);
            } else {
                mc_cloud_debug('PayFast ITN Error: Failed to update membership for user ID: ' . $cloud_user_id . ' - ' . $response);
            }
        } else {
            mc_cloud_debug('PayFast ITN Error: Membership not found for ID: ' . $membership_id);
        }
    } else {
        // Log other payment statuses for debugging
        mc_cloud_debug('PayFast ITN: Payment status is not COMPLETE. Status: ' . $payment_status);
    }
} else {
    // Log signature mismatch for debugging
    mc_cloud_debug('PayFast ITN Error: Signature mismatch. Received: ' . $payfast_data['signature'] . ' | Calculated: ' . $signature);
}

?>
