<?php

/*
 * ===================================================================
 * CLOUD AJAX PHP FILE
 * ===================================================================
 */

require('functions.php');

if (isset($_POST['function'])) {
    $function = $_POST['function'];

    // --- Permission Check for Super Admin Functions (BYPASSED) ---
    /*
    $super_admin_functions = [
        'super-update-saas', 'super-get-affiliate-details', 'super-reset-affiliate',
        'super-get-affiliates', 'super-membership-plans', 'super-get-customers',
        'super-get-customer', 'super-delete-customer', 'super-save-customer',
        'super-get-emails', 'super-save-emails', 'super-get-settings',
        'super-save-settings', 'super-save-membership-plans', 'super-save-white-label'
    ];
    if (in_array($function, $super_admin_functions) && !super_admin()) {
         // Optional: Log permission denial attempt
         mc_cloud_debug('Permission denied for super admin function: ' . $function);
         http_response_code(403); // Forbidden
         ajax_response(['status' => 'error', 'message' => 'You do not have permission to perform this action. Please contact your administrator if you believe this is an error.']); // Use ajax_response for consistent output
         // die(); // ajax_response already calls die()
    }
    */
    switch ($_POST['function']) {
        case 'registration':
            ajax_response(account_registration($_POST['details']));
            break; // <<< ADDED BREAK
        case 'login':
            ajax_response(account_login($_POST['email'], $_POST['password']));
            break; // <<< ADDED BREAK
        case 'account':
            ajax_response(account());
            break; // <<< ADDED BREAK
        case 'account-user-details':
            ajax_response(account_get_user_details());
            break; // <<< ADDED BREAK
        case 'account-save':
            ajax_response(account_save($_POST['details']));
            break; // <<< ADDED BREAK
        case 'account-reset-password':
            ajax_response(account_reset_password(mc_isset($_POST, 'email'), mc_isset($_POST, 'token'), mc_isset($_POST, 'password')));
            break; // <<< ADDED BREAK
        case 'account-welcome':
            ajax_response(account_welcome());
            break; // <<< ADDED BREAK
        case 'facebook-login':
            ajax_response(account_facebook_login($_POST['fb_data']));
            break;
        case 'account-delete':
            ajax_response(account_delete());
            break; // <<< ADDED BREAK
        case 'account-delete-agents-quota':
            ajax_response(account_delete_agents_quota());
            break; // <<< ADDED BREAK
        case 'verify':
            ajax_response(verify(mc_isset($_POST, 'email'), mc_isset($_POST, 'phone'), mc_isset($_POST, 'code_pairs')));
            break; // <<< ADDED BREAK
        case 'get-payments':
            ajax_response(membership_get_payments());
            break; // <<< ADDED BREAK
        case 'get-invoice':
            ajax_response(membership_get_invoice($_POST['payment_id']));
            break; // <<< ADDED BREAK

        // --- CORRECTED delete-invoice CASE ---
        case 'delete-invoice':
            // 1. Basic Input Validation & Sanitization
            if (!isset($_POST['file_name']) || empty($_POST['file_name'])) {
                ajax_response(['status' => 'error', 'message' => 'The filename is missing. Please provide a valid invoice filename to continue.']);
                break; // Exit case
            }

            // 2. Sanitize filename to prevent directory traversal attacks
            $file_name = basename($_POST['file_name']);

            // Optional: Add stricter validation if filenames have a known pattern
             if (!preg_match('/^inv-\d+-\d+\.pdf$/', $file_name)) {
                 ajax_response(['status' => 'error', 'message' => 'The filename format is invalid. Please ensure you are using a valid invoice filename.']);
                 break; // Exit case
             }

            // 3. Construct the full path using consistent FORWARD slashes
            $invoices_dir = MC_CLOUD_PATH . '/script/uploads/invoices';
            $path_to_delete = $invoices_dir . '/' . $file_name;

            // 4. Check if file exists before attempting to delete
            if (file_exists($path_to_delete)) {

                // 5. (Crucial for open_basedir) Verify the path is within the allowed directory
                $allowed_dir_realpath = realpath($invoices_dir);
                $file_realpath = realpath($path_to_delete);

                if (!$allowed_dir_realpath || !$file_realpath || strpos($file_realpath, $allowed_dir_realpath) !== 0) {
                    mc_cloud_debug('Attempted to delete file outside allowed path: ' . $path_to_delete . ' (Resolved: ' . $file_realpath . ') Base: ' . $allowed_dir_realpath);
                    ajax_response(['status' => 'error', 'message' => 'The file path validation failed. This may be due to security restrictions. Please contact support if you need assistance.']);
                    break; // Exit case
                }

                // 6. Attempt to delete the file
                if (unlink($path_to_delete)) {
                    ajax_response(['status' => 'success', 'message' => 'File deleted.']);
                } else {
                    mc_cloud_debug('Failed to delete invoice file (unlink failed): ' . $path_to_delete);
                    ajax_response(['status' => 'error', 'message' => 'We couldn\'t delete the file. This may be due to file permissions or server configuration. Please try again or contact support if the problem persists.']);
                }
            } else {
                ajax_response(['status' => 'error', 'message' => 'The invoice file was not found. It may have been already deleted or moved.']);
            }
            break; // <<< ADDED BREAK (Important!)
        // --- END CORRECTED delete-invoice CASE ---

        case 'membership':
            ajax_response(membership_get_active());
            break; // <<< ADDED BREAK
        case 'account-membership-details':
            ajax_response(account_membership_details());
            break;
        case 'super-login':
            ajax_response(super_login($_POST['email'], $_POST['password']));
            break; // <<< ADDED BREAK
        case 'purchase-white-label':
            ajax_response(membership_purchase_white_label(mc_isset($_POST, 'external_integration')));
            break; // <<< ADDED BREAK
        case 'purchase-credits':
            ajax_response(membership_purchase_credits($_POST['amount'], mc_isset($_POST, 'external_integration')));
            break; // <<< ADDED BREAK
        case 'set-auto-recharge-credits':
            ajax_response(membership_set_auto_recharge(mc_isset($_POST, 'enabled')));
            break; // <<< ADDED BREAK
        case 'super-get-customers':
            ajax_response(super_get_customers(mc_isset($_POST, 'membership')));
            break; // <<< ADDED BREAK
        case 'super-get-customer':
            ajax_response(super_get_customer($_POST['customer_id']));
            break; // <<< ADDED BREAK
        case 'super-delete-customer':
            ajax_response(super_delete_customer($_POST['customer_id']));
            break; // <<< ADDED BREAK
        case 'super-save-customer':
            ajax_response(super_save_customer($_POST['customer_id'], $_POST['details'], mc_isset($_POST, 'extra_details')));
            break; // <<< ADDED BREAK
        case 'super-get-emails':
            ajax_response(super_get_emails());
            break; // <<< ADDED BREAK
        case 'super-save-emails':
            ajax_response(super_save_emails($_POST['settings']));
            break; // <<< ADDED BREAK
        case 'super-get-settings':
            ajax_response(super_get_settings());
            break; // <<< ADDED BREAK
        case 'super-save-settings':
            ajax_response(super_save_settings($_POST['settings']));
            break; // <<< ADDED BREAK

        case 'super-membership-plans':
            // This function RETURNS the HTML string
            $html_content = super_membership_plans();
            // Use the specific helper for HTML output
            ajax_html_response($html_content);
            // No break; needed because ajax_html_response() calls die()

        case 'super-save-membership-plans':
            ajax_response(super_save_membership_plans($_POST['plans']));
            break; // <<< ADDED BREAK
        case 'super-get-affiliates':
            ajax_response(super_get_affiliates());
            break; // <<< ADDED BREAK
        case 'super-reset-affiliate':
            ajax_response(super_reset_affiliate($_POST['affiliate_id']));
            break; // <<< ADDED BREAK
        case 'super-get-affiliate-details':
            ajax_response(super_get_affiliate_details($_POST['affiliate_id']));
            break; // <<< ADDED BREAK
        case 'super-save-white-label':
            ajax_response(super_save_white_label($_POST['price']));
            break; // <<< ADDED BREAK

        // --- Payment Provider Specific ---
        case 'stripe-create-session':
            ajax_response(stripe_create_session($_POST['price_id'], $_POST['cloud_user_id']));
            break; // <<< ADDED BREAK
        case 'stripe-cancel-subscription':
            ajax_response(stripe_cancel_subscription());
            break; // <<< ADDED BREAK

        // Paystack
        case 'paystack-create-session': // Used for initiating payment
             // Validate inputs (basic)
             if (!isset($_POST['price_id']) || !isset($_POST['cloud_user_id'])) {
                 ajax_response(['status' => 'error', 'message' => 'Missing required parameters.']);
                 break;
             }
            ajax_response(paystack_create_session($_POST['price_id'], $_POST['cloud_user_id']));
            break;
        case 'cancel_paystack_subscription': // Used for user clicking cancel button
            $subscription_code = isset($_POST['subscription_code']) ? $_POST['subscription_code'] : null;
            $email_token = isset($_POST['email_token']) ? $_POST['email_token'] : null; // Token might be needed depending on implementation
            if (function_exists('paystack_cancel_subscription')) {
                $result = paystack_cancel_subscription($subscription_code, $email_token);
                ajax_response($result);
            } else {
                ajax_response(['status' => 'error', 'message' => 'Cancellation function not available.']);
            }
            break; // <<< ADDED BREAK
        case 'paystack-get-plans': // Might be used by admin UI?
            ajax_response(paystack_get_plans());
            break;
        case 'paystack-create-plan': // Likely Admin only, ensure permissions if used
            if (!function_exists('paystack_create_plan')) {
                ajax_response(['status' => 'error', 'message' => 'Function paystack_create_plan not implemented']);
                break;
            }
            // Add input validation here
            ajax_response(paystack_create_plan($_POST['name'], $_POST['amount'], $_POST['interval']));
            break;
        case 'paystack-create-customer': // May not be needed via AJAX if handled internally
            // Add input validation here
             ajax_response(paystack_create_customer($_POST['email'], mc_isset($_POST,'first_name'), mc_isset($_POST,'last_name')));
             break;
        case 'paystack-create-subscription': // Less likely needed via AJAX if handled by webhook/session
            // Add input validation here
            ajax_response(paystack_create_subscription($_POST['plan_code'], $_POST['customer_code'], mc_isset($_POST, 'authorization_code')));
            break;
        case 'paystack-verify-transaction': // Useful for manual verification?
             // Add input validation here
            ajax_response(paystack_verify_transaction($_POST['reference']));
            break;
        case 'paystack-subscription': // Seems redundant with paystack-create-session, keep only one? Let's keep create-session.
             // Keeping this for now, but consider removing if 'paystack-create-session' is the primary one used
             if (!isset($_POST['plan_id'])) {
                 ajax_response(['status' => 'error', 'message' => 'Missing plan ID']);
                 break; // Exit if missing plan_id
             }
             $user_id = get_active_account_id(false); // Get non-escaped ID
             if (!$user_id) {
                  ajax_response(['status' => 'error', 'message' => 'User not logged in.']);
                  break;
             }
             mc_cloud_debug(['function' => 'paystack-subscription AJAX', 'plan_id' => $_POST['plan_id'], 'user_id' => $user_id]);
             $response = paystack_create_session($_POST['plan_id'], $user_id);
             ajax_response($response);
             break; // <<< ADDED BREAK

        // Rapyd
        case 'rapyd-checkout':
            ajax_response(rapyd_create_checkout($_POST['price_id'], $_POST['cloud_user_id']));
            break; // <<< ADDED BREAK

        // Verifone
        case 'verifone-checkout':
            ajax_response(verifone_create_checkout($_POST['price_id'], $_POST['cloud_user_id']));
            break; // <<< ADDED BREAK
        case 'verifone-cancel-subscription':
            ajax_response(verifone_cancel_subscription());
            break; // <<< ADDED BREAK

        // WhatsApp / Messenger
        case 'whatsapp-sync':
            ajax_response(cloud_meta_whatsapp_sync($_POST['code']));
            break; // <<< ADDED BREAK
        case 'messenger-sync':
            ajax_response(cloud_meta_messenger_sync($_POST['access_token']));
            break; // <<< ADDED BREAK

        // Addons
        case 'purchase-addon':
            ajax_response(cloud_addon_purchase($_POST['index']));
            break; // <<< ADDED BREAK

        // Razorpay
        case 'razorpay-create-subscription':
            ajax_response(razorpay_create_subscription($_POST['price_id'], $_POST['cloud_user_id']));
            break; // <<< ADDED BREAK
        case 'razorpay-cancel-subscription':
            ajax_response(razorpay_cancel_subscription());
            break; // <<< ADDED BREAK

        // Shopify
        case 'shopify-subscription':
            ajax_response(shopify_subscription($_POST['price_id']));
            break; // <<< ADDED BREAK
        case 'shopify-cancel-subscription':
            ajax_response(shopify_cancel_subscription());
            break; // <<< ADDED BREAK

        // YooMoney
        case 'yoomoney-create-subscription':
            ajax_response(yoomoney_create_subscription($_POST['price_id']));
            break; // <<< ADDED BREAK
        case 'yoomoney-cancel-subscription':
            ajax_response(yoomoney_cancel_subscription());
            break; // <<< ADDED BREAK

        // Referrals
        case 'save-referral-payment-information':
            ajax_response(account_save_referral_payment_information($_POST['method'], $_POST['details']));
            break; // <<< ADDED BREAK
        case 'get-referral-payment-information':
            ajax_response(super_get_user_data('referral_payment_info', get_active_account_id()));
            break; // <<< ADDED BREAK

        // SaaS Update
        case 'super-update-saas':
            ajax_response(super_update_saas());
            break; // <<< ADDED BREAK

        // Default case for unknown functions
        default:
            mc_cloud_debug('Unknown AJAX function called: ' . $function);
            ajax_response(['status' => 'error', 'message' => 'The requested function is not available. Please check your request or contact support if you believe this is an error.']);
            // No break needed as it's the last one
    }
} else {
    // Handle cases where 'function' is not set in POST data
    http_response_code(400); // Bad Request
    // You might want to use ajax_response for consistency if it handles headers/die()
    ajax_response(['status' => 'error', 'message' => 'The required parameter "function" is missing from your request. Please ensure your request includes all necessary parameters.']);
    // die('Required parameter missing.');
}


// --- Helper Functions ---

// ADDED ajax_html_response (if not already in functions.php)
function ajax_html_response($html_output) {
    // Ensure no prior output interferes
    if (ob_get_level() > 0) {
        @ob_end_clean(); // Suppress errors if output buffer is empty
    }
    // Set HTML header
    header('Content-Type: text/html; charset=utf-8');
    http_response_code(200);
    // Echo the HTML and stop execution
    die($html_output);
}

// Ensure ajax_response is defined (use this version)
function ajax_response($response) {
    // Ensure no prior output interferes
    if (ob_get_level() > 0) {
       @ob_end_clean();
    }
    // Set JSON header
    header('Content-Type: application/json; charset=utf-8');
    http_response_code(200); // Assume success unless error is explicitly set elsewhere

    // Encode the response
    // Handle boolean true specifically
    if ($response === true) {
        $json_output = json_encode(['status' => 'success']);
    } elseif ($response === false) { // Handle boolean false
         $json_output = json_encode(['status' => 'error', 'message' => 'The email or password you entered is incorrect. Please check your credentials and try again, or use the "Forgot Password" link if you need to reset your password.']);
    } else {
        // Check if response is already a JSON string (less likely but possible)
        if (is_string($response)) {
             json_decode($response);
             if (json_last_error() == JSON_ERROR_NONE) {
                 $json_output = $response; // It's already JSON
             } else {
                  // Assume it's a plain string message, wrap it
                  $json_output = json_encode(['status' => 'success', 'message' => $response]);
             }
        } else {
             // Assume it's an array or object, encode it
             $json_output = json_encode($response, JSON_INVALID_UTF8_IGNORE);
        }
    }


    // Check for JSON encoding errors
    if ($json_output === false) {
        http_response_code(500); // Internal Server Error
        $json_output = json_encode(['status' => 'error', 'message' => 'We encountered a technical issue while processing your request. Please try again or contact support if the problem persists. Error details: ' . json_last_error_msg()]);
        // Fallback if even error encoding fails
        if ($json_output === false) {
             $json_output = '{"status":"error", "message":"We encountered a critical technical issue while processing your request. Please try again later or contact our support team for assistance."}';
        }
    }

    // Echo the JSON and stop execution
    die($json_output);
}

// REMOVED the standalone paystack_subscription() function definition from here

?>