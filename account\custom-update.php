<?php
/**
 * Custom Update Script for PayStack Integration
 * 
 * This script provides a safer way to update the system while preserving
 * PayStack-specific customizations.
 */

// Include the main functions file
require_once('functions.php');

// Define files with PayStack customizations that need to be preserved
$paystack_customized_files = [
    // Core files with PayStack customizations
    'account/ajax.php',
    'account/functions.php',
    'account/index.php',
    'account/super.php',
    'account/js/cloud.js',
    'account/js/cloud.min.js'
];

// Backup directory
$backup_dir = MC_CLOUD_PATH . '/paystack_backup_' . date('Y-m-d_H-i-s');

/**
 * Create backup of PayStack customized files
 */
function create_paystack_backup($files, $backup_dir) {
    // Create backup directory
    if (!file_exists($backup_dir)) {
        if (!mkdir($backup_dir, 0755, true)) {
            return ['status' => 'error', 'message' => 'Failed to create backup directory'];
        }
    }
    
    // Create subdirectories as needed
    foreach ($files as $file) {
        $file_path = MC_CLOUD_PATH . '/' . $file;
        $backup_file_path = $backup_dir . '/' . $file;
        
        // Create directory structure if it doesn't exist
        $backup_file_dir = dirname($backup_file_path);
        if (!file_exists($backup_file_dir)) {
            if (!mkdir($backup_file_dir, 0755, true)) {
                return ['status' => 'error', 'message' => 'Failed to create backup subdirectory for ' . $file];
            }
        }
        
        // Copy the file to backup
        if (file_exists($file_path)) {
            if (!copy($file_path, $backup_file_path)) {
                return ['status' => 'error', 'message' => 'Failed to backup file ' . $file];
            }
        } else {
            // Log if file doesn't exist
            mc_cloud_debug('Backup warning: File does not exist: ' . $file_path);
        }
    }
    
    return ['status' => 'success', 'message' => 'Backup created successfully at ' . $backup_dir];
}

/**
 * Run the official update
 */
function run_official_update() {
    // Call the original update function
    $update_result = super_update_saas();
    
    // Log the update result
    mc_cloud_debug('Official update result', ['result' => $update_result]);
    
    return $update_result;
}

/**
 * Restore PayStack customizations
 */
function restore_paystack_customizations($files, $backup_dir) {
    $restored_files = [];
    $failed_files = [];
    
    // These are specific code patterns we want to restore from the backup
    $paystack_patterns = [
        // Pattern for PayStack currency in super.php
        '/var CURRENCY = ".*?"/i' => function($backup_content, $updated_content) {
            if (preg_match('/var CURRENCY = "(.*?)"/i', $backup_content, $backup_matches) &&
                preg_match('/var CURRENCY = "(.*?)"/i', $updated_content, $updated_matches)) {
                
                // If the backup contains PAYSTACK_CURRENCY but the updated doesn't
                if (strpos($backup_matches[1], 'PAYSTACK_CURRENCY') !== false && 
                    strpos($updated_matches[1], 'PAYSTACK_CURRENCY') === false) {
                    
                    // Replace the currency definition with the one from backup
                    return str_replace($updated_matches[0], $backup_matches[0], $updated_content);
                }
            }
            return $updated_content;
        },
        
        // Pattern for PayStack cancel subscription in ajax.php
        '/case \'cancel_paystack_subscription\':(.*?)break;/s' => function($backup_content, $updated_content) {
            if (preg_match('/case \'cancel_paystack_subscription\':(.*?)break;/s', $backup_content, $matches)) {
                // If the updated content doesn't have this case
                if (strpos($updated_content, 'cancel_paystack_subscription') === false) {
                    // Find a good place to insert it (after another case)
                    $pos = strrpos($updated_content, 'break;');
                    if ($pos !== false) {
                        return substr_replace($updated_content, "\n        " . $matches[0], $pos + 6, 0);
                    }
                }
            }
            return $updated_content;
        },
        
        // Pattern for PayStack return URL handling in index.php
        '/\/\/ --- START: Paystack Return Processing ---(.*?)\/\/ --- END: Paystack Return Processing ---/s' => function($backup_content, $updated_content) {
            if (preg_match('/\/\/ --- START: Paystack Return Processing ---(.*?)\/\/ --- END: Paystack Return Processing ---/s', $backup_content, $matches)) {
                // If the updated content doesn't have this section
                if (strpos($updated_content, '--- START: Paystack Return Processing ---') === false) {
                    // Find a good place to insert it (after the initial PHP setup)
                    $pos = strpos($updated_content, '?>');
                    if ($pos !== false) {
                        return substr_replace($updated_content, "\n" . $matches[0] . "\n", $pos, 0);
                    }
                }
            }
            return $updated_content;
        },
        
        // Pattern for PayStack functions in functions.php
        '/function paystack_cancel_subscription(.*?)}/s' => function($backup_content, $updated_content) {
            if (preg_match('/function paystack_cancel_subscription(.*?)}/s', $backup_content, $matches)) {
                // If the updated content doesn't have this function
                if (strpos($updated_content, 'function paystack_cancel_subscription') === false) {
                    // Find a good place to insert it (before the end of the file)
                    $pos = strrpos($updated_content, '?>');
                    if ($pos !== false) {
                        return substr_replace($updated_content, "\n" . $matches[0] . "\n", $pos, 0);
                    }
                }
            }
            return $updated_content;
        }
    ];
    
    // Process each file
    foreach ($files as $file) {
        $backup_file_path = $backup_dir . '/' . $file;
        $current_file_path = MC_CLOUD_PATH . '/' . $file;
        
        // Skip if backup file doesn't exist
        if (!file_exists($backup_file_path)) {
            $failed_files[] = ['file' => $file, 'reason' => 'Backup file not found'];
            continue;
        }
        
        // Skip if current file doesn't exist (was removed in update)
        if (!file_exists($current_file_path)) {
            $failed_files[] = ['file' => $file, 'reason' => 'Current file not found after update'];
            continue;
        }
        
        // Read file contents
        $backup_content = file_get_contents($backup_file_path);
        $updated_content = file_get_contents($current_file_path);
        $original_updated_content = $updated_content; // Save for comparison
        
        // Apply pattern-based restoration
        foreach ($paystack_patterns as $pattern => $callback) {
            if (is_callable($callback)) {
                $updated_content = $callback($backup_content, $updated_content);
            }
        }
        
        // If content was modified, write it back
        if ($updated_content !== $original_updated_content) {
            if (file_put_contents($current_file_path, $updated_content)) {
                $restored_files[] = $file;
            } else {
                $failed_files[] = ['file' => $file, 'reason' => 'Failed to write modified content'];
            }
        }
    }
    
    return [
        'status' => empty($failed_files) ? 'success' : 'partial',
        'restored' => $restored_files,
        'failed' => $failed_files
    ];
}

/**
 * Main update function
 */
function custom_update_with_paystack_preservation() {
    global $paystack_customized_files, $backup_dir;
    
    // Step 1: Create backup
    $backup_result = create_paystack_backup($paystack_customized_files, $backup_dir);
    if ($backup_result['status'] === 'error') {
        return $backup_result;
    }
    
    // Step 2: Run official update
    $update_result = run_official_update();
    if (!is_bool($update_result) && !strpos($update_result, 'success') === 0) {
        return ['status' => 'error', 'message' => 'Official update failed', 'details' => $update_result];
    }
    
    // Step 3: Restore PayStack customizations
    $restore_result = restore_paystack_customizations($paystack_customized_files, $backup_dir);
    
    // Return combined results
    return [
        'status' => $restore_result['status'] === 'success' ? 'success' : 'partial',
        'backup' => $backup_result,
        'update' => $update_result,
        'restore' => $restore_result,
        'backup_dir' => $backup_dir
    ];
}

// Execute the update if this script is called directly
if (basename($_SERVER['SCRIPT_FILENAME']) === basename(__FILE__)) {
    // Check if user is super admin
    if (!super_admin()) {
        die('Access denied. Super admin privileges required.');
    }
    
    // Run the custom update
    $result = custom_update_with_paystack_preservation();
    
    // Output the result
    header('Content-Type: application/json');
    echo json_encode($result, JSON_PRETTY_PRINT);
}
