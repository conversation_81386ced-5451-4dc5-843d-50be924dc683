<?php

/*
 * ==========================================================
 * AI APP
 * ==========================================================
 *
 * Artificial Intelligence app. © 2017-2025 board.support. All rights reserved.
 *
 */

define('MC_DIALOGFLOW', '1.5.3');

/*
 * -----------------------------------------------------------
 * SYNC
 * -----------------------------------------------------------
 *
 */

if (isset($_GET['code']) && file_exists('../../include/functions.php')) {
    require('../../include/functions.php');
    mc_cloud_load();
    $info = mc_google_key();
    $query = '{ code: "' . $_GET['code'] . '", grant_type: "authorization_code", client_id: "' . $info[0] . '", client_secret: "' . $info[1] . '", redirect_uri: "' . MC_URL . '/apps/dialogflow/functions.php" }';
    $response = mc_curl('https://accounts.google.com/o/oauth2/token', $query, ['Content-Type: application/json', 'Content-Length: ' . strlen($query)]);
    die($response && isset($response['refresh_token']) ? '<script>document.location = "' . (mc_is_cloud() ? str_replace('/script', '', MC_URL) : MC_URL . '/admin.php') . '?setting=dialogflow&refresh_token=' . $response['refresh_token'] . '";</script>' : 'Error while trying to get Dialogflow token. Dialogflow code: ' . $_GET['code'] . '. Response: ' . json_encode($response));
}

/*
 * -----------------------------------------------------------
 * OBJECTS
 * -----------------------------------------------------------
 *
 * Dialogflow objects
 *
 */

class MCDialogflowEntity {
    public $data;

    function __construct($id, $values, $prompts = []) {
        $this->data = ['displayName' => $id, 'entities' => $values, 'kind' => 'KIND_MAP', 'enableFuzzyExtraction' => true];
    }

    public function __toString() {
        return $this->json();
    }

    function json() {
        return json_encode($this->data);
    }

    function data() {
        return $this->data;
    }
}

class MCDialogflowIntent {
    public $data;

    function __construct($name, $training_phrases, $bot_responses, $entities = [], $entities_values = [], $payload = false, $input_contexts = [], $output_contexts = [], $prompts = [], $id = false) {
        $training_phrases_api = [];
        $parameters = [];
        $parameters_checks = [];
        $messages = [];
        $json = json_decode(file_get_contents(MC_PATH . '/apps/dialogflow/data.json'), true);
        $entities = array_merge($entities, $json['entities']);
        $entities_values = array_merge($entities_values, $json['entities-values']);
        $project_id = false;
        if (is_string($bot_responses)) {
            $bot_responses = [$bot_responses];
        }
        if (is_string($training_phrases)) {
            $training_phrases = [$training_phrases];
        }
        for ($i = 0; $i < count($training_phrases); $i++) {
            $parts_temp = explode('@', $training_phrases[$i]);
            $parts = [];
            $parts_after = false;
            for ($j = 0; $j < count($parts_temp); $j++) {
                $part = ['text' => ($j == 0 ? '' : '@') . $parts_temp[$j]];
                for ($y = 0; $y < count($entities); $y++) {
                    $entity = is_string($entities[$y]) ? $entities[$y] : $entities[$y]['displayName'];
                    $entity_type = '@' . $entity;
                    $entity_name = str_replace('.', '-', $entity);
                    $entity_value = empty($entities_values[$entity]) ? $entity_type : $entities_values[$entity][array_rand($entities_values[$entity])];
                    if (strpos($part['text'], $entity_type) !== false) {
                        $mandatory = true;
                        if (strpos($part['text'], $entity_type . '*') !== false) {
                            $mandatory = false;
                            $part['text'] = str_replace($entity_type . '*', $entity_type, $part['text']);
                        }
                        $parts_after = explode($entity_type, $part['text']);
                        $part = ['text' => $entity_value, 'entityType' => $entity_type, 'alias' => $entity_name, 'userDefined' => true];
                        if (count($parts_after) > 1) {
                            $parts_after = ['text' => $parts_after[1]];
                        } else {
                            $parts_after = false;
                        }
                        if (!in_array($entity, $parameters_checks)) {
                            array_push($parameters, ['displayName' => $entity_name, 'value' => '$' . $entity, 'mandatory' => $mandatory, 'entityTypeDisplayName' => '@' . $entity, 'prompts' => mc_isset($prompts, $entity_name, [])]);
                            array_push($parameters_checks, $entity);
                        }
                        break;
                    }
                }
                array_push($parts, $part);
                if ($parts_after)
                    array_push($parts, $parts_after);
            }
            array_push($training_phrases_api, ['type' => 'EXAMPLE', 'parts' => $parts]);
        }
        for ($i = 0; $i < count($bot_responses); $i++) {
            array_push($messages, ['text' => ['text' => $bot_responses[$i]]]);
        }
        if (!empty($payload)) {
            $std = new stdClass;
            $std->payload = $payload;
            array_push($messages, $std);
        }
        if (!empty($input_contexts) && is_array($input_contexts)) {
            $project_id = mc_get_multi_setting('google', 'google-project-id');
            for ($i = 0; $i < count($input_contexts); $i++) {
                $input_contexts[$i] = 'projects/' . $project_id . '/agent/sessions/-/contexts/' . $input_contexts[$i];
            }
        }
        if (!empty($output_contexts) && is_array($output_contexts)) {
            $project_id = $project_id ? $project_id : mc_get_multi_setting('google', 'google-project-id');
            for ($i = 0; $i < count($output_contexts); $i++) {
                $is_array = is_array($output_contexts[$i]);
                $output_contexts[$i] = ['name' => 'projects/' . $project_id . '/agent/sessions/-/contexts/' . ($is_array ? $output_contexts[$i][0] : $output_contexts[$i]), 'lifespanCount' => ($is_array ? $output_contexts[$i][1] : 3)];
            }
        }
        $t = ['displayName' => $name, 'trainingPhrases' => $training_phrases_api, 'parameters' => $parameters, 'messages' => $messages, 'inputContextNames' => $input_contexts, 'outputContexts' => $output_contexts];
        if ($id) {
            $t['name'] = $id;
        }
        $this->data = $t;
    }

    public function __toString() {
        return $this->json();
    }

    function json() {
        return json_encode($this->data);
    }

    function data() {
        return $this->data;
    }
}

/*
 * -----------------------------------------------------------
 * DIALOGFLOW MESSAGE
 * -----------------------------------------------------------
 *
 * Send the user message to the bot and return the reply
 *
 */

$mc_recursion_dialogflow = [true, true, true, true, true];
function mc_dialogflow_message($conversation_id = false, $message = '', $token = -1, $language = false, $attachments = [], $event = '', $parameters = false, $project_id = false, $session_id = false, $audio = false) {
    global $mc_recursion_dialogflow;
    if (mc_is_cloud()) {
        mc_cloud_membership_validation(true);
    }
    $smart_reply = $event == 'smart-reply';
    $user_id = $conversation_id && !$smart_reply && mc_is_agent() ? mc_db_get('SELECT user_id FROM mc_conversations WHERE id = ' . mc_db_escape($conversation_id, true))['user_id'] : mc_get_active_user_ID();
    if (!mc_cloud_membership_has_credits('google')) {
        return mc_error('no-credits', 'mc_dialogflow_message');
    }
    $cx = mc_get_multi_setting('google', 'dialogflow-edition', mc_get_setting('dialogflow-edition')) == 'cx'; // Deprecated: mc_get_setting('dialogflow-edition', 'es')
    $query = ['queryInput' => [], 'queryParams' => $cx ? ['parameters' => ['user_id' => $user_id, 'conversation_id' => $conversation_id]] : ['payload' => ['support_board' => ['conversation_id' => $conversation_id, 'user_id' => $user_id]]]];
    $bot_id = mc_get_bot_id();
    $human_takeover = mc_dialogflow_get_human_takeover_settings();
    $human_takeover = $human_takeover['active'] ? $human_takeover : false;
    $response_success = [];
    $multilingual = mc_get_setting('dialogflow-multilingual') || mc_get_multi_setting('google', 'google-multilingual'); // Deprecated: mc_get_setting('dialogflow-multilingual')
    $multilingual_translation = mc_get_setting('dialogflow-multilingual-translation') || mc_get_multi_setting('google', 'google-multilingual-translation'); // Deprecated: mc_get_setting('dialogflow-multilingual-translation')
    $user_language = $multilingual_translation ? mc_get_user_extra($user_id, 'language') : false;
    $unknow_language_message = false;
    $dialogflow_agent = false;
    $is_human_takeover = $conversation_id && !$smart_reply && !mc_dialogflow_is_human_takeover($conversation_id);
    $message_id = false;
    $translations = false;
    $payload = [];
    if ($human_takeover && $conversation_id && mc_dialogflow_is_human_takeover($conversation_id) && mc_isset($human_takeover, 'disable_chatbot')) {
        return false;
    }
    if ($event == 'translations') {
        unset($GLOBALS['MC_LANGUAGE']);
        $translations = mc_get_current_translations();
    }
    if ($parameters) {
        $query['queryParams'][$cx ? 'parameters' : 'payload'] = array_merge($query['queryParams'][$cx ? 'parameters' : 'payload'], $parameters);
    }
    if (empty($bot_id)) {
        return new MCValidationError('bot-id-not-found');
    }
    if (!$language || empty($language[0])) {
        $language = $multilingual ? ($user_language ? $user_language : mc_get_user_language($user_id)) : false;
        $language = $language ? [$language] : ['en'];
    } else {
        $language[0] = mc_dialogflow_language_code($language[0]);
        if (count($language) > 1 && $language[1] == 'language-detection') {
            $response_success['language_detection'] = $language[0];
        }
    }
    $query['queryInput']['languageCode'] = $language[0];

    // Retrive token
    if ($token == -1 || $token === false) {
        $token = mc_dialogflow_get_token();
        if (mc_is_error($token)) {
            return $token;
        }
    }

    // Attachments
    $attachments = mc_json_array($attachments);
    for ($i = 0; $i < count($attachments); $i++) {
        $message .= ' ' . $attachments[$i][1];
    }

    if (!empty($audio)) {

        // Audio
        if (pathinfo($audio, PATHINFO_EXTENSION) == 'ogg' && mc_get_multi_setting('open-ai', 'open-ai-speech-recognition')) {
            $message .= mc_open_ai_audio_to_text($audio);
            $audio = false;
        } else {
            $audio = strpos($audio, 'http') === 0 ? mc_get($audio) : file_get_contents($audio);
            if ($cx) {
                $query['queryInput']['audio'] = ['config' => ['SampleRateHertz' => 16000, 'audioEncoding' => 'AUDIO_ENCODING_OGG_OPUS', 'languageCode' => $language[0]], 'audio' => base64_encode($audio)];
            } else {
                $query['queryInput']['audioConfig'] = ['audioEncoding' => 'AUDIO_ENCODING_UNSPECIFIED', 'languageCode' => $language[0]];
                $query['inputAudio'] = base64_encode($audio);
            }
        }
    }
    if (empty($audio) && !empty($message)) {

        // Message
        $query['queryInput']['text'] = ['text' => $message, 'languageCode' => $language[0]];
    } else if (!empty($event)) {

        // Events
        $query['queryInput']['event'] = $cx ? ['event' => $event] : ['name' => $event, 'languageCode' => $language[0]];
    }

    // Department linking
    if (!$project_id && $conversation_id && !$smart_reply) {
        $departments = mc_get_setting('dialogflow-departments');
        if ($departments && is_array($departments)) {
            $department = mc_db_get('SELECT department FROM mc_conversations WHERE id = ' . mc_db_escape($conversation_id, true))['department'];
            for ($i = 0; $i < count($departments); $i++) {
                if ($departments[$i]['dialogflow-departments-id'] == $department) {
                    $project_id = $departments[$i]['dialogflow-departments-agent'];
                    break;
                }
            }
        }
    }

    // Dialogflow response
    $session_id = $session_id ? $session_id : ($user_id ? $user_id : 'mc');
    $response = mc_dialogflow_curl('/agent/sessions/' . $session_id . ':detectIntent', $query, false, 'POST', $token, $project_id);
    mc_cloud_membership_use_credits(($cx ? 'cx' : 'es') . (empty($audio) ? '' : '-audio'), 'google', strlen($audio));
    mc_webhooks('MCDialogflowMessage', ['response' => $response, 'message' => $message, 'conversation_id' => $conversation_id]);
    if (is_string($response)) {
        if (strpos($response, 'Error 404')) {
            return ['response' => ['error' => 'Error 404. Dialogflow Project ID or Agent Name not found.']];
        }
        $response = [];
    }
    if (mc_is_error($response)) {
        return $response;
    }
    if (isset($response['error']) && (mc_isset($response['error'], 'code') == 403 || in_array($response['error']['status'], ['PERMISSION_DENIED', 'UNAUTHENTICATED']))) {
        if ($mc_recursion_dialogflow[0]) {
            $mc_recursion_dialogflow[0] = false;
            $token = mc_dialogflow_get_token(false);
            return mc_dialogflow_message($conversation_id, $message, $token, $language, [], $event);
        } else {
            mc_error('dialogflow-access-token', 'mc_dialogflow_message', $response);
        }
    }
    if ($event == 'Welcome') {
        mc_dialogflow_set_active_context('welcome', [], 2, $token, $user_id, $language[0]);
    }
    if (isset($response['error'])) {
        return ['response' => $response];
    }
    $response_query = mc_isset($response, 'queryResult', []);
    $messages = mc_isset($response_query, 'fulfillmentMessages', mc_isset($response_query, 'responseMessages', []));
    $unknow_answer = mc_dialogflow_is_unknow($response);
    $results = [];
    $message_length = strlen($message);
    if (!$messages && isset($response_query['knowledgeAnswers'])) {
        $messages = mc_isset($response_query['knowledgeAnswers'], 'answers', []);
        for ($i = 0; $i < count($messages); $i++) {
            $messages[$i] = ['text' => ['text' => [$messages[$i]['answer']]]];
        }
    }
    if (isset($messages[0]) && isset($messages[0]['text']) && $messages[0]['text']['text'][0] == 'skip-intent') {
        $unknow_answer = true;
        $messages = [];
    }
    if (isset($response_query['webhookPayload'])) {
        array_push($messages, ['payload' => $response_query['webhookPayload']]);
    }

    // Parameters
    $parameters = isset($response_query['parameters']) && count($response_query['parameters']) ? $response_query['parameters'] : [];
    if (isset($response_query['outputContexts']) && count($response_query['outputContexts']) && isset($response_query['outputContexts'][0]['parameters'])) {
        for ($i = 0; $i < count($response_query['outputContexts']); $i++) {
            if (isset($response_query['outputContexts'][$i]['parameters'])) {
                $parameters = array_merge($response_query['outputContexts'][$i]['parameters'], $parameters);
            }
        }
    }

    // Google search, spelling correction
    if ($unknow_answer && !mc_is_agent()) {
        if ($message_length > 2) {
            if ($mc_recursion_dialogflow[1] && mc_get_multi_setting('open-ai', 'open-ai-spelling-correction-dialogflow') && empty(mc_get_shortcode($message))) {
                $spelling_correction = mc_open_ai_spelling_correction($message);
                $mc_recursion_dialogflow[1] = false;
                if ($spelling_correction != $message) {
                    return mc_dialogflow_message($conversation_id, $spelling_correction, $token, $language, $attachments, $event, $parameters);
                }
            }
            $google_search_settings = mc_get_setting('dialogflow-google-search');
            if ($google_search_settings) {
                $spelling_correction = $google_search_settings['dialogflow-google-search-spelling-active'];
                $continue = $google_search_settings['dialogflow-google-search-active'] && $message_length > 4 && !mc_get_multi_setting('open-ai', 'open-ai-active');
                if ($continue) {
                    $entities = mc_isset($google_search_settings, 'dialogflow-google-search-entities');
                    if (!empty($entities) && is_array($entities)) {
                        $continue = false;
                        $entities_response = mc_isset(mc_google_analyze_entities($message, $language[0], $token), 'entities', []);
                        for ($i = 0; $i < count($entities_response); $i++) {
                            if (in_array($entities_response[$i]['type'], $entities)) {
                                $continue = true;
                                break;
                            }
                        }
                    }
                }
                if ($continue || $spelling_correction) {
                    $google_search_response = mc_get('https://www.googleapis.com/customsearch/v1?key=' . $google_search_settings['dialogflow-google-search-key'] . '&cx=' . $google_search_settings['dialogflow-google-search-id'] . '&q=' . urlencode($message), true);
                    if ($mc_recursion_dialogflow[2] && $spelling_correction && isset($google_search_response['spelling'])) {
                        $mc_recursion_dialogflow[2] = false;
                        return mc_dialogflow_message($conversation_id, $google_search_response['spelling']['correctedQuery'], $token, $language, $attachments, $event, $parameters);
                    }
                    if ($continue) {
                        $google_search_response = mc_isset($google_search_response, 'items');
                        if ($google_search_response && count($google_search_response)) {
                            $google_search_response = $google_search_response[0];
                            $google_search_message = $google_search_response['snippet'];
                            $pos = strrpos($google_search_message, '. ');
                            if (!$pos && substr($google_search_message, -3) !== '...' && substr($google_search_message, -1) === '.') {
                                $pos = strlen($google_search_message);
                            }
                            if ($pos) {
                                $google_search_message = substr($google_search_message, 0, $pos);
                                $unknow_answer = false;
                                $messages = [['text' => ['text' => [$google_search_message]]]];
                                mc_dialogflow_set_active_context('google-search', ['link' => $google_search_response['link']], 2, $token, $user_id, $language[0]);
                            } else {
                                $google_search_message = false;
                            }
                        }
                    }
                }
            }
        }
    }
    if (!mc_is_agent() || $smart_reply) {
        $detected_language = false;
        $repeated_intent = false;

        // Language detection
        if ($mc_recursion_dialogflow[3] && (mc_get_multi_setting('dialogflow-language-detection', 'dialogflow-language-detection-active') || mc_get_multi_setting('google', 'google-language-detection')) && (($unknow_answer || !$user_language) && count(mc_db_get('SELECT id FROM mc_messages WHERE user_id = ' . $user_id . ' LIMIT 3', false)) < 3)) { // Deprecated: mc_get_multi_setting('dialogflow-language-detection', 'dialogflow-language-detection-active')
            $mc_recursion_dialogflow[3] = false;
            $detected_language = mc_google_language_detection($message, $token);
            if (!empty($detected_language) && ($detected_language != $language[0] || ($user_language && $detected_language != $user_language))) {
                $dialogflow_agent = mc_dialogflow_get_agent();
                mc_language_detection_db($user_id, $detected_language);
                $user_language = $detected_language;
                $payload['event'] = 'update-user';
                if ($detected_language != $language[0] && ($detected_language == mc_isset($dialogflow_agent, 'defaultLanguageCode') || in_array($detected_language, mc_isset($dialogflow_agent, 'supportedLanguageCodes', [])))) {
                    return mc_dialogflow_message($conversation_id, $message, $token, [$detected_language, 'language-detection'], $attachments, $event);
                } else if (!$multilingual_translation) {
                    $unknow_language_message = true;
                } else {
                    $event = 'translations';
                }
            }
        }

        // Repeated Intent
        if ($conversation_id && !$smart_reply && !$unknow_answer && mc_get_multi_setting('open-ai', 'open-ai-active') && empty($response_query['parameters']) && isset($messages[0]) && isset($messages[0]['text'])) {
            $last_message = mc_get_last_message($conversation_id, false, $bot_id);
            $repeated_intent = $last_message && mc_google_get_message_translation($last_message)['message'] == $messages[0]['text']['text'][0] && empty($response_query['parameters']);
        }

        if ($unknow_answer || $repeated_intent) {

            // Multilingual and translations
            if ($mc_recursion_dialogflow[4] && $multilingual_translation && !$repeated_intent) {
                $mc_recursion_dialogflow[4] = false;
                if (empty($GLOBALS['dialogflow_languages'])) {
                    $dialogflow_agent = $dialogflow_agent ? $dialogflow_agent : mc_dialogflow_get_agent();
                    $lang = mc_isset($dialogflow_agent, 'defaultLanguageCode', $language[0]);
                } else {
                    $lang = $GLOBALS['dialogflow_languages'][0];
                }
                $message_translated = mc_google_translate([$message], $lang, $token);
                if (!empty($message_translated[0])) {
                    return mc_dialogflow_message($conversation_id, $message_translated[0][0], $token, [$language[0], 'language-translation'], $attachments, $event);
                }
            }

            // OpenAI
            if ($message_length > 4 && mc_get_multi_setting('open-ai', 'open-ai-active')) {
                if ($conversation_id && !$smart_reply) {
                    $is_human_takeover = mc_dialogflow_is_human_takeover($conversation_id);
                }
                if (!$is_human_takeover || !$conversation_id) {
                    $extra = [];
                    if ($multilingual && !$multilingual_translation) {
                        $extra['language'] = $user_language ? $user_language : mc_get_user_language($user_id);
                    }
                    if ($smart_reply) {
                        $extra['smart_reply'] = true;
                    }
                    $response_open_ai = mc_open_ai_message($message, false, false, $conversation_id, $extra, false, $attachments);
                    if (!mc_is_error($response_open_ai) && $response_open_ai[0] && $response_open_ai[1] != 'mc-human-takeover') {
                        $unknow_answer = false;
                        $messages = [['text' => ['text' => [$response_open_ai[1]]]]];
                        $response = ['dialogflow' => $response, 'openai' => $response_open_ai];
                    }
                }
            }
            if ($unknow_answer && $unknow_language_message) {
                $language_detection_message = mc_get_multi_setting('google', 'google-language-detection-message', mc_get_multi_setting('dialogflow-language-detection', 'dialogflow-language-detection-message')); // Deprecated: mc_get_multi_setting('dialogflow-language-detection', 'dialogflow-language-detection-message')
                if (!empty($language_detection_message) && $conversation_id && $detected_language) {
                    $language_name = mc_google_get_language_name($detected_language);
                    $language_detection_message = str_replace('{language_name}', $language_name, mc_t($language_detection_message, $detected_language));
                    $message_id = mc_send_message($bot_id, $conversation_id, $language_detection_message)['id'];
                    return ['token' => $token, 'messages' => [['message' => $language_detection_message, 'attachments' => [], 'payload' => ['language_detection' => true], 'id' => $message_id]], 'response' => $response, 'language_detection_message' => $language_detection_message, 'message_id' => $message_id, 'user_language' => $user_language];
                }
            }
        }
    }

    $count = count($messages);
    $is_assistant = true;
    if (is_string($response)) {
        return ['response' => $response];
    }
    $response['outputAudio'] = '';
    for ($i = 0; $i < $count; $i++) {
        if (isset($messages[$i]['text']) && $messages[$i]['text']['text'][0]) {
            $is_assistant = false;
            break;
        }
    }
    for ($i = 0; $i < $count; $i++) {
        $bot_message = '';

        // Payload
        $payload = $i ? mc_isset($messages[$i], 'payload', []) : array_merge($payload, mc_isset($messages[$i], 'payload', []));
        if ($payload && $conversation_id && !$smart_reply) {
            if (isset($payload['redirect'])) {
                $payload['redirect'] = mc_dialogflow_merge_fields($payload['redirect'], $parameters, $language[0]);
            }
            if (isset($payload['archive-chat'])) {
                mc_update_conversation_status($conversation_id, 3);
                if (mc_get_multi_setting('close-message', 'close-active')) {
                    mc_close_message($conversation_id, $bot_id);
                }
                if (mc_get_multi_setting('close-message', 'close-transcript') && mc_isset(mc_get_active_user(), 'email')) {
                    $transcript = mc_transcript($conversation_id);
                    mc_email_create(mc_get_active_user_ID(), mc_get_user_name(), mc_isset(mc_get_active_user(), 'profile_image'), mc_get_multi_setting('transcript', 'transcript-message', ''), [[$transcript, $transcript]], true, $conversation_id);
                    $payload['force-message'] = true;
                }
            }
            if (isset($payload['update-user-details']) || isset($payload['update-user-language'])) {
                $payload_user_details = mc_isset($payload, 'update-user-details', []);
                $user = mc_get_user($user_id);
                if (!mc_is_agent($user)) {
                    if (isset($payload['update-user-language'])) {
                        $language_code = $payload['update-user-language'];
                        $language_codes = mc_get_json_resource('languages/language-codes.json');
                        $language_code = mc_get_language_code_by_name($language_code, $language_codes);
                        if (strlen($language_code) > 2) {
                            $language_code = mc_google_translate([$language_code], 'en', $token);
                            if (!empty($language_code[0])) {
                                foreach ($language_codes as $key => $value) {
                                    if ($language_code[0][0] == $value) {
                                        $language_code = $key;
                                        break;
                                    }
                                }
                            }
                        }
                        if (is_string($language_code) && strlen($language_code) == 2 && isset($language_codes[$language_code])) {
                            $payload_user_details['extra'] = ['language' => $language_code, 'browser_language' => ''];
                            $user_language = $language_code;
                            if ($multilingual) {
                                $dialogflow_agent = mc_dialogflow_get_agent();
                                if ($language_code == mc_isset($dialogflow_agent, 'defaultLanguageCode') || in_array($language_code, mc_isset($dialogflow_agent, 'supportedLanguageCodes', []))) {
                                    $response_success['language_detection'] = $language_code;
                                }
                            }
                        } else {
                            return false;
                        }
                    }
                    $payload['event'] = 'update-user';
                    $user['user_type'] = '';
                    mc_update_user($user_id, array_merge($user, $payload_user_details), mc_isset($payload_user_details, 'extra', []));
                }
            }
        }

        // Google Assistant
        if ($is_assistant) {
            if (isset($messages[$i]['platform']) && $messages[$i]['platform'] == 'ACTIONS_ON_GOOGLE') {
                if (isset($messages[$i]['simpleResponses']) && isset($messages[$i]['simpleResponses']['simpleResponses'])) {
                    $item = $messages[$i]['simpleResponses']['simpleResponses'];
                    if (isset($item[0]['textToSpeech'])) {
                        $bot_message = $item[0]['textToSpeech'];
                    } else if ($item[0]['displayText']) {
                        $bot_message = $item[0]['displayText'];
                    }
                }
            }
        } else if (isset($messages[$i]['text'])) {

            // Message
            $bot_message = $messages[$i]['text']['text'][0];
        }

        // Attachments
        $attachments = [];
        if ($payload) {
            if (isset($payload['attachments'])) {
                $attachments = $payload['attachments'];
                if (!$attachments && !is_array($attachments)) {
                    $attachments = [];
                }
            }
        }

        // WooCommerce
        if (defined('MC_WOOCOMMERCE')) {
            $woocommerce = mc_woocommerce_dialogflow_process_message($bot_message, $payload);
            $bot_message = $woocommerce[0];
            $payload = $woocommerce[1];
        }

        // Send message and human takeover
        if ($bot_message || $payload) {
            if ($conversation_id && !$smart_reply) {
                $is_human_takeover = mc_dialogflow_is_human_takeover($conversation_id);
                if ($human_takeover && $unknow_answer && strlen($message) > 3 && strpos($message, ' ') && !$is_human_takeover) {
                    $human_takeover_response = mc_chatbot_human_takeover($conversation_id, $human_takeover);
                    if ($human_takeover_response[1]) {
                        $response_success['human_takeover'] = true;
                    }
                    $results = array_merge($results, $human_takeover_response[0]);
                } else {
                    $last_agent = mc_isset(mc_get_last_agent_in_conversation($conversation_id), 'id');
                    if ($is_human_takeover && (isset($payload['human-takeover']) || strpos($bot_message, 'mc-human-takeover'))) {
                        $bot_message = mc_isset($human_takeover, 'message_fallback');
                        $payload = false;
                    }
                    if (($bot_message || $payload) && (!$is_human_takeover || !empty($payload['force-message']) || ((!$last_agent || !mc_is_user_online($last_agent)) && !$unknow_answer))) {
                        if (!$bot_message && isset($payload['force-message']) && $i > 0 && isset($messages[$i - 1]['text'])) {
                            $bot_message = $messages[$i - 1]['text']['text'][0];
                        }
                        $bot_message = mc_dialogflow_merge_fields($bot_message, $parameters, $language[0]);
                        if ($multilingual_translation && $bot_message) {
                            $continue = isset($language[1]) && $language[1] == 'language-translation';
                            $user_language = $user_language ? $user_language : mc_get_user_language($user_id);
                            if (!$continue) {
                                $dialogflow_agent = $dialogflow_agent ? $dialogflow_agent : mc_dialogflow_get_agent();
                                $continue = $user_language != mc_isset($dialogflow_agent, 'defaultLanguageCode') && !in_array($user_language, mc_isset($dialogflow_agent, 'supportedLanguageCodes', []));
                            }
                            if ($continue) {
                                $message = mc_google_translate([$bot_message], $user_language, $token);
                                if (!empty($message[0])) {
                                    $bot_message = $message[0][0];
                                }
                            }
                        }
                        $bot_message = mc_open_ai_text_formatting($bot_message);
                        $message_id = mc_send_message($bot_id, $conversation_id, $bot_message, $attachments, -1, $payload)['id'];
                        array_push($results, ['message' => mc_open_ai_text_formatting($bot_message), 'attachments' => $attachments, 'payload' => $payload, 'id' => $message_id]);
                    }
                }
            } else {
                array_push($results, ['message' => mc_dialogflow_merge_fields($bot_message, $parameters, $language[0]), 'attachments' => $attachments, 'payload' => $payload]);
            }
        }
    }
    if (count($results)) {
        $response_success['token'] = $token;
        $response_success['messages'] = $results;
        $response_success['response'] = $response;
        $response_success['user_language'] = $user_language;
        $response_success['message_language'] = $language[0];
        $response_success['translations'] = $translations;
        return $response_success;
    }
    if (isset($response['error']) && mc_isset($response['error'], 'code') != 400) {
        $admin_emails = mc_db_get('SELECT email FROM mc_users WHERE user_type = "admin"', false);
        $admin_emails_string = '';
        for ($i = 0; $i < count($admin_emails); $i++) {
            $admin_emails_string .= $admin_emails[$i]['email'] . ',';
        }
        $text = 'Dialogflow Error | ' . MC_URL . '/admin.php';
        mc_email_send(substr($admin_emails_string, 0, -1), $text, $text . '<br><br>' . json_encode($response));
    }
    return ['response' => $response];
}

/*
 * -----------------------------------------------------------
 * INTENTS
 * -----------------------------------------------------------
 *
 * 1. Create an Intent
 * 2. Update an existing Intent
 * 3. Create multiple Intents
 * 4. Delete multiple Intents
 * 5. Return all Intents
 *
 */

function mc_dialogflow_create_intent($training_phrases, $bot_responses, $language = '', $conversation_id = false, $services = false) {
    global $mc_entity_types;
    $training_phrases_api = [];
    $cx = mc_get_multi_setting('google', 'dialogflow-edition', mc_get_setting('dialogflow-edition')) == 'cx'; // Deprecated: mc_get_setting('dialogflow-edition')
    $mc_entity_types = $cx ? ($mc_entity_types ? $mc_entity_types : mc_isset(mc_dialogflow_curl('/entityTypes', '', false, 'GET'), 'entityTypes', [])) : false;
    $parameters = [];

    // Training phrases and parameters
    if (is_string($bot_responses)) {
        $bot_responses = [['text' => ['text' => $bot_responses]]];
    }
    for ($i = 0; $i < count($training_phrases); $i++) {
        if (is_string($training_phrases[$i])) {
            $parts = ['text' => $training_phrases[$i]];
        } else {
            $parts = $training_phrases[$i]['parts'];
            for ($j = 0; $j < count($parts); $j++) {
                if (empty($parts[$j]['text'])) {
                    array_splice($parts, $j, 1);
                } else if ($cx && isset($parts[$j]['entityType'])) {
                    for ($y = 0; $y < count($mc_entity_types); $y++) {
                        if ($mc_entity_types[$y]['displayName'] == $parts[$j]['alias']) {
                            $id = 'parameter_id_' . $y;
                            $parts[$j]['parameterId'] = $id;
                            $new = true;
                            for ($k = 0; $k < count($parameters); $k++) {
                                if ($parameters[$k]['id'] == $id) {
                                    $new = false;
                                    break;
                                }
                            }
                            if ($new) {
                                array_push($parameters, ['id' => $id, 'entityType' => $mc_entity_types[$y]['name']]);
                            }
                            break;
                        }
                    }
                }
            }
        }
        array_push($training_phrases_api, ['type' => 'TYPE_UNSPECIFIED', 'parts' => $parts, 'repeatCount' => 1]);
    }

    // Intent name
    $name = mc_isset($training_phrases_api[0]['parts'], 'text');
    if (!$name) {
        $parts = $training_phrases_api[0]['parts'];
        for ($i = 0; $i < count($parts); $i++) {
            $name .= $parts[$i]['text'];
        }
    }

    // Create the Intent
    $query = ['displayName' => ucfirst(str_replace('-', ' ', mc_string_slug(strlen($name) > 100 ? substr($name, 0, 99) : $name))), 'priority' => 500000, 'webhookState' => 'WEBHOOK_STATE_UNSPECIFIED', 'trainingPhrases' => $training_phrases_api, 'messages' => $bot_responses];
    if ($parameters) {
        $query['parameters'] = $parameters;
    }
    $response = mc_dialogflow_curl('/agent/intents', $query, $language);
    if ($cx) {
        $flow_name = '00000000-0000-0000-0000-000000000000';
        if ($conversation_id) {
            $messages = mc_db_get('SELECT payload FROM mc_messages WHERE conversation_id = ' . mc_db_escape($conversation_id, true) . ' AND payload <> "" ORDER BY id DESC');
            for ($i = 0; $i < count($messages); $i++) {
                $payload = json_decode($messages['payload'], true);
                if (isset($payload['queryResult']) && isset($payload['queryResult']['currentPage'])) {
                    $flow_name = $payload['queryResult']['currentPage'];
                    $flow_name = substr($flow_name, strpos($flow_name, '/flows/') + 7);
                    if (strpos($flow_name, '/'))
                        $flow_name = substr($flow_name, 0, strpos($flow_name, '/'));
                    break;
                }
            }
        }
        $flow = mc_dialogflow_curl('/flows/' . $flow_name, '', $language, 'GET');
        array_push($flow['transitionRoutes'], ['intent' => $response['name'], 'triggerFulfillment' => ['messages' => $bot_responses]]);
        $response = mc_dialogflow_curl('/flows/' . $flow_name . '?updateMask=transitionRoutes', $flow, $language, 'PATCH');
    }
    $response['response_open_ai'] = $services != 'dialogflow' && mc_chatbot_active(false, true) ? mc_open_ai_qea_training([[$training_phrases[0], $bot_responses[0]['text']['text']]], $language) : true;
    if (isset($response['displayName']) && $response['response_open_ai']) {
        return true;
    }
    return $response;
}

function mc_dialogflow_update_intent($intent, $training_phrases, $language = '', $services = false) {
    $intent_name = is_string($intent) ? $intent : $intent['name'];
    $pos = strpos($intent_name, '/intents/');
    $intent_name = $pos ? substr($intent_name, $pos + 9) : $intent_name;
    if (is_string($intent)) {
        $intent = mc_dialogflow_get_intents($intent_name, $language);
    }
    if (!isset($intent['trainingPhrases'])) {
        $intent['trainingPhrases'] = [];
    }
    for ($i = 0; $i < count($training_phrases); $i++) {
        array_push($intent['trainingPhrases'], ['type' => 'TYPE_UNSPECIFIED', 'parts' => ['text' => $training_phrases[$i]], 'repeatCount' => 1]);
    }
    $response = mc_dialogflow_curl('/agent/intents/' . $intent_name . '?updateMask=trainingPhrases', $intent, $language, 'PATCH');
    if ($services != 'dialogflow' && mc_chatbot_active(false, true)) {
        $response['response_open_ai'] = mc_open_ai_qea_training([[$training_phrases[0], $services]], $language);
    }
    return isset($response['name']) ? true : $response;
}

function mc_dialogflow_batch_intents($intents, $language = '') {
    if (mc_get_multi_setting('google', 'dialogflow-edition', mc_get_setting('dialogflow-edition')) == 'cx') { // Deprecated: mc_get_setting('dialogflow-edition', 'es')
        $response = [];
        for ($i = 0; $i < count($intents); $i++) {
            array_push($response, mc_dialogflow_create_intent($intents[$i]->data['trainingPhrases'], $intents[$i]->data['messages'], $language));
        }
        return $response;
    } else {
        $intents_array = [];
        for ($i = 0; $i < count($intents); $i++) {
            array_push($intents_array, $intents[$i]->data());
        }
        $query = ['intentBatchInline' => ['intents' => $intents_array], 'intentView' => 'INTENT_VIEW_UNSPECIFIED'];
        if (!empty($language))
            $query['languageCode'] = $language;
        return mc_dialogflow_curl('/agent/intents:batchUpdate', $query);
    }
}

function mc_dialogflow_batch_intents_delete($intents) {
    return mc_dialogflow_curl('/agent/intents:batchDelete', ['intents' => $intents]);
}

function mc_dialogflow_get_intents($intent_name = false, $language = '') {
    $next_page_token = true;
    $paginatad_items = [];
    $intents = [];
    while ($next_page_token) {
        $items = mc_dialogflow_curl($intent_name ? ('/agent/intents/' . $intent_name . '?intentView=INTENT_VIEW_FULL') : ('/agent/intents?pageSize=1000&intentView=INTENT_VIEW_FULL' . ($next_page_token !== true && $next_page_token !== false ? ('&pageToken=' . $next_page_token) : '')), '', $language, 'GET');
        if ($intent_name)
            return $items;
        $next_page_token = mc_isset($items, 'nextPageToken');
        if (mc_is_error($next_page_token))
            die($next_page_token);
        array_push($paginatad_items, mc_isset($items, 'intents'));
    }
    for ($i = 0; $i < count($paginatad_items); $i++) {
        $items = $paginatad_items[$i];
        if ($items) {
            for ($j = 0; $j < count($items); $j++) {
                if (!empty($items[$j]))
                    array_push($intents, $items[$j]);
            }
        }
    }
    return $intents;
}

/*
 * -----------------------------------------------------------
 * ENTITIES
 * -----------------------------------------------------------
 *
 * Create, get, update, delete a Dialogflow entities
 *
 */

function mc_dialogflow_create_entity($entity_name, $values, $language = '') {
    $response = mc_dialogflow_curl('/agent/entityTypes', is_a($values, 'MCDialogflowEntity') ? $values->data() : (new MCDialogflowEntity($entity_name, $values))->data(), $language);
    if (isset($response['displayName'])) {
        return true;
    } else if (isset($response['error']) && mc_isset($response['error'], 'status') == 'FAILED_PRECONDITION') {
        return new MCValidationError('duplicate-dialogflow-entity');
    }
    return $response;
}

function mc_dialogflow_update_entity($entity_id, $values, $entity_name = false, $language = '') {
    $response = mc_dialogflow_curl('/agent/entityTypes/' . $entity_id, is_a($values, 'MCDialogflowEntity') ? $values->data() : (new MCDialogflowEntity($entity_name, $values))->data(), $language, 'PATCH');
    if (isset($response['displayName'])) {
        return true;
    }
    return $response;
}

function mc_dialogflow_get_entity($entity_id = 'all', $language = '') {
    $entities = mc_dialogflow_curl('/agent/entityTypes', '', $language, 'GET');
    if (isset($entities['entityTypes'])) {
        $entities = $entities['entityTypes'];
        if ($entity_id == 'all') {
            return $entities;
        }
        for ($i = 0; $i < count($entities); $i++) {
            if ($entities[$i]['displayName'] == $entity_id) {
                return $entities[$i];
            }
        }
        return new MCValidationError('entity-not-found');
    } else
        return $entities;
}

/*
 * -----------------------------------------------------------
 * MISCELLANEOUS
 * -----------------------------------------------------------
 *
 * 1. Get a fresh Dialogflow access token
 * 2. Convert the Dialogflow merge fields to the final values
 * 3. Activate a context in the active conversation
 * 4. Return the details of a Dialogflow agent
 * 5. Chinese language sanatization
 * 6. Dialogflow curl
 * 7. Human takeover
 * 8. Check if human takeover is active
 * 9. Return the Dialogflow human takeover settings
 * 10. Execute payloads
 * 11. Add Intents to saved replies
 * 12. Check if unknow answer
 * 13. PDF to text
 * 14. JSON to text
 * 15. CSV to text
 * 16. Support Board database embedding
 * 17. Check if manual or automatic sync mode
 * 18. Data scraping
 * 19. Sitemap generation
 * 20. Check if a string terminates with a dot or similar character
 *
 */

function mc_dialogflow_get_token($token = true) {
    if ($token === true) {
        global $dialogflow_token;
        if (!empty($dialogflow_token)) {
            return $dialogflow_token;
        }
        $dialogflow_token = mc_get_external_setting('dialogflow_token');
        if ($dialogflow_token && time() < $dialogflow_token[1]) {
            $dialogflow_token = $dialogflow_token[0];
            return $dialogflow_token;
        }
    } else if ($token === false) {
        $token = mc_get_multi_setting('google', 'google-refresh-token');
    }
    if (empty($token)) {
        return mc_error('google-refresh-token-not-found', 'mc_open_ai_message', 'Click the synchronize button to get the refresh token.');
    }
    $info = mc_google_key();
    $query = '{ refresh_token: "' . $token . '", grant_type: "refresh_token", client_id: "' . $info[0] . '", client_secret: "' . $info[1] . '" }';
    $response = mc_curl('https://accounts.google.com/o/oauth2/token', $query, ['Content-Type: application/json', 'Content-Length: ' . strlen($query)]);
    $token = mc_isset($response, 'access_token');
    if ($token) {
        mc_save_external_setting('dialogflow_token', [$token, time() + $response['expires_in']]);
        $dialogflow_token = $token;
        return $token;
    }
    return json_encode($response);
}

function mc_dialogflow_merge_fields($message, $parameters, $language = '') {
    if (defined('MC_WOOCOMMERCE')) {
        $message = mc_woocommerce_merge_fields($message, $parameters, $language);
    }
    return $message;
}

function mc_dialogflow_set_active_context($context_name, $parameters = [], $life_span = 5, $token = false, $user_id = false, $language = false) {
    if (!mc_get_multi_setting('google', 'dialogflow-active')) {
        return false;
    }
    $language = $language === false ? (mc_get_multi_setting('google', 'google-multilingual') ? mc_get_user_language($user_id) : '') : $language;
    $session_id = $user_id === false ? mc_isset(mc_get_active_user(), 'id', 'mc') : $user_id;
    $parameters = empty($parameters) ? '' : ', "parameters": ' . (is_string($parameters) ? $parameters : json_encode($parameters));
    $query = '{ "queryInput": { "text": { "languageCode": "' . (empty($language) ? 'en' : $language) . '", "text": "mc-trigger-context" }}, "queryParams": { "contexts": [{ "name": "projects/' . mc_get_multi_setting('google', 'google-project-id') . '/agent/sessions/' . $session_id . '/contexts/' . $context_name . '", "lifespanCount": ' . $life_span . $parameters . ' }] }}';
    return mc_dialogflow_curl('/agent/sessions/' . $session_id . ':detectIntent', $query, false, 'POST', $token);
}

function mc_dialogflow_get_agent() {
    return mc_dialogflow_curl('/agent', '', '', 'GET');
}

function mc_dialogflow_language_code($language) {
    return $language == 'zh' ? 'zh-cn' : ($language == 'zt' ? 'zh-tw' : $language);
}

function mc_dialogflow_curl($url_part, $query = '', $language = false, $type = 'POST', $token = false, $project_id = false) {

    // Project ID
    if (!$project_id) {
        $project_id = mc_get_multi_setting('google', 'google-project-id');
        if (empty($project_id)) {
            return mc_error('project-id-not-found', 'mc_dialogflow_curl');
        }
    }

    // Retrive token
    $token = empty($token) || $token == -1 ? mc_dialogflow_get_token() : $token;
    if (mc_is_error($token)) {
        return mc_error('token-error', 'mc_dialogflow_curl');
    }

    // Language
    if (!empty($language)) {
        $language = (strpos($url_part, '?') ? '&' : '?') . 'languageCode=' . $language;
    }

    // Query
    if (!is_string($query)) {
        $query = json_encode($query);
    }

    // Edition and version
    $edition = mc_get_multi_setting('google', 'dialogflow-edition', mc_get_setting('dialogflow-edition', 'es')); // Deprecated: mc_get_setting('dialogflow-edition', 'es')
    $version = 'v2beta1/projects/';
    $cx = $edition == 'cx';
    if ($cx) {
        $version = 'v3beta1/';
        $url_part = str_replace('/agent/', '/', $url_part);
    }

    // Location
    $location = mc_get_multi_setting('google', 'dialogflow-location', mc_get_setting('dialogflow-location', '')); // Deprecated: mc_get_setting('dialogflow-location', '')
    $location_session = $location && !$cx ? '/locations/' . substr($location, 0, -1) : '';

    // Send
    $url = 'https://' . $location . 'dialogflow.googleapis.com/' . $version . $project_id . $location_session . $url_part . $language;
    $response = mc_curl($url, $query, ['Content-Type: application/json', 'Authorization: Bearer ' . $token, 'Content-Length: ' . strlen($query)], $type);
    return $type == 'GET' ? json_decode($response, true) : $response;
}

function mc_dialogflow_human_takeover($conversation_id, $auto_messages = false) {
    $human_takeover = mc_dialogflow_get_human_takeover_settings();
    $conversation_id = mc_db_escape($conversation_id, true);
    $bot_id = mc_get_bot_id();
    $data = mc_db_get('SELECT A.id AS `user_id`, A.email, A.first_name, A.last_name, A.profile_image, B.agent_id, B.department, B.status_code FROM mc_users A, mc_conversations B WHERE A.id = B.user_id AND B.id = ' . $conversation_id);
    $user_id = $data['user_id'];
    $messages = mc_db_get('SELECT A.user_id, A.message, A.attachments, A.creation_time, B.first_name, B.last_name, B.profile_image, B.user_type FROM mc_messages A, mc_users B WHERE A.conversation_id = ' . $conversation_id . ' AND A.user_id = B.id AND A.message <> "' . $human_takeover['confirm'] . '" AND A.message NOT LIKE "%mc-human-takeover%" AND A.payload NOT LIKE "%human-takeover%" ORDER BY A.id ASC', false);
    $count = count($messages);
    $last_message = $messages[$count - 1]['message'];
    $response = [];
    mc_send_message($bot_id, $conversation_id, '', [], 2, ['human-takeover' => true]);
    $GLOBALS['human-takeover-' . $conversation_id] = true;

    // Human takeover message and status code
    $message = $human_takeover['message_confirmation'];
    if (!empty($message)) {
        $message_id = mc_send_message($bot_id, $conversation_id, $message, [], 2, ['human-takeover-message-confirmation' => true, 'preview' => $last_message])['id'];
        array_push($response, ['message' => $message, 'id' => $message_id]);
    }

    // Auto messages
    if ($auto_messages) {
        $auto_messages = ['offline', 'follow_up'];
        for ($i = 0; $i < count($auto_messages); $i++) {
            $auto_message = $i == 0 || empty($data['email']) ? mc_execute_bot_message($auto_messages[$i], $conversation_id, $last_message) : false;
            if ($auto_message) {
                array_push($response, $auto_message);
            }
        }
    }

    // Notifications
    mc_send_agents_notifications($last_message, str_replace('{T}', mc_get_setting('bot-name', 'Chatbot'), mc_('This message has been sent because {T} does not know the answer to the user\'s question.')), $conversation_id, false, $data, ['email' => mc_email_get_conversation_code($conversation_id, 20, true)]);

    // Slack
    if (defined('MC_SLACK') && mc_get_setting('slack-active')) {
        for ($i = 0; $i < count($messages); $i++) {
            mc_send_slack_message($user_id, mc_get_user_name($messages[$i]), $messages[$i]['profile_image'], $messages[$i]['message'], mc_isset($messages[$i], 'attachments', []), $conversation_id);
        }
    }

    return $response;
}

function mc_chatbot_human_takeover($conversation_id, $human_takeover_settings) {
    if ($human_takeover_settings['auto']) {
        $human_takeover_messages = mc_dialogflow_human_takeover($conversation_id);
        $messages = [];
        for ($j = 0; $j < count($human_takeover_messages); $j++) {
            array_push($messages, ['message' => mc_t($human_takeover_messages[$j]['message']), 'attachments' => [], 'payload' => false, 'id' => $human_takeover_messages[$j]['id']]);
        }
        return [$messages, true];
    } else {
        $human_takeover_message = '[chips id="mc-human-takeover" options="' . str_replace(',', '\,', mc_rich_value($human_takeover_settings['confirm'], false)) . ',' . str_replace(',', '\,', mc_rich_value($human_takeover_settings['cancel'], false)) . '" message="' . mc_rich_value($human_takeover_settings['message']) . '"]';
        $message_id = mc_send_message(mc_get_bot_id(), $conversation_id, $human_takeover_message)['id'];
        return [[['message' => $human_takeover_message, 'attachments' => [], 'payload' => false, 'id' => $message_id]], false];
    }
}

function mc_dialogflow_is_human_takeover($conversation_id) {
    $name = 'human-takeover-' . $conversation_id;
    if (isset($GLOBALS[$name])) {
        return $GLOBALS[$name];
    }
    $agent_ids = mc_get_agents_ids();
    $response = mc_isset(mc_db_get('SELECT id FROM mc_messages WHERE (user_id IN (' . implode(',', $agent_ids) . ') || payload = "{\"human-takeover\":true}") AND conversation_id = ' . mc_db_escape($conversation_id, true) . ' AND creation_time > "' . mc_gmt_now(864000) . '" ORDER BY id DESC LIMIT 1'), 'id');
    if ($response) {
        $response = empty(mc_db_get('SELECT id FROM mc_messages WHERE id >= ' . $response . ' AND conversation_id = ' . mc_db_escape($conversation_id, true) . ' AND payload = "{\"event\":\"conversation-status-update-3\"}"'));
    }
    $GLOBALS[$name] = $response;
    return $response;
}

function mc_dialogflow_get_human_takeover_settings() {
    $settings = mc_get_setting('dialogflow-human-takeover');
    return ['active' => $settings['dialogflow-human-takeover-active'], 'message' => mc_t(mc_isset($settings, 'dialogflow-human-takeover-message', 'I\'m a chatbot. Do you want to get in touch with one of our agents?')), 'message_confirmation' => mc_t(mc_isset($settings, 'dialogflow-human-takeover-message-confirmation', 'Alright! We will get in touch soon!')), 'message_fallback' => mc_t(mc_isset($settings, 'dialogflow-human-takeover-message-fallback', 'An agent has already been contacted and will respond shortly.')), 'confirm' => mc_t(mc_isset($settings, 'dialogflow-human-takeover-confirm', 'Yes')), 'cancel' => mc_t(mc_isset($settings, 'dialogflow-human-takeover-cancel', 'Cancel')), 'auto' => $settings['dialogflow-human-takeover-auto'], 'disable_chatbot' => $settings['dialogflow-human-takeover-disable-chatbot']];
}

function mc_dialogflow_payload($payload, $conversation_id, $message = false, $extra = false) {
    if (isset($payload['agent'])) {
        mc_update_conversation_agent($conversation_id, $payload['agent'], $message);
    }
    if (isset($payload['department'])) {
        mc_update_conversation_department($conversation_id, $payload['department'], $message);
    }
    if (isset($payload['tags'])) {
        mc_tags_update($conversation_id, $payload['tags'], true);
    }
    if (isset($payload['human-takeover'])) {
        $messages = mc_dialogflow_human_takeover($conversation_id, $extra && isset($extra['source']));
        $source = mc_isset($extra, 'source');
        if ($source) {
            for ($i = 0; $i < count($messages); $i++) {
                $message = $messages[$i]['message'];
                $attachments = mc_isset($messages[$i], 'attachments', []);
                mc_messaging_platforms_send_message($message, $extra, $messages[$i]['id'], $attachments);
            }
        }
    }
    if (isset($payload['send-email'])) {
        $send_to_active_user = $payload['send-email']['recipient'] == 'active_user';
        mc_email_create($send_to_active_user ? mc_get_active_user_ID() : 'agents', $send_to_active_user ? mc_get_setting('bot-name') : mc_get_user_name(), $send_to_active_user ? mc_get_setting('bot-image') : mc_isset(mc_get_active_user(), 'profile_image'), $payload['send-email']['message'], mc_isset($payload['send-email'], 'attachments'), false, $conversation_id);
    }
    if (isset($payload['redirect']) && $extra) {
        $message_id = mc_send_message(mc_get_bot_id(), $conversation_id, $payload['redirect']);
        mc_messaging_platforms_send_message($payload['redirect'], $extra, $message_id);
    }
    if (isset($payload['transcript']) && $extra) {
        $transcript_url = mc_transcript($conversation_id);
        $attachments = [[$transcript_url, $transcript_url]];
        $message_id = mc_send_message(mc_get_bot_id(), $conversation_id, '', $attachments);
        mc_messaging_platforms_send_message($extra['source'] == 'ig' || $extra['source'] == 'fb' ? '' : $transcript_url, $attachments, $message_id);
    }
    if (isset($payload['rating'])) {
        mc_set_rating(['conversation_id' => $conversation_id, 'agent_id' => mc_isset(mc_get_last_agent_in_conversation($conversation_id), 'id', mc_get_bot_id()), 'user_id' => mc_get_active_user_ID(), 'message' => '', 'rating' => $payload['rating']]);
    }
}

function mc_dialogflow_saved_replies() {
    $settings = mc_get_settings();
    $saved_replies = mc_get_setting('saved-replies', []);
    $intents = mc_dialogflow_get_intents();
    $count = count($saved_replies);
    for ($i = 0; $i < count($intents); $i++) {
        if (isset($intents[$i]['messages'][0]) && isset($intents[$i]['messages'][0]['text']) && isset($intents[$i]['messages'][0]['text']) && isset($intents[$i]['messages'][0]['text']['text'])) {
            $slug = mc_string_slug($intents[$i]['displayName']);
            $existing = false;
            for ($j = 0; $j < $count; $j++) {
                if ($slug == $saved_replies[$j]['reply-name']) {
                    $existing = true;
                    break;
                }
            }
            if (!$existing) {
                array_push($saved_replies, ['reply-name' => $slug, 'reply-text' => $intents[$i]['messages'][0]['text']['text'][0]]);
            }
        }
    }
    $settings['saved-replies'][0] = $saved_replies;
    return mc_save_settings($settings);
}

function mc_dialogflow_is_unknow($dialogflow_response) {
    $dialogflow_response = mc_isset($dialogflow_response, 'response', $dialogflow_response);
    $query_result = mc_isset($dialogflow_response, 'queryResult', []);
    return (mc_isset($query_result, 'action') == 'input.unknown' || (isset($query_result['match']) && $query_result['match']['matchType'] == 'NO_MATCH')) || (mc_get_multi_setting('google', 'dialogflow-confidence') && mc_isset($query_result, 'intentDetectionConfidence') < floatval(mc_get_multi_setting('google', 'dialogflow-confidence'))) || isset($dialogflow_response['error']);
}

function mc_pdf_to_text($path) {
    if (file_exists($path)) {
        require('pdf/autoload.php');
        $parser = new \Smalot\PdfParser\Parser();
        $pdf = $parser->parseFile($path);
        return $pdf->getText();
    }
    return '';
}

function mc_json_to_text($path_or_data, $indent = 0) {
    $text = '';
    $prefix = str_repeat('  ', $indent);
    if ($indent === 0) {
        $path_or_data = json_decode(file_get_contents($path_or_data), true);
    }
    foreach ($path_or_data as $key => $value) {
        $text .= is_array($value) ? $prefix . ucfirst($key) . ':' . PHP_EOL . mc_json_to_text($value, $indent + 1) : $prefix . ucfirst($key) . ': ' . $value . PHP_EOL;
    }
    return $text;
}

function mc_csv_to_text($url) {
    $data = mc_csv_read($url);
    $text = '';
    if ($data) {
        $keys = array_keys($data[0]);
        foreach ($data as $row) {
            foreach ($keys as $key) {
                $text .= ucfirst($key) . ': ' . (isset($row[$key]) ? $row[$key] : '') . PHP_EOL;
            }
            $text .= PHP_EOL;
        }
    }
    return $text;
}

function mc_get_sitemap_urls($sitemap_url, &$urls = []) {
    $xml = mc_get($sitemap_url);
    $sitemap = new SimpleXmlElement($xml);
    foreach ($sitemap->url as $url) {
        if (!strpos($url->loc, 'mailto:') && !strpos($url->loc, 'javascript:')) {
            array_push($urls, strval($url->loc));
        }
    }
    foreach ($sitemap->sitemap as $sitemap_entry) {
        $nested_sitemap_url = strval($sitemap_entry->loc);
        mc_get_sitemap_urls($nested_sitemap_url, $urls);
    }
    return $urls;
}

function mc_ai_is_manual_sync($source) {
    switch ($source) {
        case 'google':
            return !mc_is_cloud() || !defined('GOOGLE_CLIENT_ID') || mc_get_multi_setting('google', 'google-sync-mode', 'manual') == 'manual'; // Deprecated: remove default , 'manual'
        case 'open-ai':
            return (!mc_is_cloud() || !defined('OPEN_AI_KEY') || mc_get_multi_setting('open-ai', 'open-ai-sync-mode', 'manual') == 'manual') && mc_defined('OPEN_AI_KEY', -1) != trim(mc_get_multi_setting('open-ai', 'open-ai-key')); // Deprecated: remove default , 'manual'
    }
    return false;
}

function mc_is_string_ends($string) {
    return in_array(substr(trim($string), -1), ['.', ',', ':', '!', '?', ';', '።', '।', '。', '။']);
}

/*
 * -----------------------------------------------------------
 * SMART REPLY
 * -----------------------------------------------------------
 *
 * 1. Return the suggestions
 * 2. Update a smart reply conversation with a new message
 * 3. Generate the conversation transcript data for a dataset
 *
 */

function mc_dialogflow_smart_reply($message, $dialogflow_languages = false, $token = false, $conversation_id = false, $user_id = false) {
    $suggestions = [];
    $smart_reply_response = false;
    if (!empty($dialogflow_languages)) {
        $GLOBALS['dialogflow_languages'] = $dialogflow_languages;
    }
    $token = empty($token) ? mc_dialogflow_get_token() : $token;
    $dialogflow_active = mc_chatbot_active(true, false);
    $messages = $dialogflow_active ? mc_dialogflow_message($conversation_id, $message, $token, false, [], 'smart-reply') : [];
    if (mc_is_error($messages)) {
        return mc_error('smart-reply-error', 'mc_dialogflow_smart_reply', $messages);
    }
    if (!empty($messages['messages']) && !mc_dialogflow_is_unknow($messages['response'])) {
        for ($i = 0; $i < count($messages['messages']); $i++) {
            $value = $messages['messages'][$i]['message'];
            if (!empty($value) && !strpos($value, 'mc-human-takeover')) {
                array_push($suggestions, $value);
            }
        }
        if (mc_get_multi_setting('google', 'google-multilingual-translation') && $messages['message_language'] != mc_get_user_language(mc_get_active_user_ID())) {
            $translation = mc_google_translate($suggestions, mc_get_user_language(mc_get_active_user_ID()));
            if (!empty($translation[0])) {
                for ($i = 0; $i < count($suggestions); $i++) {
                    if (!empty($translation[0][$i])) {
                        $suggestions[$i] = $translation[0][$i];
                    }
                }
            }
        }
    }
    if (!count($suggestions) && !$dialogflow_active && (mc_get_multi_setting('open-ai', 'open-ai-active') || mc_get_multi_setting('open-ai', 'open-ai-smart-reply'))) {
        $suggestions = mc_isset(mc_open_ai_smart_reply($message, $conversation_id), 'suggestions', []);
    }
    return ['suggestions' => $suggestions, 'token' => mc_isset($messages, 'token'), 'dialogflow_languages' => $dialogflow_languages, 'smart_reply' => $smart_reply_response];
}

function mc_dialogflow_knowledge_articles($articles = false, $language = false) {
    $language = $language ? mc_dialogflow_language_code($language) : false;
    if (mc_isset(mc_dialogflow_get_agent(), 'defaultLanguageCode') != 'en') {
        return 'dialogflow-language-not-supported';
    }
    if (!$articles) {
        $articles = mc_get_articles(false, false, true, false, 'all');
    }
    if ($articles) {

        // Create articles file
        $faq = [];
        for ($i = 0; $i < count($articles); $i++) {
            $content = strip_tags($articles[$i]['content']);
            if (mb_strlen($content) > 150) {
                $content = mb_substr($content, 0, 150);
                $content = mb_substr($content, 0, mb_strrpos($content, ' ') + 1) . '... [button link="#article-' . $articles[$i]['id'] . '" name="' . mc_('Read more') . '" style="link"]';
                $content = str_replace(', ...', '...', $content);
            }
            array_push($faq, [$articles[$i]['title'], $content]);
        }
        $file_path = mc_csv($faq, false, 'dialogflow-faq', false);
        $file = fopen($file_path, 'r');
        $file_bytes = fread($file, filesize($file_path));
        fclose($file);
        unlink($file_path);

        // Create new knowledge if not exist
        $knowledge_base_name = mc_get_external_setting('dialogflow-knowledge', []);
        if (!isset($knowledge_base_name[$language ? $language : 'default'])) {
            $query = ['displayName' => 'Support Board'];
            if ($language) {
                $query['languageCode'] = $language;
            }
            $name = mc_isset(mc_dialogflow_curl('/knowledgeBases', $query, false, 'POST'), 'name');
            $name = substr($name, strripos($name, '/') + 1);
            $knowledge_base_name[$language ? $language : 'default'] = $name;
            mc_save_external_setting('dialogflow-knowledge', $knowledge_base_name);
            $knowledge_base_name = $name;
        } else {
            $knowledge_base_name = $knowledge_base_name['default'];
        }

        // Save knowledge in Dialogflow
        $documents = mc_isset(mc_dialogflow_curl('/knowledgeBases/' . $knowledge_base_name . '/documents', '', false, 'GET'), 'documents', []);
        for ($i = 0; $i < count($documents); $i++) {
            $name = $documents[0]['name'];
            $response = mc_dialogflow_curl(substr($name, stripos($name, 'knowledgeBases/') - 1), '', false, 'DELETE');
        }
        $response = mc_dialogflow_curl('/knowledgeBases/' . $knowledge_base_name . '/documents', ['displayName' => 'Support Board', 'mimeType' => 'text/csv', 'knowledgeTypes' => ['FAQ'], 'rawContent' => base64_encode($file_bytes)], false, 'POST');
        if ($response && isset($response['error']) && mc_isset($response['error'], 'status') == 'NOT_FOUND') {
            mc_save_external_setting('dialogflow-knowledge', false);
            return false;
        }
    }
    return true;
}

function mc_generate_sitemap($url) {
    require_once('sitemap-generator.php');
    set_time_limit(900);
    $path = mc_upload_path() . '/sitemap.xml';
    $smg = new SitemapGenerator([
        'SITE_URL' => $url,
        'ALLOW_EXTERNAL_LINKS' => false,
        'ALLOW_ELEMENT_LINKS' => false,
        'CRAWL_ANCHORS_WITH_ID' => '',
        'KEYWORDS_TO_SKIP' => [],
        'SAVE_LOC' => $path,
        'PRIORITY' => 1,
        'CHANGE_FREQUENCY' => 'daily',
        'LAST_UPDATED' => date('Y-m-d'),
    ]);
    $smg->GenerateSitemap();
    $urls = mc_get_sitemap_urls(mc_upload_path(true) . '/sitemap.xml');
    unlink($path);
    return $urls;
}

/*
 * -----------------------------------------------------------
 * OPEN AI
 * -----------------------------------------------------------
 *
 * 1. OpenAI curl
 * 2. Send a message and returns the OpenAI reply
 * 3. Generate Dialogflow user expressions
 * 4. Generate user questions
 * 5. Generate the smart replies
 * 6. Spelling correction
 * 7. Remove auto generated AI texts
 * 8. Check if the message returned by OpenAI is valid
 * 9. Upload a file to OpenAI
 * 10. Embedding functions
 * 11. PDF or TEXT file to paragraphs
 * 12. Get the default gpt model
 * 13. Send an audio file to OpenAI and return it's transcription
 * 14. Return the OpenAI key
 * 15. OpenAI Assistant
 * 16. AI data scraper
 * 17. Troubleshoting
 * 18. HTML to paragraphs
 * 23. Server-side training
 * 20. Get training file names
 * 21. Playground message
 * 22. Create a temporary conversation to test the chatbot
 * 23. Check if an URL is a of a file compatible with the OpenAI training
 * 24. Execute set data
 * 25. Execute actions
 * 26. Get max tokens
 * 27. Send fallback message
 *
 */

function mc_open_ai_curl($url_part, $post_fields = [], $type = 'POST') {
    if (mc_cloud_membership_has_credits('open-ai')) {
        $open_ai_key = mc_open_ai_key();
        $response = mc_curl('https://api.openai.com/v1/' . $url_part, json_encode($post_fields, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE), ['Content-Type: application/json', 'Authorization: Bearer ' . $open_ai_key], $type, 30);
        if (mc_is_debug() && isset($response['error'])) {
            return mc_error('open-ai-error', 'mc_open_ai_curl', $response['error']);
        }
        if (mc_is_cloud() && mc_defined('OPEN_AI_KEY') == $open_ai_key) {
            $tokens = mc_isset(mc_isset($response, 'usage'), 'total_tokens');
            $model = mc_isset($post_fields, 'model', 'gpt-4.1-mini');
            if (!$tokens) {
                return mc_error('no-usage', 'mc_open_ai_curl');
            }
            mc_cloud_membership_use_credits($model, 'open-ai', $tokens);
        }
        return $response;
    }
    return mc_error('no-credits', 'mc_open_ai_curl');
}

function mc_open_ai_message($message, $max_tokens = false, $model = false, $conversation_id = false, $extra = false, $audio = false, $attachments = [], $context = false) {
    global $MC_OPEN_AI_PLAYGROUND;
    global $MC_OPEN_AI_RECURSION_CHECK;
    global $MC_OPEN_AI_RECURSION_CHECK_2;
    global $MC_OPEN_AI_RECURSION_CHECK_3;
    $language = strtolower(mc_isset($extra, 'language'));
    $attachments_response = [];
    $is_google_search = $extra == 'embeddings-search';
    $is_scraping = $extra == 'scraping';
    $is_embeddings = mc_isset($extra, 'embeddings') || $is_google_search;
    $is_rewrite = $extra == 'rewrite';
    $is_smart_reply = mc_isset($extra, 'smart_reply');
    $message = $audio ? mc_open_ai_audio_to_text($audio, $language, mc_isset($extra, 'user_id'), false, $conversation_id) : (is_string($message) ? trim($message) : $message);
    $message_ = '';
    $attachments = mc_json_array($attachments);
    if (!$is_embeddings) {
        for ($i = 0; $i < count($attachments); $i++) {
            if (strpos($attachments[$i][0], 'voice_message') === false) {
                $message_ .= $attachments[$i][1] . ', ';
            }
        }
        if ($message_) {
            $message = trim(str_replace('..', '.', $message . ($message ? '.' : '') . ' ' . substr($message_, 0, -2)));
        }
    }
    if (empty($message)) {
        return [true, false, false, false];
    }
    if (mc_is_cloud()) {
        mc_cloud_membership_validation(true);
        if (!mc_cloud_membership_has_credits('open-ai')) {
            return mc_error('no-credits', 'mc_open_ai_message');
        }
    }
    $settings = mc_get_setting('open-ai');
    $response = false;
    $conversation_status_code = false;
    $dialogflow_active = mc_chatbot_active(true, false);
    $token = mc_isset($extra, 'token');
    $human_takeover = false;
    $human_takeover_settings = mc_dialogflow_get_human_takeover_settings();
    $human_takeover_active = $human_takeover_settings['active'];
    $payload = [];
    $unknow_answer = false;
    $open_ai_mode = mc_isset($settings, 'open-ai-mode', '');
    $extra_response = false;
    $client_side_payload = [];
    $messages = $conversation_id ? mc_db_get('SELECT A.id, A.message, A.payload, A.user_id, A.creation_time, B.user_type FROM mc_messages A, mc_users B, mc_conversations C WHERE A.conversation_id = ' . mc_db_escape($conversation_id, true) . ' AND A.conversation_id = C.id AND B.id = A.user_id ORDER BY A.id ASC LIMIT 1000', false) : [['message' => $is_embeddings ? $message['user_prompt'] : $message, 'user_type' => 'user']];
    $count = count($messages);
    $flows_structured_output = false;
    $is_chips_response = false;
    $user_id = mc_isset($extra, 'user_id', mc_get_active_user_ID());
    $is_embedding_response = false;
    $is_human_takeover = false;
    $is_multilingual_via_translation = mc_get_setting('dialogflow-multilingual-translation') || mc_get_multi_setting('google', 'google-multilingual-translation'); // Depreacted: mc_get_setting('dialogflow-multilingual-translation')
    $model = $model ? $model : mc_isset($settings, 'open-ai-custom-model', mc_isset($settings, 'open-ai-model', 'gpt-4.1-mini'));
    $chat_model = $model != 'gpt-3.5-turbo-instruct';
    $url_part = $chat_model ? 'chat/completions' : 'completions';
    $query = ['model' => $model, 'temperature' => floatval(mc_isset($settings, 'open-ai-temperature', 1)), 'presence_penalty' => floatval(mc_isset($settings, 'open-ai-presence-penalty', 0)), 'frequency_penalty' => floatval(mc_isset($settings, 'open-ai-frequency-penalty', 0)), 'top_p' => 1, 'tools' => []];
    if ($token == 'false') {
        $token = false;
    }
    if (!$dialogflow_active) {
        $is_human_takeover = !$is_rewrite && !$is_scraping && !$is_smart_reply && $conversation_id && $MC_OPEN_AI_PLAYGROUND === null && mc_dialogflow_is_human_takeover($conversation_id);
        if ($is_human_takeover && $count) {
            $time = mc_gmt_now(600, true);
            $message_fallback = $human_takeover_active ? $human_takeover_settings['message_fallback'] : false;
            for ($i = $count - 1; $i > -1; $i--) {
                if (mc_is_agent($messages[$i]['user_type'], true) && mc_is_user_online($messages[$i]['user_id'])) {
                    return [true, false];
                }
            }
            if ($message_fallback) {
                for ($i = $count - 1; $i > -1; $i--) {
                    if (strpos($messages[$i]['payload'], 'human-takeover-message-fallback')) {
                        if (strtotime($messages[$i]['creation_time']) > $time) {
                            $message_fallback = false;
                        }
                        break;
                    }
                }
                if ($message_fallback) {
                    mc_send_message(mc_get_bot_id(), $conversation_id, $message_fallback, $attachments_response, false, ['human-takeover-message-fallback' => true]);
                    return [true, $message_fallback, $token, true];
                }
            }
            if (mc_isset($human_takeover_settings, 'disable_chatbot')) {
                return [true, false];
            }
        }

        // Human takeover messaging apps
        if (mc_isset($extra, 'messaging-app') && $human_takeover_active && !$is_smart_reply) {
            $is_button_confirm = mc_rich_value($human_takeover_settings['confirm'], false) == $message;
            if ($is_button_confirm) {
                $last_messages = mc_db_get('SELECT message, payload FROM mc_messages WHERE conversation_id = ' . mc_db_escape($conversation_id, true) . ' ORDER BY id DESC LIMIT 2', false);
                if ($last_messages && count($last_messages) > 1 && strpos($last_messages[1]['message'] . $last_messages[1]['payload'], 'mc-human-takeover')) {
                    return [true, mc_dialogflow_human_takeover($conversation_id), false, $is_button_confirm];
                }
            } else if (mc_rich_value($human_takeover_settings['cancel'], false) == $message) {
                return [true, false, false, false];
            }
        }

        // Multilingual
        if (!$is_embeddings && !$is_rewrite && !$is_scraping && (mc_get_setting('front-auto-translations') || mc_get_setting('dialogflow-multilingual') || mc_get_multi_setting('google', 'google-multilingual') || mc_get_setting('google-translation') || mc_get_multi_setting('google', 'google-translation') || $is_multilingual_via_translation)) { // Deprecated: mc_get_setting('dialogflow-multilingual') + mc_get_setting('google-translation')
            if (!$language && (mc_get_multi_setting('dialogflow-language-detection', 'dialogflow-language-detection-active') || mc_get_multi_setting('google', 'google-language-detection')) && strlen($message) > 2) { // Deprecated: mc_get_multi_setting('dialogflow-language-detection', 'dialogflow-language-detection-active')
                $language = mc_get_user_extra($user_id, 'language') ?: mc_google_language_detection($message, $token);
                if ($language) {
                    mc_language_detection_db($user_id, $language);
                    $payload['event'] = 'update-user';
                }
            } elseif (!$language) {
                $language = mc_get_user_language($user_id);
            }
        }
    }

    // Assistant
    if ($open_ai_mode == 'assistant') {
        if ($conversation_id) {
            $response = mc_open_ai_assistant($message, $conversation_id, !$is_smart_reply && !$is_rewrite && !$is_scraping);
            if (mc_is_error($response)) {
                $response = '';
            }
        } else {
            $open_ai_mode = '';
        }
    } else {

        // Flows structured output check and chips response
        if (!$is_smart_reply && !$is_rewrite && !$is_embeddings && !$is_scraping) {
            if (($count == 1 || ($count == 2 && $messages[0]['user_type'] == 'bot')) && $conversation_id) {
                $flow_start = mc_flows_on_conversation_start_or_load($messages, $language, $conversation_id);
                if ($flow_start) {
                    return [true, [['message' => $flow_start]], false, false];
                }
            }
            for ($i = $count - 1; $i > -1; $i--) {
                $is_break = false;
                $payload_temp = mc_isset($messages[$i], 'payload');
                if (strpos($payload_temp, 'flow_end_so') !== false) {
                    $flows_structured_output = false;
                    $is_break = true;
                }
                if (strpos($payload_temp, 'flow_so') !== false) {
                    $flows_structured_output = json_decode($payload_temp, true);
                    $is_break = true;
                }
                if ($is_break) {
                    break;
                }
            }
            for ($i = $count - 2; $i > -1; $i--) {
                $message_text = $messages[$i]['message'];
                if ($message_text) {
                    if (strpos($message_text, '[chips ') !== false) {
                        $message_text = mc_isset(mc_get_shortcode($message_text, false), 0);
                        $message_id = mc_isset($message_text, 'id');
                        if (strpos($message_id, 'flow_') === 0) {
                            $message_options = explode(',', mc_isset($message_text, 'options'));
                            $flow_identifier = explode('_', substr($message_id, 5));
                            for ($j = 0; $j < count($message_options); $j++) {
                                if ($message_options[$j] == $message) {
                                    $next_response = mc_flows_get_open_ai_message_response($flow_identifier[0], $flow_identifier[1], $flow_identifier[2], $j, $payload);
                                    if ($next_response[0] !== false) {
                                        $response = $next_response[0];
                                        if ($is_multilingual_via_translation && $language != mc_get_multi_setting('open-ai', 'open-ai-training-data-language', 'en') && !mc_is_rich_message($response) && !strpos($response, '[action ')) {
                                            $response = mc_t($response, $language);
                                        }
                                    }
                                    $payload = array_merge($payload, $next_response[1]);
                                    $attachments_block = mc_isset($next_response[1], 'attachments');
                                    if ($attachments_block) {
                                        $attachments_response = array_merge($attachments_response, $attachments_block);
                                    }
                                    if ($response) {
                                        $response = ['choices' => [['message' => ['content' => trim($response)]]]];
                                        $is_chips_response = true;
                                    }
                                }
                            }
                        }
                    }
                    break;
                }
            }
        }
        for ($i = 0; $i < $count; $i++) {
            $payload_temp = mc_isset($messages[$i], 'payload');
            if (strpos($payload_temp, 'action') !== false) {
                $payload_temp = json_decode($payload_temp, true);
                if (!empty($payload_temp['action'])) {
                    $messages[$i]['message'] .= ' ' . $payload_temp['action'];
                }
            }
        }
    }

    // Embeddings
    if (!$is_embeddings && !$flows_structured_output && !$is_rewrite && !$is_scraping && !$response && !$is_chips_response && in_array($open_ai_mode, ['sources', 'all', ''])) { // Deprecated. Remove All
        $extra_embeddings = ['conversation_id' => $conversation_id, 'user_id' => $user_id, 'attachments' => $attachments];
        if ($is_smart_reply) {
            $extra_embeddings['smart_reply'] = true;
        }
        if ($context) {
            $extra_embeddings['context'] = $context;
        }
        if (!$dialogflow_active && $is_multilingual_via_translation) {
            $embeddings_language = mc_open_ai_embeddings_language();
            if (!empty($embeddings_language) && !in_array($language, $embeddings_language)) {
                $translation = mc_google_translate([$message], $embeddings_language[0], $token);
                if (!empty($translation[0])) {
                    $message = $translation[0][0];
                }
                $response = mc_open_ai_embeddings_message($message, false, $extra_embeddings);
                if ($response) {
                    $translation = mc_google_translate([$response['message']], $language, $token);
                    if (!empty($translation[0])) {
                        $response['message'] = $translation[0][0];
                    }
                }
            } else {
                $response = mc_open_ai_embeddings_message($message, false, $extra_embeddings);
            }
        } else {
            $response = mc_open_ai_embeddings_message($message, false, $extra_embeddings);
        }
        if ($response) {
            $client_side_payload = $response['payload'];
            $payload = array_merge($payload, mc_isset($response, 'payload_message', []));
            $attachments_response = $response['attachments'];
            $embedding_extra = $response['embedding_extra'];
            $embedding_extra_json = json_encode($embedding_extra, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);
            $response = $response['message'];
            $entities = ['language' => 'language name (e.g. spanish or italian)'];
            foreach ($entities as $entity => $entity_description) {
                $entity_ = '{' . $entity . '}';
                if (strpos($response, $entity_) || strpos($embedding_extra_json, $entity_)) {
                    $properties = [];
                    $query_ = $query;
                    unset($query_['tools']);
                    $properties[$entity] = ['type' => 'string', 'description' => $entity_description];
                    $query_['messages'] = [['role' => 'user', 'content' => $message]];
                    $query_['response_format'] = ['type' => 'json_schema', 'json_schema' => ['name' => $entity, 'schema' => ['type' => 'object', 'properties' => $properties, 'required' => [$entity], 'additionalProperties' => false], 'strict' => true]];
                    $response_json = mc_open_ai_curl($url_part, $query_);
                    if ($response_json && !empty($response_json['choices'])) {
                        $response_json = mc_isset(json_decode(mc_isset($response_json['choices'][0]['message'], 'content', '[]'), true), $entity);
                        if ($response_json) {
                            if ($entity == 'language') {
                                $response_json = mc_get_language_code_by_name($response_json);
                                if (strlen($response_json) == 2 && !mc_is_agent()) {
                                    $payload['event'] = 'update-user';
                                    mc_update_user($user_id, [], ['language' => [$response_json, 'Language']]);
                                }
                            }
                            $response = str_replace($entity_, $response_json, $response);
                            $embedding_extra_json = str_replace($entity_, $response_json, $embedding_extra_json);
                            if (mc_get_multi_setting('google', 'google-multilingual-translation')) {
                                $translation = mc_google_translate([$response], $response_json);
                                if (!empty($translation[0])) {
                                    $response = $translation[0][0];
                                }
                            }
                        }
                    }
                }
            }
            if ($embedding_extra && !mc_is_agent()) {
                $embedding_extra_set_data = mc_isset(json_decode($embedding_extra_json, true), 'set_data');
                if ($embedding_extra_set_data) {
                    mc_open_ai_execute_set_data($embedding_extra_set_data);
                    $embedding_extra_set_data_ = [];
                    foreach ($embedding_extra_set_data as $key => $value) {
                        array_push($embedding_extra_set_data_, [$key, $value]);
                    }
                    if (isset($embedding_extra_set_data['archive_conversation']) || isset($embedding_extra_set_data['human_takeover'])) {
                        $conversation_status_code = 'skip';
                    }
                    $execute_actions = mc_open_ai_execute_actions($embedding_extra_set_data_, $conversation_id);
                    $client_side_payload = array_merge($client_side_payload, $execute_actions['client_side_payload']);
                    $attachments_response = array_merge($attachments_response, $execute_actions['attachments']);
                }
            }
        }
        $is_embedding_response = true;
    }
    if ($is_chips_response || $flows_structured_output || !$response) {
        $is_function_calling_only = !$is_chips_response && !$flows_structured_output && !$response && !$is_embeddings && !$is_rewrite && !$is_scraping && $open_ai_mode == 'sources';
        $max_tokens = intval($max_tokens ? $max_tokens : mc_isset($settings, 'open-ai-tokens', 150));
        $is_translations = mc_get_multi_setting('google', 'google-translation');
        $first_message = false;
        $open_ai_length = 0;
        $open_ai_max_tokens = mc_open_ai_get_max_tokens($model);
        $prompt_real_time = $is_embeddings && !$is_google_search && mc_get_setting('dialogflow-google-search', 'dialogflow-google-search-active') ? ' If the user message is about real-time information, a calendar date or time, recent events, or current information, write exactly "I don\'t know."' : '';
        $prompt_language = mc_get_user_language($is_smart_reply ? mc_get_active_user_ID() : $user_id);
        $prompt_language = $prompt_language && $prompt_language != 'en' ? ' If the answer is included, always answer to the user message in the language of the "' . strtoupper($prompt_language) . '" language code.' : '';
        $prompt = $is_scraping ? $message : mc_isset($settings, 'open-ai-prompt', $is_embeddings ? 'Provide extensive answers to the user message from the context below. If the answer is not included, write exactly "I don\'t know." in English language and stop after that. Do not provide external knowledge. Do not answer unrelated questions. Never break character.' . $prompt_language . $prompt_real_time : $prompt_language);
        if ($context) {
            $prompt = 'The user\'s message might be about "' . $context . '".' . PHP_EOL . PHP_EOL . $prompt;
        }
        if ($is_translations) {
            for ($i = 0; $i < $count; $i++) {
                $messages[$i] = mc_google_get_message_translation($messages[$i]);
            }
        }
        if (!empty($settings['open-ai-logit-bias'])) {
            $query['logit_bias'] = json_decode($settings['open-ai-logit-bias'], true);
        }
        $query_messages = $chat_model ? [] : '';
        if ($max_tokens && ($max_tokens != 150 || !$chat_model)) {
            $query['max_tokens'] = $max_tokens;
        }
        if ($prompt) {
            $message_context = is_string($message) ? $message : $message['context'];
            if (strlen($message_context) > 9999) {
                $message_context = substr($message_context, 0, 9999);
            }
            if (mc_is_rich_message($message_context) || strpos($message_context, '[action ')) {
                $prompt .= ' If your answer includes text in square brackets, provide all strings in square brackets as they are and stop generating additional text' . ($prompt_language ? ' and do not translate the text within square brackets but leave the original text as it is' : '') . '.';
            }
            if ($chat_model) {
                $first_message = ['role' => 'developer', 'content' => $prompt . ($is_embeddings ? PHP_EOL . PHP_EOL . 'Context: """' . $message_context . '"""' : '')];
            } else {
                $first_message = 'prompt: """' . str_replace(['"', PHP_EOL], ['\'', ' '], $prompt) . '"""' . ($is_embeddings ? PHP_EOL . PHP_EOL . 'Context: """' . $message_context . '"""' : '') . PHP_EOL . PHP_EOL;
            }
            $open_ai_length += strlen($message_context);
        }
        for ($i = $count - 1; $i > -1; $i--) {
            $message_text = $messages[$i]['message'];
            if (intval(($open_ai_length + strlen($message_text)) / 4) < $open_ai_max_tokens) {
                if (mc_open_ai_is_valid($message_text) || $is_scraping) {
                    $message_is_agent = mc_is_agent($messages[$i]['user_type']);
                    if (!$is_scraping || !$message_is_agent) {
                        if ($chat_model) {
                            array_unshift($query_messages, ['role' => $message_is_agent ? 'assistant' : 'user', 'content' => $message_text]);
                        } else {
                            $query_messages = ($message_is_agent ? 'AI: ' : 'Human: ') . $message_text . PHP_EOL . $query_messages;
                        }
                        $open_ai_length += strlen($message_text);
                    }
                }
            } else {
                break;
            }
        }

        // Vision
        if (!$is_smart_reply && !$is_rewrite && !$is_google_search && !$is_scraping && in_array($model, ['gpt-4.1-nano', 'gpt-4.1-mini', 'gpt-4.1', 'gpt-4o-mini', 'gpt-4o', 'gpt-4', 'gpt-4-32k']) && mc_isset($settings, 'open-ai-vision')) {
            foreach ($attachments as $attachment) {
                if (preg_match('/\.(jpe?g|png|webp|gif|bmp|tiff?)$/i', $attachment[1])) {
                    array_push($query_messages, ['role' => 'user', 'content' => [['type' => 'image_url', 'image_url' => ['url' => $attachment[1]]]]]);
                    if (isset($message['user_prompt'])) {
                        $message['user_prompt'] = str_replace($attachment[1], '', $message['user_prompt']);
                    }
                }
            }
        }

        if (empty($query_messages)) {
            return [false, false];
        }
        if ($chat_model) {
            if ($first_message) {
                array_unshift($query_messages, $first_message);
            }

            // Structured output
            if ($flows_structured_output) {
                $flows_structured_output_string = $flows_structured_output['flow_so'];
                $block = mc_flows_get_by_string($flows_structured_output_string);
                if ($block) {
                    $descriptions = ['full_name' => 'The person name e.g. Olivia Smith', 'email' => 'The email address e.g. <EMAIL>', 'password' => 'An string used as a password', 'address' => 'An full address e.g. 90 Fetter Ln, London EC4A 1EN or 125 W 24th St, New York, NY 10011, USA', 'country' => 'A country name or code e.g. US or United Kingdom', 'state' => 'The state name e.g. New York', 'phone' => 'The phone number e.g. +393203057977', 'language' => 'The language name or language code e.g. Spanish or ES', 'company' => 'The business or company name e.g. Nike', 'webiste' => 'The website URL e.g. www.google.com', 'city' => 'The city e.g. San Francisco', 'postal_code' => 'The postal code e.g. 10001 or SW1A 1AA', 'birthdate' => 'The birthdate e.g. 25 July 1990 or 31/05/89'];
                    $properties = [];
                    $required = [];
                    for ($i = 0; $i < count($block['details']); $i++) {
                        $details = $block['details'][$i];
                        $properties[$details[0]] = ['type' => ['string', 'null'], 'description' => mc_isset($details, 1, mc_isset($descriptions, $details[0], mc_string_slug($details[0], 'string')))];
                        array_push($required, $details[0]);
                    }
                    $query['response_format'] = ['type' => 'json_schema', 'json_schema' => ['name' => 'flow-' . mc_string_slug($flows_structured_output_string, 'slug', true), 'schema' => ['type' => 'object', 'properties' => $properties, 'required' => $required]]];
                    if (count($query_messages) > 1) {
                        array_shift($query_messages);
                    }
                }
            }

            // Function calling
            if (!$is_smart_reply && !$is_rewrite && !$is_google_search && !$is_scraping && in_array($model, ['gpt-4.1-nano', 'gpt-4.1-mini', 'gpt-4.1', 'o3-mini', 'o4-mini', 'o1', 'gpt-4o-mini', 'gpt-4o', 'gpt-4', 'gpt-4-32k'])) {
                if ($human_takeover_active) {
                    $query['tools'] = [['type' => 'function', 'function' => ['name' => 'mc-human-takeover', 'description' => 'I want to contact a human support agent or team member. I want human support. How can I have support?', 'parameters' => ['type' => 'object', 'properties' => json_decode('{}'), 'required' => []]]]];
                }
                if (!$flows_structured_output && empty($MC_OPEN_AI_RECURSION_CHECK_2)) {
                    $qea = mc_get_external_setting('embedding-texts', []);
                    for ($i = 0; $i < count($qea); $i++) {
                        if (!empty($qea[$i][2])) {
                            $properties = [];
                            $properties_required = [];
                            foreach ($qea[$i][5] as $value) {
                                $property_slug = mc_string_slug($value[0], 'slug', true);
                                $properties[$property_slug] = ['type' => 'string', 'description' => $value[1]];
                                if ($value[2]) {
                                    $properties[$property_slug]['enum'] = explode(',', $value[2]);
                                }
                                array_push($properties_required, $property_slug);
                            }
                            array_push($query['tools'], ['type' => 'function', 'function' => [
                                'name' => substr(mc_string_slug(is_string($qea[$i][0]) ? $qea[$i][0] : $qea[$i][0][0], 'slug', true), 0, 20) . '-' . $i, // Deprecated Replace is_string($qea[$i][0]) ? $qea[$i][0] : $qea[$i][0][0] with $qea[$i][0][0]
                                'description' => is_string($qea[$i][0]) ? $qea[$i][0] : $qea[$i][0][0], // Deprecated Replace is_string($qea[$i][0]) ? $qea[$i][0] : $qea[$i][0][0] with $qea[$i][0][0]
                                'strict' => true,
                                'parameters' => [
                                    'type' => 'object',
                                    'properties' => count($properties) ? $properties : (object) [],
                                    'required' => $properties_required,
                                    'additionalProperties' => false
                                ]
                            ]]);
                        }
                    }
                    if (mc_is_cloud()) {
                        require_once(MC_CLOUD_PATH . '/account/functions.php');
                        if (shopify_get_shop_name()) {
                            $query['tools'] = array_merge($query['tools'], shopify_open_ai_function());
                        }
                    }
                    if (defined('MC_WOOCOMMERCE') && !mc_get_setting('wc-disable-bot-integration')) {
                        $query['tools'] = array_merge($query['tools'], mc_woocommerce_open_ai_function());
                    }
                }
            }
            if (!empty($message['user_prompt']) && is_string($query_messages[count($query_messages) - 1]['content'])) {
                $query_messages[count($query_messages) - 1]['content'] = $message['user_prompt'];
            }
            $query['messages'] = $query_messages;
        } else {
            $query['prompt'] = ($first_message ? $first_message : '') . $query_messages . 'AI: ' . PHP_EOL;
            $query['stop'] = ['Human:', 'AI:'];
        }
        if (isset($extra['query'])) {
            $query = array_merge($query, $extra['query']);
        }
        if (empty($query['tools'])) {
            unset($query['tools']);
        }

        // OpenAI response
        if (!$is_chips_response) {
            $continue = true;
            if (!empty($query_messages) && !empty($query['tools'])) {
                if (empty($MC_OPEN_AI_RECURSION_CHECK_3) && !empty($query['response_format']) && !$is_function_calling_only) {
                    $MC_OPEN_AI_RECURSION_CHECK_3 = true;
                    $tools = $query['tools'];
                    $query['messages'][0]['content'] = '';
                    $query['tools'] = [];
                    $response = mc_open_ai_curl($url_part, $query);
                    $query['messages'][0]['content'] = $query_messages[0]['content'];
                    $query['tools'] = $tools;
                    $continue = empty($response['choices']) || empty($response['choices'][0]['message']);
                }
                if ($continue && empty($MC_OPEN_AI_RECURSION_CHECK_2) && (mc_isset($query_messages[0], 'role') == 'developer' || $is_function_calling_only) && (count($query['tools']) > 1 || $query['tools'][0]['function']['name'] != 'mc-human-takeover')) {
                    $MC_OPEN_AI_RECURSION_CHECK_2 = true;
                    $human_takeover_tool = $query['tools'][0]['function']['name'] == 'mc-human-takeover' ? $query['tools'][0] : false;
                    if ($is_function_calling_only) {
                        array_unshift($query['messages'], ['role' => 'system', 'content' => 'Only respond when a function call is appropriate. Do not reply to general questions or small talk. If you cannot reply, reply exactly and only "I don\'t know".']);
                    } else {
                        $query['messages'][0]['content'] = '';
                    }
                    if ($human_takeover_tool) {
                        array_shift($query['tools']);
                    }
                    $response = mc_open_ai_curl($url_part, $query);
                    $continue = empty($response['choices']);
                    if (!$continue && empty($response['choices'][0]['message']['tool_calls'])) {
                        $open_ai_response = $response['choices'][0]['message'];
                        $continue = empty($open_ai_response['tool_calls']);
                        if ($continue) {
                            $open_ai_response_embeddings = mc_open_ai_embeddings_generate($open_ai_response['content']);
                            if (!empty($open_ai_response_embeddings) && isset($open_ai_response_embeddings[0]['embedding'])) {
                                foreach ($query['tools'] as $tool) {
                                    $tool_embeddings = mc_open_ai_embeddings_generate($tool['function']['description']);
                                    if (!empty($tool_embeddings) && isset($tool_embeddings[0]['embedding']) && mc_open_ai_embeddings_compare($tool_embeddings[0]['embedding'], $open_ai_response_embeddings[0]['embedding'], $tool_embeddings[0]['text'], $open_ai_response_embeddings[0]['text']) > 0.4) {
                                        $continue = false;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    if ($human_takeover_tool) {
                        $query['tools'] = [$human_takeover_tool];
                    }
                    $query['messages'][0]['content'] = $query_messages[0]['content'];
                }
            }
            if ($continue && !$is_function_calling_only) {
                $response = mc_open_ai_curl($url_part, $query);
            }
        }
        if ($MC_OPEN_AI_PLAYGROUND !== null) {
            $MC_OPEN_AI_PLAYGROUND['usage'] = mc_isset($response, 'usage');
            $MC_OPEN_AI_PLAYGROUND['query'] = $query;
            $MC_OPEN_AI_PLAYGROUND['embeddings'] = mc_isset($GLOBALS, 'MC_OPEN_AI_PLAYGROUND_E');
        }
        if ($response && !empty($response['choices'])) {
            if (isset($query['n'])) {
                return $response['choices'];
            }
            $response_message = $response['choices'][0]['message'];
            $response_message_content = $response_message['content'];
            $tool_calls = mc_isset($response_message, 'tool_calls');
            if ($tool_calls && count($tool_calls) > 1 && $tool_calls[0]['function']['name'] == 'mc-human-takeover') {
                array_shift($tool_calls);
                $response['choices'][0]['message']['tool_calls'] = $tool_calls;
            }
            $function_calling = mc_open_ai_function_calling($response, mc_isset($query, 'tools'), $conversation_id);
            if ($function_calling) {

                // Function calling response
                if ($function_calling[0] == 'mc-human-takeover') {
                    $response = 'mc-human-takeover';
                } else if ($function_calling[2] == 'mc-ignore-call') {
                    $MC_OPEN_AI_RECURSION_CHECK_2 = true;
                    return mc_open_ai_message(is_string($message) ? $message : mc_isset($message, 'user_prompt', $message), $max_tokens, $model, $conversation_id, $extra);
                } else if ($chat_model) {
                    unset($query['tools']);
                    if ($is_embeddings) {
                        array_shift($query_messages);
                    }
                    if ($function_calling[0] == 'mc-shortcode') {
                        $query_messages = array_slice($query_messages, -5);
                        $button_text = mc_('More details');
                        array_unshift($query_messages, ['role' => 'developer', 'content' => 'If the last user message is about showing products that meet the user criteria format the response using the following shortcode and replace the strings like {{id}} with the correct values: [slider image-1="{{image}}" header-1="{{title}}" description-1="{{description}}..." link-1="{{url}}" link-text-1="' . $button_text . '" extra-1="{{price}}" image-2="{{image}}" header-2="{{title}}" description-2="{{description}}..." link-2="{{url}}" link-text-2="' . $button_text . '" extra-2="{{price}}" image-3="{{image}}" header-3="{{title}}" description-3="{{description}}..." link-3="{{url}}" link-text-3="' . $button_text . '" extra-3="{{price}}"]. If your response include only one product, use this shortcode: [card image="{{image}}" header="{{title}}" description="{{description}}..." link="{{link}}" link-text="' . $button_text . '" extra="{{price}}"]. If the response contains image links show the images with the shortcode [slider-images images="URL,URL,URL"].']);
                    }
                    if ($function_calling[0] == 'payload') {
                        $payload = array_merge($payload, $function_calling[3]);
                    }
                    $tool_calls = $tool_calls[0];
                    array_push($query_messages, $response_message, ['role' => 'tool', 'content' => json_encode($function_calling[2], JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE), 'tool_call_id' => $tool_calls['id']]);
                    $query['messages'] = $query_messages;
                    $response = mc_open_ai_curl('chat/completions', $query);
                    if (isset($response['error'])) {
                        return [false, $response];
                    }
                    if (!empty($response['choices'])) {
                        $response_message_content = mc_isset($response['choices'][0], 'message', [])['content'];
                        if ($function_calling[0] == 'mc-shortcode') {
                            if (strpos($response_message_content, '[slider ') || strpos($response_message_content, '[card ') || strpos($response_message_content, '[slider-images ')) {
                                $response_message_content_shortcode = mc_get_shortcode($response_message_content, false);
                                if (!empty($response_message_content_shortcode)) {
                                    $message_ = str_replace($response_message_content_shortcode[0]['shortcode'], '', $response_message_content);
                                    $message_ = str_replace(': .', '.', preg_replace('/\s+/', ' ', $message_));
                                    if (substr($message_, -1) === '.') {
                                        $message_ = substr($message_, 0, -1);
                                    }
                                    $message_ = trim($message_);
                                    mc_send_message(mc_get_bot_id(), $conversation_id, $message_);
                                    mc_messaging_platforms_send_message($message_, $conversation_id);
                                    $response_message_content = $response_message_content_shortcode[0]['shortcode'];
                                }
                            }
                        } else {
                            $response_message_content = mc_open_ai_text_formatting($response_message_content);
                        }
                    }
                    $function_calling = false;
                }
            } else if ($flows_structured_output) {

                // Flows structured output response
                $user_data = empty($tool_calls) ? (substr($response_message_content, 0, 2) == '{"' ? json_decode($response_message_content, true) : []) : json_decode(mc_isset($tool_calls[0]['function'], 'arguments', '{}'), true);
                $block = mc_flows_get_by_string($flows_structured_output_string);
                $required_missing = '';
                foreach ($block['details'] as $detail) {
                    if (empty($user_data[$detail[0]]) || $user_data[$detail[0]] == 'unknown') {
                        if ($detail[2]) {
                            $required_missing .= mc_string_slug($detail[0], 'string') . ', ';
                        } else {
                            $user_data[$detail[0]] = mc_is_agent() ? '' : ($detail[0] == 'email' ? mc_get_active_user()['email'] : mc_get_user_extra($user_id, $detail[0]));
                        }
                    }
                }
                if (empty($required_missing)) {
                    if (!mc_is_agent()) {
                        $full_name = mc_isset($user_data, 'full_name');
                        if ($full_name) {
                            $full_name = mc_split_name($full_name);
                            $user_data['first_name'] = $full_name[0];
                            $user_data['last_name'] = $full_name[1];
                            unset($user_data['full_name']);
                        }
                        mc_update_user(mc_get_active_user_ID(), $user_data, $user_data, true, true);
                    }
                    $payload['flow_end_so'] = true;
                    if ($MC_OPEN_AI_PLAYGROUND !== null) {
                        $MC_OPEN_AI_PLAYGROUND['payload'] = ['flow_end_so' => true];
                    }
                    $next_response = mc_flows_get_open_ai_message_response($block['index'][0], $block['index'][1], $block['index'][2], $block['index'][3], $payload);
                    if ($next_response[0] !== false) {
                        $response_message_content = $next_response[0];
                        $embeddings_language = mc_open_ai_embeddings_language();
                        if ($language && !empty($embeddings_language) && !in_array($language, $embeddings_language)) {
                            $translation = mc_google_translate([$response_message_content], $language, $token);
                            if (!empty($translation[0])) {
                                $response_message_content = $translation[0][0];
                            }
                        }
                    }
                    $payload = array_merge($payload, $next_response[1]);
                } else {
                    $required_missing = 'Rewrite the following text' . ($language ? ' and translate it to "' . strtoupper($language) . '" language' : '') . ': "What is your ' . substr($required_missing, 0, -2) . '"?';
                    $response_message_content = mc_isset(mc_open_ai_message($required_missing, false, false, false, 'rewrite'), 1, $required_missing);
                }
            }
            if ($chat_model && $response_message_content && strpos($response_message_content, '[action ') !== false && !$is_smart_reply && !$is_rewrite && !$is_google_search && !$is_scraping) {

                // Action response
                $action = mc_flows_execute($response_message_content, $messages, $language, $conversation_id);
                $response = $action[0];
                $client_side_payload = array_merge($client_side_payload, $action[1]);
                $attachments_response = array_merge($attachments_response, $action[2]);
                $payload = array_merge($payload, ['action' => $action[3]['shortcode']]);
            } else if (mc_isset($function_calling, 0) != 'mc-shortcode') {

                // Normal response
                $response = mc_open_ai_text_formatting($chat_model ? $response_message_content : $response['choices'][0]['text']);
            }
        } else {
            if (isset($response['error'])) {
                return [false, $response];
            } else {
                $response = false;
            }
        }
    }
    $unknow_answer = !mc_open_ai_is_valid($response);

    // Human Takeover and Google Search
    if (!$is_rewrite && !$is_scraping) {
        if ($is_smart_reply) {
            return $response && !$unknow_answer ? [true, $response, $token, $extra_response, $MC_OPEN_AI_PLAYGROUND, $client_side_payload, $attachments_response, $payload] : [false, false];
        }
        $message_ = mc_isset($message, 'user_prompt', $message);
        if (empty($MC_OPEN_AI_RECURSION_CHECK) && !$is_google_search && $unknow_answer && strlen($message_) > 4) {
            $google_search_settings = mc_get_setting('dialogflow-google-search');
            $MC_OPEN_AI_RECURSION_CHECK = true;
            if (mc_isset($google_search_settings, 'dialogflow-google-search-active')) {
                $google_search_response = mc_isset(mc_get('https://www.googleapis.com/customsearch/v1?key=' . $google_search_settings['dialogflow-google-search-key'] . '&cx=' . $google_search_settings['dialogflow-google-search-id'] . '&q=' . urlencode($message_), true), 'items');
                if (!empty($google_search_response)) {
                    $limiter = 0;
                    $google_search_page_response_text = '';
                    for ($i = 0; $i < count($google_search_response); $i++) {
                        $google_search_page_response = mc_open_ai_html_to_paragraphs(mc_isset($google_search_response[$i], 'link'));
                        if ($google_search_page_response[1] == 200) {
                            $google_search_page_response_text .= ($limiter ? PHP_EOL . '--------------------------------------------------------------------------------' . PHP_EOL : '') . implode(PHP_EOL . ' ' . PHP_EOL, array_column($google_search_page_response[0], 0));
                            $limiter++;
                            if ($limiter > 4) {
                                break;
                            }
                        }
                    }
                    $google_search_response = mc_open_ai_message(['context' => trim($google_search_page_response_text), 'user_prompt' => $message_], false, false, false, 'embeddings-search');
                    if ($google_search_response[0] && !empty($google_search_response[1]) && empty($google_search_response[5]['unknow_answer'])) {
                        $response = $google_search_response[1];
                        $unknow_answer = false;
                    }
                }
            }
        }
        $human_request = $response == 'mc-human-takeover';
        $human_takeover = !$is_embeddings && !$dialogflow_active && $human_takeover_active && ($human_request || ($unknow_answer && ((strlen($message) > 3 && strpos($message, ' ')) || strlen($message) > 10)));
        if ($human_takeover && $conversation_id) {
            if (!$is_human_takeover) {
                if (mc_isset($extra, 'messaging-app') == 'em') {
                    return [true, mc_dialogflow_human_takeover($conversation_id), $token, true];
                }
                $human_takeover = mc_chatbot_human_takeover($conversation_id, $human_takeover_settings);
                return [true, $human_takeover[0], $token, $human_takeover[1]];
            }
            return [true, '', $token];
        }
        if ($human_request && $is_human_takeover) {
            $response = $human_takeover_settings['message_fallback'];
            $payload['human-takeover-message-fallback'] = true;
            if ($is_embeddings) {
                $extra_response = 'skip-references';
            }
        } else if (!$response && empty($payload)) {
            $response = $dialogflow_active || $is_embeddings ? false : mc_t(mc_isset($settings, 'open-ai-fallback-message', 'Sorry, I didn\'t get that. Can you rephrase?'), $language);
        } else if (!$dialogflow_active && $is_human_takeover && $unknow_answer) {
            $response = false;
        }
    }

    // Response
    if ($response || !empty($payload)) {
        if ($conversation_id && !$is_embeddings && !$is_rewrite && !$is_scraping && !empty($response) && !$dialogflow_active) {
            mc_send_message(mc_get_bot_id(), $conversation_id, $response, $attachments_response, $conversation_status_code, $payload);
            mc_webhooks('MCOpenAIMessage', ['response' => $response, 'message' => $message, 'conversation_id' => $conversation_id]);
        }
        if ($unknow_answer) {
            $client_side_payload['unknow_answer'] = true;
        }
        return [true, $response, $token, $extra_response, $MC_OPEN_AI_PLAYGROUND, $client_side_payload, $attachments_response, $payload];
    }
    return [$is_embedding_response, $response];
}

function mc_open_ai_user_expressions($message) {
    $settings = mc_get_setting('open-ai');
    $response = mc_open_ai_curl('chat/completions', ['messages' => [['role' => 'user', 'content' => 'Create a numbered list of minimum 5 variants of this sentence and only return the list. Change all the words with another word: """' . $message . '""""']], 'model' => mc_open_ai_get_gpt_model(), 'max_tokens' => 200, 'temperature' => floatval(mc_isset($settings, 'open-ai-temperature', 1)), 'presence_penalty' => floatval(mc_isset($settings, 'open-ai-presence-penalty', 0)), 'frequency_penalty' => floatval(mc_isset($settings, 'open-ai-frequency-penalty', 0))]);
    $error = mc_isset($response, 'error');
    $choices = mc_isset($response, 'choices');
    if ($choices) {
        $choices = explode("\n", trim($choices[0]['message']['content']));
        for ($i = 0; $i < count($choices); $i++) {
            $expression = trim($choices[$i]);
            if (in_array(substr($expression, 0, 2), [($i + 1) . '.', ($i + 1) . ')'])) {
                $expression = trim(substr($expression, 2));
            }
            if (substr($expression, 0, 1) === '.') {
                $expression = trim(substr($expression, 1));
            }
            $choices[$i] = $expression;
        }
        return $choices;
    } else if ($error) {
        return mc_error($error['type'], 'mc_open_ai_user_expressions', $error['message']);
    }
    return $response;
}

function mc_open_ai_user_expressions_intents() {
    $intents = mc_dialogflow_get_intents();
    $response = 0;
    $history = mc_get_external_setting('open-ai-intents-history', []);
    for ($i = 0; $i < count($intents); $i++) {
        $intent_name = substr($intents[$i]['name'], strripos($intents[$i]['name'], '/') + 1);
        if (in_array(mc_isset($intents[$i], 'action'), ['input.unknown', 'input.welcome']) || in_array($intent_name, $history)) {
            continue;
        }
        $messages = [];
        $training_phrases = $intents[$i]['trainingPhrases'];
        for ($j = 0; $j < count($training_phrases); $j++) {
            $parts = $training_phrases[$j]['parts'];
            $message = '';
            for ($y = 0; $y < count($parts); $y++) {
                $message .= $parts[$y]['text'];
            }
            array_push($messages, strtolower($message));
        }
        $count = count($messages) > 5 ? 5 : count($messages);
        $user_expressions_final = [];
        for ($j = 0; $j < $count; $j++) {
            if (strlen($messages[$j]) > 5) {
                $user_expressions = mc_open_ai_user_expressions($messages[$j]);
                for ($y = 0; $y < count($user_expressions); $y++) {
                    $expression = $user_expressions[$y];
                    if (!in_array(strtolower($expression), $messages) && strlen($expression) > 4)
                        array_push($user_expressions_final, $expression);
                }
            }
        }
        if (count($user_expressions_final)) {
            if (mc_dialogflow_update_intent($intents[$i], $user_expressions_final) === true) {
                array_push($history, $intent_name);
                mc_save_external_setting('open-ai-intents-history', $history);
            } else
                $response++;
        }
    }
    return $response === 0 ? true : $response;
}

function mc_open_ai_smart_reply($message, $conversation_id) {
    $response = mc_open_ai_message($message, false, mc_open_ai_get_gpt_model(), $conversation_id, ['smart_reply' => true]);
    $suggestions = [];
    if (mc_is_error($response) || isset($response[1]) && mc_isset($response[1], 'error')) {
        return mc_error('openai-error', 'mc_open_ai_smart_reply', $response, true);
    }
    for ($i = 0; $i < count($response); $i++) {
        if ($response[$i] && !is_bool($response[$i])) {
            $suggestion = is_string($response[$i]) ? $response[$i] : mc_isset(mc_isset($response[$i], 'message'), 'content');
            if (!in_array($suggestion, $suggestions) && strlen($suggestion) > 2) {
                array_push($suggestions, $suggestion);
            }
        }
    }
    return ['suggestions' => $suggestions];
}

function mc_open_ai_spelling_correction($message) {
    if (strlen($message) < 2) {
        return $message;
    }
    $message_original = $message;
    $skip = [];
    $text_formatting = [];
    $regexes = [['/`[\S\s]*?`/', 0], ['/\b(?:(?:https?|ftp):\/\/|www\.)[-a-z0-9+&@#\/%?=~_|!:,.;]*[-a-z0-9+&@#\/%=~_|]/i', PREG_PATTERN_ORDER]];
    $index = 0;
    for ($i = 0; $i < count($regexes); $i++) {
        preg_match_all($regexes[$i][0], $message, $skip_sub, $regexes[$i][1]);
        $skip_sub = $skip_sub[0];
        for ($j = 0; $j < count($skip_sub); $j++) {
            $message = str_replace($skip_sub[$j], '{{' . $index . '}}', $message);
            array_push($skip, $skip_sub[$j]);
            $index++;
        }
    }
    if ($message == '{{0}}') {
        return $message_original;
    }
    $regexes = ['/\*(.*?)\*/', '/__(.*?)__/', '/~(.*?)~/', '/```(.*?)```/', '/`(.*?)`/'];
    for ($i = 0; $i < count($regexes); $i++) {
        $values = [];
        if (preg_match_all($regexes[$i], $message, $values)) {
            for ($j = 0; $j < count($values[0]); $j++) {
                $message = str_replace($values[0][$j], $values[1][$j], $message);
                array_push($text_formatting, [$values[0][$j], $values[1][$j]]);
            }
        }
    }
    $shortcode = mc_get_shortcode($message);
    if (!empty($shortcode)) {
        $message = str_replace($shortcode[0]['shortcode'], 'shortcode', $message);
    }
    if ($message && $message != 'shortcode') {
        $model = mc_open_ai_get_gpt_model();
        $response = mc_open_ai_curl('chat/completions', ['model' => $model, 'messages' => [['role' => 'user', 'content' => 'Fix spelling mistakes of the following text, return only the corrected version, or the original if none, never add comments, and do not remove text markdown: "' . $message . '"']]]);
        $error = mc_isset($response, 'error');
        if ($response && isset($response['choices']) && count($response['choices'])) {
            $response = $response['choices'][0]['message']['content'];
            $response = mc_open_ai_is_valid($response) && strlen($response) > (strlen($message) * 0.5) ? $response : $message;
            if (count($skip) != substr_count($response, '{{')) {
                return $message_original;
            }
            for ($i = 0; $i < count($skip); $i++) {
                $response = str_replace('{{' . $i . '}}', $skip[$i], $response);
            }
            for ($i = 0; $i < count($text_formatting); $i++) {
                $response = str_replace($text_formatting[$i][1], $text_formatting[$i][0], $response);
            }
            $response = mc_open_ai_text_formatting($response);
            return empty($shortcode) ? $response : str_replace('shortcode', $shortcode[0]['shortcode'], $response);
        } else if ($error) {
            mc_error($error['type'], 'mc_open_ai_spelling_correction', $error['message'], true);
        }
    }
    return $message_original;
}

function mc_open_ai_text_formatting($message) {
    if (empty($message)) {
        return $message;
    }
    $message = preg_replace_callback('/\[([^\]]+)\]\(([^)]+)\)/', function ($matches) {
        return $matches[2] . '#mc-' . str_replace(' ', '--', $matches[1]);
    }, str_replace(['**', '- *'], ['*', "\n*"], $message));
    $message = preg_replace('/mailto:([^#]+)#mc-.*?/', '', $message);
    $message = str_replace(['```html', '```php', '```css', '```js', '```javascript', '```c++', '```sql', '```plaintext', '```nginx', '```bash'], '```', $message);
    if (strpos($message, '- ') || (strpos($message, '1. ') !== false && strpos($message, '2. ') !== false) || (strpos($message, '### ') || strpos($message, '#### '))) {
        $rows = preg_split("/\r\n|\n|\r/", $message);
        $message = '';
        $is_list = false;
        for ($i = 0; $i < count($rows); $i++) {
            $row = $rows[$i];
            if (!empty(trim($row))) {
                for ($j = 1; $j < 30; $j++) {
                    $row = str_replace('     ' . $j . '. ', '  - ', $row);
                }
                $row = str_replace(['     - ', '    - ', '   - '], '  - ', $row);
                $is_line = strpos($row, '- ') === 0 || strpos($row, ' - ') === 0;
                $is_inner = strpos($row, '  - ') === 0;
                if ($is_line || $is_inner || (in_array(strpos($row, '. '), [1, 2]) && is_numeric(substr($row, 0, 1)))) {
                    $row = trim(str_replace([',', '"', ':', '[', ']'], ['\,', '\'', '\:', '', ''], substr($row, $is_inner ? 4 : 2)));
                    if ($row[0] == '.') {
                        $row = substr($row, 1);
                    }
                    if ($is_inner) {
                        $row = '- ' . $row;
                    }
                    if (!$is_list) {
                        $message .= '[list' . ($is_line ? '' : ' numeric="true"') . ' values="' . $row . PHP_EOL;
                        $is_list = true;
                    } else {
                        $message .= ',' . $row . PHP_EOL;
                    }
                    $row_next = isset($rows[$i + 1]) ? trim($rows[$i + 1]) : false;
                    if ((!$is_list || strpos($row, '*:')) && $row_next && $row_next[0] != '-' && !(is_numeric($row_next[0]) && $row_next[1] == '.')) {
                        $rows[$i + 1] = '  - ' . $row_next;
                    }
                } else {
                    if (strpos($row, '### ') === 0 || strpos($row, '#### ') === 0) {
                        $row = '*' . trim(str_replace('#', '', str_replace('*', '', $row))) . '*' . (empty($rows[$i + 1]) ? '' : PHP_EOL);
                    }
                    if ($is_list && strpos($row, '   ') !== 0) {
                        $message = $message . '"]' . (preg_match('/^\n/', $row) === 1 ? '' : PHP_EOL) . $row . PHP_EOL;
                        $is_list = false;
                    } else {
                        $message .= ($is_list ? str_replace([',', '"', ':'], ['\,', '\'', '\:'], $row) : $row) . (strpos($row, '```') !== false ? '' : PHP_EOL);
                    }
                }
            } else if (!$is_list) {
                $message .= PHP_EOL . PHP_EOL;
            }
        }
        if ($is_list) {
            $message = $message . '"]';
        }
    }
    $rows = preg_split("/\r\n|\n|\r/", $message);
    $message = '';
    $is_open = false;
    $is_open_2 = false;
    for ($i = 0; $i < count($rows); $i++) {
        if (strpos($rows[$i], '```') !== false && $i) {
            if (!$is_open) {
                if (!empty($rows[$i - 1])) {
                    $rows[$i - 1] .= PHP_EOL;
                } else if ($i > 1 && empty($rows[$i - 2])) {
                    $rows[$i - 2] = '{S}';
                }
            } else {
                if (empty($rows[$i + 1])) {
                    $rows[$i + 1] = '{S}';
                    if (isset($rows[$i + 2]) && empty($rows[$i + 2])) {
                        $rows[$i + 2] = '{S}';
                    }
                }
            }
            $is_open = !$is_open;
        } else {
            if (!$is_open_2 && mc_is_rich_message($rows[$i])) {
                if (!empty($rows[$i - 1])) {
                    $rows[$i - 1] .= PHP_EOL;
                } else if ($i > 1 && empty($rows[$i - 2])) {
                    $rows[$i - 2] = '{S}';
                }
                $is_open_2 = true;
            }
            if ($is_open_2 && strpos($rows[$i], ']')) {
                if (empty($rows[$i + 1])) {
                    $rows[$i + 1] = '{S}';
                    if (isset($rows[$i + 2]) && empty($rows[$i + 2])) {
                        $rows[$i + 2] = '{S}';
                    }
                }
                $is_open_2 = false;
            }
        }
    }
    for ($i = 0; $i < count($rows); $i++) {
        if (strpos($rows[$i], '{S}') === false) {
            $message .= $rows[$i] . PHP_EOL;
        }
    }
    $message = str_replace('```,', '```' . PHP_EOL . ',', $message);
    while (in_array(mb_substr($message, 0, 1), ["\n", "\r", '\\n', '\\', ',', ':', '?', '!', '"', '”', '\''])) {
        $message = mb_substr($message, 1);
    }
    while (in_array(mb_substr($message, -1), ["\n", "\r", '\\n', '\\', ',', ':', '”', '\''])) {
        $message = mb_substr($message, 0, -1);
    }
    while (strpos($message, "\n ")) {
        $message = str_replace("\n ", "\n", $message);
    }
    if (!strpos(mb_substr($message, 0, -5), '"')) {
        while (mb_substr($message, -1) == '"') {
            $message = mb_substr($message, 0, -1);
        }
    }
    while (mb_substr($message, -1) == '"' && substr_count($message, '"') % 2 != 0) {
        $message = mb_substr($message, 0, -1);
    }
    if (mb_substr($message, 0, 2) == 'n ') {
        $message = mb_substr($message, 2);
    }
    if (preg_match('/(\|[^\r\n]+\|)(?:\r?\n\|[-:|]+)((?:\r?\n\|[^\r\n]+\|)+)/s', $message, $matches)) {
        $table = $matches[0];
        $lines = explode("\n", $table);
        $header = [];
        $values = [];
        foreach ($lines as $index => $line) {
            $columns = array_map('trim', explode('|', trim($line, '|')));
            if ($index == 0) {
                $header = $columns;
            } elseif ($index > 1 && !empty($columns)) {
                $values[] = implode(':', $columns);
            }
        }
        $header = implode(', ', $header);
        $values = implode(',', $values);
        $message = trim(str_replace($table, PHP_EOL . PHP_EOL . '[table header="' . $header . '" values="' . $values . '"]' . PHP_EOL . PHP_EOL, $message));
    }
    return trim(str_replace(['The fixed text is:', '(with correct punctuation)', 'Fix: ', 'Fixed: ', 'Corrected text:', 'A:', 'Answer: ', 'Question:', 'Fixed text:'], '', $message));
}

function mc_open_ai_is_valid($message) {
    return $message ? ($message == 'I don\'t know.' || $message == 'I don\'t know' || substr($message, -9) == 'don\'t know' || substr($message, -10) == 'don\'t know.' ? false : preg_match('/(Non lo so.|Bilmiyorum.|не знаю|Tôi không biết.|我不知道。|Jeg vet ikke.|Nie wiem.|Nu știu.|ne vem|nuk e di.|не знам.|Abdi henteu terang.|jag vet inte.|ฉันไม่รู้.|hindi ko alam.|Я не знаю.|ja neviem.|わからない。|არ ვიცი.|모르겠습니다.|aš nežinau.|не знам.|Би мэдэхгүй.|saya tak tahu.|ကျွန်တော်မသိပါ။|Ik weet het niet.|Je ne sais pas.|չգիտեմ։|لا أعرف.|аз не знам|Não sei.|Nevím.|Jeg ved det ikke.|Δεν ξέρω.|No sé.|ma ei tea.|من نمی دانم.|En tiedä.|אני לא יודע.|मुझें नहीं पता।|ne znam|Nem tudom.|Aku tidak tahu.|Ég veit það ekki.|Ich weiß nicht.|I can\'t help|I can\'t assist with that|provide real-time|Sorry, as an AI model|don\'t have access to real-time|don\'t have real-time|can\'t access the internet or real-time|provide real-time information|access to real-time|don\'t have access to real-time|not included in the context|mc-human-takeover|provide more context|provide a valid text|What was that|I didn\'t get that|I don\'t understand|no text provided|provide the text|I cannot provide|I don\'t have access|I don\'t have any|As a language model|I do not have the capability|I do not have access|modelo de lenguaje de IA|no tengo acceso|no tinc accés|En tant qu\'IA|je n\'ai pas d\'accès|en tant qu\'intelligence artificielle|je n\'ai pas accès|programme d\'IA|স্মার্ট AI কম্পিউটার প্রোগ্রাম|আমি একটি AI|আমি জানি না|我無法回答未來的活動|AI 語言模型|我無法提供|作為AI|我無法得知|作為一名AI|我無法預測|作为AI|我没有未来预测的功能|作為一個AI|我無法預測未來|作为一个AI|我无法预测|我不具备预测|我作为一个人工智能|Как виртуальный помощник|я не могу предоставить|как AI-ассистента|Как ИИ|Как искусственный интеллект|я не имею доступа|я не могу ответить|я не могу предсказать|como um modelo de linguagem|eu não tenho informações|sou um assistente de linguagem|Não tenho acesso|modelo de idioma de AI|não é capaz de fornecer|não tenho a capacidade|como modelo de linguagem de IA|como uma AI|não tenho um|como modelo de linguagem de inteligência artificial|como modelo de linguagem AI|não sou capaz|poiché sono un modello linguistico|non posso fornire informazioni|in quanto intelligenza artificiale|non ho la capacità|non sono in grado|non ho la possibilità|non posso dare|non posso fare previsioni|non posso predire|in quanto sono un\'Intelligenza Artificiale|Come assistente digitale|come assistente virtuale|Si një AI|nuk mund të parashikoj|Si inteligjencë artificiale|nuk kam informacion|Nuk mund të jap parashikime|nuk mund të parashikoj|لا يمكنني توفير|نموذجًا لغة|لا يمكنني التنبؤ|AI भाषा मॉडल हूँ|मैं एक AI|मुझे इसकी जानकारी नहीं है|मैं आपको बता नहीं सकती|AI सहायक|मेरे पास भविष्य के बारे में कोई जानकारी नहीं है|का पता नहीं है|не мога да|Като AI|не разполагам с|нямам достъп|ne mogu pratiti|Nisam u mogućnosti|nisam sposoban|ne mogu prikazivati|ne mogu ti dati|ne mogu pružiti|nemam pristup|nemam sposobnosti|nemam trenutne informacije|nemam sposobnost|ne mogu s preciznošću|nemůžu předpovídat|nemohu s jistotou|Jako AI|nemohu předpovídat|nemohu s jistotou znát|Jako umělá inteligence|nemám informace|nemohu predikovat|Jako NLP AI|nemohu předvídat|nedokážu předvídat|nemám schopnost|som AI|som en AI|har jeg ikke adgang|Jeg kan desværre ikke besvare|jeg ikke har adgang|kan jeg ikke give|jeg har ikke|har jeg ikke mulighed|Jeg er en AI og har ikke|har jeg ikke evnen|Jeg kan desværre ikke hjælpe med|jeg kan ikke svare|Som sprog AI|jeg ikke i stand)/i', $message) !== 1) : false;
}

function mc_open_ai_upload($path, $post_fields = []) {
    return mc_curl('https://api.openai.com/v1/files', array_merge(['file' => new CurlFile($path, 'application/json')], $post_fields), ['Content-Type: multipart/form-data', 'Authorization: Bearer ' . mc_open_ai_key()], 'UPLOAD', 30);
}

function mc_open_ai_file_training($url) {
    $response = mc_open_ai_source_file_to_paragraphs($url);
    mc_file_delete($url);
    return is_array($response) ? mc_open_ai_embeddings_generate($response, $url) : $response;
}

function mc_open_ai_url_training($url) {
    $response = mc_open_ai_html_to_paragraphs($url);
    $embedding_urls = mc_open_ai_get_training_source_names();
    if ($response[1] != 200) {
        return [false, 'http-error-' . $response[1], 'The URL ' . $url . ' returned a ' . $response[1] . ' error: ' . PHP_EOL . strip_tags(preg_replace('/(<(script|style)\b[^>]*>).*?(<\/\2>)/is', "", $response[0]))];
    }
    if (in_array(rtrim($url, '/'), $embedding_urls) || in_array($url . '/', $embedding_urls)) {
        return [true];
    }
    if (empty($response[0])) {
        return [false, 'The page at ' . $url . ' has not content or is not accessible.'];
    }
    return is_array($response) ? mc_open_ai_embeddings_generate($response[0], $url) : $response;
}

function mc_open_ai_qea_training($questions_answers, $language = false, $reset = false, $update_index = false) {
    $db_embeddings = $reset ? [] : mc_get_external_setting('embedding-texts', []);
    $db_embeddings_questions = $reset ? [] : array_column($db_embeddings, 0);
    $questions_answers_to_save = $reset ? [] : $db_embeddings;
    $embeddings = [];
    $set_data_null_keys = ['transcript', 'transcript_email', 'human_takeover', 'archive_conversation'];
    for ($i = 0; $i < count($db_embeddings); $i++) {
        $db_embeddings[$i] = [$db_embeddings[$i][0], $db_embeddings[$i][1], mc_isset($db_embeddings[$i], 6)];
    }

    // Update an existing question and answer
    if ($update_index !== false && $update_index !== '') {
        $db_embeddings[$update_index] = [$questions_answers[0], $questions_answers[1], mc_isset($questions_answers, 6)];
        $questions_answers_to_save[$update_index] = $questions_answers;
        $questions_answers = [];
    }

    // Update embeddings and questions
    for ($i = 0; $i < count($questions_answers); $i++) {
        $questions = [];
        if (!empty($questions_answers[$i][0])) {
            if (isset($questions_answers[$i][5]) && !is_array($questions_answers[$i][5])) {
                $questions_answers[$i][5] = [];
            }
            if (count(mc_isset($questions_answers[$i], 6, [])) == 1 && !$questions_answers[$i][6][0][1] && !in_array($questions_answers[$i][6][0][0], $set_data_null_keys)) {
                $questions_answers[$i][6] = [];
            }
            for ($j = 0; $j < count($questions_answers[$i][0]); $j++) {
                if (!in_array($questions_answers[$i][0][$j], $db_embeddings_questions)) {
                    $questions_answers[$i][1] = str_replace('..', '.', $questions_answers[$i][1], $questions_answers[$i][1]);
                    array_push($questions, $questions_answers[$i][0][$j]);
                }
            }
            array_push($questions_answers_to_save, $questions_answers[$i]);
            array_push($db_embeddings, [$questions, $questions_answers[$i][1], $language, mc_isset($questions_answers[$i], 6)]);
        }
    }
    for ($i = 0; $i < count($db_embeddings); $i++) {
        for ($j = 0; $j < count($db_embeddings[$i][0]); $j++) {
            $extra = [];
            if (!empty($db_embeddings[$i][3])) {
                foreach ($db_embeddings[$i][3] as $value) {
                    if ($value[1] || in_array($value[0], $set_data_null_keys)) {
                        $extra['set_data'][$value[0]] = $value[1];
                    }
                }
            }
            $question = trim($db_embeddings[$i][0][$j]);
            array_push($embeddings, [[$question, $db_embeddings[$i][1]], mc_isset($db_embeddings[$i], 2, ''), 'qea', $extra ? $extra : false]);
        }
    }

    // Delete embeddings of deleted or updated questions
    $files_names = mc_isset(mc_get_external_setting('embedding-sources'), 'mc-database', []);
    for ($i = 0; $i < count($files_names); $i++) {
        $file_path = mc_open_ai_embeddings_get_file($files_names[$i]);
        if (file_exists($file_path)) {
            $embeddings_file = json_decode(file_get_contents($file_path), true);
            $embeddings_file_final = [];
            $is_updated = false;
            for ($j = 0; $j < count($embeddings_file); $j++) {
                $is_deleted = true;
                for ($y = 0; $y < count($embeddings); $y++) {
                    if (trim($embeddings_file[$j]['text']) == trim(is_string($embeddings[$y][0]) ? $embeddings[$y][0] : $embeddings[$y][0][0]) && json_encode(mc_isset($embeddings_file[$j], 'extra')) == json_encode(mc_isset($embeddings[$y], 3))) {
                        $is_deleted = false;
                        break;
                    }
                }
                if (!$is_deleted) {
                    array_push($embeddings_file_final, $embeddings_file[$j]);
                } else {
                    $is_updated = true;
                }
            }
            if ($is_updated) {
                if (count($embeddings_file_final)) {
                    mc_file($file_path, json_encode($embeddings_file_final, JSON_UNESCAPED_UNICODE));
                } else {
                    mc_file_delete($file_path);
                }
            }
        }
    }
    mc_save_external_setting('embedding-texts', $questions_answers_to_save);
    $response = mc_open_ai_embeddings_generate($embeddings, 'mc-database');
    return $response[0] ? true : $response;
}

function mc_open_ai_articles_training() {
    $paragraphs = [];
    $articles = mc_get_articles(false, false, true, false, 'all');
    for ($i = 0; $i < count($articles); $i++) {
        array_push($paragraphs, [strip_tags($articles[$i]['title'] . ' ' . $articles[$i]['content']), mc_isset($articles[$i], 'language'), 'article-' . $articles[$i]['id']]);
    }
    return count($paragraphs) ? mc_open_ai_embeddings_generate($paragraphs, 'mc-articles') : true;
}

function mc_open_ai_conversations_training() {
    $last_check = explode('|', mc_get_external_setting('open-ai-embeddings-conversations', '2017-01-01 01:00:00|0'));
    $last_check_unix = strtotime($last_check[0]);
    $count_conversations = $last_check[1];
    $conversations = mc_db_get('SELECT conversation_id, creation_time FROM mc_messages WHERE creation_time > "' . $last_check[0] . '" GROUP BY conversation_id', false);
    $paragraphs = [];
    if ($conversations) {
        $bot_id = mc_get_bot_id();
        $agent_ids = mc_get_agents_ids();
        $language = mc_open_ai_embeddings_language();
        if (empty($language)) {
            $language = mc_get_user_language($agent_ids[0]);
        } else {
            $language = $language[0];
        }
        for ($i = 0; $i < count($conversations); $i++) {
            $conversation_id = $conversations[$i]['conversation_id'];
            $messages = mc_db_get('SELECT user_id, message, payload FROM mc_messages WHERE conversation_id = ' . $conversation_id . ' AND message != "" AND creation_time > "' . $last_check[0] . '" ORDER BY id ASC', false);
            $messages_final = [];
            $count = count($messages);
            for ($j = 0; $j < $count; $j++) {
                $user_id = $messages[$j]['user_id'];
                $is_agent = in_array($user_id, $agent_ids);
                if (strlen($messages[$j]['message']) < 3 || !strpos($messages[$j]['message'], ' ') || ($user_id == $bot_id) || (!$is_agent && $j < ($count - 2) && $messages[$j + 1]['user_id'] == $bot_id && !in_array($messages[$j + 2]['user_id'], $agent_ids)) && (!isset($messages[$j + 3]) || !strpos($messages[$j + 3]['payload'], 'mc-human-takeover'))) {
                    continue;
                }
                array_push($messages_final, [$is_agent, $messages[$j]]);
            }
            for ($j = count($messages_final) - 1; $j > -1; $j--) {
                if (!$messages_final[$j][0]) {
                    $messages_final[$j] = false;
                } else {
                    break;
                }
            }
            $question = '';
            $answer = '';
            for ($j = 0; $j < count($messages_final); $j++) {
                if ($messages_final[$j]) {
                    $is_agent = $messages_final[$j][0];
                    $message = strip_tags(mc_google_get_message_translation($messages_final[$j][1], $language)['message']);
                    if ($is_agent) {
                        $answer .= (!$answer || mc_is_string_ends($answer) ? '' : '.') . ' ' . $message;
                    } else {
                        $question .= (!$question || mc_is_string_ends($question) ? '' : '.') . ' ' . $message;
                    }
                    if ($answer && $question && $is_agent && (empty($messages_final[$j + 1]) || !$messages_final[$j + 1][0])) {
                        array_push($paragraphs, [[trim($question), trim($answer)], '', 'conversation-' . $conversation_id]);
                        $answer = '';
                        $question = '';
                    }
                }
            }
            if (strtotime($conversations[$i]['creation_time']) > $last_check_unix) {
                $count_conversations++;
            }
        }
        $response = mc_open_ai_embeddings_generate($paragraphs, 'mc-conversations');
        if ($response) {
            mc_save_external_setting('open-ai-embeddings-conversations', mc_gmt_now() . '|' . $count_conversations);
        }
    }
    return $paragraphs;
}

function mc_open_ai_embeddings_delete($sources_to_delete) {
    $is_all = $sources_to_delete == 'all';
    $is_all_website = $sources_to_delete == 'all-website';
    $is_all_conversations = $sources_to_delete == 'all-conversations';
    if (empty($sources_to_delete) || (!$is_all && !$is_all_website && !$is_all_conversations && !is_array($sources_to_delete))) {
        return false;
    }
    $embedding_sources_new = [];
    $embedding_sources = mc_get_external_setting('embedding-sources', []);
    $deleted = 0;
    $count_sources_to_delete = is_array($sources_to_delete) ? count($sources_to_delete) : 0;
    foreach ($embedding_sources as $key => $value) {
        if ($is_all || ($is_all_website && !in_array($key, ['mc-conversations', 'mc-articles', 'mc-database']) && !mc_open_ai_is_file($key)) || ($is_all_conversations && $key == 'mc-conversations') || (!$is_all_website && !$is_all_conversations && in_array($key, $sources_to_delete))) {
            for ($i = 0; $i < count($value); $i++) {
                $file_name = mc_open_ai_embeddings_get_file($value[$i]);
                if (file_exists($file_name)) {
                    if (unlink($file_name)) {
                        $deleted++;
                    }
                }
            }
        } else {
            for ($i = 0; $i < $count_sources_to_delete; $i++) {
                $index = array_search($sources_to_delete[$i], $value);
                if ($index !== false) {
                    $file_name = mc_open_ai_embeddings_get_file($sources_to_delete[$i]);
                    if (file_exists($file_name)) {
                        if (unlink($file_name)) {
                            $deleted++;
                            array_splice($value, $index, 1);
                            break;
                        }
                    }
                }
            }
            $embedding_sources_new[$key] = $value;
        }
    }
    mc_db_query('DELETE FROM mc_settings WHERE name = "embeddings-language" LIMIT 1');
    mc_save_external_setting('embedding-sources', $embedding_sources_new);
    if ($is_all || $is_all_conversations) {
        mc_save_external_setting('open-ai-embeddings-conversations', '');
    }
    if ($is_all) {
        $files = scandir(mc_open_ai_embeddings_get_path());
        for ($i = 0; $i < count($files); $i++) {
            if ($files[$i] != '.' && $files[$i] != '..') {
                unlink(mc_open_ai_embeddings_get_path() . $files[$i]);
            }
        }
        mc_save_external_setting('embedding-texts', []);
        return true;
    }
    return $is_all_website || $is_all_conversations || $deleted == $count_sources_to_delete;
}

function mc_open_ai_embeddings_generate($paragraphs_or_string, $save_source = false) {
    if (is_string($paragraphs_or_string)) {
        if (mb_substr(trim($paragraphs_or_string), 0, 1) == '[') {
            $paragraphs_or_string_ = json_decode($paragraphs_or_string, true);
            if (!empty($paragraphs_or_string_)) {
                $paragraphs_or_string == $paragraphs_or_string_;
            } else {
                $paragraphs_or_string = [[$paragraphs_or_string, false]];
            }
        } else {
            $paragraphs_or_string = [[$paragraphs_or_string, false]];
        }
    }
    if (!mc_cloud_membership_has_credits('open-ai')) {
        return mc_error('no-credits', 'mc_open_ai_embeddings_generate');
    }
    if (isset($paragraphs_or_string[0][0]) && is_string($paragraphs_or_string[0][0])) {
        $paragraphs_or_string = mc_open_ai_embeddings_split_paragraphs($paragraphs_or_string, -1, [])[0];
    }
    $paragraphs_or_string_final = $paragraphs_or_string;
    $chars_limit = false;
    $chars_count = 0;
    $answers = [];
    $answers_final = [];
    if ($save_source) {
        $paragraphs_or_string_final = [];
        $path = mc_open_ai_embeddings_get_path();
        $embeddings = mc_open_ai_embeddings_get();
        $embedding_texts = [];
        if (mc_is_cloud()) {
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            $chars_limit = cloud_embeddings_chars_limit();
        }
        for ($i = 0; $i < count($embeddings); $i++) {
            $embedding = $embeddings[$i];
            $embedding_file = json_decode(file_get_contents($path . $embedding), true);
            $texts = array_column($embedding_file, 'text');
            if ($chars_limit) {
                $chars_count += strlen(implode(array_column($embedding_file, 'answer')));
            }
            for ($j = 0; $j < count($texts); $j++) {
                $texts[$j] = [$texts[$j], $embedding];
            }
            $embedding_texts = array_merge($embedding_texts, $texts);
        }

        // Remove duplicates and adjust paragraphs
        for ($i = 0; $i < count($paragraphs_or_string); $i++) {
            if (is_string($paragraphs_or_string[$i])) {
                $paragraphs_or_string[$i] = [$paragraphs_or_string[$i], false];
            }
            if (is_array($paragraphs_or_string[$i][0])) {
                array_push($answers, trim($paragraphs_or_string[$i][0][1]));
                $paragraphs_or_string[$i][0] = $paragraphs_or_string[$i][0][0];
            } else {
                array_push($answers, '');
            }
            if (empty(trim($paragraphs_or_string[$i][0]))) {
                continue;
            }
            if (isset($paragraphs_or_string[$i][2]) && $paragraphs_or_string[$i][2] != 'qea' && strpos($paragraphs_or_string[$i][2], 'article-') === false && strpos($paragraphs_or_string[$i][2], 'conversation-') === false && strpos($paragraphs_or_string[$i][2], 'flow-') === false && !strpos($paragraphs_or_string[$i][0], 'More details at ')) {
                $paragraphs_or_string[$i][0] .= (mc_is_string_ends($paragraphs_or_string[$i][0]) ? '' : '.') . ' More details at ' . $paragraphs_or_string[$i][2] . '.';
            }
            $paragraphs_or_string[$i][0] = trim($paragraphs_or_string[$i][0]);
            $text = $paragraphs_or_string[$i][0];
            $duplicate = false;
            for ($j = 0; $j < count($paragraphs_or_string); $j++) {
                if ($text == trim(is_array($paragraphs_or_string[$j][0]) ? $paragraphs_or_string[$j][0][0] : $paragraphs_or_string[$j][0]) && $j != $i) {
                    $duplicate = true;
                    break;
                }
            }
            if (!$duplicate) {
                for ($j = 0; $j < count($embedding_texts); $j++) {
                    if ($embedding_texts[$j][0] == $text) {
                        $duplicate = true;
                        break;
                    }
                }
                if (!$duplicate) {
                    array_push($paragraphs_or_string_final, $paragraphs_or_string[$i]);
                    array_push($answers_final, $answers[$i]);
                }
            }
        }
        if ($chars_limit) {
            for ($i = 0; $i < count($paragraphs_or_string_final); $i++) {
                $chars_count += strlen($paragraphs_or_string_final[$i][0]) + strlen($answers_final[$i]);
            }
            for ($i = 0; $i < count($embedding_texts); $i++) {
                $chars_count += strlen($embedding_texts[$i][0]);
            }
        }
    }
    if ($chars_limit && $chars_count > $chars_limit) {
        return [false, 'chars-limit-exceeded', $chars_limit, $chars_count];
    }
    $data_all = [];
    $paragraphs = mc_open_ai_embeddings_split_paragraphs($paragraphs_or_string_final, 0, $answers_final);
    $index = $paragraphs[1];
    $answers = $paragraphs[2];
    $paragraphs = $paragraphs[0];
    $errors = [];

    // Generate embeddings
    while ($paragraphs) {
        $paragraphs_texts = [];
        $paragraphs_languages = [];
        for ($i = 0; $i < count($paragraphs); $i++) {
            array_push($paragraphs_texts, is_string($paragraphs[$i]) ? $paragraphs[$i] : $paragraphs[$i][0]);
            array_push($paragraphs_languages, is_string($paragraphs[$i]) ? '' : (is_string($paragraphs[$i][1]) ? $paragraphs[$i][1] : ''));
        }
        $response = mc_open_ai_curl('embeddings', ['model' => 'text-embedding-3-small', 'input' => $paragraphs_texts]);
        $data = mc_isset($response, 'data');
        if ($data) {
            for ($i = 0; $i < count($data); $i++) {
                $data[$i]['text'] = trim($paragraphs_texts[$i]);
                $data[$i]['language'] = $paragraphs_languages[$i];
                if (isset($paragraphs[$i][2])) {
                    $data[$i]['source'] = $paragraphs[$i][2];
                }
                if (isset($paragraphs[$i][3])) {
                    $attachments = mc_isset($paragraphs[$i][3], 'attachments', []);
                    for ($x = 0; $x < count($attachments); $x++) {
                        $paragraphs[$i][3]['attachments'][$x] = [basename($attachments[$x]), $attachments[$x]];
                    }
                    $data[$i]['extra'] = $paragraphs[$i][3];
                }
                if (isset($answers[$i])) {
                    $data[$i]['answer'] = $answers[$i];
                }
            }
            $data_all = array_merge($data_all, $data);
        } else {
            array_push($errors, $response);
        }
        $paragraphs = mc_open_ai_embeddings_split_paragraphs($paragraphs_or_string_final, $index, $answers_final);
        if (empty($paragraphs[0])) {
            $paragraphs = false;
        } else {
            $index = $paragraphs[1];
            $answers = $paragraphs[2];
            $paragraphs = $paragraphs[0];
        }
    }

    // Save embedding files
    if ($save_source) {
        $len_total = 0;
        $embeddings_part = [];
        $count = count($data_all);
        $response = [];
        $embedding_sources = mc_get_external_setting('embedding-sources', []);
        for ($i = 0; $i < $count; $i++) {
            $len_total += strlen(json_encode($data_all[$i]));
            array_push($embeddings_part, $data_all[$i]);
            if ($len_total > 2000000 || $i == $count - 1) {
                $name = bin2hex(openssl_random_pseudo_bytes(10));
                array_push($response, mc_file(mc_open_ai_embeddings_get_file($name), json_encode($embeddings_part, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE)));
                $embeddings_part = [];
                $len_total = 0;
                if (isset($embedding_sources[$save_source])) {
                    if (!in_array($name, $embedding_sources[$save_source])) {
                        array_push($embedding_sources[$save_source], $name);
                    }
                } else {
                    $embedding_sources[$save_source] = [$name];
                }
            }
        }

        // Delete embeddings of deleted articles
        $count = count(mc_isset($embedding_sources, $save_source, []));
        if ($save_source == 'mc-articles' && $count) {
            $paragraphs_or_strings_text = array_column(mc_open_ai_embeddings_split_paragraphs($paragraphs_or_string, -1, [])[0], 0);
            for ($i = 0; $i < $count; $i++) {
                $file_name = mc_open_ai_embeddings_get_file($embedding_sources[$save_source][$i]);
                if (file_exists($file_name)) {
                    $article_embeddings = json_decode(file_get_contents($file_name), true);
                    $is_save = false;
                    for ($j = 0; $j < count($article_embeddings); $j++) {
                        if (!in_array($article_embeddings[$j]['text'], $paragraphs_or_strings_text)) {
                            array_splice($article_embeddings, $j, 1);
                            $j--;
                            $is_save = true;
                        }
                    }
                    if ($is_save) {
                        if (count($article_embeddings)) {
                            mc_file($file_name, json_encode($article_embeddings, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE));
                        } else {
                            unlink($file_name);
                            $embedding_sources[$save_source][$i] = false;
                        }
                    }
                }
            }
        }

        // Delete old embedding file references
        $embedding_sources_final = [];
        foreach ($embedding_sources as $key => $file_names) {
            $embedding_source_file_names = [];
            for ($i = 0; $i < count($file_names); $i++) {
                if (file_exists(mc_open_ai_embeddings_get_file($file_names[$i]))) {
                    array_push($embedding_source_file_names, $file_names[$i]);
                }
            }
            if (count($embedding_source_file_names)) {
                $embedding_sources_final[$key] = $embedding_source_file_names;
            }
        }
        mc_save_external_setting('embedding-sources', $embedding_sources_final);

        //  Miscellaneous
        if (!empty($paragraphs_languages)) {
            $language_code = strtolower(substr($paragraphs_languages[0], 0, 2));
            if ($language_code) {
                $embeddings_language = mc_open_ai_embeddings_language();
                if (!in_array($language_code, $embeddings_language)) {
                    array_push($embeddings_language, $language_code);
                    mc_save_external_setting('embeddings-language', $embeddings_language);
                }
            }
        }
        if (!file_exists($path . 'index.html')) {
            mc_file($path . 'index.html', 'Forbidden');
        }
        return empty($paragraphs_or_string_final) ? [true, []] : [$response, $errors];
    }
    return $data_all;
}

function mc_open_ai_embeddings_split_paragraphs($paragraphs, $last_index, $answers) {
    $response = [];
    $len_total = 0;
    $paragraphs_2 = [];
    $answers_2 = [];
    for ($i = 0; $i < count($paragraphs); $i++) {
        $len = strlen($paragraphs[$i][0]);
        if ($len > 8000) {
            $splits = mb_str_split($paragraphs[$i][0], 8000);
            for ($j = 0; $j < count($splits); $j++) {
                array_push($paragraphs_2, [$splits[$j], $paragraphs[$i][1]]);
                if (isset($answers[$i])) {
                    array_push($answers_2, $answers[$i]);
                }
            }
        } else {
            array_push($paragraphs_2, $paragraphs[$i]);
            if (isset($answers[$i])) {
                array_push($answers_2, $answers[$i]);
            }
        }
    }
    if ($last_index !== -1) {
        for ($i = $last_index; $i < count($paragraphs_2); $i++) {
            $len = strlen($paragraphs_2[$i][0]);
            if ($len_total + $len < 100000 || !$len_total) {
                array_push($response, $paragraphs_2[$i]);
                $len_total += $len;
                $last_index = $i;
            } else {
                break;
            }
        }
    } else {
        $response = $paragraphs_2;
    }
    return [$response, $last_index + 1, $answers_2];
}

function mc_open_ai_embeddings_compare($vector_1, $vector_2, $vector_1_text, $vector_2_text) {
    $dot_product = 0.0;
    $norm_query = 0.0;
    $norm_text = 0.0;
    for ($i = 0; $i < count($vector_1); $i++) {
        $dot_product += $vector_1[$i] * $vector_2[$i];
        $norm_query += $vector_1[$i] ** 2;
        $norm_text += $vector_2[$i] ** 2;
    }
    return min((($norm_query > 0 && $norm_text > 0) ? ($dot_product / (sqrt($norm_query) * sqrt($norm_text))) : 0.0) + (mb_stripos($vector_2_text, $vector_1_text, 0, 'UTF-8') !== false ? 0.2 : 0.0), 1.0);
}

function mc_open_ai_embeddings_message($user_prompt, $language = false, $extra = false, $min_score = 0.2) {
    global $MC_OPEN_AI_PLAYGROUND;
    $user_prompt_embeddings = mc_open_ai_embeddings_generate($user_prompt);
    if (!empty($user_prompt_embeddings) && isset($user_prompt_embeddings[0]['embedding'])) {
        $scores = [];
        $user_prompt_embeddings = $user_prompt_embeddings[0]['embedding'];
        $embeddings = mc_open_ai_embeddings_get();
        $path = mc_open_ai_embeddings_get_path();
        $embedding_languages = [];
        if ($language) {
            $language = strtolower($language);
        }
        for ($i = 0; $i < count($embeddings); $i++) {
            $embeddings_content = json_decode(file_get_contents($path . $embeddings[$i]), true);
            for ($j = 0; $j < count($embeddings_content); $j++) {
                $embedding = $embeddings_content[$j];
                $embedding_language = mc_isset($embedding, 'language');
                if ($embedding_language && is_string($embedding_language)) {
                    $embedding_language = substr($embedding_language, 0, 2);
                    if (!in_array($embedding_language, $embedding_languages)) {
                        array_push($embedding_languages, $embedding_language);
                    }
                }
                if (!$language || !$embedding_language || $embedding_language == $language || count(mc_open_ai_embeddings_language()) == 1) {
                    $score = !empty($user_prompt_embeddings) && !empty($embedding['embedding']) ? mc_open_ai_embeddings_compare($user_prompt_embeddings, $embedding['embedding'], $user_prompt, $embedding['text']) : 0;
                    if ($score > $min_score && (empty($embedding['extra']) || empty($embedding['extra']['conditions']) || mc_automations_validate($embedding['extra']['conditions'], true))) {
                        array_push($scores, ['score' => $score, 'text' => $embedding['text'], 'answer' => mc_isset($embedding, 'answer'), 'source' => mc_isset($embedding, 'source'), 'extra' => mc_isset($embedding, 'extra')]);
                    }
                }
            }
        }
        $count = count($scores);
        if ($count) {
            usort($scores, function ($a, $b) {
                return $a['score'] <=> $b['score'];
            });
            if ($count > 10) {
                $scores = array_slice($scores, -10);
            }
            $count = count($scores);
            if ($count && $scores[$count - 1]['score'] > 0.9) {
                $scores = [$scores[$count - 1]];
                $count = 1;
            }
            $context = '';
            $model = mc_open_ai_get_gpt_model();
            $context_max_length = in_array($model, ['gpt-4.1-nano', 'gpt-4.1-mini', 'gpt-4.1']) ? 500000 : (in_array($model, ['o1-mini', 'gpt-4', 'gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo']) ? 32000 : (in_array($model, ['o3-mini', 'o4-mini', 'o1']) ? 50000 : 4000));
            for ($i = $count - 1; $i > -1; $i--) {
                if (mb_strlen($context) < $context_max_length) {
                    $answer = $scores[$i]['answer'];
                    $text_ = $scores[$i]['text'];
                    $text = (substr($text_, 0, 9) != 'Question:' ? 'Question: ' : '') . $text_ . ($answer ? (mc_is_string_ends($text_) ? '' : '.') . PHP_EOL . PHP_EOL . 'Answer: ' . $answer : '');
                    $chips_pos = strpos($text, 'Answer: [chips');
                    if ($chips_pos === false || !strpos(substr($text, $chips_pos), $user_prompt)) {
                        $context .= ($context ? PHP_EOL . PHP_EOL : '') . $text;
                    }
                }
            }
            $context = trim($context);
            if (mb_strlen($context) > $context_max_length) {
                $context = mb_substr($context, 0, $context_max_length);
            }
            if ($MC_OPEN_AI_PLAYGROUND !== null) {
                $GLOBALS['MC_OPEN_AI_PLAYGROUND_E'] = $scores;
            }
            $extra_ = ['embeddings' => true, 'user_id' => mc_isset($extra, 'user_id')];
            if (!empty($extra['smart_reply'])) {
                $extra_['smart_reply'] = true;
            }
            $response = mc_open_ai_message(['context' => $context, 'user_prompt' => $user_prompt], false, false, mc_isset($extra, 'conversation_id'), $extra_, false, mc_isset($extra, 'attachments'), mc_isset($extra, 'context'));
            if ($response) {
                if (empty($response[0])) {
                    if (!empty($response[1])) {
                        mc_error('open-ai-error', 'mc_open_ai_message', mc_isset($response[1], 'error', $response[1]));
                    }
                } else {
                    if (mc_open_ai_is_valid($response[1])) {
                        $response_text = mc_open_ai_text_formatting($response[1]);
                        $top_score = $scores[count($scores) - 1];
                        $embedding_extra = false;
                        if ($extra == 'translation') {
                            $message = mc_google_translate([$response_text], $language);
                            if (!empty($message[0])) {
                                $response_text = $message[0][0];
                            }
                        }
                        if (mc_get_multi_setting('open-ai', 'open-ai-source-links') && (empty($response[3]) || $response[3] != 'skip-references')) {
                            $sources_string = '';
                            $index = 1;
                            $is_articles_home = mc_get_articles_page_url();
                            for ($i = 0; $i < $count; $i++) {
                                $source = mc_isset($scores[$i], 'source');
                                $is_article = $is_articles_home && strpos($source, 'article-') === 0;
                                if ($source && strpos($sources_string, $source) === false && ($is_article || strpos($source, 'http') === 0) && strpos($response_text, $source) === false) {
                                    $sources_string .= ($is_article ? mc_get_article_url(substr($source, 8)) : $source) . '#mc-' . $index . ' | ';
                                    $index++;
                                }
                            }
                            if ($sources_string && !strpos($sources_string, mc_('References') . ': ')) {
                                $response_text .= PHP_EOL . PHP_EOL . mc_('References') . ': ' . substr($sources_string, 0, -3);
                            }
                        }
                        if ($top_score['score'] > 0.45 || strpos(mc_string_slug($top_score['text']), mc_string_slug(substr($response_text, 1, -1)))) {
                            $embedding_extra = mc_isset($top_score, 'extra');
                            $attachments = mc_isset($embedding_extra, 'attachments');
                            if ($attachments) {
                                $response[6] = array_merge($response[6], $attachments);
                            }
                        }
                        return ['message' => str_replace('disabled="true"]', ']', $response_text), 'payload' => $response[5], 'payload_message' => $response[7], 'attachments' => $response[6], 'embedding_extra' => $embedding_extra];
                    }
                }
            }
        } else if ($extra != 'translation' && $language && count($embedding_languages) && !in_array($language, $embedding_languages) && (mc_get_setting('dialogflow-multilingual-translation') || mc_get_multi_setting('google', 'google-multilingual-translation'))) { // Deprecated: mc_get_setting('dialogflow-multilingual-translation')
            $message = mc_google_translate([$user_prompt], $embedding_languages[0]);
            if (!empty($message[0])) {
                return mc_open_ai_embeddings_message($message[0][0], $embedding_languages[0], 'translation');
            }
        }
    }
    return false;
}

function mc_open_ai_embeddings_language() {
    global $MC_EMBEDDINGS_LANGUAGES;
    if (!empty($MC_EMBEDDINGS_LANGUAGES)) {
        return $MC_EMBEDDINGS_LANGUAGES;
    }
    $embeddings_language = mc_get_external_setting('embeddings-language');
    if (!$embeddings_language || empty($embeddings_language[0]) || (is_string($embeddings_language) && strpos($embeddings_language, '[') === false)) { // Deprecated: remove || (is_string($embeddings_language) && strpos($embeddings_language, '[') === false)
        $embeddings = mc_open_ai_embeddings_get();
        $embeddings_language = [mc_get_multi_setting('open-ai', 'open-ai-training-data-language', 'en')];
        for ($i = 0; $i < count($embeddings); $i++) {
            $embeddings_single = json_decode(file_get_contents(mc_open_ai_embeddings_get_path() . $embeddings[$i]), true);
            for ($j = 0; $j < count($embeddings_single); $j++) {
                $language_code = mc_isset($embeddings_single[$j], 'language');
                if ($language_code && is_string($language_code)) {
                    $language_code = strtolower(substr($language_code, 0, 2));
                    if (!in_array($language_code, $embeddings_language)) {
                        array_push($embeddings_language, $language_code);
                    }
                }
            }
        }
        mc_save_external_setting('embeddings-language', $embeddings_language ? $embeddings_language : '-');
    } else if ($embeddings_language == '-') {
        $embeddings_language = '';
    }
    $MC_EMBEDDINGS_LANGUAGES = $embeddings_language;
    return $embeddings_language;
}

function mc_open_ai_embeddings_get() {
    $files = scandir(mc_open_ai_embeddings_get_path());
    $embeddings = [];
    for ($i = 0; $i < count($files); $i++) {
        $file = $files[$i];
        if (strpos($file, 'embeddings-') === 0) {
            array_push($embeddings, $files[$i]);
        }
    }
    return $embeddings;
}

function mc_open_ai_embeddings_get_path() {
    $name = 'MC_EMBEDDINGS_PATH';
    if (isset($GLOBALS[$name])) {
        return $GLOBALS[$name];
    }
    $path = mc_upload_path() . '/embeddings/';
    $cloud = mc_is_cloud() ? mc_cloud_account() : false;
    if (!file_exists($path)) {
        mkdir($path, 0755, true);
    }
    if ($cloud) {
        require_once(MC_CLOUD_PATH . '/account/functions.php');
        $path .= $cloud['user_id'] . '/';
        if (!file_exists($path)) {
            mkdir($path, 0755, true);
        }
    }
    $GLOBALS[$name] = $path;
    return $path;
}

function mc_open_ai_embeddings_get_information() {
    $information = ['limit' => false, 'files' => [0, 0], 'website' => [0, 0], 'qea' => [0, 0], 'flows' => [0, 0], 'articles' => [0, 0], 'conversations' => [0, 0]];
    $sources = mc_get_external_setting('embedding-sources', []);
    $path = mc_open_ai_embeddings_get_path();
    $total = 0;
    foreach ($sources as $key => $embeddings) {
        $key_info = $key == 'mc-database' ? 'qea' : ($key == 'mc-conversations' ? 'conversations' : ($key == 'mc-articles' ? 'articles' : ($key == 'mc-flows' ? 'flows' : (mc_open_ai_is_file($key) ? 'files' : 'website'))));
        for ($i = 0; $i < count($embeddings); $i++) {
            $text_len = strlen(implode('', array_column(mc_open_ai_embeddings_get_file($embeddings[$i], true), 'text')));
            $information[$key_info][0] += $text_len;
            $total += $text_len;
        }
        switch ($key_info) {
            case 'website':
            case 'files':
                $count = $information[$key_info][1] + 1;
                break;
            case 'conversations':
                $count = explode('|', mc_get_external_setting('open-ai-embeddings-conversations', '0|0')); // Deprecated
                $count = isset($count[1]) ? $count[1] : 0; // Deprecated. Replace with: $information[$key_info][1] = explode('|', mc_get_external_setting('open-ai-embeddings-conversations', '0|0'))[0]
                break;
            case 'articles':
                $count = count(mc_get_articles());
                break;
            case 'qea':
                $count = count(mc_get_external_setting('embedding-texts', []));
                break;
            case 'flows':
                $count = count(mc_flows_get());
                break;
        }
        $information[$key_info][1] = $count;
    }
    $information['total'] = $total;
    if (mc_is_cloud()) {
        require_once(MC_CLOUD_PATH . '/account/functions.php');
        $information['limit'] = cloud_embeddings_chars_limit();
    }
    return $information;
}

function mc_open_ai_embeddings_get_file($embedding_id, $return_content = false) {
    $path = mc_open_ai_embeddings_get_path() . 'embeddings-' . $embedding_id . '.json';
    return $return_content ? (file_exists($path) ? json_decode(file_get_contents($path), true) : []) : $path;
}

function mc_open_ai_embeddings_update_single($embedding_id, $index, $text) {
    $path = mc_open_ai_embeddings_get_path() . 'embeddings-' . $embedding_id . '.json';
    $embedding = mc_open_ai_embeddings_get_file($embedding_id, true);
    $chars_count = 0;
    $answer = false;
    if (is_array($text) && isset($text['answer'])) {
        $answer = $text['answer'];
        $text = $text['question'];
    }
    if ($text) {
        if (mc_is_cloud()) {
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            $chars_limit = cloud_embeddings_chars_limit();
            if ($chars_limit) {
                $embeddings = mc_open_ai_embeddings_get();
                for ($i = 0; $i < count($embeddings); $i++) {
                    $embedding_file = json_decode(file_get_contents(mc_open_ai_embeddings_get_path() . $embeddings[$i]), true);
                    $chars_count += strlen(implode(array_column($embedding_file, 'text'))) + strlen(implode(array_column($embedding_file, 'answer')));
                }
                if ($chars_count > $chars_limit) {
                    return [false, 'chars-limit-exceeded', $chars_limit, $chars_count];
                }
            }
        }
        $response = mc_open_ai_curl('embeddings', ['model' => 'text-embedding-3-small', 'input' => [$text]]);
        $data = mc_isset($response, 'data');
        if (!empty($data)) {
            $embedding[$index]['text'] = $text;
            $embedding[$index]['embedding'] = $data[0]['embedding'];
            if ($answer) {
                $embedding[$index]['answer'] = $answer;
            }
            mc_file($path, json_encode($embedding, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE));
            return true;
        }
    } else {
        array_splice($embedding, $index, 1);
        if (empty($embedding)) {
            mc_open_ai_embeddings_delete([$embedding_id]);
        } else {
            mc_file($path, json_encode($embedding, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE));
        }
        return true;
    }
    return $response;
}

function mc_open_ai_embeddings_get_conversations() {
    $embeddings = mc_isset(mc_get_external_setting('embedding-sources'), 'mc-conversations', []);
    $response = [];
    for ($i = 0; $i < count($embeddings); $i++) {
        $embedding = mc_open_ai_embeddings_get_file($embeddings[$i], true);
        for ($y = 0; $y < count($embedding); $y++) {
            $id = $y . '-' . $embeddings[$i];
            array_push($response, ['question' => $embedding[$y]['text'], 'answer' => $embedding[$y]['answer'], 'id' => $id]);
        }
    }
    return $response;
}

function mc_open_ai_embeddings_save_conversations($qea) {
    $response = [];
    for ($i = 0; $i < count($qea); $i++) {
        $id = explode('-', $qea[$i]['id']);
        array_push($response, mc_open_ai_embeddings_update_single($id[1], $id[0], $qea[$i]));
    }
    return $response;
}

function mc_open_ai_source_file_to_paragraphs($url) {
    $extension = mc_open_ai_is_file($url);
    $paragraphs = [];
    $text = false;
    if (!$extension) {
        mc_file_delete($url);
        return 'invalid-file-extension';
    }
    if (in_array($extension, ['pdf', 'json', 'csv'])) {
        $upload_url = mc_upload_path(true);
        $file = strpos($url, $upload_url) === 0 ? mc_upload_path() . str_replace($upload_url, '', $url) : mc_download_file($url, 'mc_open_ai_source_file.' . $extension, false, [], 0, true);
        switch ($extension) {
            case 'pdf':
                $text = mc_pdf_to_text($file);
                break;
            case 'json':
                $text = mc_json_to_text($file);
                break;
            case 'csv':
                $text = mc_csv_to_text($file);
                break;
        }
        mc_file_delete($file);
    } else {
        $text = trim(mc_get($url));
    }
    if ($text) {
        $encoding = mb_detect_encoding($text);
        if (strpos($encoding, 'UTF-16') !== false) {
            $text = mb_convert_encoding($text, 'UTF-8', 'UTF-16');
        }
        $separator = ['።', '。', '။', '.', '।'];
        for ($i = 0; $i < count($separator); $i++) {
            if (strpos($text, $separator[$i])) {
                $separator = $separator[$i];
                break;
            }
        }
        $parts = is_string($separator) ? explode($separator . ' ', $text) : [$text];
        $paragraph = '';
        for ($i = 0; $i < count($parts); $i++) {
            $part = trim($parts[$i]);
            $length_1 = mb_strlen($paragraph);
            $length_2 = mb_strlen($parts[$i]);
            if (($length_1 + $length_2 < 2000) || $length_1 < 100 || $length_2 < 100) {
                $paragraph .= $part;
            } else {
                array_push($paragraphs, [$paragraph ? $paragraph . ' ' . $part : $part, '', mc_beautify_file_name(basename($url))]);
                $paragraph = '';
            }
        }
        if ($paragraph) {
            array_push($paragraphs, [$paragraph, '', mc_beautify_file_name(basename($url))]);
        }
    }
    return $paragraphs;
}

function mc_open_ai_get_gpt_model() {
    $model = mc_get_multi_setting('open-ai', 'open-ai-model', 'gpt-4.1-mini');
    return $model == 'gpt-3.5-turbo-instruct' ? 'gpt-3.5-turbo' : $model;
}

function mc_open_ai_audio_to_text($path_or_url, $audio_language = false, $user_id = false, $message_id = false, $conversation_id = false) {
    $is_delete = false;
    if (!mc_cloud_membership_has_credits('open-ai')) {
        return mc_error('no-credits', 'mc_open_ai_audio_to_text');
    }
    if (!$audio_language) {
        $audio_language = mc_get_user_language($user_id ? $user_id : mc_get_active_user_ID());
    }
    if (strpos($path_or_url, 'http') === 0) {
        $path_file = mc_upload_path(false, true) . '/' . basename($path_or_url);
        if (file_exists($path_file)) {
            $path_or_url = $path_file;
        } else {
            $is_delete = true;
            $path_or_url = mc_download_file($path_or_url, 'temp_open_ai_' . basename($path_or_url), false, [], 0, true);
        }
    }
    if (!$audio_language) {
        $audio_language = mc_get_user_language();
    }
    $response = mc_curl('https://api.openai.com/v1/audio/transcriptions', ['file' => new CURLFile($path_or_url), 'model' => 'whisper-1', 'language' => mc_isset(['br' => 'pt'], $audio_language, $audio_language)], ['Content-Type: multipart/form-data', 'Authorization: Bearer ' . mc_open_ai_key()], 'POST', 30);
    $message = mc_isset($response, 'text');
    if ($message) {
        if ($conversation_id || $message_id) {
            if (!$message_id) {
                $message_id = mc_isset(mc_db_get('SELECT id FROM mc_messages WHERE conversation_id = ' . mc_db_escape($conversation_id, true) . ' ORDER BY id DESC LIMIT 1'), 'id');
            }
            if ($message_id) {
                if (mc_get_multi_setting('open-ai', 'open-ai-speech-recognition')) {
                    mc_update_message($message_id, $message);
                } else {
                    mc_db_query('UPDATE mc_messages SET message = "' . mc_db_escape($message) . '" WHERE id = ' . $message_id);
                }
            }
        }
    } else {
        mc_error('open-ai-error', 'mc_open_ai_audio_to_text', $response);
    }
    if ($is_delete) {
        mc_file_delete($path_or_url);
    }
    return $message;
}

function mc_open_ai_key() {
    return mc_ai_is_manual_sync('open-ai') ? trim(mc_get_multi_setting('open-ai', 'open-ai-key')) : OPEN_AI_KEY;
}

function mc_open_ai_assistant($message, $conversation_id, $human_takeover_check = true) {
    $assistant_id = mc_get_multi_setting('open-ai', 'open-ai-assistant-id');
    $conversation = mc_db_get('SELECT extra_2, department FROM mc_conversations WHERE id = ' . mc_db_escape($conversation_id, true));
    $thread_id = mc_isset($conversation, 'extra_2');
    $department_id = mc_isset($conversation, 'department');
    if ($department_id) {
        $assistants = mc_get_setting('open-ai-assistants');
        if ($assistants && is_array($assistants)) {
            for ($i = 0; $i < count($assistants); $i++) {
                if ($assistants[$i]['open-ai-assistants-department-id'] == $department_id) {
                    $assistant_id = $assistants[$i]['open-ai-assistants-id'];
                    break;
                }
            }
        }
    }
    if (!$assistant_id) {
        return mc_error('open-ai-error', 'mc_open_ai_assistant', 'No assistant ID', true);
    }
    $header = ['Content-Type: application/json', 'OpenAI-Beta: assistants=v2', 'Authorization: Bearer ' . trim(mc_get_multi_setting('open-ai', 'open-ai-key'))];
    $url_part = 'https://api.openai.com/v1/';
    if ($thread_id) {
        $response = mc_curl($url_part . 'threads/' . $thread_id . '/messages', json_encode(['role' => 'user', 'content' => $message], JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE), $header);
    }
    $response = mc_curl($url_part . 'threads' . ($thread_id ? '/' . $thread_id : '') . '/runs', json_encode($thread_id ? ['assistant_id' => $assistant_id] : ['assistant_id' => $assistant_id, 'thread' => ['messages' => [['role' => 'user', 'content' => $message]]]], JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE), $header);
    $run_id = mc_isset($response, 'id');
    if ($run_id) {
        if (!$thread_id) {
            $thread_id = mc_isset($response, 'thread_id');
            mc_db_query('UPDATE mc_conversations SET extra_2 = "' . mc_db_escape($thread_id) . '" WHERE id = ' . mc_db_escape($conversation_id, true));
        }
        for ($i = 0; $i < 30; $i++) {
            sleep(1);
            $response = json_decode(mc_curl($url_part . 'threads/' . $thread_id . '/runs/' . $run_id, '', $header, 'GET'), true);
            $status = mc_isset($response, 'status');
            if ($status == 'completed') {
                $response = json_decode(mc_curl($url_part . 'threads/' . $thread_id . '/messages', '', $header, 'GET'), true);
                $message = isset($response['data']) ? $response['data'][0]['content'][0]['text']['value'] : '';
                if ($message) {
                    $message = preg_replace('/【[\s\S]+?】/', '', mc_open_ai_text_formatting($message));
                }
                return $message;
            } else if ($status == 'expired') {
                break;
            } else {
                $function_calling = mc_open_ai_function_calling($response, false, $conversation_id);
                if ($function_calling) {
                    if ($human_takeover_check || $function_calling[0] != 'mc-human-takeover') {
                        mc_curl($url_part . 'threads/' . $thread_id . '/runs/' . $run_id . '/submit_tool_outputs', json_encode(['tool_outputs' => [['tool_call_id' => $function_calling[1], 'output' => $function_calling[2]]]], JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE), $header);
                    }
                    if ($function_calling[0] == 'mc-human-takeover') {
                        return $function_calling[0];
                    }
                }
            }
        }
    } else if (isset($response['error'])) {
        $error = mc_isset($response['error'], 'message');
        if (strpos($error, 'active run')) {
            $run_id = substr($error, strrpos($error, ' run_') + 1, -1);
            for ($i = 0; $i < 30; $i++) {
                sleep(2);
                if (mc_isset(json_decode(mc_curl($url_part . 'threads/' . $thread_id . '/runs/' . $run_id, '', $header, 'GET'), true), 'status') == 'completed') {
                    return mc_open_ai_assistant($message, $conversation_id, $human_takeover_check);
                }
            }
        }
    }
    return mc_error('open-ai-error', 'mc_open_ai_assistant', $response);
}

function mc_open_ai_data_scraping($conversation_id, $prompt_id) {
    if (!mc_cloud_membership_has_credits('open-ai')) {
        return mc_error('no-credits', 'mc_open_ai_audio_to_text');
    }
    $prompt = mc_open_ai_data_scraping_get_prompts()[$prompt_id];
    if ($prompt_id == 'summary') {
        $messages = mc_get_conversation(false, $conversation_id)['messages'];
        $text = 'Here is the conversation to summarize: ' . PHP_EOL;
        foreach ($messages as $message) {
            $text .= (mc_is_agent($message['user_type']) ? 'Agent' : 'User') . ': ' . $message['message'] . PHP_EOL;
        }
        $response = mc_open_ai_curl('chat/completions', ['model' => mc_open_ai_get_gpt_model(), 'messages' => [['role' => 'developer', 'content' => 'You are a helpful assistant that summarizes conversations between users and agents. Generate a summary of the conversation with the key user questions and agent answers. Only return the summary and nothing else. Do not add any other text. Do not ask questions.'], ['role' => 'user', 'content' => $text]]]);
        $response = mc_isset(mc_isset(mc_isset($response, 'choices', [[]])[0], 'message'), 'content', '');
        $response = [true, $response, false, null, empty($response) ? ['unknow_answer' => true] : []];
    } else {
        $response = mc_open_ai_message($prompt[0] . ' from the user messages. Do not scrape anything else, return only the scraped information separated by breaklines, do not add text. If the information is not included, write exactly "I don\'t know.', false, false, $conversation_id, 'scraping');
    }
    if (!$response || !$response[0]) {
        mc_error('open-ai-error', 'mc_open_ai_data_scrape', $response);
    } else if (empty($response[5]['unknow_answer'])) {
        $lines = preg_split("/\r\n|\n|\r/", $response[1]);
        $text = '';
        if (in_array('duplicate', $prompt[1])) {
            $lines = array_unique($lines);
        }
        for ($i = 0; $i < count($lines); $i++) {
            $line = $lines[$i] . '';
            $lines[$i] = '';

            for ($j = 0; $j < count($prompt[1]); $j++) {
                $check = $prompt[1][$j];
                if (strpos($line, $check) !== false || ($check == 123 && is_numeric($line))) {
                    continue 2;
                }
            }
            $count = count($prompt[2]);
            if ($count) {
                $valid = false;
                for ($j = 0; $j < count($prompt[2]); $j++) {
                    $check = $prompt[2][$j];
                    if (strpos($line, $check) !== false || ($check == 123 && !is_numeric($line))) {
                        $valid = true;
                        break;
                    }
                }
                if (!$valid) {
                    continue;
                }
            }
            $text .= trim($line) . PHP_EOL;
        }
        return $text;
    } else {
        $response = '';
    }
    return $response;
}

function mc_open_ai_data_scraping_get_prompts($type = false) {
    $prompts = ['login' => ['The login details are made up of a URL or IP address, a username or email, and a password. Scrape all login details', [], [], 'Login information'], 'links' => ['Scrape all links and URLs', ['@', 123], ['http', 'www'], 'Links and URLs'], 'contacts' => ['Scrape addresses, phone numbers and emails', ['http'], [], 'Contact information'], 'summary' => ['Generate a summary of the conversation', [], [], 'Summary']];
    if ($type == 'name') {
        foreach ($prompts as $key => $value) {
            $prompts[$key] = mc_($value[3]);
        }
    }
    return $prompts;
}

function mc_open_ai_function_calling($response, $query_tools = false, $conversation_id = false) {
    $function = false;
    $function_name = false;
    $id = false;
    if (mc_isset($response, 'status') == 'requires_action') {
        $response = mc_isset($response, 'required_action');
        if ($response) {
            $response = mc_isset(mc_isset($response, 'submit_tool_outputs'), 'tool_calls');
            if (!empty($response)) {
                $function = mc_isset($response[0], 'function');
                $id = $response[0]['id'];
            }
        }
    } else if (!empty($response['choices'])) {
        $response = mc_isset(mc_isset($response['choices'][0], 'message'), 'tool_calls');
        if (!empty($response)) {
            $function = mc_isset($response[count($response) - 1], 'function');
        }
    }
    if ($function) {
        $function_name = $function['name'];
        if ($function_name == 'mc-human-takeover') {
            return [$function_name, $id, ''];
        }
        if (($function_name == 'mc-shopify' || $function_name == 'mc-shopify-single') && mc_is_cloud()) {
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            $arguments = json_decode($function['arguments'], true);
            if ($arguments) {
                return shopify_ai_function_calling($function_name, $id, $arguments, $query_tools);
            }
        }
        if (defined('MC_WOOCOMMERCE') && !mc_get_setting('wc-disable-bot-integration')) {
            $arguments = json_decode($function['arguments'], true);
            if (mc_woocommerce_open_ai_check_function_name($function_name)) {
                if ($arguments) {
                    return mc_woocommerce_open_ai_function_calling($function_name, $id, $arguments, $query_tools);
                }
            } else if (mc_woocommerce_open_ai_check_function_name($function_name, 2)) {
                return mc_woocommerce_open_ai_function_calling_2($function_name, $id, $arguments);
            }
        }
        $qea = mc_get_external_setting('embedding-texts', []);
        for ($i = 0; $i < count($qea); $i++) {
            $qea[$i][0] = is_array($qea[$i][0]) ? $qea[$i][0] : [$qea[$i][0]]; // Deprecated;
            for ($j = 0; $j < count($qea[$i][0]); $j++) {
                if (substr(mc_string_slug($qea[$i][0][$j], 'slug', true), 0, 20) . '-' . $i == $function_name) {
                    $function['user_id'] = mc_get_active_user_ID();
                    $function['conversation_id'] = $conversation_id;
                    $response = mc_curl($qea[$i][2], $function, $qea[$i][4] ? explode(',', $qea[$i][4]) : [], $qea[$i][3], 30);
                    return [$function_name, $id, $response];
                }
            }
        }
    }
    return false;
}

function mc_open_ai_troubleshoot($debug = false) {
    $message = 'Hello world!';
    $conversation_id = false;
    $response = false;
    if ($debug) {
        $_GET['debug'] = true;
    } else {
        $response = mc_curl('https://api.openai.com/v1/embeddings', '{"model":"text-embedding-3-small","input":["Hello world!"]}', ['Content-Type: application/json', 'Authorization: Bearer ' . mc_open_ai_key()], 'POST');
        $error = mc_isset($response, 'error');
        if ($error) {
            return mc_isset($error, 'message', $error);
        }
    }
    $response = mc_open_ai_message($message);
    if ($response && !mc_is_error($response) && $response[0]) {
        $conversation_id = mc_open_ai_dummy_data([['user', $message]]);
        $response = mc_open_ai_message($message, false, false, $conversation_id);
        if ($response && !mc_is_error($response) && $response[0]) {
            if (mc_get_multi_setting('open-ai', 'open-ai-mode') == 'assistant') {
                $response = mc_open_ai_assistant($message, $conversation_id, false);
                if (mc_is_error($response)) {
                    $response = $response->error;
                    return isset($response['response']) && isset($response['response']['error']) ? $response['response']['error']['message'] : $response['message'];
                }
            }
            if ($response && !mc_is_error($response) && $response[0]) {
                mc_open_ai_dummy_data('delete');
                if ($debug) {
                    return true;
                } else {
                    return mc_open_ai_troubleshoot(true);
                }
            }
        }
    }
    if ($conversation_id) {
        mc_open_ai_dummy_data('delete');
    }
    if (mc_is_error($response)) {
        if ($response->code() == 'no-credits') {
            return str_replace('{R}', '<a href="' . (defined('MC_CLOUD_DOCS') ? MC_CLOUD_DOCS : '') . '#cloud-credits" target="_blank" class="mc-link-text">' . mc_('here') . '</a>', mc_('Credits are required to use some features in automatic sync mode. If you don\'t want to buy credits, switch to manual sync mode and use your own API key. For more details click {R}.'));
        }
        $response = $response->response() ? $response->response() : ($response->message() ? $response->message() : $response->code());
        return $response;
    }
    return isset($response[1]) && isset($response[1]['error']) ? $response[1]['error']['message'] : $response;
}

function mc_open_ai_html_to_paragraphs($url) {
    error_reporting(E_ERROR | E_PARSE);
    libxml_use_internal_errors(true);
    $response = mc_curl($url, '', [], 'GET-SC');
    if ($response[1] == 200) {
        $paragraphs = [];
        $response = $response[0];
        $html_start = strpos($response, '<html');
        $html_end = strpos($response, '>', $html_start);
        $html_tag_content = substr($response, $html_start, $html_end - $html_start);
        $language = '';
        if (strpos($html_tag_content, 'lang=') !== false) {
            $lang_start = strpos($html_tag_content, 'lang=') + 6;
            $language = strtolower(substr($html_tag_content, $lang_start, strpos($html_tag_content, '"', $lang_start) - $lang_start));
        }
        $body_start = strpos($response, '<body');
        $body_content = substr($response, $body_start, strpos($response, '</body>') - $body_start);
        $body_content = str_replace('><', '> <', $body_content);
        $body_content = str_replace(['<br>', '<br />'], "\n", $body_content);
        $body_content = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/i', '', $body_content);
        $body_content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/i', '', $body_content);
        $duplicate_check_strings = [];
        if ($body_content) {
            preg_match_all('/<a\s[^>]*href=["\'](#.*?)["\']/i', $body_content, $matches);
            foreach ($matches[1] as $match) {
                $match = substr($match, 1);
                $pos = strpos($body_content, 'id="' . $match . '"');
                if (!$pos) {
                    $pos = strpos($body_content, 'id=\'' . $match . '\'');
                }
                if ($pos) {
                    $body_content = substr_replace($body_content, '<p>More details at ' . $url . '#' . $match . '.</p>', strpos($body_content, '>', $pos) + 1, 0);
                }
            }
            preg_match_all('/<a\b[^>]*>.*?<\/a>/is', $body_content, $matches);
            $matches = array_unique($matches[0]);
            foreach ($matches as $match) {
                $match_ = preg_replace('/<img\b[^>]*>/i', '', str_replace('</a>', '', preg_replace('/\s+/', ' ', str_replace(["\r\n", "\r", "\n"], '', $match))));
                $match_ = substr($match_, strpos($match_, '>') + 1);
                if (empty(trim($match_))) {
                    $body_content = str_replace($match, '', $body_content);
                }
            }
            $dom = new DOMDocument();
            @$dom->loadHTML('<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"></head>' . $body_content);
            $xpath = new DOMXPath($dom);
            $uls = $xpath->query('//ul');
            foreach ($uls as $ul) {
                try {
                    if (isset($ul->nodeValue)) {
                        $code = '';
                        $lis = $xpath->query('.//li', $ul);
                        $index = 0;
                        foreach ($lis as $li) {
                            $sub_uls = isset($li->nodeValue) ? $xpath->query('.//ul', $li) : [];
                            foreach ($sub_uls as $sub_ul) {
                                try {
                                    $li->removeChild($sub_ul);
                                } catch (Exception $e) {
                                }
                            }
                            $text = isset($li->textContent) ? trim($li->textContent) : '';
                            if (str_word_count($text) > 1 && strlen($text) > 10) {
                                $code .= '###B###' . ($index + 1) . '. ' . $text;
                            }
                            $index++;
                        }
                        $ul->nodeValue = '';
                        if ($code) {
                            $ul->appendChild($dom->createTextNode($code));
                        }
                    }
                } catch (Exception $e) {
                }
            }
            $ps = $xpath->query('//p');
            foreach ($ps as $p) {
                $p->nodeValue .= '###B###';
            }
            $spans_labels = $xpath->query('//span | //label');
            foreach ($spans_labels as $el) {
                try {
                    if (isset($el->nodeValue)) {
                        $el->nodeValue = ' ' . $el->nodeValue . ' ';
                    }
                } catch (Exception $e) {
                }
            }
            $as = $xpath->query('//a');
            foreach ($as as $a) {
                try {
                    $href = trim($a->getAttribute('href'));
                    if ($href && $href != '#' && strpos($href, 'javascript:') === false && strpos($href, 'data:') === false) {
                        if (strpos($href, 'http') !== 0 || strpos($href, 'www') !== 0) {
                            $base_url_parts = parse_url($url);
                            $base_protocol = isset($base_url_parts['scheme']) ? $base_url_parts['scheme'] . ':' : '';
                            $base_host = isset($base_url_parts['host']) ? '//' . $base_url_parts['host'] : '';
                            $base_path = isset($base_url_parts['path']) ? rtrim(dirname($base_url_parts['path']), '/') : '';
                            $continue = true;
                            if (substr($href, 0, 2) == "//") {
                                $href = $base_protocol . $href;
                                $continue = false;
                            }
                            if ($href[0] == '/') {
                                $href = $base_protocol . $base_host . $href;
                                $continue = false;
                            }
                            if (preg_match('/^\s*$/', $href)) {
                                $href = '';
                                $continue = false;
                            }
                            if ($continue) {
                                if (substr($href, 0, 2) == './') {
                                    $href = '.' . $href;
                                    $base_full_path = $base_protocol . $base_host . $base_path;
                                    $href = rtrim($base_full_path, '/') . '/' . ltrim($href, '/');
                                } else if (strpos($href, 'http') === false && strpos($href, 'www') === false && strpos($href, 'tel:') === false && strpos($href, 'sms:') === false && strpos($href, 'file:') === false && strpos($href, 'ftp:') === false && strpos($href, 'whatsapp:') === false && strpos($href, 'mailto:') === false) {
                                    $href = $base_protocol . $base_host . '/' . $href;
                                }
                            }
                            while (preg_match('/\/\.\.\//', $href)) {
                                $href = preg_replace('/[^\/]+\/+\.\.\//', '', $href);
                            }
                            $href = str_replace(['."', "/./", '"', "'", '<', '>'], ['', '/', '%22', '%27', '%3C', '%3E'], $href);
                        }
                        $href_text = trim($a->nodeValue);
                        $a->nodeValue = $href_text ? ' [' . $href_text . ': ' . $href . ']' : $href;
                    }
                } catch (Exception $e) {
                }
            }
            $h2s = $xpath->query('//h1/following-sibling::h2[1]');
            foreach ($h2s as $h2) {
                try {
                    $prev = $h2->previousSibling;
                    if ($prev && isset($prev->nodeValue) && $prev->nodeName === 'h1') {
                        $prev->nodeValue .= ' ' . $h2->textContent;
                        $h2->parentNode->removeChild($h2);
                    }
                } catch (Exception $e) {
                }
            }
            $headers = $xpath->query('//h1 | //h2');
            foreach ($headers as $header) {
                try {
                    if (isset($header->nodeValue)) {
                        $header->nodeValue = '###P###' . $header->nodeValue . ' \n';
                    }
                } catch (Exception $e) {
                }
            }
            $all_headers = $xpath->query('//h1 | //h2 | //h3 | //h4 | //h5 | //h6');
            foreach ($all_headers as $header) {
                try {
                    if (isset($header->nodeValue)) {
                        $header->nodeValue = '###P###' . $header->nodeValue;
                    }
                } catch (Exception $e) {
                }
            }
            $text_content = $dom->textContent;
            $list = explode('###P###', $text_content);
            $list_2 = [];
            foreach ($list as $text) {
                try {
                    $text = preg_replace('/\s\s+/', ' ', $text);
                    if (str_word_count($text) > 5 && strlen($text) > 20) {
                        $list_2[] = str_replace('###B###', ' \n ', $text);
                    }
                } catch (Exception $e) {
                }
            }
            $list = [];
            for ($i = 0; $i < count($list_2); $i++) {
                $text = trim($list_2[$i]);
                while (strlen($text) < 300 && count($list_2) > $i + 1) {
                    $text .= (mc_is_string_ends($text) ? '' : ' . ') . trim($list_2[$i + 1]);
                    $i++;
                }
                $text = str_replace([' .', '. \n.'], '.', $text);
                if (substr($text, 0, 10) == '----------') {
                    $text = substr($text, 80);
                }
                while (substr($text, 0, 4) == 'http') {
                    $text = substr($text, strpos($text, ' ') + 1);
                }
                $text = str_replace('. \n.', '.', $text);
                if (strlen($text) > 3500) {
                    $texts = explode('\n', $text);
                    $text = '';
                    foreach ($texts as $j => $t) {
                        if (strlen($text) + strlen($t) < 3500 || $j == count($texts) - 1) {
                            $text .= $t;
                        } else {
                            $temp = mc_open_ai_html_to_paragraphs_2($text, $language, $url);
                            array_push($paragraphs, $temp[0]);
                            $duplicate_check_strings = array_merge($duplicate_check_strings, $temp[1]);
                            $text = '';
                        }
                    }
                } else {
                    $temp = mc_open_ai_html_to_paragraphs_2($text, $language, $url);
                    array_push($paragraphs, $temp[0]);
                    $duplicate_check_strings = array_merge($duplicate_check_strings, $temp[1]);
                }
            }
        }

        // Check for duplicated content
        $embedding_sources = mc_get_external_setting('embedding-sources', []);
        $count = count($duplicate_check_strings);
        $count_paragraphs = count($paragraphs);
        foreach ($embedding_sources as $key => $value) {
            if (!mc_open_ai_is_file($key) && mc_isset(parse_url($url), 'host') == mc_isset(parse_url($key), 'host')) {
                for ($i = 0; $i < count($value); $i++) {
                    $text = implode('', array_column(mc_open_ai_embeddings_get_file($value[$i], true), 'text'));
                    for ($y = 0; $y < $count; $y++) {
                        $duplicate_check_string = trim($duplicate_check_strings[$y]);
                        if (strpos($text, $duplicate_check_string) !== false) {
                            for ($j = 0; $j < $count_paragraphs; $j++) {
                                $paragraphs[$j][0] = trim(str_replace($duplicate_check_string, '', $paragraphs[$j][0]));
                            }
                        }
                    }
                }
            }
        }
        return [$paragraphs, 200];
    }
    return $response;
}

function mc_open_ai_html_to_paragraphs_2($text, $language, $url) {
    if (substr($text, 0, 10) == '----------') {
        $text = substr($text, 80);
    }
    return [[preg_replace('!\s+!', ' ', str_replace(['\\n', '\n'], ' ', $text)), $language, $url], explode("\n", str_replace(['\\n', '\n'], "\n", $text))];
}

function mc_open_ai_trainig_server_side() {
    ignore_user_abort(true);
    set_time_limit(900);
    $embedding_keys = mc_open_ai_get_training_source_names();
    mc_open_ai_embeddings_delete('all-website');
    for ($i = 0; $i < count($embedding_keys); $i++) {
        $key = $embedding_keys[$i];
        if (mc_open_ai_is_file($key) || in_array($key, ['mc-conversations', 'mc-articles', 'mc-database'])) {
            continue;
        }
        $urls = strpos($key, '.xml') ? mc_get_sitemap_urls($key) : [$key];
        for ($j = 0; $j < count($urls); $j++) {
            $paragraphs = mc_isset(mc_open_ai_html_to_paragraphs($urls[$j]), 0);
            if (count($paragraphs)) {
                $response = mc_open_ai_embeddings_generate($paragraphs, $urls[$j]);
                if ($response[1] == 'chars-limit-exceeded') {
                    die($response[1]);
                }
            }
        }
    }
    mc_open_ai_articles_training();
    return true;
}

function mc_open_ai_get_training_source_names() {
    return array_keys(mc_get_external_setting('embedding-sources', []));
}

function mc_open_ai_playground_message($messages) {
    $GLOBALS['MC_OPEN_AI_PLAYGROUND'] = [];
    $response = false;
    $count = $messages ? count($messages) : false;
    if ($count) {
        mc_db_query('DELETE FROM mc_users WHERE first_name = "open-ai-temp-user"');
        $conversation_id = mc_open_ai_dummy_data($messages);
        $message = $messages[$count - 1][1];
        if ($count > 1 && strpos($messages[$count - 2][1], 'id="mc-human-takeover"') && $message == mc_dialogflow_get_human_takeover_settings()['confirm']) {
            $response = [true, mc_dialogflow_get_human_takeover_settings()['message_confirmation']];
        } else {
            $response = mc_open_ai_message($message, false, false, $conversation_id);
        }
        if (!mc_is_error($response)) {
            $response[1] = $response[0] ? preg_replace("/(\n){3,}/", "\n\n", str_replace(["\r", "\t"], "", is_string($response[1]) || empty($response[1][0]) ? $response[1] : $response[1][0]['message'])) : mc_isset(mc_isset($response[1], 'error'), 'message');
            mc_open_ai_dummy_data('delete');
            array_push($response, $conversation_id);
        }
    }
    return $response;
}

function mc_open_ai_dummy_data($messages) {
    mc_db_query('DELETE FROM mc_users WHERE first_name = "open-ai-temp-user"');
    if ($messages === 'delete') {
        return;
    }
    $user_id = mc_db_query('INSERT INTO mc_users(first_name, last_name, user_type, token, creation_time) VALUES ("open-ai-temp-user", "", "lead", "open-ai-temp-user", NOW())', true);
    $conversation_id = mc_db_query('INSERT INTO mc_conversations(user_id, title, creation_time) VALUES (' . $user_id . ', "open-ai-temp-conversation", NOW())', true);
    $query = '';
    for ($i = 0; $i < count($messages); $i++) {
        $query .= '(' . (mc_is_agent($messages[$i][0]) ? mc_get_bot_id() : $user_id) . ', ' . $conversation_id . ', "' . mc_db_escape($messages[$i][1]) . '", "", "' . mc_db_json_escape(mc_isset($messages[$i], 2, '')) . '", NOW()),';
    }
    if ($query) {
        mc_db_query('INSERT INTO mc_messages(user_id, conversation_id, message, attachments, payload, creation_time) VALUES ' . substr($query, 0, -1));
    }
    return $conversation_id;
}

function mc_open_ai_is_file($url) {
    $extension = strtolower(pathinfo($url, PATHINFO_EXTENSION));
    return in_array($extension, ['pdf', 'txt', 'csv', 'json']) ? $extension : false;
}

function mc_open_ai_execute_set_data($user_data) {
    if (mc_is_agent()) {
        return false;
    }
    $full_name = mc_isset($user_data, 'full_name');
    if ($full_name) {
        $full_name = mc_split_name($full_name);
        $user_data['first_name'] = $full_name[0];
        $user_data['last_name'] = $full_name[1];
    }
    return mc_update_user(mc_get_active_user_ID(), $user_data, $user_data, true, true);
}

function mc_open_ai_execute_actions($data, $conversation_id) {
    $client_side_payload = [];
    $attachments_response = [];
    for ($i = 0; $i < count($data); $i++) {
        $action = $data[$i];
        switch ($action[0]) {
            case 'tags':
                mc_tags_update($conversation_id, explode(strpos($action[1], '|') ? '|' : ',', $action[1]), true);
                break;
            case 'agent':
                mc_update_conversation_agent($conversation_id, $action[1]);
                break;
            case 'department':
                mc_update_conversation_department($conversation_id, $action[1]);
                break;
            case 'send_email_agents':
            case 'send_email':
            case 'transcript_email':
            case 'archive_conversation':
                $is_archive_conversation = $action[0] == 'archive_conversation';
                $is_transcript = $action[0] == 'transcript_email' || $is_archive_conversation;
                if ($is_archive_conversation) {
                    mc_update_conversation_status($conversation_id, 3);
                    if (mc_get_multi_setting('close-message', 'close-active')) {
                        mc_close_message($conversation_id, mc_get_bot_id());
                    }
                    $client_side_payload['event'] = 'conversation-status-update-3';
                }
                if ((!$is_archive_conversation || mc_get_multi_setting('close-message', 'close-transcript')) && mc_isset(mc_get_active_user(), 'email')) {
                    $attachments = [];
                    if ($is_transcript) {
                        $transcript = mc_transcript($conversation_id);
                        $attachments = [[$transcript, $transcript]];
                    }
                    $bot = mc_db_get('SELECT profile_image, first_name FROM mc_users WHERE user_type = "bot" LIMIT 1');
                    mc_email_create($action[0] == 'send_email_agents' ? 'agents' : mc_get_active_user_ID(), $bot['first_name'], $bot['profile_image'], $is_transcript ? mc_get_multi_setting('transcript', 'transcript-message', '') : str_replace('|', ',', $action[1]), $attachments, true, $conversation_id);
                }
                break;
            case 'transcript':
                $transcript = mc_transcript($conversation_id);
                array_push($attachments_response, [$transcript, $transcript]);
                break;
            case 'redirect':
                $client_side_payload['redirect'] = 'https://' . str_replace('https://', '', $action[1]);
                break;
            case 'open_article':
                $client_side_payload['open_article'] = $action[1];
                break;
            case 'human_takeover':
                mc_dialogflow_human_takeover($conversation_id);
                break;
        }
    }
    return ['client_side_payload' => $client_side_payload, 'attachments' => $attachments_response];
}

function mc_open_ai_send_fallback_message($conversation_id) {
    mc_send_message(mc_get_bot_id(), $conversation_id, mc_t(mc_get_multi_setting('open-ai', 'open-ai-fallback-message', 'Sorry, I didn\'t get that. Can you rephrase?'), mc_get_user_language(mc_get_active_user_ID())));
}

/*
 * -----------------------------------------------------------
 * FLOWS
 * -----------------------------------------------------------
 *
 * 1. Save the flows
 * 2. Return the flows
 * 3. Return the block message to send to the user
 * 4. Return a block by the string identifier
 * 5. Return the next block container of the given block
 * 6. Send the start message for flows that start on new converstations
 *
 */

function mc_flows_save($flows) {
    $flows = json_decode($flows, true);
    $previous_flows = mc_flows_get();
    $response = mc_save_external_setting('open-ai-flows', $flows);
    if ($response === true) {
        $paragraphs = [];
        $updated_flows = [];
        for ($i = 0; $i < count($flows); $i++) {
            $flow_name = 'flow-' . $flows[$i]['name'];
            $is_updated = true;

            // Check if the flow has been updated
            for ($j = 0; $j < count($previous_flows); $j++) {
                if ($previous_flows[$j] == $flows[$i]) {
                    $is_updated = false;
                    break;
                }
            }

            // Train the chatbot
            if ($is_updated) {
                $steps = $flows[$i]['steps'];
                $count = count($steps) - 1;
                for ($j = 0; $j < $count; $j++) {
                    $block_cnts = $steps[$j];
                    $index = 0;
                    for ($y = 0; $y < count($block_cnts); $y++) {
                        $blocks = $block_cnts[$y];
                        for ($x = 0; $x < count($blocks); $x++) {
                            $block = $blocks[$x];
                            if ($block['type'] == 'start' && $block['start'] == 'message' && !mc_isset($block, 'disabled')) {
                                $block_cnts_next_step = $steps[$j + 1];
                                $answer = '';
                                $answer_attachments = [];
                                $blocks_next = $block_cnts_next_step[$index];
                                for ($k = 0; $k < count($blocks_next); $k++) {
                                    $answer_block = mc_flows_get_block_code($blocks_next[$k], $flows[$i]['name'] . '_' . ($j + 1) . '_' . $index . '_' . $k, false) . ' ';
                                    $answer = mc_flows_merge_actions($answer, $answer_block);
                                    if (!empty($blocks_next[$k]['attachments'])) {
                                        $answer_attachments = array_merge($answer_attachments, $blocks_next[$k]['attachments']);
                                    }
                                }
                                if ($answer || !empty($answer_attachments)) {
                                    $extra = [];
                                    if (!empty($block['conditions'])) {
                                        $extra['conditions'] = $block['conditions'];
                                    }
                                    if (!empty($answer_attachments)) {
                                        $extra['attachments'] = $answer_attachments;
                                    }
                                    if (is_string($block['message'])) // Deprecated
                                        $block['message'] = [['message' => $block['message']]]; // Deprecated
                                    $extra = empty($extra) ? false : $extra;
                                    for ($k = 0; $k < count($block['message']); $k++) {
                                        array_push($paragraphs, [[$block['message'][$k]['message'], trim($answer)], false, $flow_name, $extra]);
                                    }
                                }
                                $index++;
                            }
                        }
                    }
                }
                array_push($updated_flows, $flow_name);
            }
        }

        // Delete the previous embeddings
        $embedding_sources_all = mc_get_external_setting('embedding-sources');
        $embedding_sources = mc_isset($embedding_sources_all, 'mc-flows', []);
        $embedding_sources_new = [];
        $flow_names = array_column($flows, 'name');
        for ($i = 0; $i < count($embedding_sources); $i++) {
            $embeddings = mc_open_ai_embeddings_get_file($embedding_sources[$i], true);
            $embeddings_new = [];
            for ($y = 0; $y < count($embeddings); $y++) {
                if (!in_array($embeddings[$y]['source'], $updated_flows) && in_array(substr($embeddings[$y]['source'], 5), $flow_names)) {
                    array_push($embeddings_new, $embeddings[$y]);
                }
            }
            $file_path = mc_open_ai_embeddings_get_file($embedding_sources[$i]);
            if (empty($embeddings_new)) {
                mc_file_delete($file_path);
            } else {
                array_push($embedding_sources_new, $embedding_sources[$i]);
                if ($embeddings_new != $embeddings) {
                    mc_file($file_path, json_encode($embeddings_new, JSON_UNESCAPED_UNICODE));
                }
            }
        }
        if ($embedding_sources_new != $embedding_sources) {
            $embedding_sources_all['mc-flows'] = $embedding_sources_new;
            mc_save_external_setting('embedding-sources', $embedding_sources_all);
        }

        // Return
        $response = mc_open_ai_embeddings_generate($paragraphs, 'mc-flows');
        return $response[0] ? true : $response;
    }
    return $response;
}

function mc_flows_get($flow_name = false) {
    $flows = mc_get_external_setting('open-ai-flows', []);
    if ($flow_name) {
        for ($i = 0; $i < count($flows); $i++) {
            if ($flows[$i]['name'] == $flow_name) {
                return $flows[$i];
            }
        }
        return false;
    }
    return $flows;
}

function mc_flows_get_block_code($block, $flow_identifier, $is_merge_fields = true) {
    switch ($block['type']) {
        case 'button_list':
            $options_text = '';
            for ($i = 0; $i < count($block['options']); $i++) {
                $options_text .= $block['options'][$i] . ',';
            }
            return '[chips id="flow_' . $flow_identifier . '" options="' . substr($options_text, 0, -1) . '" message="' . ($is_merge_fields ? mc_merge_fields($block['message']) : $block['message']) . '"]';
        case 'message':
            return $is_merge_fields ? mc_merge_fields($block['message']) : $block['message'];
        case 'video':
            preg_match('/(?:v=|\/)([a-zA-Z0-9_-]{11})/', $block['url'], $matches);
            return $matches ? ($is_merge_fields ? mc_merge_fields($block['message']) : $block['message']) . ' [video type="' . (strpos($block['url'], 'vimeo') ? 'vimeo' : 'youtube') . '" id="' . $matches[1] . '"]' : false;
        case 'get_user_details':
            return '[action flow-so="' . $flow_identifier . '"]';
        case 'action':
        case 'set_data':
            $string = '';
            $slugs = $block['type'] == 'action' ? ['actions', 'actions'] : ['data', 'set-data'];
            $items = $block[$slugs[0]];
            for ($i = 0; $i < count($items); $i++) {
                $string .= $items[$i][0] . (empty($items[$i][1]) ? '' : ':' . str_replace(',', '|', $items[$i][1])) . ',';
            }
            return '[action ' . $slugs[1] . '="' . substr($string, 0, -1) . '"]';
        case 'rest_api':
            return '[action rest-api="' . $flow_identifier . '"]';
        case 'condition':
            $flow_identifier = explode('_', $flow_identifier);
            $next_block_cnt = mc_flows_get_next_block_cnt($flow_identifier[0], $flow_identifier[1], $flow_identifier[2], mc_automations_validate($block['conditions'], true) ? 0 : 1);
            $response = '';
            if ($next_block_cnt) {
                for ($i = 0; $i < count($next_block_cnt[0]); $i++) {
                    $block_code = mc_flows_get_block_code($next_block_cnt[0][$i], $flow_identifier[0] . '_' . ($flow_identifier[1] + 1) . '_' . $next_block_cnt[1] . '_' . $i);
                    if (strpos($block_code, '[action ') !== false) {
                        $response = mc_flows_merge_actions($response, $block_code);
                    } else {
                        $response .= ' ' . $block_code;
                    }
                }
            }
            return $response;
    }
    return false;
}

function mc_flows_get_by_string($flow_identifier, $type = 'block') {
    $flow_identifier = explode('_', $flow_identifier);
    $flow = mc_flows_get($flow_identifier[0]);
    $response = false;
    if ($flow) {
        $flow = $flow['steps'][$flow_identifier[1]];
        switch ($type) {
            case 'block_cnts':
                $response = $flow;
                break;
            case 'blocks':
                $response = $flow[$flow_identifier[2]];
                break;
            case 'block':
                $response = $flow[$flow_identifier[2]][$flow_identifier[3]];
                break;
        }
        $response['index'] = $flow_identifier;
    }
    return $response;
}

function mc_flows_get_next_block_cnt($flow_name, $current_step_index, $current_block_cnt_index, $current_connector_index = 0) {
    $flow = mc_isset(mc_flows_get($flow_name), 'steps');
    if ($flow && isset($flow[$current_step_index + 1])) {
        $current_block_cnts = $flow[$current_step_index];
        $next_block_cnt_index = $current_connector_index;
        for ($i = 0; $i < $current_block_cnt_index; $i++) {
            $blocks = $current_block_cnts[$i];
            for ($j = 0; $j < count($blocks); $j++) {
                if ($blocks[$j]['type'] == 'button_list') {
                    $next_block_cnt_index += count($blocks[$j]['options']);
                } else if ($blocks[$j]['type'] == 'get_user_details') {
                    $next_block_cnt_index++;
                } else if ($blocks[$j]['type'] == 'condition') {
                    $next_block_cnt_index += 2;
                }
            }
        }
        return [$flow[$current_step_index + 1][$next_block_cnt_index], $next_block_cnt_index];
    }
    return false;
}

function mc_flows_on_conversation_start_or_load($messages, $language, $conversation_id, $is_on_load = false) {
    $flows = mc_flows_get();
    $response = false;
    for ($i = 0; $i < count($flows); $i++) {
        $flow = $flows[$i];
        if ($flow) {
            $start_step = $flow['steps'][0][0][0];
            if ($start_step['start'] == ($is_on_load ? 'load' : 'conversation') && !$start_step['disabled'] && mc_automations_validate($start_step['conditions'], true)) {
                $next_block_cnt = mc_isset($flow['steps'][1], 0, []);
                $code = '';
                if (!empty($next_block_cnt)) {
                    $flow_id = $flow['name'] . '_1_0_';
                    for ($j = 0; $j < count($next_block_cnt); $j++) {
                        $code = mc_flows_merge_actions($code, mc_flows_get_block_code($next_block_cnt[$j], $flow_id . $j));
                    }
                }
                if ($is_on_load) {
                    return $code;
                }
                $action = mc_flows_execute($code, $messages, $language, $conversation_id);
                mc_send_message(mc_get_bot_id(), $conversation_id, $action[0] ? $action[0] : $code, $action[2]);
                $response = $action[0] ? $action[0] : $code;
            }
        }
    }
    return $response;
}

function mc_flows_execute($message, $messages, $language, $conversation_id) {
    global $MC_OPEN_AI_PLAYGROUND;
    $action = mc_get_shortcode($message, 'action');
    $count = count($messages);
    $response = false;
    $attachments_response = [];
    $client_side_payload = [];
    if ($action) {
        $response = str_replace($action['shortcode'], '', $message);
        $flow_shortcode = mc_isset($action, 'flow-so');
        if ($flow_shortcode) {
            $user_message_payload = $count ? json_decode(mc_isset($messages[$count - 1], 'payload', '{}'), true) : [];
            $user_message_payload['flow_so'] = $flow_shortcode;
            $block = mc_flows_get_by_string($flow_shortcode);
            if ($count) {
                mc_db_query('UPDATE mc_messages SET payload = "' . mc_db_json_escape($user_message_payload) . '" WHERE id = ' . $messages[$count - 1]['id']);
            }
            if ($MC_OPEN_AI_PLAYGROUND !== null) {
                $MC_OPEN_AI_PLAYGROUND['payload'] = $user_message_payload;
            }
            $response = mc_merge_fields(mc_t($block['message'], $language ? (is_string($language) ? $language : $language[0]) : false));
        }
        if (isset($action['set-data'])) {
            $data = explode(',', $action['set-data']);
            $user_data = [];
            for ($i = 0; $i < count($data); $i++) {
                $data_item = explode(':', $data[$i]);
                $user_data[$data_item[0]] = $data_item[1];
            }
            mc_open_ai_execute_set_data($user_data);
        }
        if (isset($action['actions'])) {
            $data = explode(',', $action['actions']);
            $execute_actions = mc_open_ai_execute_actions(array_map(function ($item) {
                return explode(':', $item);
            }, $data), $conversation_id);
            $client_side_payload = $execute_actions['client_side_payload'];
            $attachments_response = $execute_actions['attachments'];
        }
        if (isset($action['rest-api'])) {
            $block = mc_flows_get_by_string($action['rest-api']);
            if ($block) {
                $headers = array_map(function ($items) {
                    return implode(':', $items);
                }, $block['headers']);
                $body = json_decode(mc_isset($block, 'body', '{}'), true);
                $body['mc'] = ['user' => mc_get_active_user(), 'user_language' => mc_get_user_language(mc_get_active_user_ID())];
                $body = json_encode($body, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                $response_call = mc_curl($block['url'], $body, $headers, $block['method'], false, ['Content-Type: application/json', 'Content-Length: ' . strlen($body)]);
                $response_rest_api = is_array($response_call) ? $response_call : json_decode($response_call, true);
                $save_response = mc_isset($block, 'save_response');
                if ($save_response) {
                    $user_data = [];
                    for ($i = 0; $i < count($save_response); $i++) {
                        $keys = explode('.', $save_response[$i][1]);
                        $response_rest_api_now = $response_rest_api;
                        foreach ($keys as $key) {
                            if (isset($response_rest_api_now[$key])) {
                                $response_rest_api_now = $response_rest_api_now[$key];
                            } else {
                                $response_rest_api_now = false;
                                break;
                            }
                        }
                        if ($response_rest_api_now) {
                            $user_data[$save_response[$i][0]] = $response_rest_api_now;
                        }
                        if (!empty($user_data)) {
                            mc_update_user(mc_get_active_user_ID(), $user_data, $user_data, true, true);
                        }
                    }
                }
            }
        }
    }
    return [$response, $client_side_payload, $attachments_response, $action];
}

function mc_flows_merge_actions($actions_string, $action_string) {
    if (strpos($actions_string, '[action ') === false || strpos($action_string, '[action ') === false) {
        return $actions_string . ' ' . $action_string;
    }
    return str_replace('[action', '[action ' . str_replace(['[action', ']'], '', $action_string), $actions_string);
}

function mc_flows_get_open_ai_message_response($flow_name, $current_step_index, $current_block_cnt_index, $current_connector_index, $payload) {
    $next_block_cnt = mc_flows_get_next_block_cnt($flow_name, $current_step_index, $current_block_cnt_index, $current_connector_index);
    $response = false;
    if ($next_block_cnt) {
        $response = '';
        for ($i = 0; $i < count($next_block_cnt[0]); $i++) {
            $next_flow_name = $flow_name . '_' . ($current_step_index + 1) . '_' . $next_block_cnt[1] . '_' . $i;
            $block_code = mc_flows_get_block_code($next_block_cnt[0][$i], $next_flow_name);
            if (strpos($block_code, '[action ') !== false) {
                if ($next_block_cnt[0][$i]['type'] == 'get_user_details') {
                    $payload['flow_so'] = $next_flow_name;
                }
                $response = mc_flows_merge_actions($response, $block_code);
            } else {
                $response .= ' ' . $block_code;
            }
        }
    }
    $attachments = mc_isset($next_block_cnt[0][0], 'attachments');
    if ($attachments) {
        for ($i = 0; $i < count($attachments); $i++) {
            $attachments[$i] = [basename($attachments[$i]), $attachments[$i]];
        }
        $payload['attachments'] = $attachments;
    }
    return [$response, $payload];
}

function mc_open_ai_get_max_tokens($model) {
    $max_tokens_list = ['gpt-3.5-turbo' => 16385, 'gpt-3.5-turbo-instruct' => 4096, 'gpt-4' => 8192, 'gpt-4-turbo' => 128000, 'gpt-4o' => 128000, 'gpt-4o-mini' => 128000, 'o1' => 200000, 'o1-mini' => 128000, 'o3-mini' => 200000, 'o4-mini' => 200000, 'gpt-4.1-nano' => 1047576, 'gpt-4.1-mini' => 1047576, 'gpt-4.1' => 1047576];
    $open_ai_max_tokens = mc_isset($max_tokens_list, $model);
    if (!$open_ai_max_tokens) {
        foreach ($max_tokens_list as $key => $value) {
            if (strpos($model, $key) !== false) {
                return $value;
            }
        }
        return 99999;
    }
    return $open_ai_max_tokens;
}

function mc_flows_run_on_load($message, $conversation_id, $language = false) {
    $is_action = strpos($message, '[action') !== false;
    $shortcode = mc_get_shortcode($message, $is_action ? 'action' : false);
    $language = $language ? (is_array($language) ? $language[0] : $language) : false;
    if (!empty($shortcode)) {
        if ($is_action) {
            $flow_shortcode = mc_isset($shortcode, 'flow-so');
            if ($flow_shortcode) {
                $block = mc_flows_get_by_string($flow_shortcode);
                return mc_send_message(mc_get_bot_id(), $conversation_id, mc_merge_fields(mc_t($block['message'], $language)), [], 3, ['flow_so' => $flow_shortcode]);
            } else {
                mc_flows_execute($message, [], $language, $conversation_id);
            }
        } else {
            $shortcode = $shortcode[0];
        }
        return mc_send_message(mc_get_bot_id(), $conversation_id, mc_merge_fields(mc_t($message, $language)), [], 3);
    }
    return false;
}

/*
 * -----------------------------------------------------------
 * GOOGLE
 * -----------------------------------------------------------
 *
 * 1. Detect the language of a string
 * 2. Retrieve the full language name in the desired language
 * 3. Text translation
 * 4. Analyze Entities
 * 5. Return the client ID and secret key
 * 6. Return the message in the desired language
 * 7. Google troubleshooting
 *
 */

function mc_google_language_detection($string, $token = false) {
    $token = $token ? $token : mc_dialogflow_get_token(mc_defined('GOOGLE_REFRESH_TOKEN'));
    $query = json_encode(['q' => $string], JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);
    if (!mc_cloud_membership_has_credits('google')) {
        $response = mc_error('no-credits', 'mc_google_get_language_name');
        return mc_is_debug() ? $response : false;
    }
    $response = mc_curl('https://translation.googleapis.com/language/translate/v2/detect', $query, ['Content-Type: application/json', 'Authorization: Bearer ' . $token, 'Content-Length: ' . strlen($query)]);
    mc_cloud_membership_use_credits('translation', 'google', $string);
    if (isset($response['error']) && $response['error']['status'] == 'UNAUTHENTICATED') {
        global $mc_recursion_dialogflow;
        if ($mc_recursion_dialogflow[0]) {
            $mc_recursion_dialogflow[0] = false;
            $token = mc_dialogflow_get_token(mc_defined('GOOGLE_REFRESH_TOKEN'));
            return mc_google_language_detection($string, $token);
        }
    }
    return isset($response['data']) && !empty($response['data']['detections']) ? mc_language_code($response['data']['detections'][0][0]['language']) : false;
}

function mc_google_get_language_name($target_language_code, $token = false) {
    $token = $token ? $token : mc_dialogflow_get_token(mc_defined('GOOGLE_REFRESH_TOKEN'));
    $query = json_encode(['target' => $target_language_code], JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);
    $response = mc_curl('https://translation.googleapis.com/language/translate/v2/languages', $query, ['Content-Type: application/json', 'Authorization: Bearer ' . $token, 'Content-Length: ' . strlen($query)]);
    if (isset($response['data'])) {
        $languages = $response['data']['languages'];
        for ($i = 0; $i < count($languages); $i++) {
            if ($languages[$i]['language'] == $target_language_code) {
                return $languages[$i]['name'];
            }
        }
    }
    return $response;
}

function mc_google_translate($strings, $language_code, $token = false, $message_ids = false, $conversation_id = false) {
    if (empty($language_code)) {
        return [$strings, $token];
    }
    $translations = [];
    $token = $token ? $token : mc_dialogflow_get_token(mc_defined('GOOGLE_REFRESH_TOKEN'));
    $chunks = array_chunk($strings, 125);
    $language_code = strtolower(substr(mc_dialogflow_language_code($language_code), 0, 2));
    $language_code = mc_isset(['br' => 'pt'], $language_code, $language_code);
    $shortcode_replacements = [
        ['[chips ', '[buttons ', '[button ', '[select ', '[email ', '[articles ', '[rating ', '[list ', '[list-image ', '[table ', '[inputs ', '[card ', '[slider ', '[slider-images ', '[video ', '[image ', '[share ', '[registration]', '[timetable]', '[email]', '[articles]', ' options="', ' title="', ' message="', ' success="', ' placeholder="', ' name="', ' phone="', ' phone-required="', ' link="', ' label="', '  label-positive="', ' label-negative="', ' success-negative="', ' values="', ' header="', ' button="', ' image="', ' target="', ' extra="', ' link-text="', ' type="', ' height="', ' id="', ' url="', ' numeric="true', ']', ',', ':'],
        ['[1 ', '[2 ', '[3 ', '[4 ', '[5 ', '[7 ', '[9 ', '[10 ', '[11 ', '[12 ', '[13 ', '[14 ', '[15 ', '[16 ', '[17', '[18', '[19', '[20', '[21', ' 22="', ' 23="', ' 24="', ' 25="', ' 26="', ' 27="', ' 28="', ' 29="', ' 30="', ' 31="', ' 32="', ' 33="', ' 34="', ' 35="', ' 36="', ' 37="', ' 38="', ' 39="', ' 40="', ' 41="', ' 42="', ' 43="', ' 44="', ' 45="', ' 46=', ' 47=', ' 48=', ' 49=', '{R}', '{T}']
    ];
    $skipped_translations = [];
    $strings_original = $strings;
    if (!mc_cloud_membership_has_credits('google')) {
        return mc_error('no-credits', 'mc_dialogflow_message');
    }
    for ($j = 0; $j < count($chunks); $j++) {
        $strings = $chunks[$j];
        for ($i = 0; $i < count($strings); $i++) {
            $string = $strings[$i];
            if (strpos($string, '[') !== false || strpos($string, '="') !== false) {
                $string = str_replace($shortcode_replacements[0], $shortcode_replacements[1], $string);
                $string = str_replace('="true"', '="1"', $string);
            }
            preg_match_all('/`[\S\s]*?`/', $string, $matches);
            $matches = $matches[0];
            array_push($skipped_translations, $matches);
            for ($y = 0; $y < count($matches); $y++) {
                if ($matches[$y] != '``') {
                    $string = str_replace($matches[$y], '"' . $y . '"', $string);
                }
            }
            $strings[$i] = str_replace('"', '«»', str_replace(['\r\n', PHP_EOL, '\r', '\n'], '~~', $string));
        }
        $query = json_encode(['q' => $strings, 'target' => $language_code, 'format' => 'text'], JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);
        $response = mc_curl('https://translation.googleapis.com/language/translate/v2', $query, ['Content-Type: application/json', 'Authorization: Bearer ' . $token, 'Content-Length: ' . strlen($query)]);
        if ($response && isset($response['data'])) {
            mc_cloud_membership_use_credits('translation', 'google', json_encode($strings, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE));
            $translations_partial = mc_isset($response['data'], 'translations', []);
            for ($i = 0; $i < count($translations_partial); $i++) {
                $string = $translations_partial[$i]['translatedText'];
                while (mb_substr($string, 0, 1) == '"') {
                    $string = mb_substr($string, 1);
                }
                $string = str_replace([PHP_EOL, '\r\n', '\r', '<br>', '~~', '”', '«»', '« »', '»»', '««', '_}', '“', '""'], ["\n", "\n", "\n", "\n", "\n", '"', '"', '"', '"', '"', '}', '', '"'], $string);
                for ($y = 0; $y < count($skipped_translations[$i]); $y++) {
                    $string = str_replace('"' . $y . '"', $skipped_translations[$i][$y], $string);
                }
                $string = str_replace($shortcode_replacements[1], $shortcode_replacements[0], str_replace(['44 =', '{R}'], ['44=', '{RR}'], $string));
                $shortcodes = mc_get_shortcode($string);
                foreach ($shortcodes as $shortcode) {
                    if ($shortcode && $shortcode['shortcode_name'] == 'list') {
                        $string = str_replace($shortcode['values'], str_replace([':', ','], ['\:', '\,'], str_replace(['\\:', '\\,'], [':', ','], $shortcode['values'])), $string);
                    }
                }
                $string = str_replace('{RR}', ',', $string);
                $string = str_replace('="1"', '="true"', $string);
                $string = str_replace(['{R}', '{T}', '\,\,', ',,'], [',', ':', '\,', ','], $string);
                array_push($translations, $string);
            }
        } else {
            $error = mc_isset($response, 'error');
            if ($error) {
                if (mc_isset($error, 'status') == 'UNAUTHENTICATED') {
                    global $mc_recursion_dialogflow;
                    if ($mc_recursion_dialogflow[0]) {
                        $mc_recursion_dialogflow[0] = false;
                        $token = mc_dialogflow_get_token(mc_defined('GOOGLE_REFRESH_TOKEN'));
                        return mc_google_translate($strings_original, $language_code, $token);
                    }
                }
                mc_error('error', 'mc_google_translate', $error, mc_is_agent());
                return [$strings_original, $token];
            }
        }
    }
    $count = count($translations);
    if ($count && $message_ids && $conversation_id && $count == count($message_ids)) {
        $data = mc_db_get('SELECT id, payload FROM mc_messages WHERE id IN (' . mc_db_escape(implode(',', $message_ids)) . ') AND conversation_id = ' . mc_db_escape($conversation_id, true), false);
        for ($i = 0; $i < $count; $i++) {
            if (strlen($string) > 1) {
                $payload = json_decode($data[$i]['payload'], true);
                $payload['translation'] = $translations[$i];
                $payload['translation-language'] = $language_code;
                mc_db_query('UPDATE mc_messages SET payload = "' . mc_db_json_escape($payload) . '" WHERE id = ' . $data[$i]['id']);
            }
        }
    }
    return [$count ? $translations : $response, $token];
}

function mc_google_translate_auto($string, $user_id) {
    if (is_numeric($user_id) && (mc_get_setting('google-translation') || mc_get_multi_setting('google', 'google-translation'))) { // Deprecated: mc_get_setting('google-translation')
        $recipient_language = mc_get_user_language($user_id);
        $active_user_language = mc_get_user_language(mc_get_active_user_ID());
        if ($recipient_language && $active_user_language && $recipient_language != $active_user_language) {
            $translation = mc_google_translate([$string], $recipient_language)[0];
            if (count($translation)) {
                $translation = trim($translation[0]);
                if (!empty($translation)) {
                    return $translation;
                }
            }
        }
    }
    return $string;
}

function mc_google_translate_article($article_id, $language_code) {
    $article = mc_get_articles($article_id, false, true);
    if (count($article)) {
        $article = $article[0];
        $editos_js = json_decode($article['editor_js'], true);
        $blocks = mc_isset($editos_js, 'blocks', []);
        $strings = [$article['title']];
        foreach ($blocks as $block) {
            switch ($block['type']) {
                case 'header':
                case 'paragraph':
                    array_push($strings, html_entity_decode($block['data']['text']));
                    break;
                case 'list':
                    foreach ($block['data']['items'] as $item) {
                        array_push($strings, html_entity_decode($item));
                    }
                    break;
            }
        }
        $strings_translated = mc_google_translate($strings, $language_code);
        if (mc_is_error($strings_translated)) {
            return $article;
        }
        $index = 1;
        for ($i = 0; $i < count($blocks); $i++) {
            switch ($blocks[$i]['type']) {
                case 'header':
                case 'paragraph':
                    $blocks[$i]['data']['text'] = $strings_translated[0][$index];
                    $index++;
                    break;
                case 'list':
                    $items = [];
                    foreach ($blocks[$i]['data']['items'] as $item) {
                        array_push($items, $strings_translated[0][$index]);
                        $index++;
                    }
                    $blocks[$i]['data']['items'] = $items;
            }
        }
        for ($i = 0; $i < count($strings); $i++) {
            $article['content'] = str_replace(htmlentities($strings[$i]), htmlentities($strings_translated[0][$i]), $article['content']);
        }
        $editos_js['blocks'] = $blocks;
        $article['title'] = $strings_translated[0][0];
        $article['language'] = $language_code;
        $article['parent_id'] = $article_id;
        $article['editor_js'] = $editos_js;
        unset($article['id']);
        return $article;
    }
    return false;
}

function mc_google_language_detection_update_user($string, $user_id = false, $token = false) {
    $user_id = $user_id ? $user_id : mc_get_active_user_ID();
    $detected_language = mc_google_language_detection($string, $token);
    $language = mc_get_user_language($user_id);
    if ($detected_language != $language[0] && !empty($detected_language)) {
        $response = mc_language_detection_db($user_id, $detected_language);
        if ($response) {
            unset($GLOBALS['MC_LANGUAGE']);
            return mc_get_current_translations();
        }
    }
    return false;
}

function mc_language_detection_db($user_id, $detected_language) {
    if ($detected_language == 'un') {
        return false;
    }
    $response = mc_update_user_value($user_id, 'language', $detected_language);
    mc_db_query('DELETE FROM mc_users_data WHERE user_id = ' . mc_db_escape($user_id) . ' AND slug = "browser_language"');
    return $response;
}

function mc_google_language_detection_get_user_extra($message) {
    if ($message && (mc_get_multi_setting('google', 'google-language-detection') || mc_get_multi_setting('dialogflow-language-detection', 'dialogflow-language-detection-active'))) { // Deprecated: mc_get_multi_setting('dialogflow-language-detection', 'dialogflow-language-detection-active')
        return [mc_google_language_detection($message), 'Language'];
    }
    return '';
}

function mc_google_analyze_entities($string, $language = false, $token = false) {
    if (!strpos(trim($string), ' ')) {
        return false;
    }
    $token = $token ? $token : mc_dialogflow_get_token();
    $query = ['document' => ['type' => 'PLAIN_TEXT', 'content' => ucwords($string)]];
    if ($language) {
        $query['document']['language'] = $language;
    }
    $query = json_encode($query, JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_IGNORE);
    $response = mc_curl('https://language.googleapis.com/v1/documents:analyzeEntities', $query, ['Content-Type: application/json', 'Authorization: Bearer ' . $token, 'Content-Length: ' . strlen($query)]);
    if (isset($response['error'])) {
        trigger_error($response['error']['message']);
    }
    return $response;
}

function mc_google_key() {
    return mc_ai_is_manual_sync('google') ? [trim(mc_get_multi_setting('google', 'google-client-id')), trim(mc_get_multi_setting('google', 'google-client-secret'))] : [GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET];
}

function mc_google_get_message_translation($message, $language = false) {
    $payload = json_decode(mc_isset($message, 'payload'), true);
    $translation = mc_isset($payload, 'original-message');
    if ($translation && (!$language || $language == mc_isset($payload, 'original-message-language'))) {
        $message['message'] = $translation;
    } else {
        $translation = mc_isset($payload, 'translation');
        if ($translation && (!$language || $language == mc_isset($payload, 'translation-language'))) {
            $message['message'] = $translation;
        }
    }
    return $message;
}

function mc_google_troubleshoot($debug = false) {
    if ($debug) {
        $_GET['debug'] = true;
    }
    if (mc_chatbot_active(true, false) || mc_get_setting('ai-smart-reply')) {
        $response = mc_dialogflow_message(false, 'Hello world');
        if (mc_is_error($response)) {
            return $response;
        }
        if ($response && isset($response['response']) && isset($response['response']['error'])) {
            return $response['response']['error']['message'];
        }
    }
    if (mc_get_multi_setting('google', 'google-multilingual-translation') || mc_get_multi_setting('google', 'google-translation') || mc_get_multi_setting('google', 'google-language-detection')) {
        $query = json_encode(['q' => ['hello world'], 'target' => 'it', 'format' => 'text']);
        $token = mc_dialogflow_get_token();
        if (mc_is_error($token)) {
            return $token;
        }
        $response = mc_curl('https://translation.googleapis.com/language/translate/v2', $query, ['Content-Type: application/json', 'Authorization: Bearer ' . $token, 'Content-Length: ' . strlen($query)]);
        if ($response && isset($response['error'])) {
            return $response['error']['message'];
        }
    }
    if ($debug) {
        return true;
    } else {
        return mc_google_troubleshoot(true);
    }
}