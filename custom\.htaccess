<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    
    # Additional CORS headers for font files
    <FilesMatch "\.(woff|woff2|ttf|otf)$">
        Header set Access-Control-Allow-Methods "GET, HEAD, OPTIONS"
        Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
        Header set Access-Control-Max-Age "86400"
    </FilesMatch>
</IfModule>



<IfModule mod_rewrite.c>
  ## URL REWRITE ##
  RewriteEngine On
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule ^(.+?)/$ /$1 [R=301,NE,L]
  RewriteCond %{REQUEST_FILENAME}\.html -f
  RewriteRule ^(.)$ $1.html [NC,L]
  RewriteCond %{REQUEST_FILENAME}\.php -f
  RewriteRule ^(.)$ $1.php [NC,L]
  RewriteRule ^/?$ script/admin.php [L]
RewriteCond %{HTTPS} !=on
RewriteRule ^(.)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>