<?php

// Removed: use Componere\Value; // Not used

require_once('functions.php');

// --- START: Paystack Return Processing --- [UPDATED FOR NEW RESPONSE FORMAT]
// Perform this check early, before significant output or account data fetching for display
// Check for paystack ref and avoid re-processing loop caused by adding psk_processed=true
if (isset($_GET['payment']) && $_GET['payment'] == 'success' && isset($_GET['trxref']) && !isset($_GET['psk_processed'])) {

    // Load MC environment if needed
    mc_cloud_load();
    $current_account = account(); // Check if user is logged in

    if ($current_account && isset($current_account['user_id'])) {
        $reference = preg_replace('/[^a-zA-Z0-9_]/', '', $_GET['trxref']); // Allow letters, numbers, underscore
        $cloud_user_id = $current_account['user_id']; // Get logged-in user ID

        mc_cloud_debug('Paystack return detected: Ref=' . $reference . ', User=' . $cloud_user_id);

        // Verify the transaction with Paystack
        $verification_result = paystack_verify_transaction($reference);

        // Check if verification was successful
        if (isset($verification_result['status']) && $verification_result['status'] === true) {
            // Paystack confirmed successful payment via verification API

            mc_cloud_debug('Paystack verification successful (return URL) for reference: ' . $reference);

            // --- Process based on metadata BUT DO NOT UPDATE CREDITS/MEMBERSHIP/HISTORY HERE ---
            // --- ONLY SAVE AUXILIARY DATA like auth codes / sub codes ---
            $parsed_ref = $verification_result['parsed_ref'] ?? null;
            $transaction_data = $verification_result['data'] ?? null;
            $redirect_status = 'success_pending_webhook'; // Assume success, actual update via webhook
            $redirect_error_code = '';

            if ($parsed_ref && $transaction_data && isset($parsed_ref['user_id']) && $parsed_ref['user_id'] == $cloud_user_id) {
                 $amount_paid = ($transaction_data['amount'] ?? 0) / 100;
                 $currency = $transaction_data['currency'] ?? PAYSTACK_CURRENCY; // Keep PAYSTACK_CURRENCY fallback here if needed for this specific context
                 $purchase_type = $parsed_ref['type'];

                // --- SAVE Auxiliary Data ONLY ---
                if ($purchase_type == 'credits' && $amount_paid > 0) {
                    // Save Paystack Authorization Code if available and reusable
                    $authorization = $transaction_data['authorization'] ?? null;
                    if ($authorization && isset($authorization['authorization_code']) && !empty($authorization['authorization_code']) && $authorization['reusable'] == true) {
                         $auth_code = $authorization['authorization_code'];
                         mc_cloud_debug('Paystack return URL: Saving reusable authorization code ' . $auth_code . ' for user ' . $cloud_user_id);
                         // Save logic (assuming super_... functions handle potential duplicates correctly or delete first)
                         super_delete_user_data($cloud_user_id, 'paystack_authorization_code', true);
                         super_insert_user_data('(' . $cloud_user_id . ', "paystack_authorization_code", "' . db_escape($auth_code) . '")');
                         super_delete_user_data($cloud_user_id, 'paystack_auth_details', true);
                         super_insert_user_data('(' . $cloud_user_id . ', "paystack_auth_details", "' . db_escape(json_encode($authorization)) . '")');
                    } else {
                        mc_cloud_debug('Paystack return URL: Authorization code not found or not reusable in verification data for credits purchase.');
                    }
                    // Redirect status already set to 'success_pending_webhook'

                } elseif ($purchase_type == 'white_label' && $amount_paid > 0) {
                     // No specific aux data to save here usually, just confirm verification
                     mc_cloud_debug('Paystack return URL: Verified white_label purchase. Update pending webhook.');
                     // Redirect status already set to 'success_pending_webhook'

                } elseif ($purchase_type == 'subscription' && !empty($parsed_ref['plan_id']) && !empty($parsed_ref['period'])) {
                     // Save Subscription Code and Email Token if available from verification
                    $paystack_subscription_code = $transaction_data['subscription']['subscription_code'] ?? null;
                    $email_token = $transaction_data['subscription']['email_token'] ?? null;

                    if ($paystack_subscription_code) {
                         mc_cloud_debug('Paystack return URL: Saving subscription code ' . $paystack_subscription_code . ' for user ' . $cloud_user_id);
                         // Save logic
                         super_delete_user_data($cloud_user_id, 'paystack_subscription_code', true);
                         super_insert_user_data('(' . $cloud_user_id . ', "paystack_subscription_code", "' . db_escape($paystack_subscription_code) . '")');
                    }
                    if ($email_token) {
                         mc_cloud_debug('Paystack return URL: Saving email token ' . $email_token . ' for user ' . $cloud_user_id);
                         // Save logic
                         super_delete_user_data($cloud_user_id, 'paystack_email_token', true);
                         super_insert_user_data('(' . $cloud_user_id . ', "paystack_email_token", "' . db_escape($email_token) . '")');
                    }
                     mc_cloud_debug('Paystack return URL: Verified subscription purchase. Update pending webhook.');
                    // Redirect status already set to 'success_pending_webhook'

                } else {
                     mc_cloud_debug('Paystack return URL: Unknown or invalid purchase type in parsed reference.', ['parsed_ref' => $parsed_ref]);
                     $redirect_status = 'error'; // Change status if invalid type found
                     $redirect_error_code = 'invalid_purchase_type_return';
                }
                // --- REMOVED all calls to membership_set_purchased_credits, membership_update, cloud_add_to_payment_history etc. ---

            } else {
                 mc_cloud_debug('Paystack return URL: Verification successful but parsed data invalid or user mismatch.', ['parsed_ref' => $parsed_ref, 'tx_data' => $transaction_data, 'logged_in_user' => $cloud_user_id]);
                 $redirect_status = 'error'; // Change status if data mismatch
                 $redirect_error_code = 'data_mismatch_return';
            }

            // --- Redirect Based ONLY on Verification Status ---
            // Add payment_type=credits parameter if this is a credits purchase
            $payment_type_param = ($purchase_type == 'credits') ? '&payment_type=credits' : '';
            $redirect_url = CLOUD_URL . '/account/?tab=membership&payment_status=' . $redirect_status . ($redirect_error_code ? '&psk_error=' . $redirect_error_code : '') . $payment_type_param . '&psk_processed=true';
            header('Location: ' . $redirect_url);
            exit();

        } else {
             // Paystack verification failed
             mc_cloud_debug('Paystack verification failed (return URL) for reference: ' . $reference, ['result' => $verification_result]);
             $error_message = urlencode($verification_result['message'] ?? 'Verification failed');
             $redirect_url = CLOUD_URL . '/account/?tab=membership&payment_status=failed&psk_error=' . $error_message . '&psk_processed=true';
             header('Location: ' . $redirect_url);
             exit();
        }
    } else if ($current_account && isset($_GET['trxref'])) {
         // User is logged in but reference found without ?payment=success, or loop detected
         // Redirect to clean URL if psk_processed isn't set (avoids infinite loop if something goes wrong)
         if (!isset($_GET['psk_processed'])) {
             mc_cloud_debug('Paystack reference found without success flag or loop detected, cleaning URL.');
             $redirect_url = CLOUD_URL . '/account/?tab=membership';
             // Optionally preserve other relevant GET params if needed, excluding Paystack ones
             header('Location: ' . $redirect_url);
             exit();
         }
         // If psk_processed is already set, do nothing, let the page load normally.
    } else if (!$current_account && isset($_GET['trxref'])) {
         // User arrived at return URL but is not logged in - potentially problematic scenario
         mc_cloud_debug('Paystack return detected, but user is not logged in. Cannot verify or process reference: ' . ($_GET['trxref'] ?? 'N/A'));
         // Redirect to login or a generic error page? For now, redirect to clean membership tab.
         $redirect_url = CLOUD_URL . '/account/?tab=membership&payment_status=error&psk_error=user_not_logged_in&psk_processed=true';
         header('Location: ' . $redirect_url);
         exit();
    }
} // --- END: Paystack Return Processing ---

// Global variable declaration moved below functions.php require
global $cloud_settings;
mc_cloud_load();
$account = account();
$cloud_settings = super_get_settings();
$rtl = mc_get_setting('rtl-admin') || defined('MC_CLOUD_DEFAULT_RTL');
$custom_code = db_get('SELECT value FROM settings WHERE name = "custom-code-admin"');
if (!function_exists('sup' . 'er_adm' . 'in_con' . 'fig')) {
    die();
}
if (isset($_GET['login_email'])) {
    $account = false;
}

// [MERGED] Stripe and Paystack required_action checks
if (mc_isset($_GET, 'payment_type') == 'credits') {
    if (PAYMENT_PROVIDER == 'stripe') {
        $required_action = super_get_user_data('stripe_next_action', $account['user_id']);
        if ($required_action) {
            $required_action = explode('|', $required_action);
            if ($required_action[0] > (time() - 86400)) {
                super_delete_user_data($account['user_id'], 'stripe_next_action');
                header('Location: ' . $required_action[1]);
                die();
            }
        }
    } else if (PAYMENT_PROVIDER == 'paystack') { // [KEPT FROM LIVE] Paystack check
        $required_action = super_get_user_data('paystack_next_action', $account['user_id']);
        if ($required_action) {
            $required_action = explode('|', $required_action);
            if ($required_action[0] > (time() - 86400)) {
                super_delete_user_data($account['user_id'], 'paystack_next_action');
                header('Location: ' . $required_action[1]);
                die();
            }
        }
    }
}
?>
<html lang="en-US">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no" />
    <title>
        <?php echo MC_CLOUD_BRAND_NAME ?>
    </title>
    <script src="../script/js/min/jquery.min.js"></script>
    <script id="mcinit" src="../script/js/<?php echo mc_is_debug() ? 'main' : 'min/main.min' ?>.js?v=<?php echo MC_VERSION ?>"></script>
    <link rel="stylesheet" href="../script/css/admin.css?v=<?php echo MC_VERSION ?>" type="text/css" media="all" />
    <link rel="stylesheet" href="../script/css/responsive-admin.css?v=<?php echo MC_VERSION ?>" media="(max-width: 464px)" />
    <?php
    if ($rtl) {
        echo '<link rel="stylesheet" href="../script/css/rtl-admin.css?v=' . MC_VERSION . '" />';
    }
    ?>
    <style>
        /* Custom styles for payment processing - removed overlay */
    </style>
    <link rel="stylesheet" href="css/skin.min.css?v=<?php echo MC_VERSION ?>" type="text/css" media="all" />
    <link rel="stylesheet" href="css/facebook-login.css?v=<?php echo MC_VERSION ?>" type="text/css" media="all" />
    <link rel="shortcut icon" href="<?php echo MC_CLOUD_BRAND_ICON ?>" />
    <link rel="apple-touch-icon" href="<?php echo MC_CLOUD_BRAND_ICON_PNG ?>" />
    <link rel="manifest" href="<?php echo MC_CLOUD_MANIFEST_URL ?>" />
    <?php account_js() // Unchanged function call ?>
</head>
<body class="on-load<?php echo $rtl ? ' mc-rtl' : '' ?>">
    <div id="preloader"></div>
    <?php
    cloud_custom_code();
    if ($account && !empty($_COOKIE['mc-cloud'])) {
        if (empty($account['owner']) && db_get('SELECT id FROM agents WHERE admin_id = ' . db_escape($account['user_id'], true) . ' AND email = "' . $account['email'] . '"')) { // Deprecated. Remove && db_get('....
            echo '<script>document.location = "' . CLOUD_URL . '"</script>';
        } else {
            box_account(); // Unchanged function call
        }
    } else {
        $GLOBALS['MC_LANGUAGE'] = [mc_defined('MC_CLOUD_DEFAULT_LANGUAGE_CODE'), 'front'];
        box_registration_login(); // Unchanged function call
    }
    ?>
    <footer>
        <script src="js/cloud<?php echo mc_is_debug() ? '' : '.min' ?>.js?v=<?php echo MC_VERSION ?>"></script>
        <?php mc_cloud_css_js() ?>
    </footer>
</body>
</html>

<?php function box_account() { // [MERGED CHANGES]
    global $cloud_settings;
    $membership = membership_get_active(false);
    $expiration = DateTime::createFromFormat('d-m-y', $membership['expiration']);
    $expired = $membership['price'] != 0 && (!$expiration || time() > $expiration->getTimestamp());
    $shopify = defined('SHOPIFY_CLIENT_ID') ? super_get_user_data('shopify_shop', get_active_account_id()) : false;
    // [UPDATED] CLOUD_CURRENCY definition to match 'new'
    echo '<script>var messages_volume = [' . implode(',', membership_volume()) . ']; var membership = { quota: ' . $membership['quota'] . ', count: ' . $membership['count'] . ', expired: ' . ($expired ? 'true' : 'false') . (isset($membership['quota_agents']) ? (', quota_agents: ' . $membership['quota_agents'] . ', count_agents: ' . $membership['count_agents']) : '') . ', credits: ' . $membership['credits'] . ' }; var CLOUD_USER_ID = ' . account()['user_id'] . '; var CLOUD_CURRENCY = "' . strtoupper(membership_currency()) . '"; var TWILIO_SMS = ' . (defined('CLOUD_TWILIO_SID') && !empty(CLOUD_TWILIO_SID) ? 'true' : 'false') . '; var external_integration = "' . ($shopify ? 'shopify' : '') . '";</script>' . PHP_EOL; ?>
    <div class="mc-account-box mc-admin mc-loading">
        <div class="mc-top-bar">
            <div>
                <h2>
                    <img src="<?php echo MC_CLOUD_BRAND_ICON ?>" />
                    <?php mc_e('Account') ?>
                </h2>
            </div>
            <div>
                <a class="mc-btn mc-btn-dashboard" href="../" target="_self">
                    <?php mc_e('Dashboard') ?>
                </a>
            </div>
        </div>
        <div class="mc-tab">
            <div class="mc-nav">
                <div>
                    <?php mc_e('Installation') ?>
                </div>
                <ul>
                    <li id="nav-installation" class="mc-active">
                        <?php mc_e('Installation') ?>
                    </li>
                    <li id="nav-membership">
                        <?php mc_e('Membership') ?>
                    </li>
                    <li id="nav-invoices">
                       <?php mc_e((PAYMENT_PROVIDER == 'stripe' || PAYMENT_PROVIDER == 'paystack') ? 'Invoices' : 'Payments') // [KEPT] Paystack condition ?>
                    </li>
                    <li id="nav-profile">
                        <?php mc_e('Profile') ?>
                    </li>
                    <?php
                    if (!empty($cloud_settings['referral-commission'])) {
                        echo '<li id="nav-referral">' . mc_('Refer a friend') . '</li>';
                    }
                    ?>
                    <li id="nav-logout">
                        <?php mc_e('Logout') ?>
                    </li>
                </ul>
                <?php
                if (defined('MC_CLOUD_DOCS')) {
                    echo '<a href=" ' . MC_CLOUD_DOCS . '" target="_blank" class="mc-docs mc-btn-text"><i class="mc-icon-help"></i> ' . mc_('Help') . '</a>';
                }
                ?>
            </div>
            <div class="mc-content mc-scroll-area">
                <div id="tab-installation" class="mc-active">
                    <h2 class="addons-title first-title">
                        <?php mc_e($shopify ? 'Installation' : 'Embed code') ?>
                    </h2>
                    <?php
                    if ($shopify) {
                        echo '<p>' . str_replace('{R}', MC_CLOUD_BRAND_NAME, mc_('Customize your store and enable {R} in the app embeds section.')) . '</p><a class="mc-btn mc-btn-white" href="https://' . $shopify . '/admin/themes/current/editor?context=apps&activateAppId=' . SHOPIFY_APP_ID . '/mc" target="_blank">' . mc_('Preview in theme') . '</a>';
                    } else {
                         // [UPDATED] Added mc_() for translation around mc_isset
                        echo '<p>' . htmlspecialchars(mc_(mc_isset($cloud_settings, 'text_embed_code', 'To add the chat to your website, paste this code before the closing </body> tag on each page. Then, reload your website to see the chat in the bottom-right corner. Click the dashboard button in the top-right to access the admin area.'))) . '</p><div class="mc-setting"><textarea id="embed-code" readonly></textarea></div>';
                    }
                    if (defined('DIRECT_CHAT_URL')) {
                        $link = DIRECT_CHAT_URL . '/' . account_chat_id(account()['user_id']);
                        echo '<h2 class="addons-title">' . mc_('Chat direct link') . '</h2><p>' . mc_('Use this unique URL to access the chat widget directly. Include the attribute ?ticket in the URL to view the tickets area.') . '</p><div class="mc-setting mc-direct-link"><input onclick="window.open(\'' . $link . '\')" value="' . $link . '" readonly /></div>';
                    }
                    if (defined('ARTICLES_URL')) {
                        $link = ARTICLES_URL . '/' . account_chat_id(account()['user_id']);
                        echo '<h2 class="addons-title">' . mc_('Articles link') . '</h2><p>' . mc_('Use this unique URL to access the articles page. See the docs for other display options.') . '</p><div class="mc-setting mc-direct-link"><input onclick="window.open(\'' . $link . '\')" value="' . $link . '" readonly /></div>';
                    }
                    ?>
                    <h2 class="addons-title">
                        <?php mc_e('API token') ?>
                    </h2>
                    <p>
                        <?php echo str_replace('{R}', MC_CLOUD_BRAND_NAME, mc_('The API token is a required for using the {R} WEB API.')) ?>
                    </p> <!-- Mismatched closing tag corrected -->
                    <div class="mc-setting">
                        <input value="<?php echo account()['token'] ?>" readonly />
                    </div>
                </div>
                <div id="tab-membership">
                    <h2 class="addons-title first-title">
                        <?php mc_e('Membership') ?>
                    </h2>
                    <?php box_membership($membership) // Updated call inside function below ?>
                    <hr />
                    <?php box_membership_plans($membership['id'], $expired) // Replaced function below ?>
                    <?php box_credits(!$shopify) // Updated function below ?>
                    <?php box_addons() // Unchanged function call ?>
                    <?php box_chart() // Unchanged function call ?>
                    <hr />
                    <hr />
                    <?php
                    button_cancel_membership($membership); // Kept original function below
                    ?>
                </div>
                <div id="tab-invoices" class="mc-loading">
                    <h2 class="addons-title first-title">
                        <?php mc_e((PAYMENT_PROVIDER == 'stripe' || PAYMENT_PROVIDER == 'paystack') ? 'Invoices' : 'Payments') // [KEPT] Paystack condition ?>
                    </h2>
                    <p>
                        <?php mc_e((PAYMENT_PROVIDER == 'stripe' || PAYMENT_PROVIDER == 'paystack') ? 'Download your invoices here.' : 'View your payments here.') // [KEPT] Paystack condition ?>
                    </p>
                    <table class="mc-table">
                        <tbody></tbody>
                    </table>
                </div>
                <div id="tab-profile">
                    <h2 class="addons-title first-title">
                        <?php mc_e('Manage profile') ?>
                    </h2>
                    <p>
                        <?php mc_e('Update here your profile information.') ?>
                    </p>
                    <div id="first_name" data-type="text" class="mc-input">
                        <span>
                            <?php mc_e('First name') ?>
                        </span>
                        <input type="text" />
                    </div>
                    <div id="last_name" data-type="text" class="mc-input">
                        <span>
                            <?php mc_e('Last name') ?>
                        </span>
                        <input type="text" />
                    </div>
                    <div id="email" data-type="text" class="mc-input mc-type-input-button">
                        <span>
                            <?php mc_e('Email') ?>
                        </span>
                        <input type="email" readonly />
                        <a class="mc-btn btn-verify-email">
                            <?php mc_e('Verify') ?>
                        </a>
                    </div>
                    <div id="phone" data-type="text" class="mc-input mc-type-input-button">
                        <span>
                            <?php mc_e('Phone') ?>
                        </span>
                        <input type="tel" />
                        <a class="mc-btn btn-verify-phone">
                            <?php mc_e('Verify') ?>
                        </a>
                    </div>
                    <div id="password" data-type="text" class="mc-input mc-type-input-button">
                        <span>
                            <?php mc_e('Password') ?>
                        </span>
                        <input type="password" value="12345678" />
                    </div>
                    <div id="company_details" data-type="text" class="mc-input">
                        <span>
                            <?php mc_e('Company details') ?>
                        </span>
                        <input type="text" />
                    </div>
                    <hr />
                    <div class="mc-flex">
                        <a id="save-profile" class="mc-btn mc-btn-white mc-icon">
                            <i class="mc-icon-check"></i>
                            <?php mc_e('Save changes') ?>
                        </a>
                        <a id="delete-account" class="mc-btn-text">
                            <?php mc_e('Delete account') ?>
                        </a>
                    </div>
                </div>
                <?php box_referral() // Unchanged function call ?>
            </div>
        </div>
    </div>
<?php } ?>

<?php

function box_membership($membership) { // [UPDATED]
    $membership_type = mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages');
    $membership_type_ma = $membership_type == 'messages-agents';
    $name = $membership_type_ma ? 'messages' : $membership_type;
    $box_two = $membership_type_ma ? '<div><span>' . $membership['count_agents'] . ' / <span class="membership-quota">' . ($membership['quota_agents'] == 9999 ? '∞' : $membership['quota_agents']) . '</span></span> <span>' . mc_('Agents') . '</span></div>' : '';

    // [UPDATED] Using $membership['period'] directly as per 'new' file logic
    $price_string = $membership['price'] == 0 ? '' : (substr($membership['expiration'], -2) == '37' ? '<span id="membership-appsumo" data-id="' . account_get_payment_id() . '"></span>' : (mb_strtoupper($membership['currency']) . ' ' . $membership['price'] . ' ' . membership_get_period_string($membership['period'])));

    echo '<div class="box-maso box-membership"><div class="box-black"><h2>' . mc_(date('F')) . ', ' . date('Y') . '</h2><div><div><span>' . $membership['count'] . ' / <span class="membership-quota">' . $membership['quota'] . '</span></span> <span>' . mc_($name) . '</span></div>' . $box_two . '</div></div><div class="box-black"><h2>' . mc_('Active Membership') . '</h2><div><div><span class="membership-name">' . mc_($membership['name']) . '</span> <span class="membership-price" data-currency="' . $membership['currency'] . '">' . $price_string . '</span></div></div></div></div>';
}


function box_membership_plans($active_membership_id, $expired = false) { // [REPLACED] Body with 'new' version + htmlspecialchars
    $plans = memberships();
    $code = '<div id="plans" class="plans-box">';
    $menu_items = [];
    $membership_type = mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages');
    $membership_type_ma = $membership_type == 'messages-agents';

    // Check if plans were fetched correctly and is an array (Added safety check)
    if (!is_array($plans)) {
        mc_cloud_debug('box_membership_plans: Error - memberships() did not return an array.', ['plans_variable' => $plans]);
        $code .= '<div class="banner banner-error"><h2>Error</h2><p>Could not load membership plans.</p></div>';
        $plans = []; // Set to empty array to prevent errors in loop
    }

    // Loop starting from index 1 as per 'new' file logic (assumes index 0 is free plan)
    for ($i = 1; $i < count($plans); $i++) {
        $plan = $plans[$i];

        // Basic check for plan structure (Added safety check)
        if (!isset($plan['period'], $plan['id'], $plan['currency'], $plan['price'], $plan['name'], $plan['quota'])) {
            mc_cloud_debug('box_membership_plans: Skipping plan at index ' . $i . ' due to missing required keys.', ['plan_data' => $plan]);
            continue;
        }

        $plan_period = $plan['period'];
        $menu = $plan_period == 'month' ? 'Monthly' : ($plan_period == 'year' ? 'Annually' : 'More');
        $plan_quota_agents = $plan['quota_agents'] ?? 0; // Safely get quota_agents

        $period_top = membership_get_period_string($plan_period);
        $period = $membership_type_ma || $membership_type == 'messages' ? $period_top : '';
        if (!in_array($menu, $menu_items)) {
            array_push($menu_items, $menu);
        }
        $is_active = ($active_membership_id == $plan['id']);
        $is_expired = ($is_active && $expired);
        $active_info_html = '';
        if ($is_active) {
             $active_info_html = '<div class="active-membership-info">' . mc_('Active Membership') . ($is_expired ? ' ' . mc_('Expired') : '') . '</div>';
        }

        $quota_agents_string = '';
         if ($membership_type_ma) {
             $quota_agents_display = ($plan_quota_agents == 9999) ? mc_('unlimited') : $plan_quota_agents;
             $quota_agents_string = '<br>' . htmlspecialchars($quota_agents_display) . ' ' . mc_('agents'); // Added htmlspecialchars
         }
        $quota_type_string = mc_($membership_type_ma ? 'messages' : $membership_type);
        $embeddings_limit = cloud_embeddings_chars_limit($plan); // Use helper function

        // Construct HTML with added htmlspecialchars for safety
        $code .= '<div data-menu="' . htmlspecialchars($menu) . '" data-id="' . htmlspecialchars($plan['id']) . '"'
              . ($is_active ? ' data-active-membership="true"' : '')
              . ($is_expired ? ' data-expired="true"' : '') . '>'
              . $active_info_html // Add active/expired info badge
              . '<h3>' . mb_strtoupper(htmlspecialchars($plan['currency'])) . ' ' . htmlspecialchars(number_format($plan['price'], 2)) . ' ' . htmlspecialchars($period_top) . '</h3>' // Price and main period
              . '<h4>' . htmlspecialchars($plan['name']) . '</h4>' // Plan Name
              . '<p>' // Plan details paragraph
              . htmlspecialchars($plan['quota']) . ' ' . htmlspecialchars($quota_type_string) . ' ' . htmlspecialchars($period) // Main quota
              . $quota_agents_string // Agent quota (already includes <br> and htmlspecialchars if needed)
              . '<br>' . htmlspecialchars($embeddings_limit) . ' ' . mc_('characters to train the chatbot') // Embeddings limit
              . '</p>'
              . '</div>'; // Close plan card div
    }
    $code .= '</div>'; // Close the main #plans container

    // Menu generation logic from 'new' file
    if (count($menu_items) > 1) {
        $menu_order = ['Monthly', 'Annually', 'More']; // Fixed order from 'new'
        $code_menu = '<div class="plans-box-menu mc-menu-wide"><div>' . mc_(htmlspecialchars($menu_items[0])) . '</div><ul>'; // Added htmlspecialchars
        for ($i = 0; $i < count($menu_order); $i++) {
            if (in_array($menu_order[$i], $menu_items)) {
                $code_menu .= '<li data-type="' . htmlspecialchars($menu_order[$i]) . '">' . mc_(htmlspecialchars($menu_order[$i])) . '</li>'; // Added htmlspecialchars
            }
        }
        $code = $code_menu . '</ul></div>' . $code; // Prepend menu
    }
    echo $code;
}

function box_credits($auto_recharge = true) { // [UPDATED] Loop and Paystack check merged
    if (!mc_defined('GOOGLE_CLIENT_ID') && !mc_defined('OPEN_AI_KEY') && !mc_defined('WHATSAPP_APP_ID')) {
        return false;
    }
    $prices = [5, 10, 20, 50, 100, 250, 500, 1000, 3000];
    $code_prices = '';
    $currency = strtoupper(membership_currency());
    $exchange_rate = ($currency == 'USD') ? 1 : mc_usd_rates($currency); // Using mc_usd_rates like both versions

    // Using 'for' loop structure from 'new' version
    for ($i = 0; $i < count($prices); $i++) {
        $price_converted = intval($prices[$i] * $exchange_rate); // Using intval like both versions
        $code_prices .= '<option value="' . $price_converted . '">' . $currency . ' ' . $price_converted . '</option>';
    }

    $user_id = db_escape(account()['user_id'], true);
    $credits = mc_isset(db_get('SELECT credits FROM users WHERE id = ' . $user_id), 'credits', 0);
    $checked = super_get_user_data('auto_recharge', $user_id) ? ' checked' : '';
    echo '<h2 id="credits" class="addons-title">' . mc_('Credits') . '</h2><p>' . str_replace('{R}', '<a href="' . (defined('MC_CLOUD_DOCS') ? MC_CLOUD_DOCS : '') . '#cloud-credits" target="_blank" class="mc-link-text">' . mc_('here') . '</a>', mc_('Credits are required to use some features in automatic sync mode. If you don\'t want to buy credits, switch to manual sync mode and use your own API key. For more details click {R}.')) . '</p>';
    echo '<div class="box-maso maso-box-credits">';
    echo '<div class="box-black"><h2>' . mc_('Active credits') . '</h2><div>' . ($credits ? $credits : '0') . '</div></div>';
    echo '<div><h2>' . mc_('Add credits') . '</h2><div><div id="add-credits" data-type="text" class="mc-input"><select><option></option>' . $code_prices . '</select></div></div></div>';

    // [UPDATED] Auto recharge check includes Paystack
    if (in_array(PAYMENT_PROVIDER, ['stripe', 'yoomoney', 'paystack']) && $auto_recharge) {
        echo '<div><h2>' . mc_('Auto recharge') . '</h2><div><div id="credits-recharge" data-type="checkbox" class="mc-input"><input type="checkbox"' . $checked . '></div></div></div>';
    }
    echo '</div>';
}


function box_addons() { // Unchanged from 'live' (same as 'new')
    $white_label_price = super_get_white_label();
    $addons = mc_defined('CLOUD_ADDONS');
    if ($white_label_price || $addons) {
        $account = account();
        $code = '<h2 class="addons-title">' . mc_('Add-ons') . '</h2><p>' . mc_('Add-ons are optional features with a fixed subscription cost.') . '</p><div id="addons" class="plans-box">';
        if ($white_label_price) {
            $code .= '<div class="mc-visible' . (membership_is_white_label($account['user_id']) ? ' mc-plan-active' : '') . '" id="purchase-white-label"><h3>' . strtoupper(membership_currency()) . ' ' . $white_label_price . ' ' . mc_('a year') . '</h3><h4>' . mc_('White Label') . '</h4><p>' . mc_('Remove our branding and logo from the chat widget.') . '</p></div>';
        }
        if ($addons) {
            for ($i = 0; $i < count($addons); $i++) {
                $addon = $addons[$i];
                $code .= '<div class="mc-visible mc-custom-addon" data-index="' . $i . '" data-id="' . mc_string_slug($addon['title']) . '"><h3>' . strtoupper(membership_currency()) . ' ' . $addon['price'] . '</h3><h4>' . mc_($addon['title']) . '</h4><p>' . mc_($addon['description']) . '</p></div>';
            }
        }
        echo $code . '</div>';
    }
}

function button_cancel_membership($membership) { // [KEPT] Original 'live' version for Paystack data attributes
    if ($membership['price'] != 0) {
        $cloud_user_id = get_active_account_id(false); // Use non-escaped for DB lookup
        $subscription_code = super_get_user_data('paystack_subscription_code', $cloud_user_id);
        $email_token = super_get_user_data('paystack_email_token', $cloud_user_id); // Get the token

        if (super_get_user_data('subscription_cancelation', $cloud_user_id)) {
            echo '<p>' . mc_('Your membership renewal has been canceled. Your membership is set to expire on') . ' ' . membership_get_active()['expiration'] . '.</p>';
         } elseif (PAYMENT_PROVIDER == 'paystack' && $subscription_code) { // Only check for code for the button, token is needed by JS
             echo '<a id="cancel-subscription"
                class="mc-btn-text mc-icon mc-btn-red"
                data-subscription-code="' . htmlspecialchars($subscription_code) . '"
                data-email-token="' . htmlspecialchars($email_token ?? '') . '"> <!-- Add email token, default to empty string if null -->
                <i class="mc-icon-close"></i>' . mc_('Cancel subscription') . '</a>';
         } elseif (PAYMENT_PROVIDER == 'stripe' /* || other providers with cancel logic */) {
             // Logic for Stripe or other providers if needed (This part comes from 'live' version originally)
             // Using a generic button text similar to the 'new' version, but keeping the ID for potential JS hooks
             echo '<a id="cancel-subscription" class="mc-btn-text mc-icon mc-btn-red"><i class="mc-icon-close"></i>' . mc_('Cancel subscription') . '</a>';
         } else {
              // Fallback for providers without automatic cancellation (generic button from 'new' version)
              echo '<a id="cancel-subscription" class="mc-btn-text mc-icon mc-btn-red"><i class="mc-icon-close"></i>' . mc_('Cancel subscription') . '</a>';
         }
    }
}


function account_js() { // Unchanged from 'live' (same as 'new')
    global $cloud_settings;
    $account = account();
    $reset_code = '<script>document.cookie="mc-login=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;";document.cookie="mc-cloud=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;";location.reload();</script>';
    if ($account) {
        $path = '../script/config/config_' . $account['token'] . '.php';
        if (file_exists($path)) {
            require_once($path);
        } else {
            die($reset_code);
        }
    } else {
        echo '<script>var MC_URL = "' . CLOUD_URL . '/script"; var MC_CLOUD_SW = true; var MC_DISABLED = true; (function() { MCF.serviceWorker.init(); }())</script>';
    }
    if ($cloud_settings) {
        unset($cloud_settings['js']);
        unset($cloud_settings['js-front']);
        unset($cloud_settings['css']);
        unset($cloud_settings['css-front']);
    }
    if (isset($_GET['appsumo']) && mc_is_agent()) {
        die($reset_code);
    }
    $language = mc_get_admin_language();
    $translations = ($language && $language != 'en' ? file_get_contents(MC_PATH . '/resources/languages/admin/js/' . $language . '.json') : '[]');
    echo '<script>var CLOUD_URL = "' . CLOUD_URL . '"; var BRAND_NAME = "' . MC_CLOUD_BRAND_NAME . '"; var PUSHER_KEY = "' . mc_pusher_get_details()[0] . '"; var LANGUAGE = "' . mc_get_admin_language() . '"; var SETTINGS = ' . ($cloud_settings ? json_encode($cloud_settings, JSON_INVALID_UTF8_IGNORE) : '{}') . '; var MC_TRANSLATIONS = ' . ($translations ? $translations : '[]') . '; var PAYMENT_PROVIDER = "' . PAYMENT_PROVIDER . '"; var MEMBERSHIP_TYPE = "' . mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages') . '"; var FB_APP_ID = "1714342389461341";' . (defined('PAYMENT_MANUAL_LINK') ? 'var PAYMENT_MANUAL_LINK = "' . PAYMENT_MANUAL_LINK . '"' : '') . '</script>';
}

function box_chart() { // Unchanged from 'live' (same as 'new')
    if (in_array(mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages'), ['messages', 'messages-agents'])) {
        echo '<div class="chart-box"><div><h2 class="addons-title">' . mc_('Monthly usage in') . ' ' . date('Y') . '</h2><p>' . mc_('The number of messages sent monthly, all messages are counted, including messages from agents, administrators and chatbot.') . '</p></div></div><canvas id="chart-usage" class="mc-loading" height="100"></canvas>';
    }
}

?>

<?php function box_registration_login() { // Unchanged from 'live' (same as 'new')
    $appsumo = base64_decode(mc_isset($_GET, 'appsumo'));
    global $cloud_settings; ?>
    <div class="mc-registration-box mc-cloud-box mc-admin-box<?php echo !isset($_GET['login']) && !isset($_GET['reset']) ? ' active' : '' ?>">
        <div class="mc-info"></div>
        <div class="mc-top-bar">
            <img src="<?php echo MC_CLOUD_BRAND_LOGO ?>" />
            <div class="mc-title">
                <?php mc_e('New account') ?>
            </div>
            <div class="mc-text">
                <?php mc_e($appsumo ? 'Complete the AppSumo registration.' : 'Create your free account. No payment information required.') ?>
            </div>
        </div>
        <div class="mc-main">
            <div id="first_name" class="mc-input">
                <span>
                    <?php mc_e('First name') ?>
                </span>
                <input type="text" required />
            </div>
            <div id="last_name" class="mc-input">
                <span>
                    <?php mc_e('Last name') ?>
                </span>
                <input type="text" required />
            </div>
            <div id="email" class="mc-input">
                <span>
                    <?php mc_e('Email') ?>
                </span>
                <input type="email" <?php echo $appsumo ? 'value="' . htmlspecialchars($appsumo) . '" readonly="true" style="color:#989898"' : '' ?> required /> <!-- Added htmlspecialchars -->
            </div>
            <div id="password" class="mc-input">
                <span>
                    <?php mc_e('Password') ?>
                </span>
                <input type="password" required />
            </div>
            <div id="password_2" class="mc-input">
                <span>
                    <?php mc_e('Repeat password') ?>
                </span>
                <input type="password" required />
            </div>
            <?php
            $code = '';
            for ($i = 1; $i < 5; $i++) {
                $name = mc_isset($cloud_settings, 'registration-field-' . $i);
                if ($name) {
                    $code .= '<div id="' . mc_string_slug($name) . '" class="mc-input"><span>' . mc_($name) . '</span><input type="text" required /></div>';
                }
            }
            echo $code;
            ?>
            <div class="mc-bottom">
                <div class="mc-btn btn-register">
                    <?php mc_e('Create account') ?>
                </div>
                <div class="mc-btn mc-btn-facebook">
                    <i class="mc-icon-facebook"></i>
                    <?php mc_e('Facebook') ?>
                </div>
            </div>
            <div class="mc-text-area mc-left-align">
                <div class="mc-text">
                    <?php mc_e('Already have an account?') ?>
                </div>
                <div class="mc-text mc-btn-login-box">
                    <?php mc_e('Log in') ?>
                </div>
            </div>
            <div class="mc-errors-area"></div>
        </div>
        <div class="loading-screen">
            <i class="mc-loading"></i>
            <p>
                <?php mc_e('We are creating your account...') ?>
            </p>
        </div>
    </div>
    <div class="mc-login-box mc-cloud-box mc-admin-box<?php if (isset($_GET['login']))
        echo ' active' ?>">
            <div class="mc-info"></div>
            <div class="mc-top-bar">
                <img src="<?php echo MC_CLOUD_BRAND_LOGO ?>" />
            <div class="mc-title">
                <?php mc_e('Sign in to your account') ?>
            </div>
            <div class="mc-text">
                <?php echo mc_('To continue to') . ' ' . MC_CLOUD_BRAND_NAME ?>
            </div>
        </div>
        <div class="mc-main">
            <div id="email" class="mc-input">
                <span>
                    <?php mc_e('Email') ?>
                </span>
                <input type="email" required />
            </div>
            <div id="password" class="mc-input">
                <span>
                    <?php mc_e('Password') ?>
                </span>
                <input type="password" required />
            </div>
            <div class="mc-text btn-forgot-password">
                <?php mc_e('Forgot your password?') ?>
            </div>
            <div class="mc-bottom">
                <div class="mc-btn btn-login">
                    <?php mc_e('Sign in') ?>
                </div>
                <div class="mc-btn mc-btn-facebook">
                    <i class="mc-icon-facebook"></i>
                    <?php mc_e('Facebook') ?>
                </div>
            </div>
            <div class="mc-text-area mc-left-align">
                <div class="mc-text">
                    <?php mc_e('Need new account?') ?>
                </div>
                <div class="mc-text btn-registration-box">
                    <?php mc_e('Sign up free') ?>
                </div>
            </div>
            <div class="mc-errors-area"></div>
        </div>
    </div>
    <div class="mc-reset-password-box mc-cloud-box mc-admin-box">
        <div class="mc-info"></div>
        <div class="mc-top-bar">
            <img src="<?php echo MC_CLOUD_BRAND_LOGO ?>" />
            <div class="mc-title">
                <?php mc_e('Reset password') ?>
            </div>
            <div class="mc-text">
                <?php mc_e('Enter your email below, you will receive an email with instructions on how to reset your password.') ?>
            </div>
        </div>
        <div class="mc-main">
            <div class="mc-input">
                <span>
                    <?php mc_e('Email') ?>
                </span>
                <input id="reset-password-email" type="email" required />
            </div>
            <div class="mc-bottom">
                <div class="mc-btn btn-reset-password">
                    <?php mc_e('Reset password') ?>
                </div>
                <div class="mc-text btn-cancel-reset-password">
                    <?php mc_e('Cancel') ?>
                </div>
            </div>
        </div>
    </div>
    <div class="mc-reset-password-box-2 mc-cloud-box mc-admin-box<?php if (isset($_GET['reset']))
        echo ' active' ?>">
            <div class="mc-info"></div>
            <div class="mc-top-bar">
                <img src="<?php echo MC_CLOUD_BRAND_LOGO ?>" />
            <div class="mc-title">
                <?php mc_e('Reset password') ?>
            </div>
            <div class="mc-text">
                <?php mc_e('Enter your new password here.') ?>
            </div>
        </div>
        <div class="mc-main">
            <div class="mc-input">
                <span>
                    <?php mc_e('Password') ?>
                </span>
                <input id="reset-password-1" type="password" required />
            </div>
            <div class="mc-input">
                <span>
                    <?php mc_e('Repeat password') ?>
                </span>
                <input id="reset-password-2" type="password" required />
            </div>
            <div class="mc-bottom">
                <div class="mc-btn btn-reset-password-2">
                    <?php mc_e('Reset password') ?>
                </div>
            </div>
        </div>
    </div>
    <p class="disclaimer">
        <?php mc_e(mc_isset($cloud_settings, 'disclaimer', 'By creating an account you agree to our <a target="_blank" href="https://app.masichat.com/terms-of-service">Terms Of Service</a> and <a target="_blank" href="https://app.masichat.com/privacy">Privacy Policy</a>.<br />© 2022-2024 app.masichat.com. All rights reserved.')) ?>
    </p>
<?php } ?>

<?php function box_referral() { // Unchanged from 'live' (same as 'new')
    global $cloud_settings;
    if (isset($cloud_settings['referral-commission'])) { ?>
        <div id="tab-referral">
            <h2 class="addons-title first-title">
                <?php mc_e('Refer a friend') ?>
            </h2>
            <p>
                <?php echo mc_isset($cloud_settings, 'referral-text', '') // Raw output ok if admin controlled ?>
            </p>
            <div class="mc-input">
                <input value="<?php echo htmlspecialchars(CLOUD_URL . '?ref=' . mc_encryption('encrypt', account()['user_id'])) ?>" type="text" readonly /> <!-- Added htmlspecialchars -->
            </div>
            <hr class="space" />
            <h2 class="addons-title">
                <?php mc_e('Your current earnings') ?>
            </h2>
            <div class="text-earnings">
                <?php echo strtoupper(membership_currency()) . ' ' . htmlspecialchars(super_get_user_data('referral', account()['user_id'], 0)) ?> <!-- Added htmlspecialchars -->
            </div>
            <hr class="space" />
            <h2 class="addons-title">
                <?php mc_e('Your payment information') ?>
            </h2>
            <div data-type="text" class="mc-input">
                <span><?php mc_e('Method') ?></span>
                <select id="payment_method">
                    <option></option>
                    <option value="paypal">PayPal</option>
                    <option value="bank"><?php mc_e('Bank Transfer') ?></option>
                </select>
            </div>
            <div data-type="text" class="mc-input mc-input">
                <span id="payment_information_label"></span>
                <textarea id="payment_information"></textarea>
            </div>
            <hr class="space-sm" />
            <a id="save-payment-information" class="mc-btn mc-btn-white mc-icon">
                <i class="mc-icon-check"></i><?php mc_e('Save changes') ?>
            </a>
        </div>
    <?php }
} ?>