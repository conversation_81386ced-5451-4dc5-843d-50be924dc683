<?php

/*
 * ==========================================================
 * PERFEX CRM APP
 * ==========================================================
 *
 * Perfex CRM app. � 2017-2025 app.masichat.com. All rights reserved.
 *
 */

define('MC_PERFEX', '1.1.3');

/*
 * ----------------------------------------------------------
 * DATABASE
 * ----------------------------------------------------------
 *
 */

function mc_perfex_db_connect() {
    return mc_external_db('connect', 'perfex');
}

function mc_perfex_db_get($query, $single = true) {
    return mc_external_db('read', 'perfex', $query, $single);
}

function mc_perfex_db_query($query, $return = false) {
    return mc_external_db('write', 'perfex', $query, $return);
}

/*
 * ----------------------------------------------------------
 * USERS
 * ----------------------------------------------------------
 *
 * 1. Get the active Perfex user and register it if it's not a Masi Chat user
 * 2. Used internally by mc_get_active_user()
 * 3. Get user details
 * 4. Get all contacts
 * 5. Synch contacts with Masi Chat
 * 6. Set the profile image
 *
 */

function mc_perfex_get_active_user($user_id) {
    $user = mc_perfex_get_user($user_id, false);
    $query = '';
    if ($user && isset($user['email'])) {
        $query = 'SELECT id, token FROM mc_users WHERE email ="' . $user['email'] . '" LIMIT 1';
        $user_db = mc_db_get($query);
        if ($user_db === '') {
            $settings_extra = ['phone' => [$user['phone'], 'Phone'], 'perfex-id' => [$user['id'], 'Perfex ID']];
            $active_user = mc_get_active_user();
            if ($active_user && ($active_user['user_type'] == 'lead' || $active_user['user_type'] == 'visitor')) {
                mc_update_user($user['id'], $user, $settings_extra, false);
            } else {
                mc_add_user($user, $settings_extra, false);
            }
            $user = mc_db_get($query);
        } else {
            $user = $user_db;
        }
        if (mc_is_error($user) || !isset($user['token']) || !isset($user['id'])) {
            return false;
        } else {
            return mc_login('', '', $user['id'], $user['token']);
        }
    } else {
        return false;
    }
}

function mc_perfex_get_active_user_function($return, $login_app) {
    if ($return === false) {
        $return = mc_perfex_get_active_user($login_app);
    } else {
        $user = mc_perfex_get_user($login_app, false);
        if (mc_is_error($user))
            die($user);
        if (isset($user['email']) && $user['email'] != $return['email']) {
            $return = mc_perfex_get_active_user($login_app);
        }
    }
    if (isset($return[1])) {
        $return = array_merge($return[0], ['cookie' => $return[1]]);
    }
    return $return;
}

function mc_perfex_get_user($user_id, $admin = true) {
    $contact_id = false;
    if (is_array($user_id)) {
        $contact_id = $user_id[1];
        $user_id = $user_id[0];
    }
    $contact_id = mc_db_escape($contact_id);
    $user_id = mc_db_escape($user_id);
    $perfex_user = mc_perfex_db_get('SELECT ' . ($admin ? 'staffid AS `id`, profile_image, firstname AS `first_name`, lastname AS `last_name`, email, password, facebook, linkedin, skype, phonenumber AS `phone`' : 'id, profile_image, firstname AS `first_name`, lastname AS `last_name`, email, password, phonenumber AS `phone`') . ' FROM ' . MC_PERFEX_DB_PREFIX . ($admin ? 'staff' : 'contacts') . ' WHERE ' . ($admin ? 'staffid' : 'userid') . ' = ' . $user_id . ($admin || empty($contact_id) ? '' : (' AND id = ' . $contact_id)));
    if (!empty($perfex_user)) {
        $perfex_user['profile_image'] = mc_perfex_set_profile_image($perfex_user, $admin);
    }
    return $perfex_user;
}

function mc_perfex_get_all_contacts() {
    return mc_perfex_db_get('SELECT id, profile_image, firstname AS `first_name`, lastname AS `last_name`, email, password, phonenumber AS `phone` FROM ' . MC_PERFEX_DB_PREFIX . 'contacts', false);
}

function mc_perfex_sync() {
    $contacts = mc_perfex_get_all_contacts();
    if (mc_is_error($contacts))
        return $contacts;
    for ($i = 0; $i < count($contacts); $i++) {
        $extra = [];
        $contacts[$i]['profile_image'] = mc_perfex_set_profile_image($contacts[$i], false);
        if ($contacts[$i]['phone']) {
            $extra['phone'] = [$contacts[$i]['phone'], 'Phone'];
        }
        mc_add_user($contacts[$i], $extra);
    }
    return true;
}

function mc_perfex_set_profile_image($user, $admin = true) {
    return empty($user['profile_image']) || $user['profile_image'] == 'null' ? '' : (mc_get_setting('perfex-url') . '/uploads/' . ($admin ? 'staff' : 'client') . '_profile_images/' . $user['id'] . '/thumb_' . $user['profile_image']);
}

/*
 * ----------------------------------------------------------
 * KNOWLEDGE BASE ARTICLES
 * ----------------------------------------------------------
 *
 * Import articles into Masi Chat
 *
 */

function mc_perfex_articles_sync() {
    $existing_article_titles = array_column(mc_get_articles(false, false, true), 'title');
    $perfex_articles = mc_perfex_db_get('SELECT subject, description FROM ' . MC_PERFEX_DB_PREFIX . 'knowledge_base', false);
    mc_debug($perfex_articles);
    if (mc_is_error($perfex_articles)) {
        return $perfex_articles;
    }
    for ($i = 0; $i < count($perfex_articles); $i++) {
        if (!in_array($perfex_articles[$i]['subject'], $existing_article_titles)) {
            mc_save_article(['title' => $perfex_articles[$i]['subject'], 'content' => $perfex_articles[$i]['description']]);
        }
    }
    return true;
}

?>