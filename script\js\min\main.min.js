"use strict";!function(e){var t,i,s,a,n,o,r,l,c,d,u,h,g,p,f,m,v,b,_,y,w="3.7.9",S=!1,A=!1,k=!1,T=!1,C=!1,B=[],x=!1,E=[9999999,""],$=[!1,!1],I=document.title,R={},j=e(window).width()<465,L="",M=!1,N="undefined",D=!0,U=6e4*(new Date).getTimezoneOffset(),P=!1,O=!1,q=[],F=[0,!1],G=[0,!1],W=!1,z=!1,H=0,V=typeof Shopify!==N;e.fn.extend({manualExpandTextarea:function(){var t=this[0];t.style.height="auto",t.style.maxHeight="25px",window.getComputedStyle(t),t.style.height=(t.scrollHeight>350?350:t.scrollHeight)+"px",t.style.maxHeight="",e(t).trigger("textareaChanged")},autoExpandTextarea:function(){var t=this[0];t.addEventListener("input",(function(i){e(t).manualExpandTextarea()}),!1)}}),function(){var e=[].slice;String.prototype.autoLink=function(){var t,i,s,a,n,o,r;return o=/(^||[\s\n]|<[A-Za-z]*\/?>)((?:https?|ftp):\/\/[\-A-Z0-9+\u0026\u2019@#\/%?=()~_|!:,.;]*[\-A-Z0-9+\u0026@#\/%=~_|])/gi,0<(n=1<=arguments.length?e.call(arguments,0):[]).length?(a=n[0],t=a.callback,s=function(){var e;for(i in e=[],a)r=a[i],"callback"!==i&&e.push(" "+i+"='"+r+"'");return e}().join(""),this.replace(o,(function(e,i,a){return""+i+(("function"==typeof t?t(a):void 0)||"<a href='"+a+"'"+s+">"+a+"</a>")}))):this.replace(o,"$1<a href='$2'>$2</a>")}}.call(this);var J={visibility_status:"visible",ajax:function(t,i=!1){f?(f[0].push(t),f[1].push(i)):(f=[[t],[i]],setTimeout((()=>{let s=f[1],a={"login-cookie":J.loginCookie()};Z()&&(a.user_id=Z().id),typeof MC_LANG!=N&&(a.language=MC_LANG),P&&(a.cloud=P),location.search.includes("debug")&&(a.debug=!0),e.ajax({method:"POST",url:MC_AJAX_URL,data:e.extend({function:"ajax_calls",calls:f[0]},a)}).done((e=>{let a;if(Array.isArray(e))a=e;else if("invalid-session"===e)setTimeout((()=>{J.reset()}),1e3);else{if(S&&MC_ADMIN_SETTINGS.cloud&&e.includes("no-credits")&&!e.includes('"value":"no-credits'))return MCCloud.creditsAlertQuota();try{a="string"==typeof e||e instanceof String?JSON.parse(e):e}catch(i){return void this.ajax_error(e,t)}}for(var n=0;n<a.length;n++){let e=a[n];if(Array.isArray(e)||(e=a),"success"==e[0])(i=s[n])&&i(e[1]);else if(J.errorValidation(e))i&&i(e);else{S&&("security-error"==e[1]&&setTimeout((()=>{J.reset()}),1e3),MCAdmin.conversations.busy=!1),ne.is_busy_update=!1,ne.busy(!1);let i=JSON.parse(e);if(Array.isArray(i)&&"error"==i[0]&&i[2]&&i[3])J.error(i[3],i[2]);else{let i=JSON.stringify(e).replace(/\\/g,"").replace(/\"/g,"").replace(/\[/g,"").replace(/\]/g,"").replace("error,","");i=i.replace(/Masi Chat Error/g,"Error"),J.error(i,t.function)}}}})).fail(((e,t,i)=>{i&&(this.ajax_error("HTTP CURL ERROR"),console.log("Connection issue detected"))})),f=!1}),100))},ajax_error:function(e,t=!1){S&&(MCAdmin.conversations.busy=!1,le.dialogflow.smart_reply_busy=!1),le.dialogflow.busy&&(le.dialogflow.busy=!1,!S&&ne.conversation&&(J.ajax({function:"open-ai-send-fallback-message",conversation_id:ne.conversation.id}),ne.typing(-1,"stop"))),ne.is_busy_update=!1,ne.busy(!1);let i="unknown",s="";if(e&&("string"==typeof e?(e.includes("HTTP CURL ERROR")||e.includes("connection")||e.includes("timeout")?i="connection-error":e.includes("500")||e.includes("server error")?i="server-error":e.includes("404")||e.includes("not found")?i="not-found-error":e.includes("security")||e.includes("invalid-session")?i="security-error":(e.includes("quota")||e.includes("limit")||e.includes("credits"))&&(i="quota-exceeded"),s=e):"object"==typeof e&&(e.error_type?i=e.error_type:"error"===e.status&&e.message&&(s=e.message,e.message.includes("connection")||e.message.includes("network")?i="connection-error":e.message.includes("server")?i="server-error":e.message.includes("timeout")?i="timeout-error":(e.message.includes("upload")||e.message.includes("file"))&&(i="upload-error")))),S?console.error("AJAX Error:",i,s||"Request could not be completed",t?t.function:""):console.log("Request could not be completed"),S){let e=t&&t.function?t.function:"",a=this.showUserFriendlyError(i);console.error("AJAX Error Details:",s,`Function: MCF.ajax.${e}`),J.error(a,`MCF.ajax.${e}`)}else ne.showErrorMessage(this.showUserFriendlyError(i));J.event("MCAjaxError",{type:i,message:s,function:t?t.function:void 0})},cors:function(e="GET",t,i){let s=new XMLHttpRequest;if("withCredentials"in s)s.open(e,t,!0);else{if(typeof XDomainRequest==N)return!1;s=new XDomainRequest,s.open(e,t)}s.onload=function(){i(s.responseText)},s.onerror=function(){return!1},s.send()},upload:function(e,t){P&&e.append("cloud",P),jQuery.ajax({url:MC_URL+"/include/upload.php",cache:!1,contentType:!1,processData:!1,data:e,type:"POST",success:function(e){t(e)}})},getFileType:function(e){return/.jpg|.jpeg|.png|.gif|.webp/.test(e)?"image":/.mp3|.ogg|.wav|.aac/.test(e)?"audio":/.mp4|.mkv|.vob|.3gp|.webm/.test(e)?"video":"file"},UTC:function(e){return new Date(e).getTime()-U},null:function(e){return typeof e===N||null===e||"null"===e||!1===e||!(e.length>0||"number"==typeof e||typeof e.length==N)||e===N},deactivateAll:function(){i.find(".mc-popup, .mc-tooltip, .mc-list .mc-menu, .mc-select ul").mcActive(!1)},deselectAll:function(){window.getSelection?window.getSelection().removeAllRanges():document.selection&&document.selection.empty()},getURL:function(e=!1,t=!1){if(t||(t=location.search),0==e){for(var i=t.split("?").pop().split("&"),s={},a=0;a<i.length;a++){var n=i[a].split("=");s[n[0]]=J.escape(n[1])}return s}return t.indexOf("?")>0&&(t=t.substr(0,t.indexOf("?"))),J.escape(decodeURIComponent((new RegExp("[?|&]"+e+"=([^&;]+?)(&|#|;|$)").exec(t)||[,""])[1].replace(/\+/g,"%20")||""))},URL:function(){return window.location.href.substr(0,window.location.href.indexOf("?"))},stringToSlug:function(e){let t={"ก":"k","ข":"kh","ฃ":"kh","ค":"kh","ฅ":"kh","ฆ":"kh","ง":"ng","จ":"ch","ฉ":"ch","ช":"ch","ซ":"s","ฌ":"ch","ญ":"y","ฎ":"d","ฏ":"t","ฐ":"th","ฑ":"th","ฒ":"th","ณ":"n","ด":"d","ต":"t","ถ":"th","ท":"th","ธ":"th","น":"n","บ":"b","ป":"p","ผ":"ph","ฝ":"f","พ":"ph","ฟ":"f","ภ":"ph","ม":"m","ย":"y","ร":"r","ล":"l","ว":"w","ศ":"s","ษ":"s","ส":"s","ห":"h","ฬ":"l","อ":"o","ฮ":"h","ะ":"a","ั":"a","า":"a","ำ":"am","ิ":"i","ี":"i","ึ":"ue","ื":"ue","ุ":"u","ู":"u","เ":"e","แ":"ae","โ":"o","ใ":"ai","ไ":"ai","ا":"a","ب":"b","ت":"t","ث":"th","ج":"j","ح":"h","خ":"kh","د":"d","ذ":"dh","ر":"r","ز":"z","س":"s","ش":"sh","ص":"s","ض":"d","ط":"t","ظ":"z","ع":"a","غ":"gh","ف":"f","ق":"q","ك":"k","ل":"l","م":"m","ن":"n","ه":"h","و":"w","ي":"y","你":"ni","好":"hao","世":"shi","界":"jie","我":"wo","是":"shi","中":"zhong","国":"guo","人":"ren","谢":"xie","再":"zai","见":"jian"},i="åàáãäâèéëêìíïîòóöôùúüûñç·/_,:;";e=e.trim().toLowerCase().split("").map((e=>t[e]||e)).join("");for(var s=0;s<30;s++)e=e.replace(new RegExp(i.charAt(s),"g"),"aaaaaaeeeeiiiioooouuuunc------".charAt(s));return e.replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,"").replace(/ /g,"")},slugToString:function(e){return(e=e.replace(/_/g," ").replace(/-/g," ")).charAt(0).toUpperCase()+e.slice(1)},random:function(){let e="";for(var t=5;t>0;--t)e+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[Math.floor(62*Math.random())];return e},isAgent:function(e){return"agent"==e||"admin"==e||"bot"==e},beautifyTime:function(e,t=!1,i=!1){let s;if("0000-00-00 00:00:00"==e)return"";if(e.indexOf("-")>0){let t=e.split(/[- :]/);s=new Date(t[0],t[1]-1,t[2],t[3],t[4],t[5])}else{let t=e.split(/[. :]/);s=new Date(t[2],t[1]-1,t[0],t[3],t[4],t[5])}let a=new Date,n=new Date(Date.UTC(s.getFullYear(),s.getMonth(),s.getDate(),s.getHours(),s.getMinutes(),s.getSeconds())),o=(a-n)/864e5*(i?-1:1),r=[K("Sunday"),K("Monday"),K("Tuesday"),K("Wednesday"),K("Thursday"),K("Friday"),K("Saturday")],l=n.toLocaleTimeString(navigator.language,{hour:"2-digit",minute:"2-digit"});return"0"===l.charAt(0)&&(l.includes("PM")||l.includes("AM"))&&(l=l.substring(1)),o<1&&a.getDate()==n.getDate()?t?`<span>${K("Today")}</span> <span>${l}</span>`:`<span data-today>${l}</span>`:o<6?`<span>${r[n.getDay()]}</span>${t?` <span>${l}</span>`:""}`:`<span>${n.toLocaleDateString(void 0,{year:"2-digit",month:"2-digit",day:"2-digit"})}</span>${t?` <span>${l}</span>`:""}`},unix:function(e){let t=e.split(/[- :]/);return Date.UTC(t[0],t[1]-1,t[2],t[3],t[4],t[5])},getLocationTimeString:function(e,t){if(e.timezone){let i={};i.timezone=e.timezone.value,i.country=e.country?e.country.value:i.timezone.split("/")[0].replace(/_/g," "),i.city=e.city?e.city.value:i.timezone.split("/")[1].replace(/_/g," "),J.cors("GET","https://worldtimeapi.org/api/timezone/"+i.timezone,(function(e){if(e=JSON.parse(e),J.null(e)||e.error)J.error(e,"MCF.getLocationTimeString()"),t(responseonSuccess);else{let s=e.datetime.replace("T"," ");t(`${new Date(s.substr(0,s.indexOf("."))).toLocaleString([],{hour:"2-digit",minute:"2-digit"})} ${K("in")} ${i.city?i.city:""}${i.country?", "+i.country:""}`)}}))}},dateDB:function(e){return"now"==e?((e=(new Date).toISOString().replace("T"," ")).indexOf(".")>0&&(e=e.substr(0,e.indexOf("."))),e):`${e.getFullYear()}-${e.getMonth()+1}-${e.getDate()} ${e.getHours()}:${e.getMinutes()}:${e.getSeconds()}`},updateUsersActivity:function(e,t,i){Y.active?i(S&&MC_ADMIN_SETTINGS.bot_id==t||!S&&R.bot_id==t||Y.online_ids.includes(t)?"online":"offline"):J.ajax({function:"update-users-last-activity",user_id:e,return_user_id:t,check_slack:!S&&R.slack_active},(e=>{i("online"===e?"online":"offline")}))},search:function(e,t){(e=e.toLowerCase())!=a?(clearTimeout(k),k=setTimeout((function(){a=e,t()}),1e3)):i.find(".mc-search-btn i").mcLoading(!1)},searchClear:function(t,i){e(t).next().val()&&(e(t).next().val(""),i())},error:function(e,t){if(S&&MCAdmin.is_logout)return;e instanceof Error&&(e=e.message),"string"==typeof e&&("."==e[e.length-1]&&(e=e.slice(0,-1)),e=e.replace(/Masi Chat/g,"Masi Chat"));let s="";"string"==typeof e&&(e.includes("duplicate")&&e.includes("email")?s="duplicate-email":e.includes("duplicate")&&e.includes("phone")?s="duplicate-phone":e.includes("invalid")&&e.includes("login")?s="invalid-login":e.includes("invalid")&&e.includes("password")?s="invalid-password":e.includes("invalid")&&e.includes("email")?s="invalid-email":e.includes("invalid")&&e.includes("phone")?s="invalid-phone":e.includes("user")&&e.includes("not found")?s="user-not-found":e.includes("missing")&&e.includes("data")?s="missing-user-data":e.includes("quota")||e.includes("limit")||e.includes("credits")?s="quota-exceeded":e.includes("connection")||e.includes("network")?s="connection-error":e.includes("server")?s="server-error":e.includes("timeout")?s="timeout-error":e.includes("upload")||e.includes("file size")?s="upload-error":e.includes("file type")?s="file-type-error":e.includes("api")||e.includes("service")?s="api-error":e.includes("payment")?s="payment-error":e.includes("subscription")?s="subscription-error":e.includes("conversation")?s="conversation-error":e.includes("message")?s="message-error":e.includes("attachment")?s="attachment-error":e.includes("rate")&&e.includes("limit")?s="rate-limit-exceeded":e.includes("whatsapp")&&e.includes("template")?s="whatsapp-template-error":e.includes("re-engagement")||e.includes("whatsapp")&&e.includes("24 hour")?s="re-engagement-error":(e.includes("security")||e.includes("session"))&&(s="security-error"));let a=s?this.showUserFriendlyError(s):"We encountered an issue. Please try again later or contact support.";throw S?(!e||t.includes("update-users-last-activity")||t.startsWith("security-error")||MCAdmin.infoPanel(`<p>${a}</p>`,"info",!1,"error"),le.dialogflow.smart_reply_busy=!1,console.error("Processing error:",e,`Function: ${t}`)):(ne.showErrorMessage(a),console.log("Request could not be completed")),i.find(".mc-loading").mcLoading(!1),ne.busy(!1),J.event("MCError",{message:e,function_name:t,error_code:s,user_friendly_message:a}),new Error("An error occurred while processing your request")},errorValidation:function(e,t=!0){return Array.isArray(e)&&"validation-error"===e[0]&&(!0===t||e[1]==t)},showUserFriendlyError:function(e){let t="We encountered an issue. Please try again later.";switch(e){case"duplicate-email":t="This email address is already registered. Please use a different email or try to log in.";break;case"duplicate-phone":t="This phone number is already registered. Please use a different number or try to log in.";break;case"user-not-found":t="User not found. Please check your information and try again.";break;case"invalid-login":t="Invalid login credentials. Please check your email and password and try again.";break;case"invalid-password":t="The password you entered is incorrect. Please try again or use the password reset option.";break;case"invalid-email":t="Please enter a valid email address.";break;case"invalid-phone":t="Please enter a valid phone number.";break;case"missing-user-data":t="Please fill in all required fields.";break;case"invalid-otp":t="The verification code you entered is invalid. Please check and try again.";break;case"quota-exceeded":t="Your account has reached its usage limit. Please upgrade your plan to continue.";break;case"ip-ban":t="Too many login attempts. For security reasons, please try again after a few hours.";break;case"security-error":t="Your session has expired. Please refresh the page and log in again.";break;case"connection-error":t="We couldn't connect to the server. Please check your internet connection and try again.";break;case"server-error":t="The server encountered an error. Please try again later.";break;case"timeout-error":t="The request timed out. Please try again.";break;case"upload-error":t="There was a problem uploading your file. Please try again with a smaller file or a different format.";break;case"file-type-error":t="This file type is not supported. Please upload a different file.";break;case"file-size-error":t="The file is too large. Please upload a smaller file.";break;case"api-error":t="There was a problem connecting to an external service. Please try again later.";break;case"invalid-key":t="The API key is invalid or has expired. Please contact support.";break;case"no-credits":t="You have used all of your credits. Please upgrade your plan to continue using this feature.";break;case"payment-error":t="There was a problem processing your payment. Please check your payment details and try again.";break;case"subscription-error":t="There was a problem with your subscription. Please contact support.";break;case"conversation-error":t="There was a problem with this conversation. Please try starting a new conversation.";break;case"message-error":t="Your message could not be sent. Please try again.";break;case"attachment-error":t="There was a problem with your attachment. Please try again.";break;case"rate-limit-exceeded":t="You're sending messages too quickly. Please wait a moment before trying again.";break;case"whatsapp-template-error":t="There was a problem with the WhatsApp template. Please try a different template or contact support.";break;case"re-engagement-error":t="This WhatsApp message could not be sent. Due to WhatsApp policy, you can only reply to users within 24 hours of their last message."}return t},loginForm:function(t,i=!1,s=!1){if(!(t=e(t)).mcLoading()){i=!1===i?t.closest(".mc-rich-login"):e(i);let a=e.trim(i.find("#email input").val()),n=e.trim(i.find("#password input").val());a&&n?(J.ajax({function:"login",email:a,password:n},(e=>{if(e&&Array.isArray(e)){if(!S&&this.isAgent(e[0].user_type))re.showErrorMessage(i,"You cannot sign in as an agent."),ne.scrollBottom();else{let t=new ie(e[0]);t.set("conversation_id",!!ne.conversation&&ne.conversation.id),this.loginCookie(e[1]),this.event("MCLoginForm",t),s&&s(e)}"wp"==J.setting("wp-users-system")&&le.wordpress.ajax("wp-login",{user:a,password:n})}else i.find(".mc-info").html(K("ip-ban"===e?"Too many login attempts. Please retry again in a few hours.":"Invalid email or password.")).mcActive(!0),S||ne.scrollBottom();t.mcLoading(!1)})),i.find(".mc-info").html("").mcActive(!1),t.mcLoading(!0)):(i.find(".mc-info").html(K("Please insert email and password.")).mcActive(!0),ne.scrollBottom())}},loginCookie:function(e=!1){if(!1===e)return this.cookie("mc-login")?this.cookie("mc-login"):Q("login");R.cloud?Q("login",e):this.cookie("mc-login",e,3650,"set")},login:function(e="",t="",i="",s="",a=!1){J.ajax({function:"login",email:e,password:t,user_id:i,token:s},(e=>!(0==e||!Array.isArray(e))&&(this.loginCookie(e[1]),a&&a(e),!0)))},logout:function(e=!0){ne.stopRealTime(),this.cookie("mc-login","","",!1),this.cookie("mc-cloud","","",!1),Q("open-conversation",""),Q("login",""),ne.conversations=!1,Z(!1),typeof mc_beams_client!==N&&mc_beams_client.stop(),typeof MC_AJAX_URL!==N&&J.ajax({function:"logout"},(()=>{J.event("MCLogout"),e&&setTimeout((()=>{location.reload()}),500)}))},activeUser:function(){return Z()},getActiveUser:function(e=!1,t){let i=le.login();!i&&(Q("wp-login")||Q("whmcs-login")||Q("perfex-login")||Q("aecommerce-login"))&&(this.cookie("mc-login","","","delete"),Z(!1),Q("login",""),Q("wp-login",""),Q("whmcs-login",""),Q("perfex-login",""),Q("aecommerce-login","")),J.ajax({function:"get-active-user",db:e,login_app:JSON.stringify(i),user_token:J.getURL("token")},(e=>{if(!e)return t(),!1;if(e.cookie&&J.loginCookie(e.cookie),e.user_type)if(!S&&J.isAgent(e.user_type)){let e="You are logged in as both agent and user. Logout or use another browser, Incognito or Private mode, to login as user. Force a logout by running the function MCF.reset() in the console.";Q("double-login-alert")||(Q("double-login-alert",!0),alert(e)),console.warn("Masi Chat: "+e),J.event("MCDoubleLoginError")}else Z(new ie(e,e.phone?{phone:e.phone}:{})),Y.start(),i&&Q(i[1]+"-login",!0),t(),J.event("MCActiveUserLoaded",e)}))},reset:function(){let e=["mc-login","mc-cloud","mc-dialogflow-disabled"];for(var t=0;t<e.length;t++)this.cookie(e[t],"",0,!1);try{localStorage.removeItem("masi-chat")}catch(e){}this.logout()},lightbox:function(s){let a=e(S?i:t).find(".mc-lightbox-media");a.mcActive(!0).find(" > div").html(s),S&&(MCAdmin.open_popup=a)},storage:function(e,t=N){try{if(typeof localStorage==N)return!1}catch(e){return!1}let i=localStorage.getItem("masi-chat");if(i=null===i?{}:JSON.parse(i),t===N)return e in i&&i[e];t?i[e]=t:delete i[e],localStorage.setItem("masi-chat",JSON.stringify(i))},storageTime:function(e,t=!1){let i=new Date;if(!1!==t)return 0==Q(e)||i.getTime()-Q(e)>36e5*t&&(Q(e,!1),!0);Q(e,i.getTime())},cookie:function(e,t=!1,i=!1,s="get",a=!1){let n="https:"==location.protocol?"SameSite=None;Secure;":"",o=window[S?"MC_ADMIN_SETTINGS":"CHAT_SETTINGS"],r=o&&o.cookie_domain?"domain="+o.cookie_domain+";":"";if("get"==s){if(!D)return this.storage(e);let t=document.cookie.split(";");for(var l=0;l<t.length;l++){for(var c=t[l];" "==c.charAt(0);)c=c.substring(1);if(0==c.indexOf(e)){let t=c.substring(e.length+1,c.length);return!this.null(t)&&t}}return!1}if("set"==s)if(D){let s=new Date;s.setTime(s.getTime()+i*(a?1:86400)*1e3),document.cookie=e+"="+t+";expires="+s.toUTCString()+";path=/;"+n+r}else this.storage(e,t);else this.cookie(e)&&(D?document.cookie=e+"="+t+";expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;"+n+r:this.storage(e,""))},setting:function(e,t=-1){if(-1===t)return typeof R!==N&&e in R&&R[e];typeof R!==N&&(R[e]=t)},shortcode:function(e){return oe.shortcode(e)},event:function(t,i){e(document).trigger(t,i);let s=S?typeof MC_ADMIN_SETTINGS!==N&&MC_ADMIN_SETTINGS.webhooks:R.webhooks,a={MCGetUser:"get-user",MCSMSSent:"sms-sent",MCLoginForm:"login",MCRegistrationForm:"registration",MCUserDeleted:"user-deleted",MCNewMessagesReceived:"new-messages",MCNewConversationReceived:"new-conversation",MCSlackMessageSent:"slack-message-sent",MCMessageDeleted:"message-deleted",MCRichMessageSubmit:"rich-message",MCNewEmailAddress:"new-email-address"};if(s&&t in a){if(!0!==s&&(Array.isArray(s)||(s=s.replace(/ /g,"").split(",")),!s.includes(a[t])))return;J.ajax({function:"webhooks",function_name:t,parameters:i})}},translate:function(e){if(!S&&J.null(R)||S&&typeof MC_TRANSLATIONS===N)return e;let t=S?MC_TRANSLATIONS:R.translations;return t&&t[e]&&t[e]?t[e]:e},escape:function(e){return e?e.replace(/</gi,"&lt;").replace(/javascript:|onclick|onerror|ontoggle|onmouseover|onload|oncontextmenu|ondblclick|onmousedown|onmouseenter|onmouseleave|onmousemove|onmouseout|onmouseup/gi,""):""},strip:function(e){e=e.replace("```","");let t=[/\*([^\**]+)\*/,/\__([^\____]+)\__/,/\~([^\~~]+)\~/,/\`([^\``]+)\`/];for(var i=0;i<2;i++)t.forEach((t=>{e=e.replace(t,(e=>e.replace(/[\*\_\~\`]/g,"")))}));return e.replace(/\\,/g,",").replace(/\\:/g,":")},visibilityChange:function(e=""){this.visibility_status=e;let t=S&&typeof MCAdmin!==N;"hidden"==e?(S||ne.stopRealTime(),ne.tab_active=!1,this.visibility_was_hidden=!0):(Z()&&!S&&ne.startRealTime(),ne.tab_active=!0,clearInterval(C),clearInterval(ne.audio_interval),ne.conversation&&(ne.conversation.updateMessagesStatus(),(ne.chat_open||S)&&ne.updateNotifications(ne.conversation.id),t&&setTimeout((()=>{MCAdmin.conversations.notificationsCounterReset(ne.conversation.id)}),2e3)),t&&(Date.now()-(j?6e4:18e4)>H&&(MCAdmin.conversations.update(),H=Date.now()),j&&ne.update()),document.title=I,this.serviceWorker.closeNotifications())},settingsStringToArray:function(e){if(this.null(e))return[];let t=[];e=e.split(",");for(var i=0;i<e.length;i++){let s=e[i].split(":");t[s[0]]="false"!=s[1]&&("true"==s[1]||s[1])}return t},openWindow:function(e,t=550,i=350){let s=screen.width/2-t/2,a=screen.height/2-i/2;return window.open(e,"targetWindow","toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width="+t+",height="+i+", top="+a+", left="+s),!1},convertUTCDateToLocalDate:function(e,t=0){return e=new Date(e),e=new Date(e.getTime()+36e5*t),new Date(e.getTime()+-1*U)},loadResource:function(e,t=!1,i=!1,s=!1){let a=document.createElement(t?"script":"link");e?(t?a.src=e:a.href=e,a.type=t?"text/javascript":"text/css"):a.innerHTML=s,i&&(a.onload=function(){i()}),t||(a.rel="stylesheet"),document.head.appendChild(a)},debounce:function(e,t,i=500){t in B||(B[t]=!0,e(),setTimeout((()=>{delete B[t]}),i))},handleAjaxError:function(e,t="",i=!1){let s="unknown",a="",n="";e&&("string"==typeof e?(e.includes("HTTP CURL ERROR")||e.includes("connection")||e.includes("timeout")?s="connection-error":e.includes("500")||e.includes("server error")?s="server-error":e.includes("404")||e.includes("not found")?s="not-found-error":e.includes("security")||e.includes("invalid-session")?s="security-error":e.includes("quota")||e.includes("limit")||e.includes("credits")?s="quota-exceeded":"duplicate-email"===e?s="duplicate-email":"ip-ban"===e&&(s="ip-ban"),a=e):"object"==typeof e&&(e.error_type?(s=e.error_type,n=e.error_type):"error"===e.status&&e.message?(a=e.message,e.message.includes("connection")||e.message.includes("network")?s="connection-error":e.message.includes("server")?s="server-error":e.message.includes("timeout")?s="timeout-error":(e.message.includes("upload")||e.message.includes("file"))&&(s="upload-error")):Array.isArray(e)&&"validation-error"===e[0]&&(s="validation-error",n=e[1])));let o=n?this.showUserFriendlyError(n):this.showUserFriendlyError(s);if(S?console.error("Error:",s,a||"Request could not be completed",t):console.log("Request could not be completed"),i&&"function"==typeof i)i(o,s,a);else if(S){let e=o;console.error("Error Details:",a,`Function: ${t}`),this.error(e,t)}else ne.showErrorMessage(o);return this.event("MCHandleError",{type:s,message:a,function:t,user_friendly_message:o}),{type:s,message:a,user_friendly_message:o}},serviceWorker:{sw:!1,timeout:!1,init:function(){navigator.serviceWorker&&navigator.serviceWorker.register(S||"undefined"!=typeof MC_CLOUD_SW?MC_URL.replace("/script","")+"/sw.js?v="+w:R.push_notifications_url+"?v="+w).then((e=>{e.update(),this.sw=e})).catch((function(e){console.warn(e)}))},initPushNotifications:function(){S&&("pusher"==MC_ADMIN_SETTINGS.push_notifications_provider||"onesignal"!=MC_ADMIN_SETTINGS.push_notifications_provider)||!S&&("pusher"==R.push_notifications_provider||"pushalert"!=R.push_notifications_provider)?Y.initPushNotifications():e.getScript("https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.page.js",(()=>{window.OneSignalDeferred=window.OneSignalDeferred||[],OneSignalDeferred.push((e=>{e.init({appId:MC_ADMIN_SETTINGS.push_notifications_id})})),OneSignalDeferred.push((e=>{e.User.PushSubscription.addEventListener("change",(t=>{if(t.current.optedIn){let t=(S&&MC_ADMIN_SETTINGS.cloud?MC_ADMIN_SETTINGS.cloud.cloud_user_id+"-":!S&&R.cloud?R.cloud.cloud_user_id+"-":"")+(S?MC_ACTIVE_AGENT.id:Z().id);1==t&&(t="MC-1"),e.User.addTag("user_type",S?"agents":"users"),e.login(t)}J.event("MCPushNotificationSubscription",t.current)}))})),z=!1}))},closeNotifications:function(e=0){this.sw&&this.sw.getNotifications().then((t=>{if(t.length)for(let e=0;e<t.length;e+=1)t[e].close();else e<300&&"visible"==J.visibility_status&&setTimeout((()=>{this.closeNotifications(e+1)}),10)}))},pushNotification:function(e,t=!1){let i=S?MC_ACTIVE_AGENT.profile_image:Z().image;J.ajax({function:"push-notification",title:S?MC_ACTIVE_AGENT.full_name:Z().name,message:J.strip(e),icon:i.indexOf("user.svg")>0?R.notifications_icon:i,interests:t||ne.getRecipientUserID(),conversation_id:!!ne.conversation&&ne.conversation.id},(e=>e))}},beautifyAttachmentName:function(e){let t=e.indexOf("_");return-1!==t?e.substring(t+1):e}},Y={channels:{},channels_presence:[],active:!1,pusher:!1,started:!1,pusher_beams:!1,initialized:!1,online_ids:[],beams_loaded:!1,init:function(t=!1){if(Y.active){if(this.pusher)return!t||t();if(!t)return;e(window).one("MCPusherInit",(()=>{t()})),this.initialized=!0,typeof Pusher===N?e.getScript("https://js.pusher.com/8.2.0/pusher.min.js",(()=>{window.Pusher=Pusher,this.init_2()}),!0):this.init_2()}},init_2:function(){this.pusher=new Pusher(S?MC_ADMIN_SETTINGS.pusher_key:R.pusher_key,{cluster:S?MC_ADMIN_SETTINGS.pusher_cluster:R.pusher_cluster,channelAuthorization:{endpoint:MC_URL+"/include/pusher.php",params:{login:J.loginCookie(),cloud_user_id:!!R.cloud&&R.cloud.cloud_user_id}}}),J.event("MCPusherInit")},initPushNotifications:function(){(Z()||S)&&(this.beams_loaded?this.initPushNotifications_2():e.getScript("https://js.pusher.com/beams/2.0.0-beta.0/push-notifications-cdn.js",(()=>{this.initPushNotifications_2()}),!0))},initPushNotifications_2:function(){window.navigator.serviceWorker.ready.then((e=>{this.pusher_beams=new PusherPushNotifications.Client({instanceId:S?MC_ADMIN_SETTINGS.push_notifications_id:R.push_notifications_id,serviceWorkerRegistration:e}),J.serviceWorker.closeNotifications(),this.pusher_beams.start().then((()=>this.pusher_beams.setDeviceInterests(S?[MC_ACTIVE_AGENT.id,"agents"]:[Z().id,"users"]))).catch(console.error),z=!1}))},start:function(){S||this.started||!Z()||(this.active&&this.init((()=>{this.event("client-typing",(e=>{e.user_id==ne.agent_id&&ne.conversation&&e.conversation_id==ne.conversation.id&&(ne.typing(-1,"start"),clearTimeout(T),T=setTimeout((()=>{ne.typing(-1,"stop")}),1e3))})),this.event("new-message",(e=>{!(e&&Z()&&e.conversation_id)||Z().getConversationByID(e.conversation_id)&&ne.conversation&&ne.conversation.id==e.conversation_id?ne.update():ne.updateConversations()})),this.presence(1,(()=>{this.started=!0,ne.automations.runAll()}))})),R.push_notifications_users&&("pusher"!=R.push_notifications_provider&&"onesignal"==R.push_notifications_provider||(typeof Notification!=N&&"granted"==Notification.permission?this.initPushNotifications():z=!0)))},subscribe:function(e,t=!1){if(!this.pusher)return this.init((()=>{this.subscribe(e,t)}));e=this.cloudChannelRename(e);let i=this.pusher.subscribe(e);i.bind("pusher:subscription_error",(e=>console.log(e))),i.bind("pusher:subscription_succeeded",(()=>{this.channels[e]=i,t&&t()}))},event:function(e,t,i="private-user-"+Z().id){if(!this.pusher)return this.init((()=>{this.event(e,t,i)}));let s=i;(i=this.cloudChannelRename(i))in this.channels?(this.channels[i].unbind(e),this.channels[i].bind(e,(e=>{t(e)}))):this.subscribe(s,(()=>{this.event(e,t,s)}))},trigger:function(e,t={},i="private-user-"+Z().id){if(0==e.indexOf("client-"))return this.channels[this.cloudChannelRename(i)].trigger(e,t);J.ajax({function:"pusher-trigger",channel:i,event:e,data:t},(e=>e))},presence:function(e=1,t){if(!this.pusher)return this.init((()=>{this.presence()}));let i=this.pusher.subscribe(this.cloudChannelRename("presence-"+e));i.bind("pusher:subscription_succeeded",(i=>{if(i.count>98)return this.subscribe(e+1);i.each((e=>{this.presenceCheck(e)&&this.online_ids.push(e.id)})),ne.updateUsersActivity(),t&&t()})),i.bind("pusher:subscription_error",(e=>console.log(e))),i.bind("pusher:member_added",(e=>{this.presenceCheck(e)&&this.presenceAdd(e.id),S&&J.storageTime("online-user-notification-"+e.id,24)&&(MCAdmin.users.onlineUserNotification(e),J.storageTime("online-user-notification-"+e.id))})),i.bind("pusher:member_removed",(e=>{this.presenceRemove(e.id)})),this.channels_presence.push(i),!S&&R.slack_active&&(this.event("add-user-presence",(e=>{this.presenceAdd(e.agent_id)})),J.ajax({function:"slack-presence",list:!0},(e=>{for(var t=0;t<e.length;t++)this.presenceAdd(e[t]);ne.updateUsersActivity()})))},presenceCheck:function(e){let t=J.isAgent(e.info.user_type);return(S&&!t||!S&&t)&&!this.online_ids.includes(e.id)},presenceAdd:function(e){typeof e==N||this.online_ids.includes(e)||(this.online_ids.push(e),this.presenceUpdateAdmin(e),ne.updateUsersActivity())},presenceRemove:function(e){if(typeof e==N)return;let t=this.online_ids.indexOf(e);-1!==t?(this.online_ids.splice(t,1),this.presenceUpdateAdmin(e),ne.updateUsersActivity()):S&&i.find(`.mc-conversation-busy[data-agent="${e}"]`).remove()},presenceUnsubscribe:function(){for(var e=0;e<this.channels_presence.length;e++)this.channels_presence[e].unsubscribe(this.cloudChannelRename("presence-"+(e+1)))},presenceUpdateAdmin:function(e){S&&(i.find(".mc-area-users.mc-active").length&&MCAdmin.users.update(),Z()&&Z().id==e&&MCAdmin.users.updateUsersActivity())},cloudChannelRename:function(e){return R.cloud||S&&MC_ADMIN_SETTINGS.cloud?e+"-"+(S?MC_ADMIN_SETTINGS.cloud.cloud_user_id:R.cloud.cloud_user_id):e}};function X(t){return!!e(t).mcLoading()||(e(t).mcLoading(!0),!1)}function Q(e,t=N){return J.storage(e,t)}function K(e){return J.translate(e)}function Z(e=-1){if(-1===e)return window.mc_current_user;window.mc_current_user=e}function ee(){let e=S?MC_ADMIN_SETTINGS.sound.volume:R.sound.volume;ne.audio&&e&&(ne.audio.volume=e)}function te(e){let t=Math.floor(e/60);return(t||"0")+":"+((e-=60*t)<10?"0"+e:e)}window.MCF=J,window.MCPusher=Y,window.mc_current_user=!1,e.fn.mcActive=function(t=-1){return-1===t?e(this).hasClass("mc-active"):(e(this).setClass("mc-active",t),this)},e.fn.mcLoading=function(t="check"){return"check"==t?e(this).hasClass("mc-loading"):(e(this).setClass("mc-loading",t),this)},e.fn.mcTogglePopup=function(t=!1){let s=!0;return S&&(MCAdmin.open_popup=!1),e(this).mcActive()?(e(this).mcActive(!1),i.removeClass("mc-popup-active"),s=!1):(i.addClass("mc-popup-active"),i.find(".mc-popup").mcActive(!1),t&&e(this).css("left",e(t).offset().left+15).mcActive(!0),S&&setTimeout((()=>{MCAdmin.open_popup=this}),500),J.deselectAll()),s},e.fn.mcUploadFiles=function(t,i=!1){let s=e(this).prop("files");for(var a=!1===i?0:i;a<(!1===i?s.length:i+1);a++){let e=s[a],i=e.size/1048576,n=S?MC_ADMIN_SETTINGS.max_file_size:R.max_file_size;if(i>n){let e=K("Maximum upload size is {R}MB. File size: {R2}MB.").replace("{R}",n).replace("{R2}",i.toFixed(2));S?MCAdmin.infoPanel(e,"info"):alert(e)}let o=new FormData;o.append("file",e),J.upload(o,t)}e(this).value=""},e.fn.setProfile=function(t=!1,i=!1){return J.null(t)&&(t=Z()?Z().name:""),J.null(i)&&(i=Z()?Z().image:MC_URL+"/media/user.svg"),t&&e(this).removeClass("mc-profile-empty"),e(this).find("img").attr("src",i),e(this).find(".mc-name").html(t),this},e.fn.setClass=function(t,i=!0){return i?e(this).addClass(t):e(this).removeClass(t),this};class ie{constructor(e={},t={}){this.details=e,this.extra=t,this.conversations=[],this.processArray(e)}get id(){return this.get("id")?this.get("id"):this.get("user_id")}get type(){return this.get("user_type")}get email(){return this.get("email")}get name(){return this.details.first_name?this.details.first_name+(this.details.last_name?" "+this.details.last_name:""):""}get nameBeautified(){let e=S?MC_ADMIN_SETTINGS.visitor_default_name:R.visitor_default_name;return!e||this.details.last_name&&"#"!=this.details.last_name.charAt(0)?this.name:e}get image(){return this.get("profile_image")}get language(){let e=this.getExtra("language");return e||(e=this.getExtra("browser_language")),e?e.value.toLowerCase():""}get(e){return e in this.details&&!J.null(this.details[e])?this.details[e]:""}getExtra(e){return e in this.extra&&!J.null(this.extra[e])?this.extra[e]:""}set(e,t){this.details[e]=t}setExtra(e,t){this.extra[e]=t}processArray(e){if(e&&e.details){for(var t=0;t<e.details.length;t++)this.setExtra(e.details[t].slug,e.details[t]);delete e.details,this.details=e}}update(e){this.id?J.ajax({function:"get-user",user_id:this.id,extra:!0},(t=>{this.processArray(t),e(),J.event("MCGetUser",this)})):J.error("Missing user ID","MCUser.update")}getConversations(e=!1,t){this.id?J.ajax({function:"get-user-conversations",user_id:this.id,exclude_id:t,agent:J.isAgent(this.type)},(t=>{if(!J.errorValidation(t)){let s=[];for(var i=0;i<t.length;i++)ne.isConversationAllowed(t[i].source,t[i].conversation_status_code)&&s.push(new ae([new se(t[i])],t[i]));this.conversations=s,e&&e(s)}})):J.error("Missing user ID","MCUser.getConversations")}getConversationsCode(e=!1){let t="",i=ne.conversation?ne.conversation.id:-1;e||(e=this.conversations);for(var s=0;s<e.length;s++)if(e[s]instanceof ae){if(!S&&!ne.isConversationAllowed(e[s].get("source"),e[s].status_code))continue;let n=0,o=i==e[s].id;if(!S&&!o)for(var a=0;a<ne.notifications.length;a++)ne.notifications[a][0]==e[s].id&&n++;t+=`<li ${o?'class="mc-active" ':""}data-conversation-status="${o?0:e[s].status_code}" data-conversation-id="${e[s].id}" data-department="${e[s].get("department")}">${e[s].getCode()}${n?'<span data-count="'+n+'">'+n+"</span>":""}</li>`}else J.error("Conversation not of type MCConversation","MCUser.getConversationsCode");return t}getFullConversation(e=!1,t=!1){!1!==e?J.ajax({function:"get-conversation",conversation_id:e},(e=>{let i=[];if(e){if("agent-not-authorized"===e)return void(window.location.href=J.URL());for(var s=0;s<e.messages.length;s++)i.push(new se(e.messages[s]))}t&&t(new ae(i,!!e&&e.details))})):J.error("Missing conversation ID","MCUser.getFullConversation")}getConversationByID(e,t=!1){for(var i=0;i<this.conversations.length;i++)if(this.conversations[i].id==e)return t?i:this.conversations[i];return!1}addConversation(e){if(e instanceof ae){let i=e.id,s=!0;for(var t=0;t<this.conversations.length;t++)if(this.conversations[t].id==i){this.conversations[t]=e,s=!1;break}return s&&this.conversations.unshift(e),s}J.error("Conversation not of type MCConversation","MCUser.addConversation")}removeConversation(e){let t=this.getConversationByID(e,!0);!1!==t&&this.conversations.splice(t,1)}getLastConversation(){return!this.isConversationsEmpty()&&this.conversations[this.conversations.length-1]}isConversationsEmpty(){return 0==this.conversations.length}isExtraEmpty(){return 0===Object.keys(this.extra).length&&this.extra.constructor===Object}delete(e){this.id?J.ajax({function:"delete-user",user_id:this.id},(()=>(J.event("MCUserDeleted",this.id),e(),!0))):J.error("Missing user ID","MCUser.delete")}}window.MCUser=ie;class se{constructor(e={}){this.details=Object.assign({},e);let t=["message_status_code","message_id","message_profile_image","message_first_name","message_last_name","message_user_id","message_user_type"],i=["source","extra","title","tags","agent_id","department","last_update_time","conversation_creation_time","conversation_id","conversation_status_code","conversation_user_id"];for(var s=0;s<t.length;s++)e[t[s]]&&(this.details[t[s].replace("message_","")]=e[t[s]]),delete this.details[t[s]];this.details.first_name&&(this.details.full_name=this.details.first_name+(this.details.last_name?" "+this.details.last_name:"")),e.last_update_time&&(this.details.creation_time=e.last_update_time);for(s=0;s<i.length;s++)delete this.details[i[s]];let a=this.get("payload");if(a)try{var n=JSON.parse(this.get("payload").replace("\\'","'"));a=n&&"object"==typeof n?n:{}}catch(e){a={}}else a={};this.set("payload",a)}get id(){return this.get("id")}get attachments(){return J.null(this.details.attachments)?[]:JSON.parse(this.details.attachments)}get message(){return S?this.payload("translation")&&this.payload("translation-language")==MC_ADMIN_SETTINGS.active_agent_language?this.payload("translation"):this.payload("original-message-language")==MC_ADMIN_SETTINGS.active_agent_language?this.payload("original-message"):this.get("message"):this.get("message")}get(e){return e in this.details&&!J.null(this.details[e])?this.details[e]:""}set(e,t){this.details[e]=t}payload(e=!1,t=!1){let i=this.get("payload");if(!1!==e&&!1!==t)i[e]=t,this.set("payload",i);else if(!1!==e)return e in i?i[e]:!(!i.id||i.id!=e)&&i;return["boolean","string"].includes(typeof i)?[]:i}getCode(){let e=J.isAgent(this.details.user_type),t=this.message,i=this.attachments,s=S?MCAdmin.conversations.messageMenu(e,t):"",a="",n="",o=S&&MC_ADMIN_SETTINGS.show_profile_images||!S&&(e&&!R.hide_agents_thumb||!e&&R.display_users_thumb)?`<div class="mc-thumb"><img loading="lazy" src="${this.details.profile_image}"><div class="mc-tooltip"><div>${this.details.full_name}</div></div></div>`:"",r=(S&&e||!S&&!e?"mc-right":"")+(o?" mc-thumb-active":""),l="",c=!S&&e&&R.sender_name||S&&"chat-admin"==MC_ADMIN_SETTINGS.sender_name?`<span class="mc-agent-name">${this.get("full_name")}</span>`:"",d=!!S&&this.payload().delivery_failed;if(!t&&!i.length)return"";if(e){t=t.replace(/\n/g,"<br>"),t=t.replace(/`([\s\S]*?)`/g,(e=>e.replace(/\[/g,"&#91;")));let e=t.match(/\[([^\[\]]*(?:\[[^\[\]]*\][^\[\]]*)*)\]/g)||[],i=!1,s=e.length;for(var u=0;u<s;u++){let s=oe.shortcode(e[u]);if(s[0])if("action"==s[0])t=t.replace(e[u],"");else{let a=oe.generate(s[1],s[0]);a&&(t=t.replace(e[u],a),i=!0,l=`data-type="${s[0]}"`)}}i&&(r+=" mc-rich-cnt",s>1&&(l='data-type="multiple"'))}else if(t.includes("[rating ")){let e=oe.shortcode(t);t=oe.generate(e[1],e[0])}let h=t.includes("data-success")?[...t.matchAll(/data-success="([^"]*)"/g)].map((e=>e[1])):[];for(u=0;u<h.length;u++)t=t.replace(h[u],"{R"+u+"}");t=this.render(t);for(u=0;u<h.length;u++)t=t.replace("{R"+u+"}",h[u]);if(i.length){a='<div class="mc-message-attachments">';for(u=0;u<i.length;u++){let e=i[u][1],s=e+i[u][0];if("image"==J.getFileType(s)){let t="";i[u].length>2&&(t=i[u][2].split("|"),t=`width="${t[0]}" style="aspect-ratio: ${t[0]} / ${t[1]}"`),n+=`<div class="mc-image${s.includes(".png")?" mc-image-png":e.includes("sticker_")?" mc-image-sticker":""}"><img loading="lazy" src="${e}" ${t}/></div>`}else"audio"==J.getFileType(s)||e.includes("voice_message")||e.includes("audioclip")?((S&&!MC_ADMIN_SETTINGS.speech_recognition||!S&&!R.speech_recognition)&&(t=""),a+=`<div class="mc-player"><div class="mc-player-btn mc-icon-play"></div><div class="mc-player-speed"><div class="mc-player-speed-number">1</div><div class="mc-icon-close"></div></div><div class="mc-player-download mc-icon-arrow-down"></div><audio><source src="${e}" type="audio/mpeg"></audio></div>`):a+=`<a rel="noopener" target="_blank" href="${e}">${J.beautifyAttachmentName(i[u][0])}</a>`}a+="</div>"}return`<div data-id="${this.details.id}" class="${r}" ${l}>${o}<div class="mc-cnt"><div class="mc-message${n&&!t?" mc-message-media":""}"${d?' style="opacity:.7"':""}>${d?MCAdmin.conversations.getDeliveryFailedMessage(d):""}${(c+t+n).trim()}</div>${a}<div class="mc-time">${J.beautifyTime(this.details.creation_time,!0)}${S&&e&&2==this.details.status_code?'<i class="mc-icon-check"></i>':""}</div></div>${s}</div>`}render(t=!1){!1===t&&(t=""+this.details.message);let i=t.length,s=t.match(/```([\s\S]*?)```/g)||[];for(var a=0;a<s.length;a++)t=t.replace(s[a],"[code-"+a+"]");if(t=(t=(t=(t=(t=t.replace(/(?:\r\n|\r|\n)/g,"<br>")).replace(/\*([^\**]+)\*/g,"<b>$1</b>")).replace(/__(.+?)__/g,"<i>$1</i>")).replace(/\~([^\~~]+)\~/g,"<del>$1</del>")).replace(/\`([^\``]+)\`/g,"<code>$1</code>"),((6==i||5==i)&&t.startsWith("&#x")||i<3&&t.match(/(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/))&&(t=`<span class="emoji-large">${t}</span>`),t.includes("](http")){let e=t.split("[");t="";for(a=0;a<e.length;a++)e[a].includes("](http")&&(e[a]=e[a].substring(e[a].indexOf("](")+2,e[a].length-1)),t+=e[a]}t.includes("www.")&&(t=t.replaceAll("www.","https://www.").replaceAll("https://https:","https:").replaceAll("http://https:","http:"));let n=[['href="http',"[L1]"],['src="http',"[L2]"],['url("http',"[L3]"],["url('http","[L4]"],['extra="http',"[L5]"],['data-link="http',"[L6]"]];for(a=0;a<n.length;a++)t=t.replaceAll(n[a][0],n[a][1]);t.includes("http")&&(t=t.autoLink({target:"_blank",callback:function(e){return e.includes("#mc-")?`<a href="${e.split("#mc-")[0]}" target="_blank">${e.split("#mc-")[1].replaceAll("--"," ")}</a>`:null}}));for(a=0;a<n.length;a++)t=t.replaceAll(n[a][1],n[a][0]);for(a=0;a<s.length;a++)t=t.replace("[code-"+a+"]","<pre>"+e.trim(e.trim(s[a].replace(/```<br>/g,"```").replace(/<br>```/g,"```")).replace(/```/g,"").replace(/(?:\r\n|\r|\n)/g,"<br>"))+"</pre>");return t.replace(/&amp;lt;/g,"&lt;")}strip(e=!1){return J.strip(!1===e?""+this.details.message:e)}}window.MCMessage=se;class ae{constructor(e,t){this.details=J.null(t)?{}:t,Array.isArray(e)?(this.messages=[],e.length&&(e[0]instanceof se?this.messages=e:J.error("Messages not of type MCMessage","MCConversation.constructor"))):J.error("Message array not of type Array","MCConversation.constructor");let i=["conversation_id","conversation_user_id","conversation_first_name","conversation_last_name","conversation_profile_image","conversation_user_type","conversation_creation_time","conversation_status_code"],s=["payload"];for(var a=0;a<i.length;a++)t[i[a]]&&(this.details[i[a].replace("conversation_","")]=t[i[a]]),delete this.details[i[a]];for(a=0;a<s.length;a++)delete this.details[s[a]];t&&(this.details.tags="tags"in t?"string"==typeof t.tags?t.tags.split(","):t.tags:[])}get id(){return this.get("id")}get status_code(){return this.get("status_code")}get(e){if(e in this.details&&!J.null(this.details[e]))return this.details[e];if("title"==e){if(this.details.title)return this.details.title;if(this.details.first_name)return this.details.first_name+" "+this.details.last_name;if(this.messages.length)return this.messages[0].get("full_name")}return""}set(e,t){this.details[e]=t}getMessage(e){for(var t=0;t<this.messages.length;t++)if(this.messages[t].id==e)return this.messages[t].set("index",t),this.messages[t];return!1}getLastMessage(){for(var e=this.messages.length-1;e>-1;e--)if(this.messages[e].message||this.messages[e].attachments.length||this.messages[e].payload("preview"))return this.messages[e];return!1}getLastUserMessage(e=!1,t=!1){!1===e&&(e=this.messages.length-1);for(var i=e;i>-1;i--){let e=this.messages[i],s=e.get("user_type");if((e.message||e.attachments.length)&&(!t&&!J.isAgent(s)||!0===t&&("agent"==s||"admin"==s)||"bot"==t&&"bot"==s||"no-bot"==t&&"bot"!=s||"all"==t&&J.isAgent(s)))return this.messages[i].set("index",i-1),this.messages[i]}return!1}getNextMessage(e,t=!1){let i=this.messages.length;for(var s=0;s<i;s++)if(this.messages[s].id==e&&s<i-1){for(var a=s+1;a<i;a++){let e=this.messages[a].get("user_type"),i=this.messages[s+1];if(!t||"agent"==t&&J.isAgent(e)||"user"==t&&!J.isAgent(e))return i}break}return!1}getUserMessages(e="user"){let t=[],i="user"==e?["visitor","lead","user"]:"agents"==e?["agent","admin"]:["bot"];for(var s=0;s<this.messages.length;s++)i.includes(this.messages[s].get("user_type"))&&(this.messages[s].set("index",s),t.push(this.messages[s]));return t}updateMessage(e,t){if(t instanceof se){for(var i=0;i<this.messages.length;i++)if(this.messages[i].id==e)return this.messages[i]=t,!0}else J.error("Message not of type MCMessage","MCConversation.updateMessage");return!1}addMessages(e){if(Array.isArray(e))for(var t=0;t<e.length;t++)e[t]instanceof se&&this.messages.push(e[t]);else e instanceof se?this.messages.push(e):J.error("Messages not of type MCMessage","MCConversation.addMessages()");return this}getCode(e=!1){let t=this.getLastMessage();if(t){let s=t.message;if(!s&&t.payload("preview")&&(s=t.payload("preview")),S&&(s=t.payload().preview?t.payload().preview:s),s=s.replace(/(\r\n|\n|\r)/gm," "),!1!==s.indexOf("[")){let e=s.match(/\[.+?\]/g)||[];if(e.length){let t=oe.shortcode(e[0]);t[0]&&(s=s.replace(e[0],"action"==t[0]?"":K(t[1].message?t[1].message:t[1].title?t[1].title:t[1].name?t[1].name:t[1].link?t[1].link:t[1].values?t[1].values.replaceAll(",",", ").replaceAll("  "," "):t[1].options?t[1].options.replaceAll(",",", ").replaceAll("  "," "):K(J.slugToString(t[0])))))}}if(!s&&t.attachments.length)for(var i=0;i<t.attachments.length;i++)s+=t.attachments[i][0]+" ";if(s=J.strip(s),s.length>114&&(s=s.substr(0,114)+" ..."),e)return s;let a=this.get("title");return(!a||Z()&&Z().name==a||A&&R.tickets_conversations_title_user)&&(a=Z()&&t.get("user_id")==Z().id?K("You"):t.get("full_name")),`<div class="mc-conversation-item" data-user-id="${this.get("user_id")}"><img loading="lazy" src="${t.get("profile_image")}"><div><span class="mc-name">${a}</span><span class="mc-time">${J.beautifyTime(t.get("creation_time"))}</span></div><div class="mc-message">${s}</div></div>`}return""}deleteMessage(e){for(var t=0;t<this.messages.length;t++)if(this.messages[t].id==e)return this.messages.splice(t,1),!0;return!1}searchMessages(e,t=!1){let i=[];for(var s=0;s<this.messages.length;s++){let a=this.messages[s].message;(t&&a==e||!t&&a.includes(e))&&(this.messages[s].set("index",s),i.push(this.messages[s]))}return i}getAttachments(){let e=[];for(var t=0;t<this.messages.length;t++){let s=this.messages[t].attachments;for(var i=0;i<s.length;i++){let a=s[i][1];e.push([s[i][0],a,a.substr(a.lastIndexOf(".")+1),this.messages[t].id])}}return e}updateMessagesStatus(e=!1){if(e)for(var t=0;t<this.messages.length;t++){let i=this.messages[t].id;if(e.includes(i)){let e=n.find(`[data-id="${i}"] .mc-time`);e.find("i").length||e.append('<i class="mc-icon-check"></i>')}}else if(!S&&"visible"==J.visibility_status){e=[];for(t=0;t<this.messages.length;t++){let i=this.messages[t];J.isAgent(i.get("user_type"))&&2!=i.get("status_code")&&(e.push(i.id),i.set("status_code",2))}e.length&&J.ajax({function:"update-messages-status",message_ids:e})}}}window.MCConversation=ae;var ne={emoji_options:{range:0,range_limit:47,list:[],list_now:[],touch:!1},initialized:!1,editor_listening:!1,conversation:!1,is_busy:!1,is_busy_update:!1,is_busy_populate:!1,chat_open:!1,real_time:!1,agent_id:-1,agent_online:!1,user_online:!1,expanded:!1,main_header:!0,start_header:!1,desktop_notifications:!1,flash_notifications:!1,id_last_message:0,id_last_message_conversation:0,datetime_last_message_conversation:"2000-01-01 00:00:00",audio:!1,audio_interval:!1,tab_active:!0,notifications:Q("notifications")?Q("notifications"):[],typing_settings:{typing:!1,sent:!1,timeout:!1},email_sent:!1,dashboard:!1,articles:!1,articles_allowed_ids:!1,articles_category:!1,slack_channel:[-1,-1],skip:!1,queue_interval:!1,departments:!1,default_department:null,default_agent:null,default_tags:null,offline_message_set:!1,label_date:!1,label_date_show:!1,sendMessage:function(t=-1,i="",s=[],a=!1,l=!1,c=!1){let d=p&&le.dialogflow.active(),u=!1,h=this.conversation;if(!Z()&&!S)return this.addUserAndLogin((()=>this.sendMessage(t,i,s,a,l)),!0),void this.busy(!0);if(!h){let e=!S&&Z().getLastConversation();if(!e||"new-conversation"==L||ne.default_department&&ne.default_department!=e.get("department")||ne.default_agent&&ne.default_agent!=e.get("agent_id"))return this.newConversation(c,t,"",[],S&&MC_ACTIVE_AGENT.department?MC_ACTIVE_AGENT.department:null,null,(()=>this.sendMessage(t,i,s,a,l))),void this.busy(!0);this.openConversation(e.id),this.setConversation(e),L=!1}this.calculateLabelDateFirst(),-1==t&&(t=S?MC_ACTIVE_AGENT.id:Z().id);let f=t!=g;if(i||s.length||(i=r.val().trim(),o.find(".mc-attachments > div").each((function(){let t=[e(this).attr("data-name"),e(this).attr("data-value")];e(this).attr("data-size")&&t.push(e(this).attr("data-size")),s.push(t)})),S&&MCAdmin.must_translate&&i&&(le.dialogflow.translate([i],Z().language,(e=>{if(e.length){let t=S?MC_ADMIN_SETTINGS.active_agent_language:Z().language;l?(l["original-message"]=i,l["original-message-language"]=t):l={"original-message":i,"original-message-language":t},e[0]&&(i=e[0])}this.sendMessage(t,i,s,a,l,c)})),u=!0)),this.busy(!0),f&&(r.val("").css("height",""),o.find(".mc-attachments").html("")),o.mcActive(!1),!u)if(!1===c&&t==g&&(c="skip"),S||!f||d||(c=2),i||s.length||l){let e={user_id:t,user:Z(),conversation_id:h.id,conversation:h,conversation_status_code:c,attachments:s};J.ajax({function:"send-message",user_id:t,conversation_id:h.id,message:i,attachments:s,conversation_status_code:c,queue:!S&&R.queue&&f,payload:l,recipient_id:!!S&&Z().id},(n=>{let o=S||!d||n.human_takeover_active;S||t!=g||(this.dashboard?this.updateConversations():this.chat_open||this.updateNotifications(h.id,n.id)),(S&&!this.user_online||!S&&!this.agent_online)&&this.update(),S||!f||p||(this.followUp(),this.offlineMessage()),!S&&f&&(!l||"mc-human-takeover"!=l.id&&J.null(l["skip-dialogflow"]))&&le.dialogflow.message(i,s),!S&&f&&"visitor"==Z().type?J.ajax({function:"update-user-to-lead",user_id:t},(()=>{Z().set("user_type","lead"),R.slack_active&&o&&this.slackMessage(t,Z().name,Z().image,i,s)})):o&&!this.skip&&(S&&MC_ADMIN_SETTINGS.slack_active?this.slackMessage(Z().id,MC_ACTIVE_AGENT.full_name,MC_ACTIVE_AGENT.profile_image,i,s):R.slack_active&&this.slackMessage(Z().id,f?Z().name:R.bot_name,f?Z().image:R.bot_image,i,s)),f&&R.language_detection&&h&&i.split(" ").length&&i.length>3&&!J.storage("language-detection-completed")&&(J.ajax({function:"google-language-detection-update-user",user_id:t,string:i,token:le.dialogflow.token},(e=>{e&&(R.translations=e)})),J.storage("language-detection-completed",!0)),!this.articles||S||!R.articles||R.office_hours||this.isInitDashboard()||setTimeout((()=>{this.conversation&&h.id==this.conversation.id&&(this.sendMessage(g,"[articles]"),this.scrollBottom(),this.articles=!1)}),5e3),n.queue&&this.queue(this.conversation.id),e.message=n.message,e.message_id=n.id,J.event("MCMessageSent",e),A&&MCTickets.onMessageSent(),a&&a(e),n.notifications.length&&J.event("MCNotificationsSent",n.notifications),this.skip&&(this.skip=!1),this.busy(!1)})),f&&(i=J.escape(i),n.append(new se({id:"sending",profile_image:S?MC_ACTIVE_AGENT.profile_image:Z().image,full_name:S?MC_ACTIVE_AGENT.full_name:Z().name,creation_time:"0000-00-00 00:00:00",message:i.replaceAll("<","&lt;"),user_type:S?"agent":"user"}).getCode().replace('<div class="mc-time"></div>',`<div class="mc-time">${K("Sending")}<i></i></div>`))),this.dashboard||!f&&!this.isBottom()||this.scrollBottom()}else this.busy(!1)},updateMessage:function(e,t=""){J.ajax({function:"update-message",message_id:e,message:t})},sendEmail:function(e,t,i=!1,s=!1){let a=i?!0===i?Z().id:i:this.getRecipientUserID();if(!S&&!isNaN(a)&&this.agent_online)return!1;J.ajax({function:"create-email",recipient_id:a,sender_name:S?i?MC_ACTIVE_AGENT.full_name:Z().name:i?R.bot_name:Z().name,sender_profile_image:S?i?MC_ACTIVE_AGENT.profile_image:Z().name:i?R.bot_image:Z().image,message:e,attachments:t,department:!!this.conversation&&this.conversation.get("department"),conversation_id:!!this.conversation&&this.conversation.id},(e=>{s&&s(e)}))},sendSMS:function(e){let t=this.getRecipientUserID();if(!S&&!isNaN(t)&&this.agent_online)return!1;J.ajax({function:"send-sms",to:t,message:e,conversation_id:!!this.conversation&&this.conversation.id},(t=>{"sent"==t.status||"queued"==t.status?J.event("MCSMSSent",{recipient_id:this.getRecipientUserID(),message:e,response:t}):t.message&&J.error(t.message,"MCChat.sendSMS")}))},desktopNotification:function(e,t,i,s=!1,a=!1){if("granted"!==Notification.permission)Notification.requestPermission();else{J.serviceWorker.sw.showNotification(e,{body:J.strip(t),icon:i.indexOf("user.svg")>0?R.notifications_icon:i}).onclick=()=>{S?s?(MCAdmin.conversations.openConversation(s,0==a?Z().id:a),MCAdmin.conversations.update()):a&&MCAdmin.profile.show(a):this.start(),window.focus()}}},getRecipientUserID:function(){return S?Z().id:this.lastAgent(!1)?this.lastAgent(!1).user_id:J.null(this.conversation.get("agent_id"))?J.null(this.conversation.get("department"))?"agents":"department-"+this.conversation.get("department"):this.conversation.get("agent_id")},submit:function(){if(!this.is_busy){if(v&&v.mcActive()){let e=v.find(".mc-btn-mic");return v.mcActive(!1),e.hasClass("mc-icon-pause")&&e.click(),void setTimeout((()=>{let e=new FormData,t=!!this.conversation&&this.conversation.get("source");"wa"==t||q.length?(e.append("file",new File(["wa"==t?MCAudioRecorder.blob():new Blob(q,{type:"ig"==t?"audio/wav":"audio/mp3"})],"voice_message."+("ig"==t?"wav":"mp3"))),J.upload(e,(e=>{ne.uploadResponse(e),this.submit()}))):this.submit(),v.find(".mc-icon-close").click()}),100)}this.sendMessage(),z&&J.serviceWorker.initPushNotifications(),S&&MCAdmin.conversations.setStatus(1,!1,!0)}},initChat:function(){S||J.getActiveUser(!0,(()=>{let e=!1!==Z(),t=!!e&&Z().type;if(A||!R.popup||Q("popup")||j&&R.popup_mobile_hidden||this.popup(),ne.automations.runAll(),A||!R.privacy||R.registration_required||Q("privacy-approved")){if(typeof Notification!==N&&!R.push_notifications_users&&(["all","users"].includes(R.desktop_notifications)||S&&"agents"==R.desktop_notifications)&&(this.desktop_notifications=!0),(["all","users"].includes(R.flash_notifications)||S&&"agents"==R.flash_notifications)&&(this.flash_notifications=!0),this.registration(!0)&&!A)return this.registration(),void(!e&&R.visitors_registration&&this.addUserAndLogin());e||!(typeof MC_WP_WAITING_LIST!==N||R.visitors_registration||R.welcome||A||R.flow_on_load)||A&&R.tickets_registration_required?!this.conversation&&e?this.populateConversations():this.finalizeInit():this.addUserAndLogin((()=>{this.welcome(),le.dialogflow.flowOnLoad(),le.woocommerce.waitingList(),this.finalizeInit()})),R.header_name&&e&&"user"==t&&!A&&l.find(".mc-title").html(`${K("Hello")} ${Z().nameBeautified}!`),this.welcome(),Y.active||setInterval((()=>{this.updateConversations(),this.updateUsersActivity()}),10200),le.dialogflow.flowOnLoad(),le.woocommerce.waitingList(),this.scrollBottom(!0)}else this.privacy()}))},finalizeInit:function(){this.initialized||(t.attr("style",""),S||A||(this.isInitDashboard()&&this.showDashboard(),!j&&window.innerHeight<760&&t.find(" > .mc-body").css("max-height",window.innerHeight-130+"px")),this.initialized=!0,S||(Z()&&!this.registration(!0)&&(Q("open-conversation")&&this.openConversation(Q("open-conversation")),J.getURL("conversation")&&this.openConversation(J.getURL("conversation"))),(!this.chat_open&&(!j&&Q("chat-open")||"open"==J.getURL("chat"))||J.getURL("conversation"))&&setTimeout((()=>{this.start()}),500),R.woocommerce_returning_visitor&&(!1===Q("returning-visitor")?J.storageTime("returning-visitor"):J.storageTime("returning-visitor",24)&&!Q("returning-visitor-processed")&&setTimeout((()=>{J.ajax({function:"woocommerce-returning-visitor"},(()=>{Q("returning-visitor-processed",!0)}))}),15e3)),R.timetable_type&&ne.offlineMessage(),R.queue_human_takeover&&le.dialogflow.humanTakeoverActive()&&(R.queue=!0),e(window).on("resize",(function(){!j&&window.innerHeight<760&&t.find(" > .mc-body").css("max-height",window.innerHeight-130+"px")})),le.dialogflow.flowOnLoad()),A&&MCTickets.init(),J.event("MCInit"))},start:function(){this.initialized&&(this.populate(),this.headerAgent(),this.updateUsersActivity(),this.startRealTime(),this.popup(!0),this.conversation&&this.updateNotifications(this.conversation.id),t.mcActive(!0),e("body").addClass("mc-chat-open"),this.chat_open=!0,"open"!=R.welcome_trigger||this.registration(!0)||this.welcome(),le.martfury.privateChat(),this.calculateLabelDates())},open:function(i=!0){i&&!this.chat_open?(this.start(),this.chat_open=!0,this.startRealTime(),t.mcActive(!0),e("body").addClass("mc-chat-open"),Q("chat-open",!0),this.conversation&&Q("last-open-message",this.conversation.getLastMessage().id),j&&history.pushState({"chat-open":!0},"",""),J.event("MCChatOpen")):!i&&this.chat_open&&(t.mcActive(!1),this.stopRealTime(),this.chat_open=!1,Q("chat-open",!1),e("body").removeClass("mc-chat-open"),J.event("MCChatClose"))},openConversation:function(e){Z().getFullConversation(e,(t=>{if(!t.id||!ne.isConversationAllowed(t.get("source"),t.status_code))return Q("open-conversation",""),!1;this.setConversation(t),this.hideDashboard(),this.populate(),this.main_header=!1,Q("chat-open")&&ne.open(),Q("queue")==e&&this.queue(e),(this.chat_open||A)&&this.updateNotifications(e),A&&MCTickets.activateConversation(t),Q("open-conversation",e),J.event("MCConversationOpen",t)}))},update:function(){if(this.conversation){if(this.is_busy_update)return;let e=this.conversation.getLastMessage(),t=!1;J.ajax({function:"get-new-messages",conversation_id:this.conversation.id,datetime:this.datetime_last_message_conversation,last_id:this.id_last_message_conversation},(i=>{let s=i.length;if(this.is_busy_update=!1,this.conversation&&Array.isArray(i)&&s>0&&(!e||e.id!=i[s-1].id||e.message!=i[s-1].message||e.payload!=i[s-1].payload||e.attachments!=i[s-1].attachments)){let e="",o=[],r=[],l=!1;this.calculateLabelDateFirst();for(var a=0;a<s;a++)if(!(r.includes(i[a].id)||S&&this.conversation.id!=i[a].conversation_id)){let s=new se(i[a]),c=s.payload();if(this.id_last_message_conversation=s.id,this.datetime_last_message_conversation=s.get("creation_time"),c.event){let e=c.event;("delete-message"!=e||!1===this.conversation.getMessage(s.id))&&(S||s.message||s.attachments.length||c)||this.deleteMessage(s.id),"woocommerce-update-cart"!=e||S||le.woocommerce.updateCart(c.action,c.id),"woocommerce-checkout"!=e||S||le.wordpress.ajax("url",{url_name:"checkout"},(e=>{setTimeout((()=>document.location=e),500)})),le.dialogflow.active()||"conversation-status-update-3"!=e&&"conversation-status-update-4"!=e&&"activate-bot"!=e||(le.dialogflow.active("activate"),l=!0),"conversation-status-update-3"==e&&this.conversationArchived()}c["human-takeover"]&&R.queue_human_takeover&&(R.queue=!0,ne.queue(ne.conversation.id)),c["human-takeover-fallback"]&&(le.dialogflow.typing_enabled=!1),this.conversation.getMessage(i[a].id)?(this.conversation.updateMessage(s.id,s),n.find(`[data-id="${s.id}"]`).replaceWith(s.getCode()),t=!0):((s.message||s.attachments.length)&&n.find('[data-id="sending"]').remove(),this.conversation.id==i[a].conversation_id&&(this.conversation.addMessages(s),e+=s.getCode(),t=!1)),this.conversation.updateMessagesStatus(),o.push(s),r.push(s.id),this.chat_open&&Q("last-open-message",s.id),S||!this.dashboard&&this.chat_open&&this.tab_active||s.get("user_id")==Z().id||!s.message&&!s.attachments.length||this.updateNotifications(this.conversation.id,s.id)}n.append(e);let c=this.conversation.getLastMessage(),d=!!c&&c.get("user_type"),u=J.isAgent(d),h=u&&"bot"!=d;!S&&h&&(this.chat_open&&(!c||c.message.includes("mc-rich-success")||i[0].payload&&i[0].payload.includes("conversation-status-update")||this.setConversationStatus(0),R.follow&&clearTimeout(k)),l||le.dialogflow.active(!1)),Q("queue")==this.conversation.id&&h&&this.queue("clear"),!o.length||J.null(o[0].message)&&J.null(o[0].attachments)&&1==s||(S||this.tab_active||this.flashNotification(),this.audio&&(!S&&u||S&&!u)&&this.playSound()),this.headerAgent(),t||this.dashboard||(this.scrollBottom(),setTimeout((()=>{this.scrollBottom()}),300)),!R.auto_open||!this.dashboard&&this.chat_open||this.open(),h&&this.typing(-1,"stop"),this.busy(!1),J.event("MCNewMessagesReceived",{messages:o,conversation_id:this.conversation.id}),A&&MCTickets.onNewMessageReceived(o[0],this.conversation.id)}})),this.is_busy_update=!0,setTimeout((()=>{this.is_busy_update=!1}),5e3)}else this.updateConversations()},updateConversations:function(){Z()&&J.ajax({function:"get-new-user-conversations",datetime:this.id_last_message},(e=>{if(e.length){this.id_last_message=e[0].message_id,this.chat_open&&Q("last-open-message",this.id_last_message);for(var i=0;i<e.length;i++){let t=e[i].conversation_status_code;if(!ne.isConversationAllowed(e[i].source,t))continue;let s=e[i].conversation_id,a=new se(e[i]),n=new ae([a],e[i]),o=Z().addConversation(n);e[i].message_user_id==Z().id||this.conversation.id==s&&this.chat_open||!a.message&&!a.attachments.length||(this.updateNotifications(s,a.id),R.auto_open&&this.open());let r=a.payload();if("boolean"!=typeof r&&r.event){if("open-chat"==r.event&&((this.conversation.id!=s||this.dashboard)&&this.openConversation(s),j||setTimeout((()=>{this.open()}),500)),!a.message&&!a.attachments.length)continue}this.tab_active||(this.desktop_notifications&&ne.desktopNotification(a.get("full_name"),a.message,a.get("profile_image")),this.flash_notifications&&this.flashNotification(),S||!this.audio||!R.sound||this.chat_open&&!this.dashboard&&this.conversation.id==s||J.null(a.message)&&J.null(a.attachments)||this.playSound()),o&&J.event("MCNewConversationReceived",n),A&&MCTickets.onConversationReceived(n),!this.conversation&&o&&this.openConversation(n.id)}this.conversation&&this.conversation.updateMessagesStatus(),t.find(".mc-user-conversations").html(Z().getConversationsCode()),t.find(".mc-dashboard-conversations").setClass("mc-conversations-hidden",t.find(".mc-user-conversations > li").length>3)}}))},populate:function(){if(this.conversation){let t="",i=n.find(" > .mc-notify-message"),s=!1;this.conversation.id;for(var e=0;e<this.conversation.messages.length;e++){let i=this.conversation.messages[e],a=J.beautifyTime(i.get("creation_time"));a.includes("today")&&(a=`<span>${K("Today")}</span>`),a!=s&&(i.message||i.attachments.length)&&(t+=`<div class="mc-label-date">${a}</div>`,s=a),t+=i.getCode()}n.html((i.length?i[0].outerHTML:"")+t),this.dashboard||(this.scrollBottom(),this.calculateLabelDates())}else Z()&&!Z().isConversationsEmpty()&&(R.disable_dashboard?this.openConversation(Z().conversations[0].id):this.showDashboard())},populateConversations:function(e=!1){!this.is_busy_populate&&Z()&&(this.is_busy_populate=!0,setTimeout((()=>{this.is_busy_populate=!1}),5e3),Z().getConversations((i=>{let s=i.length,a=[];if(s){let e=Date.now(),o=i[0].messages[0];this.id_last_message=o.id;for(var n=0;n<s;n++)a.push(i[n].id),A||1!=i[n].status_code||!(Q("last-open-message")<o.id)||this.conversation&&this.conversation.id==i[n].id||this.updateNotifications(i[n].id,o.id),!j&&e-J.UTC(i[n].messages[0].get("creation_time"))<6e3&&this.open();t.find(".mc-user-conversations").html(Z().getConversationsCode()),t.find(".mc-dashboard-conversations").setClass("mc-conversations-hidden",t.find(".mc-user-conversations > li").length>3)}t.setClass("mc-no-conversations",!s),this.initialized&&"open-conversation"!=L||1!=s||this.isInitDashboard()||Q("open-conversation")||(this.openConversation(Z().getLastConversation().id),"open-conversation"==L&&(L=""));for(n=0;n<ne.notifications.length;n++)this.updateNotifications(ne.notifications[n][0],!!a.includes(ne.notifications[n][0])&&ne.notifications[n][1]);e&&e(i),this.finalizeInit(),J.event("MCPopulateConversations",{conversations:i})})))},newConversation:function(e,i=-1,s="",a=[],n=null,o=null,r=!1){Z()?J.ajax({function:"new-conversation",status_code:e,title:A?t.find(".mc-ticket-title input").val():null,department:J.null(n)?this.default_department:n,agent_id:J.null(o)?this.default_agent:o,tags:this.default_tags,source:A?"tk":""},(t=>{if(J.errorValidation(t,"user-not-found"))return void this.addUserAndLogin((()=>{this.newConversation(e,i,s,a,n,o,r)}));let l=new ae([],t.details);this.setConversation(l),(s||a.length)&&this.sendMessage(i,s,a),i!=g&&setTimeout((()=>{this.queue(l.id)}),1e3),Z().conversations.push(l),r&&r(l)})):J.error("activeUser() not setted","MCChat.newConversation")},setConversation:function(e){if(e instanceof ae){let i=Z().conversations;this.conversation=e,this.id_last_message_conversation=this.conversation.getLastMessage()?this.conversation.getLastMessage().id:0,this.datetime_last_message_conversation=0==this.conversation.getLastMessage()?"2000-01-01 00:00:00":this.conversation.getLastMessage().get("creation_time"),e.id!=this.conversation.id&&this.queue(e.id);for(var t=0;t<i.length;t++)if(i[t].id==e.id){i[t]=e;break}Q("open-conversation",e.id),le.dialogflow.typing_enabled=!0,J.event("MCActiveConversationChanged",e)}else J.error("Value not of type MCConversation","MCChat.setConversation")},queue:function(e){if("clear"==e)return t.removeClass("mc-notify-active mc-queue-active"),n.find(" > .mc-notify-message").remove(),clearInterval(this.queue_interval),this.queue_interval=!1,Q("queue",""),void(R.queue_sound&&!ne.tab_active&&ne.playSound(999));!S&&R.queue&&J.ajax({function:"queue",conversation_id:e,department:this.conversation.get("department")},(i=>{n.find(" > .mc-notify-message").remove();let s=i[0];if(0==s)this.queue("clear");else{let a=(R.queue_response_time?parseInt(R.queue_response_time):5)*s,o=K(R.queue_message?R.queue_message:"Please wait for an agent. You are number {position} in the queue. Your waiting time is approximately {minutes} minutes.").replace("{position}","<b>"+s+"</b>").replace("{minutes}","<b>"+a+"</b>");i[1]&&n.prepend(`<div class="mc-notify-message mc-rich-cnt"><div class="mc-cnt"><div class="mc-message">${o}</div></div></div>`),!1===this.queue_interval&&(this.queue_interval=setInterval((()=>{this.queue(e)}),10100),i[1]&&t.addClass("mc-notify-active mc-queue-active"),Q("queue",e))}J.event("MCQueueUpdate",s)}))},getDepartmentCode(e,t){if(this.departments)if("all"==e){let e="";for(var i in this.departments)this.getDepartmentCode(this.departments[i].id,(t=>{e+=t}));t(e)}else t(`<div data-color="${this.departments[e].color}">${this.departments[e].image?`<img loading="lazy" src="${this.departments[e].image}" />`:""}<div>${this.departments[e].name}<div></div>`);else J.ajax({function:"get-departments"},(i=>{i&&(this.departments=i,this.getDepartmentCode(e,t))}))},startRealTime:function(){Y.active||(this.stopRealTime(),this.real_time=setInterval((()=>{this.update(),this.typing(S?Z()?Z().id:-1:this.agent_id,"check")}),1e3))},stopRealTime:function(){clearInterval(this.real_time)},updateUsersActivity:function(){Z()&&J.updateUsersActivity(Z().id,this.agent_id,(t=>{this.typing_settings.typing||("online"==t||this.agent_id==g?(e(c).addClass("mc-status-online").html(K("Online")),this.agent_online=this.agent_id!=g):(e(c).removeClass("mc-status-online").html(K("Away")),this.agent_online=!1))}))},busy:function(e){o&&o.find(".mc-loader").mcActive(e),this.is_busy=e,J.event("MCBusy",e)},headerAgent:function(e=!1){if(!S&&!A&&!this.dashboard&&this.conversation&&(-1==this.agent_id||this.conversation.getLastMessage()&&J.isAgent(this.conversation.getLastMessage().get("user_type"))&&this.conversation.getLastMessage().get("user_id")!=this.agent_id)){let t=this.lastAgent(!1);t=t&&le.dialogflow.humanTakeoverActive()&&Y.active&&!Y.presenceCheck({info:{user_type:"agent"},id:t.user_id})?t:this.lastAgent(),!t&&e&&(t={user_id:R.bot_id,full_name:R.bot_name,profile_image:R.bot_image}),t&&(this.agent_id=t.user_id,this.headerReset(),l.addClass("mc-header-agent").attr("data-agent-id",this.agent_id).html(`<div class="mc-dashboard-btn mc-icon-arrow-left"></div><div class="mc-profile"><img loading="lazy" src="${t.profile_image}" /><div><span class="mc-name">${t.full_name}</span><span class="mc-status">${K("Away")}</span></div><i class="mc-icon mc-icon-close ${R.close_chat?"mc-close-chat":"mc-responsive-close-btn"}"></i></div><div class="mc-label-date-top"></div>`),c=l.find(".mc-status"),this.updateUsersActivity(),this.label_date=l.find(".mc-label-date-top"),J.storageTime("header-animation",1)&&this.headerAnimation())}},headerReset:function(){0==this.start_header&&(this.start_header=[l.html(),l.attr("class")]),l.removeClass("mc-header-main mc-header-brand mc-header-agent mc-header-minimal"),this.main_header=!1},headerAnimation:function(){l.addClass("mc-header-animation"),setTimeout((()=>{l.removeClass("mc-header-animation")}),8e3),J.storageTime("header-animation")},lastAgent:function(e=!0){let t=!1;if(this.conversation){let i=this.conversation.getLastUserMessage(!1,!e||"all");i&&(t={user_id:i.get("user_id"),full_name:i.get("full_name"),profile_image:i.get("profile_image")})}return t},scrollBottom:function(e=!1){setTimeout((()=>{u.scrollTop(e?0:u[0].scrollHeight),this.scrollHeader()}),20)},isBottom:function(){return u[0].scrollTop===u[0].scrollHeight-u[0].offsetHeight},scrollHeader:function(){if(this.main_header&&this.dashboard){let e=u.scrollTop();e>-1&&e<1e3&&l.find(".mc-content").css({opacity:1-e/500,top:e/10*-1+"px"})}},showDashboard:function(){S||A||(t.addClass("mc-dashboard-active"),l.removeClass("mc-header-agent"),this.hidePanel(),this.start_header&&l.html(this.start_header[0]).addClass(this.start_header[1]),u.find(" > div").mcActive(!1),t.find(".mc-dashboard").mcActive(!0),this.populateConversations(),this.conversation=!1,this.agent_id=-1,this.stopRealTime(),this.dashboard=!0,this.main_header=!0,this.scrollBottom(!0),J.event("MCDashboard"))},hideDashboard:function(){S||A||(n.mcActive(!0),t.removeClass("mc-dashboard-active").find(".mc-dashboard").mcActive(!1),this.dashboard=!1,this.headerAgent(),this.scrollHeader(0),this.chat_open&&this.startRealTime(),J.event("MCDashboardClosed"))},showPanel:function(e,i){if(A)return MCTickets.showPanel(e,i);let s=u.find(" > .mc-panel-"+e);s.length&&(u.find(" > div").mcActive(!1),s.mcActive(!0),this.start_header||(this.start_header=[l.html(),l.attr("class")]),l.attr("class","mc-header mc-header-panel").html(`<span>${K(i)}</span><div class="mc-dashboard-btn mc-icon-close"></div>`),t.addClass("mc-panel-active"),this.dashboard=!0),J.event("MCPanelActive",e)},hidePanel:function(){t.removeClass("mc-panel-active"),l.removeClass("mc-header-panel")},clear:function(){this.conversation=!1,n.html("")},updateNotifications:function(e,i=!1){let s=!1;if(i){for(var a=0;a<this.notifications.length;a++)this.notifications[a][0]==e&&i==this.notifications[a][1]&&(s=!0);s||(this.notifications.push([e,i]),!this.dashboard&&this.conversation&&this.conversation.id!=e&&this.headerAnimation())}else{let t=[];for(a=0;a<this.notifications.length;a++)this.notifications[a][0]==e?s=!0:t.push(this.notifications[a]);!S&&s&&["0","2",0,2].includes(this.conversation.status_code)&&this.setConversationStatus(1),this.notifications=t}let n=this.notifications.length;Q("notifications",this.notifications),t.find(".mc-chat-btn span").attr("data-count",n).html(n>-1?n:0),J.event("MCNotificationsUpdate",{conversation_id:e,message_id:i})},setConversationStatus:function(e){return!!this.conversation&&(J.ajax({function:"update-conversation-status",conversation_id:this.conversation.id,status_code:e},(()=>{this.conversation.set("status_code",e),J.event("MCActiveConversationStatusUpdated",{conversation_id:this.conversation.id,status_code:e})})),!0)},typing:function(t=-1,i="check"){if(this.conversation){let s=this.agent_online||S&&this.user_online;if("check"==i&&!Y.active&&-1!=t&&t!=g&&s)J.ajax({function:"is-typing",user_id:t,conversation_id:this.conversation.id},(e=>{e&&!this.typing_settings.typing?this.typing(-1,"start"):!e&&this.typing_settings.typing&&this.typing(-1,"stop")}));else if("set"==i&&s){let e=this.conversation.get("source");clearTimeout(T),e&&(e="fb"==e?[e,Z().getExtra("facebook-id").value,this.conversation.get("extra")]:"tw"==e&&[e,Z().getExtra("twitter-id").value]),Y.active?J.debounce((()=>{Y.trigger("client-typing",{user_id:S?MC_ACTIVE_AGENT.id:Z().id,conversation_id:this.conversation.id}),e&&J.ajax({function:"set-typing",source:e})}),"#2"):this.typing_settings.sent?(clearTimeout(this.typing_settings.timeout),this.typing_settings.timeout=setTimeout((()=>{J.ajax({function:"set-typing",user_id:t,conversation_id:-1},(()=>{this.typing_settings.sent=!1}))}),2e3)):(this.typing_settings.sent=!0,J.ajax({function:"set-typing",user_id:t,conversation_id:this.conversation.id,source:e}),this.typing(t,"set"))}else if("start"==i||"stop"==i){let t="start"==i;if(!S&&c)if(t)e(c).addClass("mc-status-typing").html(K("Typing"));else{let t=this.agent_online||this.agent_id==g;clearTimeout(T),e(c).removeClass("mc-status-typing").html(K(t?"Online":"Away")),t&&e(c).addClass("mc-status-online")}this.typing_settings.typing=t,J.event("MCTyping",t)}}},showArticles:function(e=!1,i=!1){let s=A?t.find(".mc-panel-main .mc-panel"):u.find(" > .mc-panel-articles");s.html("").mcLoading(!0),this.showPanel("articles",R.articles_title?R.articles_title:"Help Center"),!e&&R.articles_categories?this.getArticleCategories((e=>{this.showArticles_(e,s,{categories:e})}),"parent"):this.getArticles(!i&&e,(t=>{e&&!i?(s.html(this.getArticleCode(t[0])),s.mcLoading(!1),J.event("MCArticles",{id:e,articles:t})):this.showArticles_(t,s,{id:e,articles:t})}),!!i&&e)},showArticles_:function(e,t,i){let s="",a=typeof MC_LANG!=N&&MC_LANG[0];for(var n=0;n<e.length;n++){let t="content"in e[n];s+=`<div data-id="${e[n].id}"${t?"":' data-is-category="true"'}><div>${t?e[n].title:a&&e[n].languages&&e[n].languages[a]?e[n].languages[a].title:e[n].title}</div><span>${t?e[n].content:a&&e[n].languages&&e[n].languages[a]?e[n].languages[a].description:e[n].description?e[n].description:""}</span></div>`}t.html(`<div class="mc-articles">${s}</div>`),t.mcLoading(!1),J.event("MCArticles",i)},getArticles:function(e=!1,t=!1,i=!1,s=!1){J.ajax({function:"get-articles",categories:this.articles_category?this.articles_category:i,id:e||this.articles_allowed_ids,count:s,return_categories:!!this.articles_category,full:e,skip_language:!0},(e=>{t(e)}))},getArticleCategories:function(e=!1,t=!1){J.ajax({function:"get-articles-categories",category_type:t},(t=>{e(t)}))},searchArticles:function(t,i,s){t&&(e(i).mcLoading(!0),J.ajax({function:"search-articles",search:t},(t=>{let a="";if(0==t.length)a+=`<p class="mc-no-results">${K("No articles found.")}</p>`;else for(var n=0;n<t.length;n++)a+=`<div data-id="${t[n].id}"><div>${t[n].title}</div><span>${t[n].content}</span></div>`;e(s).html(a),e(i).mcLoading(!1)})))},setArticleRating:function(e,t,i=!1){J.ajax({function:"article-ratings",article_id:e,rating:t},(e=>{i&&i(e)}))},articleRatingOnClick:function(t){let i=e(t).closest(".mc-article");if(!i[0].hasAttribute("data-user-rating")){e(t).parent().mcLoading();let s="positive"==e(t).attr("data-rating")?1:-1,a=e(t).closest(".mc-article").attr("data-id");ne.setArticleRating(a,s,(()=>{J.storage("article-rating-"+a,s),i.attr("data-user-rating",s),e(t).parent().mcLoading(!1)}))}},getArticleCode:function(e){let t=J.storage("article-rating-"+e.id),i="";if(this.articles_categories&&!J.null(e.categories))for(var s=0;s<this.articles_categories.length;s++){let t=this.articles_categories[s].id;(e.categories.includes(t)||e.parent_category==t)&&(i+=`<span data-id="${t}">${K(this.articles_categories[s].title)}</span>`)}return`<div data-id="${e.id}"${t?` data-user-rating="${t}"`:""} class="mc-article"><div class="mc-title">${e.title}<div class="mc-close mc-icon-close"></div></div><div class="mc-content">${e.content.replace(/(?:\r\n|\r|\n)/g,"<br>")}</div>${e.link?`<a href="${e.link}" target="_blank" class="mc-btn-text"><i class="mc-icon-plane"></i>${K("Read more")}</a>`:""}${i?`<div class="mc-article-category-links">${i}</div>`:""}<div class="mc-rating"><span>${K("Rate and review")}</span><div><i data-rating="positive" class="mc-submit mc-icon-like"><span>${K("Helpful")}</span></i><i data-rating="negative" class="mc-submit mc-icon-dislike"><span>${K("Not helpful")}</span></i></div></div></div>`},initArticlesPage:function(){let t=J.getURL("article_id"),i=J.getURL("category"),s=J.getURL("search");if(R.articles_url_rewrite){let e=location.href.replace(R.articles_url_rewrite,"").split("/");"category"==e[e.length-2]&&(i=e[e.length-1]),(2==e.length&&!e[0]&&e[1]||1==e.length&&e[0])&&(t=e[e.length-1]),R.articles_page_url}if((O=e("body").find("#mc-articles")).length||(O=e("body")),O.mcLoading()){let e=MC_URL+"/include/articles.php"+(i?"?category="+i:t?"?article_id="+t:s?"?search="+s:"");P&&(e+=(e.includes("?")?"&":"?")+"cloud="+P),J.loadResource(MC_URL+"/css/articles.css"),J.cors("GET",e,(e=>{O.html(e),O.mcLoading(!1)}))}O.on("keydown",".mc-panel-side input",(function(t){13==t.which&&e(this).next().click()})),O.on("click",".mc-articles > [data-id]",(function(){cache=panel_main.html(),panel_main.mcLoading(!0),ne.getArticles(e(this).attr("data-id"),(e=>{panel_main.removeClass("mc-articles").html(ne.getArticleCode(e)),panel_main.mcLoading(!1)}))})),O.on("click",".mc-article [data-rating]",(function(){ne.articleRatingOnClick(this)})),O.on("click",".mc-article .mc-title .mc-close",(function(){O.find(".mc-panel-main").addClass("mc-articles").html(cache)})),O.on("click",".mc-submit-articles",(function(){let t=e(this).parent().find("input").val();t?(cache=panel_main.html(),panel_main.html(""),ne.searchArticles(t,this,panel_main)):panel_main.html(cache),panel_main.addClass("mc-articles")})),O.on("click",".mc-article-categories [data-id]",(function(){if(X(panel_main))return;let t="";cache=panel_main.html(),O.find(".mc-article-categories [data-id]").mcActive(!1),e(this).mcActive(!0),ne.getArticles(-1,(e=>{for(var i=0;i<e.length;i++)t+=`<div data-id="${e[i].id}"><div>${e[i].title}</div><span>${e[i].content}</span></div>`;panel_main.addClass("mc-articles").html(t||`<p class="mc-no-results">${K("No articles found.")}</p>`),panel_main.mcLoading(!1)}),e(this).attr("data-id"))}))},categoryEmoji:function(e){let t=this.emoji_options.list;if("all"==e)this.emoji_options.list_now=t;else{this.emoji_options.list_now=[];for(var i=0;i<t.length;i++)t[i].category.startsWith(e)&&this.emoji_options.list_now.push(t[i])}this.emoji_options.range=0,this.populateEmoji(0),this.populateEmojiBar()},mouseWheelEmoji:function(e){let t=this.emoji_options.range;(function(e){let t=e.originalEvent.wheelDelta;return typeof t==N&&(t=e.originalEvent.deltaY),typeof t==N&&(t=-1*e.originalEvent.detail),t})(e)>0||j&&typeof e.originalEvent.changedTouches!==N&&this.emoji_options.touch<e.originalEvent.changedTouches[0].clientY?t-=t<1?0:1:t+=t>this.emoji_options.range_limit?0:1,d.find(".mc-emoji-bar > div").mcActive(!1).eq(t).mcActive(!0),this.emoji_options.range=t,this.populateEmoji(t),e.preventDefault()},insertEmoji:function(t){t.indexOf(".svg")>0&&(t=e.parseHTML(t)[0].alt),this.insertText(t),d.mcTogglePopup()},showEmoji:function(e){d.mcTogglePopup(e)&&(S||d.css({left:o.offset().left+(A?68:20),top:o.offset().top-window.scrollY-(A?o.height()-330:304)}),d.find(".mc-emoji-list > ul").html()||jQuery.ajax({method:"POST",url:MC_AJAX_URL,data:{function:"emoji","login-cookie":J.loginCookie()}}).done((e=>{this.emoji_options.list=JSON.parse(e),this.emoji_options.list_now=this.emoji_options.list,this.populateEmoji(0),this.populateEmojiBar()})),J.deselectAll())},populateEmoji:function(e){let t="",i=j?42:48,s=e*i+i,a=this.emoji_options.list_now;s>a.length&&(s=a.length),this.emoji_options.range_limit=a.length/i-1,this.emoji_options.range=e;for(var n=e*i;n<s;n++)t+=`<li>${a[n].char}</li>`;d.find(".mc-emoji-list").html(`<ul>${t}</ul>`)},populateEmojiBar:function(){let e='<div class="mc-active"></div>',t=j?42:49;for(var i=0;i<this.emoji_options.list_now.length/t-1;i++)e+="<div></div>";this.emoji_options.range=0,d.find(".mc-emoji-bar").html(e)},clickEmojiBar:function(t){let i=e(t).index();this.populateEmoji(i),this.emoji_options.range=i,d.find(".mc-emoji-bar > div").mcActive(!1).eq(i).mcActive(!0)},searchEmoji:function(e){J.search(e,(()=>{if(e.length>1){let i=this.emoji_options.list,s=[];for(var t=0;t<i.length;t++)(i[t].category.toLowerCase().includes(e)||i[t].name.toLowerCase().includes(e))&&s.push(i[t]);this.emoji_options.list_now=s}else this.emoji_options.list_now=this.emoji_options.list;this.emoji_options.range=0,this.populateEmoji(0),this.populateEmojiBar()}))},textareaChange:function(t){let i=e(t).val();S&&(MCAdmin.conversations.savedReplies(t,i),MCAdmin.apps.openAI.rewriteButton(i)),i&&this.typing(S&&!Y.active?MC_ACTIVE_AGENT.id:Z().id,"set"),o.mcActive(i)},insertText:function(t){let i=e(r.get(0)),s=0;if(this.dashboard)return!1;if(i.get(0).selectionStart)s=i.get(0).selectionStart;else if(document.selection){i.focus();let e=document.selection.createRange();var a=document.selection.createRange().text.length;e.moveStart("character",-i.value.length),s=e.text.length-a}i.val(i.val().substr(0,s)+t+i.val().substr(s)),i.focus(),i.manualExpandTextarea(),o.mcActive(!0)},enabledAutoExpand:function(){r.length&&r.autoExpandTextarea()},privacy:function(){J.ajax({function:"get-block-setting",value:"privacy"},(e=>{u.append(`<div class="mc-privacy mc-init-form" data-decline="${e.decline}"><div class="mc-title">${e.title}</div><div class="mc-text">${e.message.replace(/\n/g,"<br>")}</div>`+(e.link?`<a target="_blank" href="${e.link}">${e["link-name"]}</a>`:"")+`<div class="mc-buttons"><a class="mc-btn mc-approve">${e["btn-approve"]}</a><a class="mc-btn mc-decline">${e["btn-decline"]}</a></div></div>`),this.finalizeInit(),J.event("MCPrivacy")})),this.dashboard||this.showDashboard(),this.dashboard=!0,t.addClass("mc-init-form-active")},popup:function(e=!1,i=!1){if(e){let e=t.find(".mc-popup-message"),i=e.attr("data-id");return Q("popup"+(J.null(i)?"":i),!0),void e.remove()}setTimeout((()=>{this.chat_open||(0==i&&(i=R.popup),t.find(".mc-popup-message").remove(),t.append(`<div data-id="${i.id?i.id:""}" class="mc-popup-message">`+(i.image?`<img loading="lazy" src="${i.image}" />`:"")+(i.title?`<div class="mc-top">${i.title}</div>`:"")+`<div class="mc-text">${i.message}</div><div class="mc-icon-close"></div></div>`),J.event("MCPopup",i))}),1e3)},followUp:function(){this.followUpCheck()&&(k=setTimeout((()=>{this.followUpCheck()&&J.ajax({function:"execute-bot-message",conversation_id:this.conversation.id,name:"follow_up",check:!1},(e=>{e.settings.sound&&this.audio&&this.audio.play(),this.skip=!0,J.storageTime("email"),J.event("MCFollowUp")}))}),!0===R.follow?R.office_hours||M?15e3:5e3:parseInt(R.follow)))},followUpCheck:function(){return!S&&this.conversation&&R.follow&&Z()&&!Z().email&&J.storageTime("email",24)},welcome:function(){A||"open"==R.welcome_trigger&&!this.chat_open||!R.office_hours&&R.welcome_disable_office_hours||!R.welcome||Q("welcome")||!Z()||J.ajax({function:"get-block-setting",value:"welcome"},(e=>{setTimeout((()=>{R.dialogflow_welcome?!1===this.conversation?this.newConversation(3,-1,"",[],null,null,(function(){le.dialogflow.welcome(e.open,e.sound)})):le.dialogflow.welcome(e.open,e.sound):(this.sendMessage(g,e.message,[],!1,!1,3),e.open&&!j&&this.start(),e.sound&&this.audio.play()),this.skip=!0,J.event("MCWelcomeMessage")}),parseInt(A?0:R.welcome_delay)),Q("welcome",!0)}))},offlineMessage:function(){if(!S&&R.timetable&&(!R.office_hours||!M&&!R.timetable_disable_agents)){let e=R.timetable_message;switch(R.timetable_type){case"header":this.offline_message_set||(e[0]&&l.find(".mc-title").html(e[0]),l.find(".mc-text").html(e[1]),this.offline_message_set=!0);break;case"info":this.offline_message_set||(n.prepend(`<div class="mc-notify-message mc-rich-cnt"><div class="mc-cnt"><div class="mc-message">${e[0]?`<b>${e[0]}</b> `:""}${e[1]}</div></div></div>`),t.addClass("mc-notify-active"),this.offline_message_set=!0);break;default:setTimeout((()=>{if(this.conversation){let t=R.timetable_hide?`${e[0]?`*${e[0]}*\n`:""}${e[1]}`:"[timetable]",i=this.conversation.searchMessages(t,!0);if(i=!!i.length&&i,i){let e=this.conversation.getLastUserMessage(!1,!0);i=!e||(e.get("index")<i[i.length-1].get("index")&&Date.now()-36e5)<J.unix(i[0].get("creation_time"))}i||this.sendMessage(g,t)}}),5e3)}}},slackMessage:function(e,t,i,s,a=[]){if(!this.conversation||!s&&!a.length)return!1;let n=this.conversation.id;J.ajax({function:"send-slack-message",user_id:e,full_name:t,profile_image:i,conversation_id:n,message:s,attachments:a,channel:this.slack_channel[0]==Z().id&&this.slack_channel[1]},(e=>{this.slack_channel=[Z().id,e[1]],J.event("MCSlackMessageSent",{message:s,conversation_id:n,slack_channel:e[1]})}))},deleteMessage:function(e){J.ajax({function:"delete-message",message_id:e},(()=>{this.conversation&&this.conversation.deleteMessage(e),n.find(`[data-id="${e}"]`).remove(),J.event("MCMessageDeleted",e)}))},registration:function(e=!1,i=R.registration_required){if(e)return R.registration_required&&(!R.registration_offline||!M)&&(typeof MC_DEFAULT_USER==N||!MC_DEFAULT_USER.email)&&(!R.registration_timetable||!R.office_hours)&&(!1===Z()||["visitor","lead"].includes(Z().type));u.append(oe.generate({},R.registration_link||"registration-login"==R.registration_required?"login":i,"mc-init-form")),this.dashboard||this.showDashboard(),this.dashboard=!0,this.finalizeInit(),t.addClass("mc-init-form-active")},addUserAndLogin:function(e=!1,t=!1){let i=typeof MC_DEFAULT_USER!=N&&MC_DEFAULT_USER?MC_DEFAULT_USER:{};i.user_type=t?"lead":"visitor",J.ajax({function:"add-user-and-login",settings:i,settings_extra:i.extra},(i=>{if(J.errorValidation(i)){if("duplicate-email"==i[1]||"duplicate-phone"==i[1])return delete MC_DEFAULT_USER.email,delete MC_DEFAULT_USER.extra.phone,this.addUserAndLogin(e,t)}else J.loginCookie(i[1]),Z(new ie(i[0])),Y.start(),Y.active||ne.automations.runAll(),e&&e(i)}))},isInitDashboard:function(){return R.init_dashboard||Z()&&Z().conversations.length>1},uploadResponse:function(t){if("success"==(t=JSON.parse(t))[0])if("extension_error"==t[1]){let e="The file you are trying to upload has an extension that is not allowed.";S?MCAdmin.infoPanel(e,"info"):alert(e)}else if(e(s).hasClass("mc-input-image")){let i=e(s).find(".image");i.attr("data-value")&&J.ajax({function:"delete-file",path:i.attr("data-value")}),i.attr("data-value","").css("background-image",""),setTimeout((()=>{i.attr("data-value",t[1]).css("background-image",`url("${t[1]}?v=${J.random()}")`).append('<i class="mc-icon-close"></i>'),s=!1}),500)}else{let e=J.beautifyAttachmentName(t[1].substr(t[1].lastIndexOf("/")+1));o.find(".mc-attachments").append(`<div data-name="${e}" data-value="${t[1]}"${t.length>2?' data-size="'+t[2][0]+"|"+t[2][1]+'"':""}>${e}<i class="mc-icon-close"></i></div>`),o.mcActive(!0)}else J.error(t[1],"mc-upload-files.change");this.busy(!1)},closeChat:function(e=!0){let t=this.conversation.id;ne.clear(),e?J.ajax({function:"update-conversation-status",conversation_id:t,status_code:3},(()=>{this.closeChat_(t)})):this.closeChat_(t)},closeChat_(e){t.find(`li[data-conversation-id="${e}"]`).remove(),L="new-conversation",ne.clear(),Q("open-conversation",""),Q("welcome",""),Q("flow_on_load",""),Z().removeConversation(e),R.disable_dashboard||ne.showDashboard()},conversationArchived:function(){let e=!A&&R.close_chat||A&&R.tickets_close;if(e&&!R.rating)return this.closeChat(!1);R.rating&&oe.rating(e)},automations:{history:[],busy:[],scroll_position_intervals:{},timeout_queue:[],runAll:function(){let t=R.automations;for(var i=0;i<t.length;i++){let n=t[i],o=n.conditions,r=0==o.length,l=!1,c=!1,d=!1;for(var s=0;s<o.length;s++){let t=o[s][1];switch(r=!1,o[s][0]){case"browsing_time":r=!0,l=t;break;case"scroll_position":r=!0,c=t;break;case"referring":case"url":let i="referring"==o[s][0]?document.referrer:window.location.href,n=o[s][2].replace(/https?:\/\/|www\./g,"").split(",");i=i.replace(/https?:\/\/|www\./g,"");for(var a=0;a<n.length;a++)if(i.includes(n[a])){r="contains"==t;break}break;case"include_urls":case"exclude_urls":let u="referring"==o[s][0]?document.referrer:window.location.href,h=o[s][2].replace(/https?:\/\/|www\./g,"").split(","),g="exclude_urls"!=o[s][0];g||(r=!0),u=u.replace(/https?:\/\/|www\./g,"");for(a=0;a<h.length;a++)if(h[a]=e.trim(h[a].replace("https://","").replace("http://","").replace("www.","")),"contains"==t&&-1!=u.indexOf(h[a])||"does-not-contain"==t&&-1==u.indexOf(h[a])||"is-exactly"==t&&h[a]==u||"is-not"==t&&h[a]!=u){r=g;break}break;case"custom_variable":let p=t.split("=");p[0]in window&&window[p[0]]==p[1]&&(r=!0);break;case"returning_visitor":case"user_type":case"cities":case"languages":case"countries":case"postal_code":case"website":case"company":case"creation_time":case"last_activity":r=Z(),d=!0;break;case"phone":r=Z()&&Z().getExtra("phone");break;case"email":r=Z()&&Z().email;break;default:r=Z()&&Z().getExtra(o[s][0])}if(!r)break}["messages","emails","sms"].includes(n.type)&&!Z()&&(r=!1),r&&(d?n.id in this.busy||(J.ajax({function:"automations-validate",automation:n},(e=>{!1!==e&&this.runAll_final(n,c,l),delete this.busy[n.id]})),this.busy[n.id]=!0):"messages"==n.type&&ne.registration(!0)||this.runAll_final(n,c,l))}},runAll_final:function(t,i,s){i?this.scroll_position_intervals[t.id]=setInterval((()=>{e(window).scrollTop()>parseInt(i)&&(s?setTimeout((()=>{this.run(t)}),1e3*parseInt(s)):this.run(t),clearInterval(this.scroll_position_intervals[t.id]))}),1e3):s?this.timeout_queue.includes(t.id)||(setTimeout((()=>{this.run(t)}),1e3*parseInt(s)),this.timeout_queue.push(t.id)):this.run(t)},run:function(e){if(!this.history.includes(e.id))switch(e.type){case"messages":case"emails":case"sms":if((!Y.active||Y.started)&&!(e.id in this.busy)){if("messages"==e.type&&ne.chat_open){let e=!!ne.conversation&&ne.conversation.getLastUserMessage(!1,"no-bot");if(e&&Date.now()-6e5<J.unix(e.get("creation_time")))return}J.ajax({function:"automations-run",automation:e},(t=>{!1!==t&&(this.history.push(e.id),"messages"!=e.type||Y.active||ne.updateConversations()),delete this.busy[e.id]})),this.busy[e.id]=!0}break;case"popups":Q("popup"+e.id)||setTimeout((()=>{if(ne.chat_open){if(e.fallback){let t=!!ne.conversation&&ne.conversation.getLastUserMessage(!1,"no-bot");(!t||Date.now()-6e5>J.unix(t.get("creation_time")))&&(ne.sendMessage(g,(J.null(e.title)?"":`*${e.title}*\n`)+e.message,[],!1,!1,0),Q("popup"+e.id,!0),this.history.push(e.id))}}else ne.popup(!1,{id:e.id,image:e.profile_image,title:e.title,message:e.message}),this.history.push(e.id)}),1e3);break;case"design":e.background&&l.css("background-image",`url("${e.background}")`),e.brand&&l.find(".mc-brand img").attr("src",e.brand),e.title&&l.find(".mc-title").html(e.title),e.message&&l.find(".mc-text").html(e.message),e.icon&&t.find(".mc-chat-btn .mc-icon").attr("src",e.icon),(e.color_1||e.color_2||e.color_3)&&J.ajax({function:"chat-css",color_1:e.color_1,color_2:e.color_2,color_3:e.color_3},(e=>{i.append(`<style>${e}</style>`)})),this.history.push(e.id);break;case"more":let s={};if(e.department&&(ne.default_department=e.department,s={function:"update-conversation-department",department:e.department}),e.agent&&(ne.default_agent=e.agent,s={function:"update-conversation-agent",agent_id:e.agent}),e.tags&&(e.tags=e.tags.split(","),ne.default_tags=e.tags,s={function:"update-tags",tags:e.tags,add:!0}),ne.conversation.id&&(e.tags||e.agent||e.department)&&(s.conversation_id=ne.conversation.id,J.ajax(s)),e.articles||e.articles_category){let i=t.find(".mc-dashboard-articles > .mc-articles");ne.articles_allowed_ids=e.articles,ne.articles_category=e.articles_category,i&&ne.getArticles(e.articles,(e=>{let t="";for(var s=0;s<2;s++)t+=`<div data-id="${e[s].id}"><div>${e[s].title}</div><span>${e[s].content}</span></div>`;i.html(t)}),e.articles_category,2)}this.history.push(e.id)}}},flashNotification:function(){clearInterval(C),C=setInterval((function(){ne.notifications.length&&(document.title=document.title==I?"("+ne.notifications.length+") "+K("New message"+(ne.notifications.length>1?"s":"")):I)}),2e3)},calculateLabelDates:function(){(S||this.chat_open)&&(x=n.find(".mc-label-date"))},calculateLabelDateFirst:function(){this.conversation.messages.length||n.append(`<div class="mc-label-date"><span>${K("Today")}</span></div>`)},playSound:function(e=!1){this.audio.play();let t=e||(S?MC_ADMIN_SETTINGS.sound.repeat:R.sound.repeat);t&&!this.tab_active&&(clearInterval(this.audio_interval),this.audio_interval=setInterval((()=>{this.audio.play(),t--,t||clearInterval(this.audio_interval)}),1e3*this.audio.duration+1500))},isConversationAllowed:function(e,t){return(!R.tickets_hide||A&&"tk"==e||!A&&"tk"!=e)&&(![3,4,"3","4"].includes(t)||!A&&!R.close_chat||A&&!R.tickets_close)}};window.MCChat=ne;var oe={rich_messsages:{email:"",button:"",video:"",image:"",woocommerce_button:"",rating:"",chips:'<div class="mc-buttons">[options]</div>',buttons:'<div class="mc-buttons">[options]</div>',select:'<div class="mc-select"><p></p><ul>[options]</ul></div>',list:'<div class="mc-text-list">[values]</div>',"list-image":'<div class="mc-image-list">[values]</div>',table:"<table><tbody>[header][values]</tbody></table>",inputs:'<div class="mc-form">[values]</div>',card:'<div class="mc-card">[settings]</div>',share:'<div class="mc-social-buttons">[settings]</div>',slider:'<div class="mc-slider"><div>[items]</div></div><div class="mc-slider-arrow mc-icon-arrow-left[class]"></div><div class="mc-slider-arrow mc-icon-arrow-right mc-active[class]"></div>',"slider-images":'<div class="mc-slider mc-slider-images"><div>[items]</div></div><div class="mc-slider-arrow mc-icon-arrow-left[class]"></div><div class="mc-slider-arrow mc-icon-arrow-right mc-active[class]"></div>'},cache:{},duplicated_email:!1,generate:function(i,s,a=""){let o,r=!0,l=i.id?i.id:J.random(),c=new se({});if(s in this.rich_messsages)o=this.rich_messsages[s];else if(s in this.cache)o=this.cache[s];else{if(!this.isShortcode(s))return!1;i.id||(l=s),o='<div class="mc-rich-loading mc-loading"></div>',J.ajax({function:"get-rich-message",name:s,settings:i},(a=>{a=this.initInputs(a),"timetable"==s&&(a=this.timetable(a)),e(S&&"chatbot"==MCAdmin.active_admin_area?".mc-playground":t).find(`.mc-rich-message[id="${l}"]`).html(`<div class="mc-content">${a}</div>`),this.cache[s]=a,ne.scrollBottom(ne.dashboard),J.event("MCRichMessageShown",{name:s,settings:i,response:a})})),r=!1}let d=i.disabled;if(r){let t,a="";switch(s){case"email":let r=[],l=Z().email,c="#"==Z().get("last_name").charAt(0);"true"==i.name&&r.push(["first_name","true"==i["last-name"]?"First name":"Name",c?"":"true"==i["last-name"]?Z().get("first_name"):Z().name,"text",!0]),"true"==i["last-name"]&&r.push(["last_name","Last name",c?"":Z().get("last_name"),"text",!0]);for(var u=0;u<r.length;u++)o+=`<div id="${r[u][0]}" data-type="text" class="mc-input mc-input-text"><span class="${r[u][2]?"mc-active mc-filled":""}">${K(r[u][1])}</span><input value="${r[u][2]}" autocomplete="false" type="${r[u][3]}" ${r[u][4]?"required":""}></div>`;if("true"==i.phone){let e=Z().getExtra("phone");this.cache.phone||(this.cache.phone=!0,J.ajax({function:"get-select-phone"},(e=>{this.cache.phone=e,n.find("#phone .mc-select-phone").html(e)}))),o+=`<div id="phone" data-type="select-input" class="mc-input mc-input-select-input"><span class="${e?"mc-active mc-filled":""}">${K("Phone")}</span><div class="mc-select-phone">${this.cache.phone?this.cache.phone:""}</div><input autocomplete="false" type="text" data-phone="true"${"false"!=i["phone-required"]?" required":""}></div>`}o+=`<div id="email" data-type="email" class="mc-input mc-input-btn"><span class="${l?"mc-active mc-filled":""}">${K(J.null(i.placeholder)?"Email":i.placeholder)}</span><input value="${l}" autocomplete="off" type="email" required><div class="mc-submit mc-icon-arrow-right"></div></div>`;break;case"image":o=`<div class="mc-image"><img loading="lazy" src="${i.url}"></div>`;break;case"video":o=`<iframe loading="lazy"${i.height?` height="${i.height}"`:""} src="https://${"youtube"==i.type?"www.youtube.com/embed/":"player.vimeo.com/video/"}${i.id}" allowfullscreen></iframe>`;break;case"select":t=i&&i.options?i.options.replace(/\\,/g,"{R}").split(","):[];for(u=0;u<t.length;u++){let e=t[u].replace(/{R}/g,",");a+=`<li data-value="${J.stringToSlug(e)}">${K(e)}</li>`}o=o.replace("[options]",a);break;case"chips":case"buttons":t=i&&i.options?i.options.replace(/\\,/g,"{R}").split(","):[];for(u=0;u<t.length;u++)a+=`<div class="mc-btn mc-submit">${K(t[u].replace(/{R}/g,","))}</div>`;o=o.replace("[options]",a);break;case"button":if(i&&i.link){let e=i.link.includes("calendly.com");o=`<a ${e?'data-action="calendly" data-extra="'+i.link+"|"+(i.success?i.success.replaceAll('"',"'"):"")+'" ':""}href="${e?"#":i.link.replace(/<i>/g,"_").replace(/<\/i>/g,"_")}"${i.target&&!e?' target="_blank"':""} class="mc-rich-btn mc-btn${"link"==i.style?"-text":""}">${K(i.name)}</a>`}break;case"list":if(i.values){t=i.values.replace(/\\,/g,"{R}").replace(/\\:/g,"{R2}").replace(/:\/\//g,"{R3}").split(",");let n="list"==s,r=n&&t.length&&t[0].indexOf(":")>0;n&&!r&&(o=o.replace("mc-text-list","mc-text-list mc-text-list-single")),i.numeric&&(o=o.replace("mc-text-list","mc-text-list mc-text-list-numeric"));for(u=0;u<t.length;u++){let i=t[u].replace(/{R}/g,","),s="-"===i.substr(0,1);a+=r&&i.includes(":")?`<div><div>${K(i.split(":")[0].replace(/{R2}/g,":").replace(/{R3}/g,"://"))}</div><div>${K(i.split(":")[1].replace(/{R2}/g,":").replace(/{R3}/g,"://"))}</div></div>`:`<div${s?' data-inner="true"':""}>${e.trim(K((s?i.substr(1):i).replace(/{R2}/g,":").replace(/{R3}/g,"://")))}</div>`}o=o.replace("[values]",a)}break;case"list-image":if(i.values){t=i.values.split(",");for(u=0;u<t.length;u++){let e=t[u].replace("://","///").split(":");a+=`<div><div class="mc-thumb" style="background-image:url('${e[0].replace("///","://")}')"></div><div class="mc-list-title">${e[1]}</div><div>${e[2]}</div></div>`}o=o.replace("[values]",a)}break;case"table":if(i.values){t=i.header.split(","),a+="<tr>";for(u=0;u<t.length;u++)a+=`<th>${t[u]}</th>`;a+="</tr>",o=o.replace("[header]",a),a="",t=i.values.split(",");for(u=0;u<t.length;u++){let e=t[u].split(":");a+="<tr>";for(var h=0;h<e.length;h++)a+=`<td>${e[h]}</td>`;a+="</tr>"}o=o.replace("[values]",a)}break;case"inputs":if(i.values){t=i.values.split(",");for(u=0;u<t.length;u++)d&&!t[u]||(a+=`<div id="${J.stringToSlug(t[u])}" data-type="text" class="mc-input mc-input-text"><span>${K(t[u])}</span><input autocomplete="false" type="text" required></div>`);a+='<div class="mc-btn mc-submit">'+K(i.button?i.button:"Send now")+"</div>",o=o.replace("[values]",a)}break;case"card":a=`${i.image?`<div class="mc-card-img" style="background-image:url('${i.image}')"></div>`:""}<div class="mc-card-header">${i.header}</div>${i.extra?`<div class="mc-card-extra">${i.extra}</div>`:""}${i.description?`<div class="mc-card-description">${i.description}</div>`:""}${i.link?`<a class="mc-card-btn" href="${i.link}"${i.target?' target="_blank"':""}>${K(i["link-text"])}</a>`:""}`,o=o.replace("[settings]",a);break;case"share":let g=i.channels?i.channels.replace(/ /g,"").split(","):["fb","tw","li","wa","pi"],p="";for(u=0;u<g.length;u++){switch(g[u]){case"fb":p="www.facebook.com/sharer.php?u=";break;case"tw":p="twitter.com/intent/tweet?url=";break;case"li":p="www.linkedin.com/sharing/share-offsite/?url=";break;case"wa":p="web.whatsapp.com/send?text=";break;case"pi":p="www.pinterest.com/pin/create/button/?url="}a+=`<div class="mc-${g[u]} mc-icon-social-${g[u]}" data-link="https://${p}${encodeURIComponent(i[g[u]])}"></div>`}o=o.replace("[settings]",a);break;case"slider":let f=0;for(u=1;u<16&&"header-"+u in i;u++)a+=`<div>${"image-"+u in i?`<div class="mc-card-img" style="background-image:url('${i["image-"+u]}')"></div>`:""}<div class="mc-card-header">${i["header-"+u]}</div>${"extra-"+u in i?`<div class="mc-card-extra">${i["extra-"+u]}</div>`:""}${"description-"+u in i?`<div class="mc-card-description">${i["description-"+u]}</div>`:""}${"link-"+u in i?`<a class="mc-card-btn" href="${i["link-"+u]}"${i.target?' target="_blank"':""}>${K(i["link-text-"+u])}</a>`:""}</div>`,f++;o=o.replace("[items]",a).replace(/\[class\]/g,1==f?" mc-hide":"");break;case"slider-images":if(i.images){let e=i.images.split(",");for(u=0;u<e.length;u++)a+=`<div class="mc-card-img" data-value="${e[u]}" style="background-image:url('${e[u]}')"></div>`;o=o.replace(/\[class\]/g,1==e.length?" mc-hide":"")}o=o.replace("[items]",a);break;case"woocommerce_button":i.settings=`checkout:${i.checkout},coupon:${i.coupon}`,o=`<a href="#" data-ids="${i.ids}" class="mc-rich-btn mc-btn">${i.name}</a>`;break;case"rating":o=`<div class="mc-rating-message mc-rating-${1==i.value?"positive":"negative"}"><div><i class="mc-icon-${1==i.value?"like":"dislike"}"></i> ${K(1==i.value?"Helpful":"Not helpful")}</div>${i.message?"<div>"+i.message+"</div>":""}</div>`,i.message=!1}}return`<div id="${l}" data-type="${s}"${d?'disabled="true"':""}${i.settings?` data-settings="${i.settings}"`:""}class="mc-rich-message mc-rich-${s} ${a}">`+(i.title?`<div class="mc-top">${c.render(K(i.title))}</div>`:"")+(i.message?`<div class="mc-text">${c.render(K(i.message))}</div>`:"")+`<div class="mc-content">${o}</div>${"email"==s?`<div data-success="${i.success?i.success.replace(/"/g,""):""}" class="mc-info"></div>`:""}</div>`},submit:function(i,s,a){if(!S&&!X(a)&&!this.is_busy){let o="",r="",l={},c=e(i).find("[data-success]").length?e(i).find("[data-success]").attr("data-success"):"",d=e(i).closest(".mc-rich-message").attr("id"),u=e(i).closest("[data-id]").attr("data-id"),h="",f={"rich-messages":{}},m=0==Z()?{profile_image:"",first_name:"",last_name:"",email:"",password:"",user_type:""}:{profile_image:Z().image,first_name:Z().get("first_name"),last_name:Z().get("last_name"),email:Z().email,password:"",user_type:""},v={},b=e(a),_="",y=!1,w=!1!==ne.conversation,S={},A={},k=i.find("#otp");if(J.null(u))u=-1;else{let e=ne.conversation.getMessage(u);h=e.message,f=e.payload(),f["rich-messages"]||(f["rich-messages"]={})}switch(e(a).hasClass("mc-btn")||e(a).hasClass("mc-select")||e(a).hasClass("mc-submit")||(b=e(a).closest(".mc-btn,.mc-select")),e(i).find(".mc-info").html("").mcActive(!1),s){case"email":if(v=re.getAll(i),e.each(v,(function(e,t){v[e]=t[0]})),v.first_name&&(m.user_type="user",v.last_name||(m.last_name="")),v.phone&&(S={phone:[v.phone,"Phone"]}),e.extend(m,v),o="Please fill in all required fields and make sure the email is valid.",c&&(c=K(c).replace("{user_email}",m.email).replace("{user_name_}",m.first_name+(v.last_name?" "+m.last_name:""))),k.mcActive()){let e=k.attr("data-otp");v.otp=!!e&&[e,i.find("#otp input").val()]}f["rich-messages"][d]={type:s,result:v},f.event="update-user",l={function:"update-user-and-message",settings:m,settings_extra:S,payload:f},y={settings:m,settings_extra:S};break;case"registration":if(v=re.getAll(i.find(".mc-form-main")),S=re.getAll(i.find(".mc-form-extra")),e.each(v,(function(e,t){v[e]=t[0]})),e.extend(m,v),A=e.extend({},m),c&&(c=K(c)),R.registration_details){for(var n in c+='[list values="',m){let e=m[n].replace(/:|,/g,"");e&&("profile_image"==n&&(e=e.substr(e.lastIndexOf("/")+1)),["password","password-check","envato-purchase-code","otp"].includes(n)?(e="********",A[n]="********"):c+=m[n]?`${K(J.slugToString(n.replace("first_name","name")))}:${e},`:"")}for(var n in S)S[n][0]&&(c+=`${K(S[n][1].replace(/:|,/g,""))}:${S[n][0].replace(/:|,/g,"")},`);c=c.slice(0,-1)+'"]'}if(k.mcActive()){let e=k.attr("data-otp");m.otp=!!e&&[e,i.find("#otp input").val()]}m.user_type="user",f["rich-messages"][d]={type:s,result:{user:A,extra:S}},f.event="update-user",l=R.registration_otp&&m.email&&!m.otp?{function:"otp",email:m.email}:{function:Z()?"update-user-and-message":"add-user-and-login",settings:m,settings_extra:S,payload:f},o=re.getRegistrationErrorMessage(i),y={settings:m,settings_extra:S};break;case"chips":case"select":case"buttons":v=J.escape(e(a).html()),c&&(c=K(c)+` *${v}*`),f["rich-messages"][d]={type:s,result:v},l={function:"update-message",payload:f},_=v,"chips"==s&&(ne.sendMessage(Z().id,v,[],!1,{id:d,event:"chips-click",result:v},"mc-human-takeover"==d&&0==b.index()&&2),"mc-human-takeover"==d&&0==e(a).index()&&le.dialogflow.humanTakeover(),e(a).closest(".mc-content").remove());break;case"inputs":if(v=re.getAll(i),o="All fields are required.",c){for(var n in c=K(c)+' [list values="',v)c+=`${K(v[n][1].replace(/:|,/g,""))}:${v[n][0].replace(/:|,/g,"")},`;c=c.slice(0,-1)+'"]'}f["rich-messages"][d]={type:s,result:v},l={function:"update-message",payload:f},y={settings:v}}if(r=h.substr(h.indexOf("["+s)),r=r.substr(0,r.indexOf("]")+1),o&&re.errors(i))return re.showErrorMessage(i,o),b.mcLoading(!1),(ne.dashboard||w&&ne.conversation.getLastMessage().id==u)&&ne.scrollBottom(),!1;if(!c&&"registration"!=s){let e=this.shortcode(r),t=e[1].id?`id="${e[1].id}"`:"",i=e[1].title?`title="${e[1].title}"`:"",a=e[1].message?`message="${e[1].message}"`:"",o="";if(["inputs","email"].includes(s)){for(var n in v)o+=v[n]+",";o=`values="${o.slice(0,-1)}"`}else o=`options="${v}"`;c=`[${"email"==s?"inputs":s} ${t} ${i} ${a} ${o} disabled="true"]`}-1!=u&&(c=c.replace(/<br>/g,"\n"),e.extend(l,{message_id:u,message:h?"chips"==s?h.replace("]",' disabled="true"]'):h.replace(r,c):c,payload:f})),J.ajax(l,(n=>{if(n&&!J.errorValidation(n)){switch(s){case"email":for(var o in m)Z().set(o,m[o]);for(var o in S)Z().setExtra(o,S[o][0]);J.loginCookie(n[1]),"mc-follow-up"==d&&J.ajax({function:"subscribe-email",email:Z().email}),ne.automations.runAll(),J.event("MCNewEmailAddress",{id:d,name:Z().name,email:Z().email});break;case"registration":let s=i.find("#otp");if(R.registration_otp&&!s.mcActive())return s.attr("data-otp",n).mcActive(!0),s.find("input").attr("required",!0).addClass("mc-error"),re.showErrorMessage(i,K("Please check your email for the one-time code.")),ne.scrollBottom(),void b.mcLoading(!1);if(J.loginCookie(n[1]),m.id=n[0].id,Z()){for(var o in m)Z().set(o,m[o]);for(var o in S)Z().setExtra(o,S[o][0]);ne.automations.runAll(),ne.welcome()}else{for(var o in Z(new ie(n[0])),S)Z().setExtra(o,S[o][0]);this.duplicated_email&&!R.init_dashboard&&(L="open-conversation"),Y.start(),ne.initChat(),this.duplicated_email||R.init_dashboard&&t.find(".mc-departments-list").length||!c||ne.sendMessage(g,c,[],!1,!1,3)}ne.dashboard&&(t.removeClass("mc-init-form-active"),e(i).remove(),ne.isInitDashboard()||R.init_dashboard&&this.duplicated_email||ne.hideDashboard()),R.wp_registration&&m.email&&m.password?le.wordpress.ajax("wp-registration",{user_id:n[0].id,first_name:n[0].first_name,last_name:n[0].last_name,password:m.password,email:m.email}):"wp"==R.wp_users_system&&le.wordpress.ajax("wp-login",{user:m.email,password:m.password}),delete this.cache.registration,setTimeout((()=>{J.event("MCRegistrationForm",{id:d,conversation_id:!!ne.conversation&&ne.conversation.id,user:m,extra:f["rich-messages"][d].result.extra})}),5e3);break;case"buttons":ne.scrollBottom()}-1==u?e(a).closest(".mc-rich-message").html(c):(b.mcLoading(!1),f.type&&"close-message"==f.type||p||ne.setConversationStatus(2)),["login","chips"].includes(s)||!R.dialogflow_send_user_details&&["email","registration"].includes(s)||le.dialogflow.message(`${d}${_?"|"+_:""}`,[],!1,y),!R.slack_active||p&&!le.dialogflow.humanTakeoverActive()||ne.slackMessage(Z().id,Z().name,Z().image,c),Y.active&&ne.update(),"registration"!=s&&"email"!=s&&J.event("MCRichMessageSubmit",{result:n,data:f["rich-messages"][d],id:d})}else"registration"==s&&J.errorValidation(n,"duplicate-email")?(J.ajax({function:"otp",email:m.email},(e=>{let t=i.find("#otp");t.attr("data-otp",e).mcActive(!0),t.find("input").attr("required",!0).addClass("mc-error"),re.showErrorMessage(i,K("This email address is already registered. Please check your email for the one-time code to log in.")),i.find(".mc-submit").html(K("Sign in")),ne.scrollBottom()})),this.duplicated_email=!0):(this.duplicated_email=!1,re.showErrorMessage(i,re.getRegistrationErrorMessage(n,"response"))),ne.dashboard&&ne.scrollBottom(),b.mcLoading(!1)}))}},shortcode:function(t){let i={},s=t.includes(" ")?t.substr(1,t.indexOf(" ")-1):t.slice(1,-1);if(/\?|"|'|`|\*/gi.test(s))return[!1,!1];let a=(t=t.slice(1,-1).substr(s.length+1)).split('" ');for(var n=0;n<a.length;n++)if(a[n].includes("=")){let t=[a[n].substr(0,a[n].indexOf("=")),a[n].substr(a[n].indexOf("=")+2)];i[e.trim(t[0])]=t[1].replace(/"/g,"")}return[s,i]},initInputs:function(t){return(t=e(e.parseHTML("<div>"+t+"</div>"))).find(".mc-input input").each((function(){e(this).val()&&e(this).siblings().addClass("mc-active mc-filled")})),t.html()},timetable:function(t){let i=e(e.parseHTML(`<div>${t}</div>`)),s=i.find("[data-offset]").attr("data-offset");return s=J.null(s)?0:parseFloat(s),i.find("[data-time]").each((function(){let a=e(this).attr("data-time").split("|");t="";for(var n=0;n<a.length;n++){if("closed"==a[n]){t+=K("Closed");break}if(a[n]){let e=a[n].split(":"),i=J.convertUTCDateToLocalDate(`01/01/2000 ${e[0]}:${e[1]}`,s);t+=i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})+(0==n||2==n?`<span>${K("to")}</span>`:1==n&&a[n+1]?"<br />":"")}}i.find(" > div > span").html(`<i class="mc-icon-clock"></i> ${K("Time zone")} ${Intl.DateTimeFormat().resolvedOptions().timeZone}`),e(this).html(t)})),i.html()},sliderChange:function(e,t="left"){let i=n.find(`#${e}`);if(i.length&&!i.hasClass("mc-moving")){let e=i.find(".mc-slider > div > div"),s=e.eq(0),a=Math.ceil(s.closest(".mc-slider").width()),n="right"==t?-1:1,o=parseFloat(parseFloat(parseFloat(s.css("margin-left"))+a*n)),r=a*(e.length-1)*-1;o<1&&o>=r&&(s.css("margin-left",o+"px"),i.addClass("mc-moving"),setTimeout((()=>{i.removeClass("mc-moving")}),1200)),i.find(".mc-icon-arrow-right").mcActive(!(r>o-15&&r<o+15)),i.find(".mc-icon-arrow-left").mcActive(o<-10)}},calendly:{script_loaded:!1,load:function(t,i,s){t=t.split("|"),this.script_loaded?this.load_(t[0],i):e.getScript("https://assets.calendly.com/assets/external/widget.js",(()=>{this.script_loaded=!0,window.addEventListener("message",(function(e){"https://calendly.com"===e.origin&&"calendly.event_scheduled"==e.data.event&&(ne.updateMessage(s,K(t[1]?t[1]:"Booking completed.")),h.mcActive(!1))})),this.load_(t[0],i)}),!0)},load_:function(e,t){Calendly.initInlineWidget({url:(e.includes("http")?"":"https://")+e+"?hide_landing_page_details=1&hide_event_type_details=1&hide_gdpr_banner=1",parentElement:h.find("> div").eq(1),prefil:"user"==Z().type?{name:Z().name,email:Z().email}:{}}),h.find("> div:first-child > div").html(K(t)),h.mcActive(!0).attr("data-id","calendly")}},rating:function(e=!1){h.find("> div:first-child > div").html(K("Rate your experience")),h.find("> div:last-child").html(`${R.rating_message?`<div class="mc-input mc-input-textarea"><textarea placeholder="${K("Add a message here...")}"></textarea></div>`:""}<div class="mc-rating"><div><i data-rating="positive" class="mc-submit mc-icon-like"><span>${K("Helpful")}</span></i><i data-rating="negative" class="mc-submit mc-icon-dislike"><span>${K("Not helpful")}</span></i></div></div>`),h.mcActive(!0).attr("data-id","rating").attr("data-close-chat",e?"true":"").css("max-height",(R.rating_message?167:80)+"px")},isShortcode:function(e){return e in this.rich_messsages||S&&MC_ADMIN_SETTINGS.rich_messages.includes(e)||!S&&R.rich_messages.includes(e)}};window.MCRichMessages=oe;var re={getAll:function(t){let i={};return e(t).find(".mc-input[id]").each(((t,s)=>{i[e(s).attr("id")]=this.get(s)})),i},get:function(t){let i=(t=e(t)).data("type"),s=K(J.escape(t.find(" > span").html()));switch(i){case"image":let e=t.find(".image").attr("data-value");return[J.null(e)?"":e,s];case"select":return[J.escape(t.find("select").val()),s];case"select-input":let i=t.find("select,input[disabled]");return[J.escape((i.is("select")||i.is("input")?i.val():t.find(".mc-select").length?t.find(".mc-select > p").attr("data-value"):t.find("> div").html())+t.find("> input").val()),s];default:let a=t.find("input");return[J.escape(a.length?a.val():t.find("[data-value]").attr("data-value")),s]}},set:function(t,i){if((t=e(t)).length){switch(t.data("type")){case"image":i?t.find(".image").attr("data-value",i).css("background-image",`url("${i}")`):t.find(".image").removeAttr("data-value").removeAttr("style");break;case"select":t.find("select").val(i);break;default:t.find("input,textarea").val(i)}return!0}return!1},clear:function(t){e(t).find(".mc-input,.mc-setting").each(((t,i)=>{this.set(i,""),e(i).find("input, select, textarea").removeClass("mc-error")})),this.set(e(t).find("#user_type"),"user")},errors:function(t){let i=!1,s=e(t).find("input, select, textarea").removeClass("mc-error");return s.each((function(t){let a=e.trim(e(this).val()),n=e(this).attr("type"),o=e(this).prop("required");(o&&!a||(o||a)&&("password"==n&&(a.length<8||s.length>t+1&&"password"==s.eq(t+1).attr("type")&&s.eq(t+1).val()!=a)||"email"==n&&(a.indexOf("@")<0||a.indexOf(".")<0||/;|:|\/|\\|,|#|"|!|=|\*|{|}|[|]|£|\$|€|~|'|>|<|\^|&/.test(a))||e(this).attr("data-phone")&&a&&(!e(this).parent().find("select").val()&&!e(this).prev().find("> div > p").attr("data-value")&&!e(this).parent().find("div").html().trim().startsWith("+")||isNaN(a)||a.includes("+")||a.length<5)))&&(i=!0,e(this).addClass("mc-error"))})),s=e(t).find("[data-required]").removeClass("mc-error"),s.each((function(){J.null(e(this).attr("data-value"))&&(e(this).addClass("mc-error"),i=!0)})),i},showErrorMessage:function(i,s){if(!i){let e=t.find(".mc-chat-conversation");i=e.length?e:t}let a="";if("string"==typeof s)if(s.includes("validation-error"))try{let e=JSON.parse(s.replace(/'/g,'"'));Array.isArray(e)&&"validation-error"===e[0]&&(a=J.showUserFriendlyError(e[1]))}catch(e){if(s.includes(":")){let e=s.split(":")[1].trim();a=J.showUserFriendlyError(e)}else a=s}else if(s.includes(":")){let e=s.split(":")[0].trim();a=J.showUserFriendlyError(e),"We encountered an issue. Please try again later."===a&&(a=s)}else{let e=J.showUserFriendlyError(s);a="We encountered an issue. Please try again later."!==e||"unknown"===s?e:s}else if("object"==typeof s)if(Array.isArray(s)&&"validation-error"===s[0])a=J.showUserFriendlyError(s[1]);else if(s.message)a=s.message;else try{a=JSON.stringify(s)}catch(e){a="Error object could not be displayed"}else a="An unexpected error occurred. Please try again.";a=K(a);let n=e(i).find(".mc-info");return 0===n.length&&(e(i).append('<div class="mc-info"></div>'),n=e(i).find(".mc-info")),n.html(a).mcActive(!0),clearTimeout(k),k=setTimeout((function(){n.mcActive(!1)}),7e3),ne.chat_open&&(e(i).hasClass("mc-chat-conversation")||e(i).closest(".mc-chat-conversation").length)&&ne.scrollBottom(),a},showSuccessMessage:function(t,i){e(t).find(".mc-info").remove(),e(t).addClass("mc-success").find(".mc-content").html(`<div class="mc-text">${i}</div>`)},getRegistrationErrorMessage(t,i="validation"){let s="";if("response"==i){if(J.errorValidation(t)){let e=t[1];return J.showUserFriendlyError(e)}return"We encountered an issue with your registration. Please check your information and try again."}return e(t).find(".mc-input:not(#password-check) [required]").each((function(){s+=", "+e(this).closest(".mc-input").find("span").html()+("password"==e(this).attr("type")?" ("+K("8 characters minimum")+")":"")})),`${s.substring(2)} ${K((s.includes(",")?"are":"is")+" required.")}`}};window.MCForm=re;var le={login:function(){return typeof MC_DEFAULT_USER!=N&&MC_DEFAULT_USER?[MC_DEFAULT_USER,"default"]:this.is("wp")&&typeof MC_WP_ACTIVE_USER!=N&&"wp"==R.wp_users_system?[[MC_WP_ACTIVE_USER,typeof MC_WP_AVATAR!=N?MC_WP_AVATAR:""],"wp"]:typeof MC_SHOPIFY_ACTIVE_USER!=N?[MC_SHOPIFY_ACTIVE_USER,"shopify"]:typeof MC_PERFEX_ACTIVE_USER!=N?[[MC_PERFEX_ACTIVE_USER,MC_PERFEX_CONTACT_ID],"perfex"]:typeof MC_WHMCS_ACTIVE_USER!=N?[MC_WHMCS_ACTIVE_USER,"whmcs"]:typeof MC_AECOMMERCE_ACTIVE_USER!=N&&[MC_AECOMMERCE_ACTIVE_USER,"aecommerce"]},is:function(e){return S?MCAdmin.apps.is(e):"wordpress"==e||"wp"==e?R.wp:e in R&&R[e]},shopify:{startCartSynchronization:function(){setInterval((()=>{Z()&&fetch("/cart.js").then((e=>e.json())).then((e=>{e={total:e.total_price,currency:e.currency,items:e.items.map((e=>({id:e.product_id,price:e.price,handle:e.handle,title:e.title,quantity:e.quantity})))};let t=JSON.stringify(e);Q("shopify-cart")!=t&&(Q("shopify-cart",t),J.ajax({function:"shopify-cart-sync",cart:e}))}))}),1e4)}},wordpress:{ajax:function(t,i,s=!1){typeof MC_WP_AJAX_URL!=N&&e.ajax({method:"POST",url:MC_WP_AJAX_URL,data:e.extend({action:"mc_wp_ajax",type:t},i)}).done((e=>{s&&s(e)}))}},woocommerce:{updateCart:function(e,t,i=!1){le.wordpress.ajax(e,{product_id:t},i)},waitingList:function(e="request",t=!1){typeof MC_WP_WAITING_LIST!==N&&("request"!=e||MC_WP_WAITING_LIST&&J.storageTime("waiting-list-"+MC_WP_PAGE_ID,24))&&Z()&&J.ajax({function:"woocommerce-waiting-list",product_id:!1===t?MC_WP_PAGE_ID:t,conversation_id:ne.conversation.id,action:e,token:this.token},(t=>{t&&(J.storageTime("waiting-list-"+MC_WP_PAGE_ID),"request"!=e||ne.chat_open&&!ne.dashboard||ne.updateConversations())}))}},dialogflow:{token:Q("dialogflow-token"),typing_enabled:!0,project_id:!1,busy:!1,message:function(t="",i=[],s=!1,a=!1,n=!1){if(!(t.length<2)||i.length){if(!n)for(var o=0;o<i.length;o++)i[o][0].includes("voice_message")&&(n=i[o][1]);if(this.active(!0,!0,!1)){let e=le.dialogflow.humanTakeoverActive();if(e&&!this.typing_enabled||this.typing(),this.chatbotLimit())return ne.sendMessage(g,this.chatbotLimit());ne.headerAgent(!0),setTimeout((()=>{let s=ne.conversation;J.ajax({function:"dialogflow-message",conversation_id:!!s&&s.id,message:t,attachments:i,parameters:a,token:this.token,dialogflow_language:Q("dialogflow-language")?Q("dialogflow-language"):MC_LANG,project_id:this.project_id,session_id:Z().id+"-"+s.id,audio:n},(i=>{if(ne.typing(-1,"stop"),!1!==i){if(i.response&&i.response.error)return console.error(i.response);if(i.human_takeover)return ne.offlineMessage(),ne.followUp(),this.active(!1);if(i.language_detection&&!Q("dialogflow-language")&&Q("dialogflow-language",[i.language_detection]),i.user_language&&!Q("dialogflow-language")&&Z().setExtra("language",i.user_language),i.translations&&(R.translations=i.translations),!J.errorValidation(i)){let n=!!i.response.queryResult&&i.response.queryResult,o=n&&("input.unknown"==n.action||n.match&&"NO_MATCH"==n.match.matchType),r=i.messages&&Array.isArray(i.messages)?i.messages:[];if(clearTimeout(k),this.token!=i.token&&(this.token=i.token,Q("dialogflow-token",i.token)),o?e&&(this.typing_enabled=!1):this.typing_enabled=!0,n){if(n.action){let e=n.action;"end"==e&&this.active(!1),J.event("MCBotAction",e)}for(var a=0;a<r.length;a++){if(r[a].payload){let e=["human-takeover","redirect","woocommerce-update-cart","woocommerce-checkout","open-article","transcript","department","agent","send-email","rich-message","tags"],t=r[a].payload;if(J.null(t)&&(t=[]),e[0]in t&&!0===t[e[0]]&&this.humanTakeover(),e[1]in t&&setTimeout((function(){t["new-window"]?window.open(t[e[1]]):document.location=t[e[1]]}),500),e[2]in t){let i=t[e[2]];le.woocommerce.updateCart(i[0],i[1],(e=>{e&&J.event("MCWoocommerceCartUpdated",{action:i[0],product:i[1]})}))}if(e[3]in t&&!0===t[e[3]]&&le.wordpress.ajax("url",{url_name:"checkout"},(e=>{setTimeout((()=>document.location=e),500)})),e[4]in t&&ne.showArticles(t[e[4]]),e[5]in t&&t[e[5]]&&J.ajax({function:"transcript",conversation_id:s.id,type:"txt"},(i=>{"email"==t[e[5]]&&Z().email?ne.sendEmail(t.message?t.message:"",[[i,i]],s.id):window.open(i)})),e[6]in t&&J.ajax({function:"update-conversation-department",conversation_id:s.id,department:t[e[6]],message:s.getLastUserMessage().message}),e[7]in t&&J.ajax({function:"update-conversation-agent",conversation_id:s.id,agent_id:t[e[7]],message:s.getLastUserMessage().message}),e[8]in t){let i=t[e[8]];ne.sendEmail(i.message,i.attachments,"active_user"==i.recipient)}e[9]in t&&ne.sendMessage(g,t[e[10]]),e[10]in t&&J.ajax({function:"update-tags",conversation_id:s.id,tags:t[e[11]]}),J.event("MCBotPayload",t)}this.chatbotLimit(!1),Q("dialogflow-language")||!n.languageCode||MC_LANG&&n.languageCode==MC_LANG[0]||Q("dialogflow-language",[n.languageCode])}if(n.diagnosticInfo){n.diagnosticInfo.end_conversation&&this.active(!1)}}if(R.slack_active&&r&&(!p||e))for(a=0;a<r.length;a++)ne.slackMessage(Z().id,R.bot_name,R.bot_image,r[a].message,r[a].attachments);J.event("MCBotMessage",{response:i,message:t})}}})),this.busy=!0}),!1!==s?s:0==R.bot_delay?2e3:parseInt(R.bot_delay))}else this.active(!0,!1,!0)&&!e(".mc-emoji-list").html().includes("<li>"+t+"</li>")&&this.openAI(t,!1,n,i)}},openAI:function(t,i=!1,s=!1,a=[]){if(R.open_ai_active){if(ne.headerAgent(!0),ne.agent_id!=g||le.dialogflow.humanTakeoverActive()&&!this.typing_enabled||this.typing(),this.chatbotLimit())return ne.sendMessage(g,this.chatbotLimit());setTimeout((()=>{J.ajax({function:"open-ai-message",message:t,conversation_id:!!ne.conversation&&ne.conversation.id,audio:s,extra:{token:le.dialogflow.token},attachments:a,context:!!R.open_ai_context_awareness&&(document.title.toUpperCase()+"\n\n\n"+e('meta[name="description"]').attr("content")||"")},(e=>{this.busy=!1,ne.typing(-1,"stop"),J.event("MCOpenAIMessage",{response:e,message:t}),e&&(e[0]||e[5])?(R.chatbot_limit&&this.chatbot_limit++,R.slack_active&&t&&(!p||le.dialogflow.humanTakeoverActive())&&ne.slackMessage(Z().id,R.bot_name,R.bot_image,e[1]),e[2]&&le.dialogflow.token!=e[2]&&(le.dialogflow.token=e.token,Q("dialogflow-token",e.token)),e[3]&&(ne.offlineMessage(),ne.followUp()),e[5]&&(e[5].redirect&&setTimeout((()=>{document.location=e[5].redirect}),500),e[5].open_article&&ne.showArticles(e[5].open_article),"conversation-status-update-3"==e[5].event&&ne.conversationArchived()),this.chatbotLimit(!1)):!1!==e[1]&&J.error(e[1].error?e[1].error.message:e[1],"MCApps.dialogflow.openAI"),i&&i(e)})),this.busy=!0}),0==R.bot_delay?2e3:parseInt(R.bot_delay))}},flowOnLoad(){R.flow_on_load&&!Q("flow-on-load")&&Z()&&(ne.conversation?J.ajax({function:"run-flow-on-load",message:R.flow_on_load,conversation_id:ne.conversation.id}):ne.newConversation(3,Z().id,"",[],null,null,(()=>{J.ajax({function:"run-flow-on-load",message:R.flow_on_load,conversation_id:ne.conversation.id})})),Q("flow-on-load",!0))},typing:function(){clearTimeout(T),T=setTimeout((()=>{ne.typing(-1,"start")}),1e3)},active:function(e=!0,t=!0,i=!0){if(!1===e)return ne.conversation.set("is_human_takeover",!0),!1;if("activate"==e&&ne.conversation.set("is_human_takeover",!1),!S&&le.dialogflow.humanTakeoverActive()&&R.dialogflow_human_takeover_disable_chatbot)return!1;let s=!(S||ne.conversation&&le.dialogflow.humanTakeoverActive()&&ne.agent_online||R.dialogflow_office_hours&&R.office_hours);return t&&i?(R.dialogflow_active||R.open_ai_active)&&s:(!t||R.dialogflow_active)&&(!i||R.open_ai_active)&&s},welcome:function(e=!1,t=!1){J.ajax({function:"dialogflow-message",message:"",conversation_id:ne.conversation.id,token:this.token,event:"Welcome",dialogflow_language:Q("dialogflow-language")?Q("dialogflow-language"):MC_LANG},(()=>{e&&ne.start(),t&&ne.audio.play()}))},humanTakeover:function(){J.ajax({function:"dialogflow-human-takeover",conversation_id:ne.conversation.id},(()=>{ne.offlineMessage(),ne.followUp(),this.active(!1),R.queue_human_takeover&&(R.queue=!0,ne.queue(ne.conversation.id))}))},humanTakeoverActive:function(){return!!ne.conversation&&ne.conversation.get("is_human_takeover")},translate:function(e,t,i,s,a){J.ajax({function:"google-translate",strings:e,language_code:t,token:this.token,message_ids:s,conversation_id:a},(e=>{this.token=e[1],i(e[0])}))},chatbotLimit:function(e=!0){if(R.chatbot_limit){let i=Q("chatbot_limit"),s=(new Date).getTime()/1e3;if(i||(i=[]),e){let e=s-R.chatbot_limit.interval,a=[];for(var t=0;t<i.length;t++)i[t]>e&&a.push(i[t]);if(Q("chatbot_limit",a),a.length>=R.chatbot_limit.quota)return R.chatbot_limit.message}else i.push(s),Q("chatbot_limit",i)}return!1}},aecommerce:{cart:function(){le.is("aecommerce")&&typeof MC_AECOMMERCE_CART!=N&&typeof MC_AECOMMERCE_ACTIVE_USER!=N&&Q("aecommerce")!=JSON.stringify(MC_AECOMMERCE_CART)&&J.ajax({function:"aecommerce-cart",cart:MC_AECOMMERCE_CART},(()=>{Q("aecommerce",JSON.stringify(MC_AECOMMERCE_CART))}))}},martfury:{privateChat:function(){let t=e("#tab-vendor > h4,.ps-product__vendor > a");if(t.length){t=t.html().toLowerCase();for(var i=0;i<R.martfury.length;i++)if(t==R.martfury[i]["martfury-linking-store"].toLowerCase()){let e=R.martfury[i]["martfury-linking-agent"],t=J.activeUser()?J.activeUser().conversations:[];ne.default_agent=e;for(var s=0;s<t.length;s++)if(t[s].get("agent_id")==e)return void ne.openConversation(t[s].id);ne.clear(),ne.hideDashboard()}}}}};function ce(){(t=e(".mc-admin, .mc-chat, .mc-tickets")).length&&typeof MC_AJAX_URL!=N?(n=t.find(".mc-list").eq(0),o=t.find(".mc-editor"),r=o.find("textarea"),u=t.find(S||A?".mc-list":"> div > .mc-scroll-area"),l=u.find(".mc-header"),d=o.find(".mc-emoji"),h=t.find(".mc-overlay-panel"),c=A?t.find(".mc-profile-agent .mc-status"):null,ne.enabledAutoExpand(),ne.audio=t.find("#mc-audio").get(0),ne.label_date=t.find(".mc-label-date-top"),J.cookie("mc-check","ok",1,"set"),"ok"!=J.cookie("mc-check")?(D=!1,console.warn("Masi Chat: cookies not available.")):J.cookie("mc-check",!1,!1,!1),S?(ee(),J.event("MCReady")):J.ajax({function:"get-front-settings",current_url:window.location.href,tickets:A,popup:!Q("popup")&&!A},(i=>{if(R=i,typeof MC_LOCAL_SETTINGS!=N&&e.extend(R,MC_LOCAL_SETTINGS),g=R.bot_id,p=R.dialogflow_human_takeover,M=R.agents_online,Y.active=R.pusher,R.language&&(MC_LANG="auto"!=R.language?[R.language,"front"]:!(!V||"en"==Shopify.locale)&&Shopify.locale),typeof MC_REGISTRATION_REQUIRED!=N&&(R.registration_required=MC_REGISTRATION_REQUIRED,R.tickets_registration_required=MC_REGISTRATION_REQUIRED),typeof MC_ARTICLES_PAGE!=N&&MC_ARTICLES_PAGE&&(ne.automations.runAll(),ne.initArticlesPage()),A&&R.tickets_manual_init||(!A||R.tickets_manual_init)&&(R.chat_manual_init||R.disable_offline&&!M||R.disable_office_hours&&!R.office_hours||R.chat_login_init&&!le.login())||ne.initChat(),R.cron&&setTimeout((function(){J.ajax({function:"cron-jobs"})}),1e4),R.cron_email_piping&&setTimeout((function(){J.ajax({function:"email-piping"})}),8e3),R.push_notifications_users&&J.serviceWorker.init(),A&&(R.tickets_default_department&&(ne.default_department=R.tickets_default_department),R.dialogflow_disable_tickets&&(R.dialogflow_active=!1,R.open_ai_active=!1)),le.is("martfury")){let e=!1;setInterval((function(){if(Z()){let t=J.cookie("XSRF-TOKEN");t&&t!=e&&(J.ajax({function:"martfury-session"}),e=t)}}),3e3)}V&&(le.shopify.startCartSynchronization(),t.addClass("mc-shopify")),ee(),le.aecommerce.cart(),setTimeout((()=>{J.event("MCReady")}),500)})),e(o).on("keydown","textarea",(function(e){if(13==e.which&&(!A||R.tickets_enter_button)&&(!S||!e.ctrlKey&&!e.shiftKey))return ne.submit(),e.preventDefault,!1;S&&13==e.which&&e.ctrlKey&&ne.insertText("\n")})),e(t).on("keydown",".mc-dashboard-articles input",(function(t){13==t.which&&e(this).next().click()})),typeof MC_DEFAULT_DEPARTMENT!==N&&(ne.default_department=MC_DEFAULT_DEPARTMENT),typeof MC_DEFAULT_AGENT!==N&&(ne.default_agent=MC_DEFAULT_AGENT),typeof MC_DIALOGFLOW_TAGS!==N&&(ne.default_tags=MC_DEFAULT_TAGS),typeof MC_DIALOGFLOW_AGENT!==N&&(le.dialogflow.project_id=MC_DIALOGFLOW_AGENT)):J.event("MCReady"),document.addEventListener("visibilitychange",(function(){J.visibilityChange(document.visibilityState)}),!1),e(t).on("click",(function(){ne.tab_active||J.visibilityChange()})),i=t,S&&(t=t.find(".mc-conversation")),e(u).on("scroll",(function(){let t=u.scrollTop(),i=x.length;if(ne.scrollHeader(),!ne.label_date_show&&i&&x[i-1].getBoundingClientRect().top>n[0].getBoundingClientRect().top+n.outerHeight()&&(ne.label_date_show=!0,i>1&&ne.label_date.html(e(x[i-2]).html())),ne.label_date_show&&(ne.label_date.mcActive(!0),clearTimeout($[0]),$[0]=setTimeout((()=>{ne.label_date.mcActive(!1)}),1500)),i)if(ne.isBottom())ne.label_date.html(e(x[i-1]).html()),ne.label_date_show=x.length&&x[x.length-1].getBoundingClientRect().top<0;else{let a=ne.label_date[0].getBoundingClientRect().top;for(var s=0;s<i;s++){let i=x[s].getBoundingClientRect().top;if(i-100<a&&i+100>a){let i=e(x[E[0]>t&&s>0?s-1:s]).html();i!=E[1]&&(ne.label_date.html(i),E[1]=i);break}}}E[0]=t})),e(n).on("click",".mc-menu-btn",(function(){let t=e(this).parent().find(".mc-menu"),i=e(t).mcActive();J.deactivateAll(),i||(e(t).mcActive(!0),J.deselectAll(),S&&(MCAdmin.open_popup=t))})),j&&(e(o).on("click",".mc-textarea",(function(){t.addClass("mc-header-hidden"),e(this).find("textarea").get(0).focus(),ne.isBottom()&&(ne.scrollBottom(),setTimeout((()=>{ne.scrollBottom()}),200))})),e(o).on("focusout",".mc-textarea",(function(){setTimeout((()=>{t.removeClass("mc-header-hidden")}),300)})),e(o).on("click",".mc-submit",(function(){r.blur()})),window.addEventListener("popstate",(function(){ne.chat_open&&ne.open(!1)}))),e(n).on("click",".mc-menu li",(function(){e(this).parent().mcActive(!1)})),e(o).on("click",".mc-submit",(function(){ne.submit()})),e("body").on("click",".mc-chat-btn,.mc-responsive-close-btn, #mc-open-chat, .mc-open-chat",(function(){ne.open(!ne.chat_open)})),e(t).on("click",".mc-dashboard-btn",(function(){ne.showDashboard(),u.find(" > .mc-panel-articles > .mc-article").length&&ne.showArticles(),Q("open-conversation",0),L=!1})),e(t).on("click",".mc-user-conversations li",(function(){ne.openConversation(e(this).attr("data-conversation-id"))})),e(t).on("click",".mc-btn-new-conversation, .mc-departments-list > div, .mc-agents-list > div",(function(){let t=e(this).data("id"),i=!1;if(!J.null(t)){let a=e(this).parent().hasClass("mc-departments-list");if(a?ne.default_department=parseInt(t):ne.default_agent=parseInt(t),e(this).parent().data("force-one")){let e=Z()?Z().conversations:[];for(var s=0;s<e.length;s++)if(a&&e[s].get("department")==t||!a&&e[s].get("agent_id")==t){i=!0,ne.openConversation(e[s].id);break}}}i||(L="new-conversation",ne.clear(),ne.hideDashboard())})),e(t).on("click",".mc-btn-all-conversations",(function(){t.find(".mc-dashboard-conversations").removeClass("mc-conversations-hidden")})),e(o).on("click",".mc-btn-attachment",(function(){ne.is_busy||o.find(".mc-upload-files").val("").click()})),e(o).on("click",".mc-attachments > div > i",(function(t){return e(this).parent().remove(),r.val()||0!=o.find(".mc-attachments > div").length||o.mcActive(!1),t.preventDefault(),!1})),e(o).on("change",".mc-upload-files",(function(t){ne.busy(!0),e(this).mcUploadFiles((function(e){ne.uploadResponse(e)})),J.event("MCAttachments")})),e(o).on("dragover",(function(t){e(this).addClass("mc-drag"),clearTimeout(k),t.preventDefault(),t.stopPropagation()})),e(o).on("dragleave",(function(t){k=setTimeout((()=>{e(this).removeClass("mc-drag")}),200),t.preventDefault(),t.stopPropagation()})),e(o).on("drop",(function(t){let i=t.originalEvent.dataTransfer.files;if(t.preventDefault(),t.stopPropagation(),i.length>0)for(var s=0;s<i.length;++s){let e=new FormData;e.append("file",i[s]),J.upload(e,(function(e){ne.uploadResponse(e)}))}return e(this).removeClass("mc-drag"),!1})),e(t).on("click",".mc-btn-all-articles:not([onclick])",(function(){ne.showArticles()})),e(t).on("click",".mc-articles > div",(function(){ne.showArticles(e(this).attr("data-id"),e(this).attr("data-is-category"))})),e(t).on("click",".mc-dashboard-articles .mc-input-btn .mc-submit-articles",(function(){ne.searchArticles(e(this).parent().find("input").val(),this,e(this).parent().next())})),e(i).on("click",".mc-article [data-rating]",(function(){ne.articleRatingOnClick(this)})),e(n).on("click",".mc-rich-button a",(function(t){let i=e(this).attr("href");if(0===i.indexOf("#")&&0===i.indexOf("#article-"))return ne.showArticles(i.replace("#article-","")),t.preventDefault(),!1})),e(h).on("click","[data-rating]",(function(){let t=h.find("> div:last-child"),i=e(this).attr("data-rating"),s=ne.conversation?ne.conversation:Z().getLastConversation(),a=s.getLastUserMessage(!1,!0);X(t),J.ajax({function:"set-rating",conversation_id:s.id,agent_id:!!a&&a.get("user_id"),user_id:Z().id,message:t.find("textarea").val(),rating:"positive"==i?1:-1},(e=>{h.attr("data-close-chat")&&ne.closeChat(),ne.update(),h.mcActive(!1),t.mcLoading(!1)}))})),e(i).on("click",".mc-lightbox-media > i",(function(){return i.find(".mc-lightbox-media").mcActive(!1),S&&(MCAdmin.open_popup=!1),!1})),e(t).on("click",".mc-image",(function(){J.lightbox(e(this).html())})),e(t).on("click",".mc-slider-images .mc-card-img",(function(){J.lightbox(`<img loading="lazy" src="${e(this).attr("data-value")}" />`)})),e(document).on("MCConversationLoaded",(function(){Q("queue")&&ne.queue(Q("queue"))})),e(o).on("click",".mc-btn-emoji",(function(){ne.showEmoji(this)})),e(d).on("click",".mc-emoji-list li",(function(t){ne.insertEmoji(e(this).html()),j&&clearTimeout(k)})),e(d).find(".mc-emoji-list").on("touchend",(function(e){k=setTimeout((()=>{ne.mouseWheelEmoji(e)}),50)})),e(d).find(".mc-emoji-list").on("mousewheel DOMMouseScroll",(function(e){ne.mouseWheelEmoji(e)})),e(d).find(".mc-emoji-list").on("touchstart",(function(e){ne.emoji_options.touch=e.originalEvent.touches[0].clientY})),e(d).on("click",".mc-emoji-bar > div",(function(){ne.clickEmojiBar(this)})),e(d).on("click",".mc-select li",(function(){ne.categoryEmoji(e(this).data("value"))})),e(d).find(".mc-search-btn input").on("change keyup paste",(function(){ne.searchEmoji(e(this).val())})),e(r).on("keyup",(function(){ne.textareaChange(this)})),e(t).on("click",".mc-privacy .mc-approve",(function(){Q("privacy-approved",!0),e(this).closest(".mc-privacy").remove(),t.removeClass("mc-init-form-active"),l.find(" > div").css({opacity:1,top:0}),ne.initChat(),A?MCTickets.showPanel(J.setting("tickets_disable_first")?"":"new-ticket"):ne.isInitDashboard()||ne.hideDashboard()})),e(t).on("click",".mc-privacy .mc-decline",(function(){let t=e(this).closest(".mc-privacy");e(t).find(".mc-text").html(e(t).attr("data-decline")),e(t).find(".mc-decline").remove(),ne.scrollBottom(!0)})),e(t).on("click",".mc-popup-message .mc-icon-close",(function(){ne.popup(!0)})),e(t).on("click",".mc-rich-message .mc-submit,.mc-rich-message:not(.mc-rich-registration) .mc-select ul li",(function(){let t=e(this).closest(".mc-rich-message");t[0].hasAttribute("disabled")||oe.submit(t,t.attr("data-type"),this)})),e(t).on("click",".mc-rich-message .mc-input > span",(function(){e(this).mcActive(!0),e(this).siblings().focus()})),e(t).on("focus focusout click",".mc-rich-message .mc-input input,.mc-rich-message .mc-input select",(function(t){switch(t.type){case"focusin":case"focus":e(this).siblings().mcActive(!0);break;case"focusout":e(this).val()?e(this).siblings().addClass("mc-filled mc-active"):setTimeout((()=>{W||e(this).siblings().mcActive(!1)}),100);break;case"click":e(this).siblings().removeClass("mc-filled")}})),e(t).on("click",".mc-input-select-input > div",(function(){W=!0,setTimeout((()=>{W=!1}),250)})),e(t).on("click",".mc-slider-arrow",(function(){oe.sliderChange(e(this).closest("[id]").attr("id"),e(this).hasClass("mc-icon-arrow-right")?"right":"left")})),e(t).on("change",'.mc-rich-message [data-type="select"] select',(function(){e(this).siblings().mcActive(!0)})),e(t).on("click",'[data-type="select-input"] > div',(function(){e(this).prev().mcActive(!0),e(this).next().addClass("mc-focus")})),e(t).on("focusout",'[data-type="select-input"] input,[data-type="select-input"] select',(function(){let t=e(this).closest(".mc-input");t.find("> input").val()+t.find("select").val()==""&&t.find("span").mcActive(!1),t.find(".mc-focus").removeClass("mc-focus")})),e(t).on("click",".mc-rich-registration .mc-login-area",(function(){let i=t.hasClass("mc-init-form-active");e(this).closest(".mc-rich-registration").replaceWith(oe.generate({},"login",i?"mc-init-form":"")),ne.scrollBottom(i)})),e(t).on("click",".mc-rich-login .mc-registration-area",(function(){if(R.registration_link)document.location=R.registration_link;else{let i=t.hasClass("mc-init-form-active");e(this).closest(".mc-rich-login").replaceWith(oe.generate({},"registration",i?"mc-init-form":"")),ne.scrollBottom(i)}})),e(t).on("click",".mc-rich-login .mc-submit-login",(function(){J.loginForm(this,!1,(i=>{let s=e(this).closest(".mc-rich-login");if(Z(new ie(i[0])),s.hasClass("mc-init-form"))L="open-conversation",ne.initChat(),Y.start(),ne.isInitDashboard()||ne.hideDashboard(),e(document).on("MCPopulateConversations",(function(){t.removeClass("mc-init-form-active"),s.remove(),e(document).off("MCPopulateConversations")}));else{s=s.closest("[data-id]");let e=ne.conversation.getMessage(s.attr("data-id")),t=`${K("Logged in as")} *${Z().name}*`;e.set("message",t),ne.updateMessage(e.id,t),s.replaceWith(e.getCode()),Y.started=!1,Y.start()}}))})),e(n).on("click",".mc-social-buttons div",(function(){J.openWindow(e(this).attr("data-link"))})),e(t).on("click",".mc-close-chat",(function(){ne.closeChat()})),e(n).on("click",'.mc-rich-woocommerce_button a, [href="#"].mc-card-btn',(function(t){let i=J.settingsStringToArray(e(this).closest(".mc-rich-message").attr("data-settings")),s="checkout"==i["link-type"]||i.checkout,a=e(this)[0].hasAttribute("data-ids")?e(this).attr("data-ids").split(","):[i.id.split("|")[e(this).parent().index()]];if(a.length){if(X(this))return;le.wordpress.ajax("button-purchase",{product_ids:a,checkout:s,coupon:i.coupon},(t=>{s?document.location=t:e(this).addClass("mc-icon-check").mcLoading(!1)}))}return t.preventDefault(),!1})),e(n).on("click","#mc-waiting-list .mc-submit",(function(){0==e(this).index()&&setTimeout((()=>{le.woocommerce.waitingList("submit")}),1e3)})),e(document).on("MCNewEmailAddress",(function(e,t){"mc-waiting-list-email"==t.id&&le.woocommerce.waitingList("submit")})),e(n).on("click",".mc-player-btn",(function(){let t=e(this).parent().find("audio").get(0),i=e(this).hasClass("mc-icon-play");n.find("audio").each((function(){e(this).get(0).pause(),e(this).unbind("ended"),e(this).parent().find(".mc-player-btn").removeClass("mc-icon-pause").addClass("mc-icon-play")})),i?(t.play(),e(this).removeClass("mc-icon-play")):t.pause(),e(t).unbind("ended"),e(t).bind("ended",(()=>{e(this).removeClass("mc-icon-pause").addClass("mc-icon-play")})),e(this).addClass("mc-icon-"+(i?"pause":"play"))})),e(n).on("click",".mc-player-speed",(function(){let t=e(this).parent();if(t.find(".mc-player-btn").hasClass("mc-icon-pause")){let i=e(this).find(".mc-player-speed-number"),s=parseFloat(i.html());s+=.5,s>2&&(s=1),t.find("audio").get(0).playbackRate=s,i.html(s)}})),e(n).on("click",".mc-player-download",(function(){window.open(e(this).parent().find("audio source").attr("src"))})),e(o).on("click",".mc-btn-audio-clip",(function(){if((m=ne.conversation&&"wa"==ne.conversation.get("source"))&&typeof MCAudioRecorder===N)return e.getScript(MC_URL+"/vendor/lame.min.js",(()=>{this.click()}),!0);navigator.mediaDevices.getUserMedia({audio:!0}).then((t=>{m?MCAudioRecorder.init(t):(typeof MCAudioRecorder!=N&&MCAudioRecorder.close(),q=[],(_=new MediaRecorder(t)).addEventListener("dataavailable",(e=>{q.push(e.data)}))),v||(v=o.find("#mc-audio-clip"),b=v.find(".mc-audio-clip-time"),v.on("click",".mc-btn-mic",(function(){let t=e(this).hasClass("mc-icon-pause");if(t)m?MCAudioRecorder.pause():_.stop();else{let e=v.find(".mc-btn-clip-player");e.hasClass("mc-icon-pause")&&e.click(),m?MCAudioRecorder.resume():_.start()}F[2]=!t,v.find(".mc-icon-play").mcActive(t),e(this).removeClass("mc-icon-"+(t?"pause":"mic")).addClass("mc-icon-"+(t?"mic":"pause"))})),v.on("click",".mc-btn-clip-player",(function(){let t=e(this).hasClass("mc-icon-pause");if(t)G[3].pause(),G[2]=!1;else if(G[3])G[3].play(),G[2]=!0;else{let t=new Audio(URL.createObjectURL(m?MCAudioRecorder.blob(!1):new Blob(q,{type:"audio/webm"})));b.html("0:00"),t.play(),t.onended=()=>{G[0]=0,G[2]=!1,G[3]=!1,b.html("0:00"),e(this).removeClass("mc-icon-pause").addClass("mc-icon-play")},G[0]=0,G[2]=!0,G[3]=t}e(this).removeClass("mc-icon-"+(t?"pause":"play")).addClass("mc-icon-"+(t?"play":"pause"))})),v.on("click",".mc-icon-close",(function(){q=[],clearInterval(F[1]),clearInterval(G[1]),v.mcActive(!1),o.mcActive(!1).removeClass("mc-audio-message-active"),m?MCAudioRecorder.pause():_.stop(),y.getTracks()[0].stop()}))),b.html("0:00"),y=t,clearInterval(F[1]),clearInterval(G[1]),F=[0,setInterval((()=>{F[2]&&(F[0]++,b.html(te(F[0])))}),1e3),!0],G=[0,setInterval((()=>{G[2]&&(G[0]++,b.html(te(G[0])))}),1e3),!1,!1],m||_.start(),v.find(".mc-btn-clip-player").removeClass("mc-icon-pause").addClass("mc-icon-play").mcActive(!1),v.find(".mc-icon-mic").removeClass("mc-icon-mic").addClass("mc-icon-pause"),v.mcActive(!0),r.val("").css("height",""),o.mcActive(!0).addClass("mc-audio-message-active")})).catch((e=>{alert(e)}))})),e(n).on("click","[data-action].mc-rich-btn",(function(t){return oe.calendly.load(e(this).attr("data-extra"),e(this).html(),e(this).closest("[data-id]").attr("data-id")),t.preventDefault(),!1})),e(h).on("click","> div:first-child i",(function(){t.find(".mc-overlay-panel").mcActive(!1),h.attr("data-close-chat")&&ne.closeChat()})),e(t).on("change input","#phone > input",(function(){let t=e(this).val().trim();if(/^[0-9+]+$/.test(t)||(t=t.replace(/[^0-9+]/g,""),e(this).val(t)),t.length>1&&0===t.indexOf("+")){let i=!1;"+1"==t.substring(0,2)&&(i=t.substring(0,2)),t.length>3&&(i=t.substring(0,3)),i&&(e(this).parent().find(`[data-value="${i}"]`).click(),e(this).parent().find(".mc-select").click(),e(this).val(t.replace(i,"")))}})),e(i).on("click",".mc-search-btn > i",(function(){let t=e(this).parent(),s=e(t).mcActive();s&&(setTimeout((()=>{e(t).find("input").val("")}),50),setTimeout((()=>{e(t).find("input").trigger("change")}),550)),e(t).mcActive(!s),e(t).find("input").get(0).focus(),i.find(".mc-select ul").mcActive(!1)})),e(i).on("click",".mc-select",(function(t){if(!t.target||!e(t.target).is("input")){let t=e(this).find("ul"),s=t.hasClass("mc-active");e(i).find(".mc-select ul").mcActive(!1),t.setClass("mc-active",!s),e(this).find(".mc-select-search").setClass("mc-active",!s),S&&(MCAdmin.open_popup=!s&&this)}})),e(i).on("click",".mc-select li",(function(){let t=e(this).closest(".mc-select"),i=e(this).data("value"),s=e(t).find(`[data-value="${i}"]`);t.find("li").mcActive(!1),t.find("p").attr("data-value",i).html(e(s).html()),s.mcActive(!0)})),e(i).on("input",".mc-select-search input",(function(t){let i=e(this).val();J.search(i,(()=>{let t=e(this).parent().parent().find("li");t.setClass("mc-hide",i.length),i.length&&(i=i.toLowerCase(),t.each((function(){(e(this).attr("data-value").includes(i)||e(this).attr("data-country").includes(i))&&e(this).removeClass("mc-hide")})))}))})),e(i).on("click",".mc-input-image .image",(function(){s=e(this).parent(),o.find(".mc-upload-files").click()})),e(i).on("click",".mc-input-image .image > .mc-icon-close",(function(t){return J.ajax({function:"delete-file",path:e(this).parent().attr("data-value")}),e(this).parent().removeAttr("data-value").css("background-image",""),t.preventDefault(),!1}))}window.MCApps=le,e(document).ready((function(){if((t=e(".mc-admin, .mc-admin-start")).length)return S=!0,void ce();let i,s,a=!1;if(typeof MC_INIT_URL!=N)MC_INIT_URL.indexOf(".js")<0&&(MC_INIT_URL+="/js/main.js?v="+w),i=MC_INIT_URL;else{let e=document.getElementsByTagName("script"),t=["init.js","main.js","min/init.min.js","min/main.min.js"];for(var n=0;n<e.length;n++){let s=e[n].src;if("mcinit"==e[n].id){i=s,a=a||i.includes("init.");break}for(var o=0;o<t.length;o++)if(s&&s.includes("/masichat/js/"+t[o])){i=s,a=a||i.includes("init.");break}}}let r=J.getURL(!1,i);if(r.url&&(i=r.url),typeof MC_DISABLED!=N&&MC_DISABLED)return;if(a)return void ce();typeof MC_TICKETS==N&&"tickets"!=r.mode||(A=!0,r.mode="tickets"),r.cloud&&(P=r.cloud);let l=!(!V||"en"==Shopify.locale)&&Shopify.locale,c=i.lastIndexOf("main.min.js");s=i.substr(0,i.lastIndexOf("main.js")>0?i.lastIndexOf("main.js")-4:c-8);let d=s+"/include/init.php"+(r.lang?"?lang="+r.lang:"")+(l?"?lang_optional="+l:"")+(r.mode?"&mode="+r.mode:"")+(P?"&cloud="+P:"");J.cors("GET",d.replace(".php&",".php?"),(t=>{let i="body";A&&e("#mc-tickets").length&&(i="#mc-tickets"),e(i).append(t),J.loadResource(s+"/css/"+(A?"tickets":"main")+".css"),A?J.loadResource(s+"/apps/tickets/tickets"+(c>0?".min":"")+".js?v="+w,!0,(()=>{ce()})):ce(),r.lang&&(MC_LANG=[r.lang,S?"admin":"front"])}))}))}(jQuery);