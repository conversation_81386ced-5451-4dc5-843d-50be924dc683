<?php

/*
 * ==========================================================
 * TWITTER POST.PHP
 * ==========================================================
 *
 * Twitter response listener. This file receive the messages from Twitter. This file requires the Twitter App.
 * © 2017-2025 app.masichat.com. All rights reserved.
 *
 */

require('../../include/functions.php');
mc_cloud_load_by_url();

if (isset($_REQUEST['crc_token'])) {
    $token = $_REQUEST['crc_token'];
    $hash = hash_hmac('sha256', $token, mc_get_multi_setting('twitter', 'twitter-consumer-secret'), true);
    echo json_encode(['response_token' => 'sha256=' . base64_encode($hash)]);
    die();
}

$raw = file_get_contents('php://input');
$response = json_decode($raw, true);
$signature = mc_isset($_SERVER, 'HTTP_X_TWITTER_WEBHOOKS_SIGNATURE');
if (!$signature)
    die();
if ($signature !== 'sha256=' . base64_encode(hash_hmac('sha256', $raw, mc_get_multi_setting('twitter', 'twitter-consumer-secret'), true))) {
    header('HTTP/1.1 403 Forbidden');
    die('Invalid signature');
}

if (isset($response['direct_message_events'])) {
    $GLOBALS['MC_FORCE_ADMIN'] = true;
    $message_create = $response['direct_message_events'][0]['message_create'];
    $message = $message_create['message_data']['text'];

    // User and conversation
    $user_id = false;
    $sender_id = $message_create['sender_id'];
    $twitter_user = $response['users'][$sender_id];
    $is_agent = str_replace('@', '', mc_get_multi_setting('twitter', 'twitter-username')) == $twitter_user['screen_name'];
    $user = mc_get_user_by('twitter-id', $is_agent ? $message_create['target']['recipient_id'] : $sender_id);
    $conversation_id = $user ? mc_isset(mc_db_get('SELECT id FROM mc_conversations WHERE source = "tw" AND user_id = ' . $user['id'] . ' ORDER BY id DESC LIMIT 1'), 'id') : false;
    if ($is_agent) {
        $user_id = mc_get_bot_id();
        if (!$user_id || !$conversation_id)
            return;
        $GLOBALS['MC_LOGIN'] = $agent;
    } else {
        if (!$user) {
            $extra = ['twitter-id' => [$sender_id, 'Twitter ID']];
            $first_name = $twitter_user['name'];
            $profile_image = mc_download_file(str_replace('_normal', '_bigger', $twitter_user['profile_image_url']));
            $last_name = '';
            $space = mb_strpos($first_name, ' ');
            if ($space) {
                $last_name = mb_substr($first_name, $space);
                $first_name = mb_substr($first_name, 0, $space);
            }
            if (defined('MC_DIALOGFLOW'))
                $extra['language'] = mc_google_language_detection_get_user_extra($message);
            $user_id = mc_add_user(['first_name' => $first_name, 'last_name' => $last_name, 'profile_image' => $profile_image, 'user_type' => 'lead'], $extra);
            $user = mc_get_user($user_id);
        } else {
            $user_id = $user['id'];
        }
        $GLOBALS['MC_LOGIN'] = $user;
        if (!$conversation_id) {
            $conversation_id = mc_new_conversation($user_id, 2, '', mc_get_setting('twitter-department'), -1, 'tw')['details']['id'];
        }
    }

    // Attachments
    $attachments = mc_isset($message_create['message_data'], 'attachment', []);
    if ($attachments) {
        $twitter = new TwitterAPIExchange(mc_get_setting('twitter'));
        $url = $attachments['media']['media_url'];
        $file = $twitter->buildOauth($url, 'GET')->performRequest();
        $date = date('d-m-y');
        $file_name = basename($url);
        $path = mc_upload_path() . '/' . $date . '/' . $file_name;
        $message = trim(str_replace($attachments['media']['url'], '', $message));
        if (mc_file($path, $file)) {
            $attachments = [[$file_name, mc_upload_path(true) . '/' . $date . '/' . $file_name]];
        } else
            $attachments = [];
    }

    // Send message
    if ($is_agent) {
        $last_agent = mc_get_last_agent_in_conversation($conversation_id);
        $last_message = mc_db_get('SELECT message, attachments FROM mc_messages WHERE (message <> "" || attachments <> "") AND conversation_id = ' . mc_db_escape($conversation_id, true) . ' AND (user_id = ' . mc_get_bot_id() . ($last_agent ? ' OR user_id = ' . $last_agent : '') . ') ORDER BY id DESC LIMIT 1');
        $response = !$last_message || $last_message['message'] != $message || json_encode(mc_isset($last_message, 'attachments', [])) != json_encode($attachments) ? mc_send_message($user_id, $conversation_id, $message, $attachments, false) : false;
    } else {
        $response = mc_send_message($user_id, $conversation_id, $message, $attachments, false);

        // Dialogflow, Notifications, Bot messages
        $response_extarnal = mc_messaging_platforms_functions($conversation_id, $message, $attachments, $user, ['source' => 'tw', 'platform_value' => $sender_id]);

        // Queue
        if (mc_get_multi_setting('queue', 'queue-active')) {
            mc_queue($conversation_id, mc_get_setting('twitter-department'));
        }

        // Online status
        mc_update_users_last_activity($user_id);
    }

    $GLOBALS['MC_FORCE_ADMIN'] = false;
} else if (isset($response['direct_message_indicate_typing_events'])) {
    $user = mc_get_user_by('twitter-id', $response['direct_message_indicate_typing_events'][0]['sender_id']);
    if ($user) {
        $GLOBALS['MC_LOGIN'] = $user;
        mc_set_typing($user['id'], mc_db_get('SELECT id FROM mc_conversations WHERE source = "tw" AND user_id = ' . $user['id'] . ' ORDER BY id DESC LIMIT 1')['id']);
    }
}

die();

?>