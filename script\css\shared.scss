
/*
*
* ==========================================================
* SHARED.SCSS
* ==========================================================
*
* Shared CSS used on both admin and front end. This file is imported only.
*
*/

$transition: all 0.4s;
$color-blue: #EA7351;
$color-dark-blue: rgb(224, 50, 116);
$color-black: rgb(36, 39, 42);
$color-gray: rgb(86, 96, 105);
$color-red: rgb(202, 52, 52);
$color-green: rgb(26, 146, 96);
$color-yellow: rgb(246, 158, 0);
$color-orange: rgb(234, 115, 81);
$color-pink: rgb(224, 50, 116);
$background-gray: rgb(245, 247, 250);
$background-color-2: rgba(39, 156, 255, 0.08);
$background-color-red: rgba(202, 52, 52, 0.1);
$background-color-yellow: rgb(242, 227, 124);
$border-color: rgb(212, 212, 212);
$white: #fff;
$box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.05);

@import "../media/icons/icons.scss";

@font-face {
    font-family: "Masi Chat Font";
    src: url("../media/fonts/regular.woff2") format("woff2");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "Masi Chat Font";
    src: url("../media/fonts/medium.woff2") format("woff2");
    font-weight: 500;
    font-style: normal;
}

@keyframes mc-fade-animation {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes mc-fade-bottom-animation {
    0% {
        transform: translateY(15px);
        opacity: 0;
    }

    100% {
        transform: none;
        opacity: 1;
    }
}

@keyframes mc-fade-bottom-center {
    0% {
        transform: translateY(15px) translateX(-50%);
        opacity: 0;
    }

    100% {
        transform: translateX(-50%);
        opacity: 1;
    }
}

.mc-main, .mc-articles-page, .mc-main input, .mc-main textarea, .mc-main select {
    font-family: "Masi Chat Font", "Helvetica Neue", "Apple Color Emoji", Helvetica, Arial, sans-serif;
}

.mc-main *, .mc-articles-page * {
    box-sizing: content-box;
    outline: none;
}

.mc-main input, .mc-articles-page input, .mc-articles-page input[text], .mc-main input[text], .mc-main textarea, .mc-main input[email] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.mc-clear {
    width: 100%;
    clear: both;
}

.mc-no-results {
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0.3px;
    margin: 0;
    color: $color-gray;
    opacity: 0.7;
}

.mc-no-results-active > .mc-no-results {
    display: block !important;
    opacity: 1;
}

.mc-no-results-active > div:not(.mc-no-results) {
    display: none !important;
}

.mc-hide, .mc-grid.mc-hide {
    display: none !important;
}

.mc-scroll-area, .mc-conversation .mc-list, .mc-list .mc-message pre, .mc-rich-table .mc-content, .mc-admin .mc-top-bar > div:first-child > ul,
.mc-dialog-box pre, .mc-horizontal-scroll {
    overflow: hidden;
    overflow-y: scroll;

    &::-webkit-scrollbar {
        width: 5px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
        background: #BFCBD3;
        border-radius: 6px;
        transition: $transition;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: $color-orange;
    }
}

.mc-list .mc-message pre, .mc-rich-table .mc-content, .mc-admin .mc-top-bar > div:first-child > ul, .mc-dialog-box pre, .mc-horizontal-scroll, [data-id="flows"] > .mc-content {
    overflow-y: hidden;

    &::-webkit-scrollbar {
        height: 5px;
    }
}

div ul.mc-menu, .mc-select ul, .mc-popup, .mc-menu-mobile > ul, .mc-menu-mobile .mc-mobile, .mc-area-users .mc-filter-btn > div, .mc-menu li ul {
    background: $white;
    border-radius: 4px;
    padding: 10px 0;
    box-shadow: $box-shadow;
    z-index: 9999995;
    list-style: none;
}

.mc-horizontal-scroll {
    overflow-y: hidden;
    overflow-x: scroll;
}

/*

# USER CONVERSATIONS
==========================================================

*/

.mc-user-conversations {
    > li {
        border-bottom: 1px solid $border-color;
        padding: 15px;
        margin: 0;
        cursor: pointer;
        border-left: 2px solid rgba(255, 255, 255, 0);
        transition: $transition;
        position: relative;

        > div {
            position: relative;
            color: rgb(60, 60, 60);
            font-size: 13px;
            padding-left: 55px;
            line-height: 25px;
            letter-spacing: 0.3px;

            > img {
                position: absolute;
                left: 0;
                top: 5px;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: block;
            }

            div:not(.mc-message) {
                display: flex;
                justify-content: space-between;
                opacity: 0.85;

                > span {
                    white-space: nowrap;
                }

                > span:first-child {
                    overflow: hidden;
                    margin-right: 15px;
                }
            }

            .mc-message {
                font-weight: 500;
                font-size: 12px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                height: 25px;
            }
        }

        &:hover,
        &.mc-active {
            background-color: $background-gray;
        }

        &.mc-active {
            border-left-color: $color-blue;
        }
    }
}

.mc-chat-btn span, .mc-user-conversations > li > [data-count] {
    position: absolute;
    width: 20px;
    height: 20px;
    text-align: center;
    background: $color-red;
    color: $white;
    border-radius: 100%;
    font-weight: 500;
    font-size: 11px;
    line-height: 22px;
    top: 0;
    left: -3px;
    z-index: 2;
    animation: mc-fade-animation 0.3s;

    &:empty,
    &[data-count="0"] {
        display: none;
    }
}

.mc-user-conversations > li {

    & > [data-count] {
        left: 20px;
        top: 33px;
        border: 3px solid $white;
    }

    &:hover > [data-count] {
        border-color: $background-gray;
    }
}

/*

# MESSAGES LIST AND CHAT
==========================================================

*/

.mc-chat, .mc-list {
    .mc-btn-text:hover {
        color: $color-orange;
    }
}

.mc-list {
    > div {
        float: left;
        clear: both;
        position: relative;
        margin: 2px 10px 25px 20px;
        box-shadow: none;
        background-color: rgb(245, 245, 245);
        border-radius: 6px;
        padding: 12px;
        max-width: calc(90% - 110px);

        &.mc-thumb-active {
            margin-left: 55px;

            .mc-thumb {
                display: block;
            }

            &.mc-rich-cnt {
                max-width: calc(90% - 115px);
            }
        }

        &.mc-rich-cnt {
            background-color: rgb(247, 247, 247);
            max-width: calc(90% - 80px);

            .mc-message {
                overflow: visible;
            }

            .mc-agent-name {
                display: none;
            }
        }

        &[data-type="slider"], &[data-type="slider-images"] {
            width: 100%;
        }

        &:first-child > span {
            margin-top: 0 !important;
        }

        &:last-child {
            margin-bottom: 30px !important;
        }

        &:not(.mc-right):last-child, > [data-id="sending"] {
            animation: mc-fade-bottom-animation .4s;
        }

        &.mc-right {
            float: right;
            margin: 2px 20px 25px 10px;
            background-color: #FCE9E6;

            &.mc-rich-cnt {
                .mc-text {
                    background-color: #FCE9E6;
                }
            }

            .mc-thumb {
                right: -35px;
                left: auto;
            }

            .mc-message, .mc-message a, .mc-rich-message .mc-text, .mc-rich-message .mc-title {
                color: $color-dark-blue;
            }

            &.mc-thumb-active {
                margin-right: 50px;
            }

            .mc-time {
                right: 0;
                left: auto;
            }

            .mc-message:not(:empty) + .mc-message-attachments .mc-player {
                border-color: rgba(125, 37, 0, .17);
            }
        }

        &:not(.mc-right):not(.mc-label-date) + .mc-right, &.mc-right + div:not(.mc-right):not(.mc-label-date) {
            margin-top: 15px;
        }
    }

    .mc-thumb {
        position: absolute;
        left: -35px;
        top: 1px;
        width: 25px;
        display: none;

        > img {
            width: 25px;
            height: 25px;
            border-radius: 50%;
        }

        > div {
            display: none;
        }
    }

    .mc-message, .mc-message a {
        color: $color-gray;
        font-size: 13px;
        line-height: 21px;
        letter-spacing: 0.3px;
        outline: none;
    }

    .mc-message {
        overflow: hidden;
        text-overflow: ellipsis;

        b {
            font-weight: 500;
        }

        code {
            font-family: monospace;
            padding: 2px 7px;
            color: rgb(106, 44, 48);
            background: rgba(238, 238, 238, .5);
            border-radius: 3px;
            border: 1px solid rgb(219, 219, 219);
            max-width: 100%;
            white-space: normal;
            word-wrap: break-word;
            overflow: hidden;
        }

        code a {
            color: rgb(106, 44, 48) !important;
            text-decoration: none;
        }

        pre {
            font-family: monospace;
            margin: 0;
            color: rgb(106, 44, 48);
            overflow-x: scroll;
            background: rgba(255, 255, 255, .5);
            padding: 5px 5px 7px 5px;
            border-radius: 3px;
            border: 1px solid rgb(219, 219, 219);

            code {
                padding: 0;
                color: rgb(106, 44, 48);
                background: none;
                display: inline;
            }
        }

        span + pre, pre + span {
            margin-top: 10px;
            display: block;
        }


        .emoji-large {
            font-size: 35px;
            line-height: 45px;
        }

        .mc-image {
            margin-top: 6px;
        }

        .mc-rich-image .mc-image {
            margin-top: 0;
        }

        .mc-agent-name {
            display: block;
            opacity: 0.8;
            font-size: 11px;
        }

        &:not(.mc-message-media) > .mc-image {
            margin-bottom: 5px;
        }
    }

    .mc-message:not(:empty) + .mc-message-attachments {
        padding-top: 15px;

        .mc-player {
            border-top: 1px solid $border-color;
        }
    }

    .mc-message-attachments {
        a {
            text-decoration: none;
            font-style: normal;
            color: $color-gray;
            position: relative;
            display: block;
            transition: $transition;
            padding-left: 25px;
            font-size: 13px;
            line-height: 21px;
            letter-spacing: 0.3px;
            overflow: hidden;
            text-overflow: ellipsis;

            &:before {
                content: "\65";
                font-family: "Masi Chat Icons";
                position: absolute;
                left: 0;
                font-size: 15px;
                opacity: 0.8;
                line-height: 21px;
            }

            &:hover {
                color: $color-blue;
            }

            & + a {
                margin-top: 7px;
            }
        }

        &:empty {
            display: none;
        }
    }

    .mc-time {
        opacity: .9;
        color: $color-gray;
        font-size: 11px;
        letter-spacing: 0.5px;
        line-height: 16px;
        bottom: -20px;
        left: 0;
        white-space: nowrap;
        position: absolute;
        display: flex;

        > span:first-child {
            display: none;
        }

        i {
            padding-left: 10px;
        }
    }

    .mc-message-media {
        margin: -12px;
        background: $white;
        text-align: right;

        .mc-image:first-child {
            margin-top: 0;
        }
    }

    [data-id="sending"] {
        .mc-message {
            opacity: .5;
        }

        &.mc-right .mc-time, .mc-time {
            right: 19px;
            flex-direction: row;

            > i {
                position: relative;

                &:after {
                    content: "...";
                    position: absolute;
                    width: 15px;
                    left: calc(100% + 5px);
                    bottom: 0;
                    font-weight: 500;
                    letter-spacing: 1px;
                    overflow: hidden;
                    animation: mc-typing 1s infinite;
                }
            }
        }
    }

    [data-type="youtube-video"] {
        padding: 0;

        .mc-rich-message {
            margin: 0;
            padding: 0;
            border-radius: 6px;
            overflow: hidden;
        }
    }
}

.mc-list > div.mc-label-date, .mc-label-date-top {
    text-align: center;
    max-width: 100% !important;
    width: auto;
    float: none !important;
    background: none;
    margin: 0 !important;

    span {
        display: inline-block;
        background: #f5f7fa;
        padding: 0 10px;
        border-radius: 4px;
        font-size: 12px;
        line-height: 26px;
        letter-spacing: .3px;
        margin: 15px auto 15px auto;
        font-weight: 400;
        color: $color-gray;
        user-select: none;
    }
}

.mc-list > div.mc-label-date:first-child {
    padding-top: 8px;
}

.mc-label-date-top {
    position: absolute;
    top: 73px;
    left: 0;
    right: 5px;
    z-index: 2;
    opacity: 0;
    transition: $transition;

    &.mc-active {
        opacity: 1;
    }

    &:empty {
        display: none;
    }
}

.mc-label-date + .mc-label-date {
    display: none;
}

/*

# CHAT HTML COMPONENTS
==========================================================

*/

.mc-list {
    .mc-form {
        & + .mc-btn {
            margin-top: 25px;
        }

        & + .mc-form {
            margin-top: 15px;
        }
    }

    table {
        border-collapse: collapse;

        th,
        td {
            padding: 5px 7px;
            border-bottom: 1px solid rgba(143, 143, 143, 0.29);
            text-align: left;
            color: $color-black;
            font-size: 12px;
            line-height: 23px;
        }

        th {
            border-bottom: 1px solid rgba(143, 143, 143, 0.6);
            font-size: 13px;
            font-weight: 500;
            white-space: nowrap;
        }

        tr:last-child td {
            border-bottom: 0;
        }
    }

    .mc-text-list {
        > div {
            padding-bottom: 6px;

            > div:first-child {
                font-weight: 500;
            }

            &:last-child {
                padding-bottom: 0;
            }
        }
    }

    .mc-text-list-single {
        > div {
            position: relative;
            padding-left: 15px;

            &:before {
                content: "";
                position: absolute;
                width: 5px;
                height: 5px;
                background: $color-gray;
                border-radius: 50%;
                top: 9px;
                left: 0;
            }

            &[data-inner="true"] {
                padding-left: 35px;

                &:before {
                    left: 15px;
                }
            }
        }
    }

    .mc-text-list-numeric {
        counter-reset: list-number;

        > div {
            padding-left: 20px;

            &:not([data-inner]):before {
                content: counter(list-number) ". ";
                width: auto;
                height: auto;
                background: none;
                top: auto;
                border-radius: 0;
                counter-increment: list-number;
            }
        }
    }

    .mc-image-list {
        > div {
            padding: 5px 0 10px 50px;
            position: relative;

            > .mc-thumb {
                display: block;
                position: absolute;
                left: 0;
                top: 4px;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background-size: cover;
                margin: 0;
                background-position: center center;
            }

            .mc-list-title {
                font-weight: 500;
                font-size: 12px;
                line-height: 18px;

                span {
                    opacity: .7;
                }
            }

            &:last-child {
                padding-bottom: 0;
            }
        }

        & + a {
            margin-top: 15px;
        }
    }

    iframe {
        width: 100%;
        border: none;
        border-radius: 4px;
        background: #000
    }

    iframe:not([height]) {
        height: 128px;
        display: block;
    }
}

.mc-player {
    display: flex;
    align-items: center;
    margin: -6.5px -11px;

    > div {
        background: transparent;
        width: 20px;
        border-radius: 15px;
        padding: 5px 10px;
        cursor: pointer;
        font-size: 13px;
        line-height: 21px;
        transition: $transition;

        &:hover {
            color: $color-blue;
        }

        &:active {
            background: rgba(0, 0, 0, .08) !important;
        }
    }

    .mc-player-btn {
        width: 38px;
        height: 34px;
        line-height: 38px;
        font-size: 14px;
        padding: 0;
        text-align: center;
        border-radius: 50%;

        &:before {
            line-height: 34px;
        }
    }

    .mc-player-speed {
        display: flex;
        align-items: center;

        .mc-player-speed-number {
            font-weight: 500;
        }

        .mc-icon-close {
            font-size: 7px;
            height: 19px;
            line-height: 21px;
            padding-left: 2px;

            &:before {
                line-height: 20px;
            }
        }
    }

    .mc-player-download {
        text-align: center;

        &:before {
            transform: translateY(2px);
            display: inline-block;
        }
    }

    audio {
        position: absolute;
        width: 0;
        height: 0;
        visibility: hidden;
    }
}

.mc-right .mc-player > div {
    background-color: #FCE9E6;
    color: $color-dark-blue;
}

#mc-audio-clip {
    display: none;
    align-items: center;
    position: absolute;
    top: 1px;
    bottom: 0;
    right: 50px;
    left: 0;
    background: rgb(251, 251, 251);
    color: $color-gray;
    z-index: 2;

    .mc-icon {
        font-size: 15px;
        height: 100%;
        line-height: 100%;
        padding: 0 15px;
        cursor: pointer;
        color: $color-gray;
        transition: color .5s;

        &:before {
            line-height: 55px;
        }
    }

    .mc-icon-mic {
        font-size: 20px;
        color: $color-red;
    }

    .mc-icon-close {
        font-size: 11px;


        &:hover {
            color: $color-red;
        }
    }

    .mc-btn-mic {
        color: $color-red;

        &:hover {
            color: $color-black;
        }
    }

    .mc-btn-clip-player {
        display: none;
        width: 11px;

        &.mc-active {
            display: block;
            margin-right: -15px;
        }

        &:hover {
            color: $color-blue;
        }
    }

    .mc-audio-clip-time {
        font-size: 15px;
        font-weight: 500;
    }

    .mc-audio-clip-cnt {
        display: flex;
        align-items: center;
        padding: 0 15px;
        height: 100%;
        border-right: 1px solid $border-color;
        border-left: 1px solid $border-color;
    }

    &.mc-active {
        display: flex;
    }
}

/*

# EDITOR
==========================================================

*/

.mc-editor {
    background: rgb(251, 251, 251);
    padding-bottom: 0;
    padding: 15px;
    position: relative;
    margin: 0;

    textarea {
        margin: 0 !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        font-size: 13px;
        letter-spacing: 0.3px;
        width: 100%;
        height: 25px;
        line-height: 25px;
        min-height: 25px !important;
        padding: 0px !important;
        outline: none !important;
        text-align: left;
        font-weight: 400;
        resize: none !important;
        border: none !important;
        color: $color-gray !important;
        background: transparent !important;
        transition: $transition;
        overflow: hidden;
        display: block;
    }

    .mc-submit {
        color: $color-blue;
        display: none;
        cursor: pointer;
        font-size: 18px;
        line-height: 19px;
        padding-left: 13px;
        height: 19px;
        transition: $transition;

        &:hover {
            color: $color-dark-blue;
        }
    }

    .mc-bar {
        position: absolute;
        display: flex;
        align-items: center;
        max-height: 26px;
        right: 0;
        top: 0;
        bottom: 0;
    }

    &.mc-active .mc-bar {
        .mc-submit {
            display: block;
        }

        .mc-btn-attachment, .mc-btn-audio-clip {
            display: none;
        }
    }

    .mc-upload-form-editor {
        display: none;
    }

    .mc-bar-icons {
        display: flex;
        justify-content: flex-start;
        width: auto;

        > div {
            width: 30px;
            height: 28px;
            margin-right: 8px;
            position: relative;
            cursor: pointer;

            &:last-child {
                margin-right: 0;
            }

            &:before {
                font-family: "Masi Chat Icons";
                position: absolute;
                left: 7px;
                font-size: 19px;
                line-height: 30px;
                color: $color-gray;
            }

            &.mc-btn-attachment:before {
                content: "\65";
            }

            &.mc-btn-saved-replies:before {
                content: "\71";
                font-size: 21px;
            }

            &.mc-btn-attachment {
                margin-right: 7px;
            }

            &.mc-btn-audio-clip {
                margin-right: 0;
            }

            &.mc-btn-emoji:before {
                content: "\72";
            }

            &.mc-btn-audio-clip:before {
                content: "\e904";
                font-size: 22px;
                left: 3px;
            }

            &.mc-btn-open-ai:not(.mc-active) {
                display: none;
            }

            &.mc-btn-woocommerce:before {
                content: "\52";
                font-size: 22px;
            }

            &.mc-btn-open-ai.mc-loading:before {
                left: 15px;
            }

            &:hover:before {
                color: $color-pink;
            }
        }
    }

    .mc-attachments {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;

        > div {
            margin: 5px 5px 5px 0;
            padding: 0 20px 0 0;
            position: relative;
            font-size: 11px;
            line-height: 13px;
            height: 13px;
            font-weight: 500;
            cursor: default;

            > i {
                position: absolute;
                right: 5px;
                font-size: 8px;
                line-height: 8px;
                width: 8px;
                height: 8px;
                top: 50%;
                margin: -5px 0 0 0;
                color: $color-red;
                cursor: pointer;
                opacity: 0;
                transition: $transition;
            }

            &:hover > i {
                opacity: 1;
            }
        }
    }

    .mc-loader {
        height: 25px;
        width: 25px;
        display: none;
        position: absolute;
        right: 0;
        background: $white;

        &.mc-active {
            display: block;
        }
    }

    &.mc-drag {
        border-color: $color-blue !important;
        background-color: rgb(224, 236, 246) !important;

        .mc-textarea, .mc-bar, .mc-attachments, .mc-suggestions {
            opacity: 0 !important;
            z-index: -1 !important;
        }

        &:before {
            content: "\30";
            font-family: "Masi Chat Icons";
            font-style: normal;
            font-weight: normal;
            text-transform: none;
            position: absolute;
            font-size: 13px;
            width: 20px;
            height: 20px;
            top: 50%;
            left: 50%;
            margin: -10px 0 0 -10px;
            color: $color-blue;
        }
    }
}

/*

# ARTICLES
==========================================================

*/

.mc-article {
    h1, h2, h3 {
        font-weight: 500;
    }

    .mc-title {
        font-weight: 500;
        font-size: 18px;
        line-height: 30px;
        padding-bottom: 25px;
        letter-spacing: 0.5px;
        position: relative;

        .mc-icon-close {
            position: absolute;
            right: 0;
            top: 0px;
            font-size: 12px;
            transition: $transition;
            cursor: pointer;

            &:hover {
                color: $color-red;
            }
        }
    }

    .mc-content {
        font-size: 13px;
        line-height: 25px;
        color: $color-gray;
        letter-spacing: 0.3px;
        white-space: break-spaces;

        > img, .mc-image {
            max-width: 100%;
            margin: 15px 0;
            display: block;
            border-radius: 3px;
        }

        h1, h2, h3, h4, h5, h6 {
            letter-spacing: .3px;
            margin: 0;
            transition: $transition;
            color: $color-black;
            font-size: 18px;
            line-height: 25px;
            margin: 15px 0;
        }

        h3 {
            font-size: 16px;
            line-height: 21px;
        }

        h4, h5, h6 {
            font-size: 14px;
            line-height: 18px;
        }

        p {
            font-weight: 400;
            font-size: 13px;
            line-height: 25px;
            overflow: hidden;
            text-overflow: ellipsis;
            color: $color-gray;
            margin: 5px 0;
            letter-spacing: .3px;

            &:empty {
                display: none;
            }
        }

        b {
            font-weight: 500;
        }

        code {
            background: $background-gray;
            border: 1px solid $border-color;
            display: block;
            border-radius: 4px;
            padding: 10px;
            white-space: pre;
            overflow-x: scroll;
        }

        > *:first-child {
            margin-top: 0;
        }
    }

    .mc-btn-text {
        margin-top: 25px;
        text-decoration: none;
    }

    ul {
        padding-left: 15px;

        li {
            list-style: disc;
        }

        &.mc-ul-ordered, &.mc-ul-unordered {
            padding-left: 15px;
        }

        &.mc-ul-ordered li {
            list-style: decimal;
        }

        &.mc-ul-unordered li {
            list-style: disc;
        }
    }

    a, ul a {
        color: $color-black;
        text-decoration: underline;
    }

    .mc-rating, .mc-btn-text + .mc-article-category-links {
        display: flex;
        padding-top: 30px;
        margin-top: 30px;
        margin-bottom: 0;
        height: 20px;
        border-top: 1px solid $border-color;
    }

    .mc-rating {
        color: $color-black;
    }

    &[data-user-rating="1"] {
        [data-rating="positive"] {
            color: $color-green;
            cursor: default;
        }

        [data-rating="negative"] {
            display: none;
        }
    }

    &[data-user-rating="-1"] {
        [data-rating="positive"] {
            display: none;
        }

        [data-rating="negative"] {
            color: $color-red;
            cursor: default;
        }
    }
}

.mc-chat .mc-article {
    > .mc-title:first-child {
        border-bottom: 1px solid $border-color;
        padding-bottom: 30px;
        margin-bottom: 30px;
    }

    h2 {
        font-size: 17px;
    }

    h3, h4, h5, h6 {
        font-size: 15px;
    }
}

.mc-panel-articles {
    animation: mc-fade-animation .5s;
}

.mc-panel-articles .mc-title .mc-icon-close {
    display: none;
}

.mc-article-category-links {
    display: flex;
    flex-wrap: nowrap;
    overflow: hidden;
    margin: 15px 0 15px 0;
    height: auto !important;

    > span {
        position: relative;
        display: block;
        font-weight: 500;
        font-size: 13px;
        margin-right: 20px;
        letter-spacing: .3px;
        transition: $transition;

        & + span:before {
            content: "";
            position: absolute;
            left: -10px;
            top: 50%;
            margin-top: -6px;
            height: 12px;
            width: 1px;
            background: $color-blue;
        }
    }
}

.bxc-raw-html > img {
    max-width: 100%;
}

/*

# LOADING
==========================================================

*/

@keyframes mc-loading {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.mc-loading {
    position: relative;
    text-indent: -999995px;
    overflow: hidden;

    i {
        display: none;
    }


    div, ul, h1, h2, h3, h4, p, span, table, a, hr {
        opacity: 0;
    }
}

.mc-loading, .mc-loader {
    &:before {
        content: "\76";
        font-family: "Masi Chat Icons";
        font-style: normal;
        font-weight: normal;
        text-transform: none;
        text-indent: 0;
        animation: mc-loading 0.6s linear infinite;
        display: block;
        width: 30px;
        height: 30px;
        line-height: 29px;
        font-size: 21px;
        text-align: center;
        position: absolute;
        left: 50%;
        top: 50%;
        margin-top: -15px;
        margin-left: -15px;
        z-index: 1;
        color: $color-orange;
    }
}

.mc-btn.mc-loading:hover:before {
    color: $white;
}

/*

# BUTTONS
==========================================================

*/

.mc-btn, a.mc-btn {
    font-size: 13px;
    line-height: 36px;
    letter-spacing: 0.3px;
    font-weight: 500;
    border-radius: 30px;
    min-width: 0;
    background-color: $color-orange;
    text-decoration: none;
    color: $white;
    height: 35px;
    padding: 0 25px;
    position: relative;
    display: inline-block;
    border: none;
    text-align: center;
    cursor: pointer;
    outline: none;
    box-shadow: none;
    transition: $transition;
    user-select: none;

    &.mc-icon {
        padding-left: 50px;

        > i {
            position: absolute;
            left: 25px;
            line-height: 36px;

            &:before {
                line-height: 36px;
            }
        }
    }

    &:hover,
    &:active {
        background-color: $color-pink;
        color: $white;
    }

    &:before {
        color: $white;
    }

    &:not(.mc-hide) + a {
        margin-left: 15px;
    }

    &.mc-rich-btn {
        color: $white !important;
        line-height: 35px !important;
    }

    &.mc-icon-check:not(.mc-loading):before {
        margin-right: 15px;
        transform: translateY(2px);
        display: inline-block;
    }
}

.mc-btn.mc-btn-black {
    background-color: $color-black;

    &:hover {
        background-color: $color-gray;
    }
}

.mc-btn.mc-btn-red {
    background-color: $color-red;

    &:hover {
        background-color: $color-black;
    }
}

.mc-btn-icon {
    position: relative;
    cursor: pointer;
    width: 33px;
    height: 33px;
    border: 1px solid rgba(255, 255, 255, 0);
    opacity: 0.8;
    border-radius: 5px;
    display: inline-block;
    text-align: center;
    transition: $transition;
    text-decoration: none !important;
    overflow: hidden;
    color: $color-black;

    i {
        line-height: 40px;
        font-size: 18px;
        width: 33px;

        &:before {
            font-size: 18px;
            line-height: 35px;
        }

        &.mc-icon-file:before {
            font-size: 19px;
            display: block;
            transform: translateY(1px);
        }
    }

    &:before {
        font-size: 23px;
        line-height: 35px;
    }

    &:hover {
        opacity: 1;
        border-color: $color-pink;
        color: $color-pink;
        background-color: rgba(224, 50, 116, 0.08);
    }

    &.mc-btn-red:hover {
        border-color: $color-red;
        color: $color-red;
        background-color: $background-color-red;
    }

    &.mc-btn-red:hover i {
        color: $color-red !important;
    }

    &.mc-loading {
        background: none !important;
        border: none !important;
        cursor: default !important;
        transition: none !important;

        &:before {
            line-height: 30px;
        }
    }
}

.mc-btn-text {
    color: $color-gray;
    font-weight: 500;
    font-size: 13px;
    line-height: 17px;
    position: relative;
    background: none !important;
    cursor: pointer;
    display: inline-block;
    vertical-align: top;
    text-decoration: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: $transition;

    i {
        margin-right: 15px;
        margin-left: 1px;
        transform: translateY(2px);
        display: inline-block;
    }

    &:hover {
        color: $color-pink;
    }

    &.mc-btn-red:hover {
        color: $color-red;
    }
}

/*

# INPUT
==========================================================

*/

.mc-input {
    display: flex;
    justify-content: space-between;
    align-items: center;

    > span {
        display: inline-block;
        width: 150px;
        min-width: 150px;
        flex-grow: 1;
        font-weight: 600;
        font-size: 14px;
        letter-spacing: 0.3px;
        color: $color-gray;
    }

    > input,
    > select,
    > textarea {
        position: static;
        border-radius: 4px !important;
        color: $color-black;
        font-size: 13px;
        line-height: 35px;
        font-weight: 400;
        border: 1px solid $border-color;
        background-color: rgb(248, 248, 249);
        outline: none;
        height: 42px;
        min-height: 42px;
        padding: 0 10px;
        transition: all 0.4s;
        width: 100%;
        margin: 0 !important;
        box-sizing: border-box;
        box-shadow: none;

        &:focus, &.mc-focus {
            border: 1px solid $color-orange;
            box-shadow: 0 0 5px rgba(234, 115, 81, 0.2);
            background: $white;
            color: $color-black;
            outline: none !important;
        }

        &.mc-error {
            border: 1px solid $color-red;
            box-shadow: 0 0 5px rgba(202, 52, 52, 0.25);
        }
    }

    > select {
        min-height: 37px;
    }

    > textarea {
        line-height: 20px;
        min-height: 75px;
        padding: 8px 10px;
    }

    > input[type=date]::-webkit-clear-button {
        -webkit-appearance: none;
        display: none;
    }

    > input[type="checkbox"] {
        background: $white;
        clear: none;
        cursor: pointer;
        display: inline-block;
        line-height: 0;
        height: 35px;
        min-height: 35px;
        outline: 0;
        padding: 0;
        margin: 0;
        text-align: center;
        vertical-align: middle;
        width: 35px;
        min-width: 35px !important;
        outline: none;
        box-shadow: none;
        -webkit-appearance: none;
        appearance: none;

        &:checked:before {
            content: "\77" !important;
            font-family: "Masi Chat Icons" !important;
            font-style: normal !important;
            font-weight: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 35px;
            font-size: 15px;
            color: $color-orange;
            margin: 0;
            width: 100%;
            height: 100%;
        }
    }

    > div {
        padding-right: 30px;
        max-width: 800px;

        span {
            font-weight: 600;
            font-size: 15px;
            line-height: 25px;
            display: block;
        }

        p {
            font-size: 13px;
            line-height: 22px;
            letter-spacing: 0.3px;
            margin: 5px 0 0 0;
            color: $color-gray;
        }
    }

    > input[type="date"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        display: none;
    }

    & + .mc-input {
        margin-top: 10px;
    }

    &.mc-input-btn {
        input:first-child {
            line-height: 40px;
        }

        > div {
            background-color: $color-orange;
            color: $white;
            height: 42px;
            line-height: 47px;
            margin-left: -3px;
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
            text-align: center;
            padding: 0;
            width: 50px;
            cursor: pointer;
            z-index: 1;
            transition: $transition;

            &:hover {
                background-color: $color-pink;
            }

            &.mc-loading:before {
                color: $white;
            }

            &:not(.mc-loading):before {
                line-height: 42px;
            }
        }
    }

    &.mc-loading select {
        color: rgb(248, 248, 249);
    }
}

.mc-input-image {
    .image {
        border-radius: 4px;
        border: 1px solid $border-color;
        width: 100%;
        height: 100px;
        padding: 0;
        position: relative;
        cursor: pointer;
        background-color: rgb(248, 248, 249);
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        transition: border-color 0.4s, background-color 0.4s, box-shadow 0.4s;

        &:before {
            content: "\70";
            font-family: "Masi Chat Icons" !important;
            position: absolute;
            left: 50%;
            top: 50%;
            font-size: 9px;
            color: $color-gray;
            width: 30px;
            height: 30px;
            line-height: 30px;
            margin: -15px 0 0 -15px;
            text-align: center;
            transform: rotate(45deg);
        }

        &:hover {
            border: 1px solid $color-orange;
            box-shadow: 0 0 5px rgba(234, 115, 81, 0.2);
            background-color: $white;

            &:before {
                color: $color-pink;
            }
        }

        > .mc-icon-close {
            position: absolute;
            z-index: 9;
            right: 10px;
            top: 10px;
            height: 21px;
            width: 21px;
            font-size: 7px;
            text-align: center;
            line-height: 23px;
            cursor: pointer;
            background: $color-gray;
            color: $white;
            border-radius: 50%;
            display: none;
        }

        &[data-value] {
            &:before {
                display: none;
            }

            > .mc-icon-close {
                display: block;

                &:hover {
                    background-color: $color-red;
                }
            }
        }
    }

    &.mc-profile-image {
        justify-content: flex-start;

        span {
            flex-grow: 0;
        }

        .image {
            width: 65px;
            height: 65px;
            padding: 0;

            .mc-icon-close {
                right: -5px;
                top: -5px;

                &:before {
                    line-height: 21px;
                }
            }
        }
    }
}

.mc-input-select-input {
    > div {
        position: absolute;
        background: none;
        left: 6px;
        top: 13px;
        opacity: 0;
        padding: 0;
        color: $color-black;
        font-size: 13px;
        line-height: 22px;
        padding-left: 5px;
        font-weight: 400;
    }

    select {
        background: none !important;
        border: none !important;
        max-width: 50px;
        margin: 0 !important;
        transform: none !important;
        height: auto !important;
        padding: 0 !important;
        box-shadow: none !important;
        visibility: visible !important;
    }

    input {
        padding: 5px 0 0 60px;
    }

    input[disabled] {
        padding: 0 5px !important;
        border: none;
        background: none;
        box-shadow: none;
        width: 35px;
    }

    [type="number"]::-webkit-outer-spin-button, [type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        -moz-appearance: textfield;
        appearance: none;
    }

    .mc-active + div {
        opacity: 1;
    }
}

.mc-select-phone {
    z-index: 9;

    li {
        display: flex;
        align-items: center;
        white-space: nowrap;
    }

    img {
        border-radius: 2px;
        height: 15px;
        width: 23px;
        margin: 0 10px 0 0;
    }

    > div > p {
        display: flex;
        align-items: center;
        white-space: nowrap;
    }

    .mc-select {
        top: -12px;
        z-index: 8;
    }

    &.mc-single-prefix + input {
        padding-left: 50px;
    }
}

.mc-input-select-input > div.mc-select-phone {
    & + input {
        padding: 5px 0 0 100px;
    }

    .mc-select-search input {
        padding: 0 10px;
    }

    ul {
        width: 110px;
        max-height: 150px;
        min-height: 0 !important;
        background: $white !important;
        margin-left: -10px;
    }
}

.mc-select-search {
    position: absolute;
    border-radius: 4px;
    display: none;
    z-index: 9;

    input {
        padding: 0 10px;
        min-height: 28px;
        height: 28px;
        border: none;
        margin: 0 -10px !important;
        width: 85px;
        display: block;
        background: $white;
        color: $color-black;
        font-size: 13px;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
    }

    & + .mc-scroll-area {
        padding-top: 28px !important;
    }

    &.mc-active {
        display: block;
    }
}

/*

# LIGHTBOX
==========================================================

*/

.mc-lightbox-media {
    position: static;
    box-shadow: none !important;
    background: none !important;
    animation: none;


    > div > img {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-height: calc(100% - 30px);
        max-width: calc(100% - 30px);
        width: auto;
        border-radius: 6px;
        box-shadow: 0 4px 14px 0 rgba(0,0,0,0.2), 0 0 0 1px rgba(0,0,0,0.05);
        animation: mc-fade-animation 0.5s;
        z-index: 9999999995;
    }

    i {
        position: fixed;
        color: $white;
        width: 30px;
        height: 30px;
        display: block;
        top: 10px;
        right: 10px;
        text-align: center;
        line-height: 35px;
        font-size: 16px;
        cursor: pointer;
        transition: $transition;
        z-index: 9999999995;

        &:hover {
            color: rgb(202, 52, 52);
        }
    }

    &:not(.mc-active) {
        display: none;
    }

    &.mc-active + .mc-lightbox-overlay {
        display: block;
    }
}

.mc-lightbox-overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: 9999995;
    background-color: rgba(0, 0, 0, 0.6);
    display: none;
    animation: mc-fade-animation 0.3s;
}

/*

# OVERLAY PANEL
==========================================================

*/

.mc-overlay-panel {
    > div:first-child {
        display: none;
        justify-content: center;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: .3px;
        padding: 5px 15px;
        height: 35px;

        .mc-btn-icon {
            margin-right: -10px;

            &:before {
                font-size: 12px;
            }
        }
    }

    > div:last-child {
        height: calc(100% - 45px);
    }

    position: absolute;
    height: 0;
    background: white;
    left: 0;
    right: 0;
    bottom: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
    box-shadow: 0px -3px 6px 0px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    transition: height .5s;

    iframe {
        opacity: 0;
    }

    &.mc-active {
        height: 100%;
        z-index: 11;

        > div:first-child {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        iframe {
            opacity: 1;
            transition: opacity .5s;
            transition-delay: .5s;
        }
    }
}

/*

# IMAGE LIGHTBOX
==========================================================

*/

.mc-image {
    background: #000;
    border-radius: 4px;
    max-width: 250px;

    > img {
        max-width: 100%;
        border-radius: 3px;
        display: block;
        cursor: pointer;
        transition: $transition;

        &:hover {
            opacity: 0.8;
        }
    }

    &.mc-image-png {
        background: none;
    }

    &.mc-image-sticker {
        background: none;
        max-width: 150px !important;
    }
}

/*

# UPLOAD INPUT
==========================================================

*/

.mc-upload-form-admin {
    visibility: hidden;
    position: absolute;
    height: 0;
    width: 0;
    top: 0;
    left: 0;
}

/*

# SEARCH BUTTON
==========================================================

*/

.mc-search-btn, .mc-filter-btn {
    position: relative;
    width: 48px;
    height: 40px;

    > i {
        position: absolute;
        width: 20px;
        height: 20px;
        font-size: 19px;
        line-height: 14px;
        top: 10px;
        right: 0;
        cursor: pointer;
        transition: color 0.4s;
        z-index: 3;
    }

    i:hover, &.mc-active i {
        color: $color-pink;
    }
}

.mc-search-btn {
    > input {
        position: absolute !important;
        right: 0;
        height: auto;
        font-size: 15px;
        line-height: 39px;
        min-width: 295px;
        padding: 0 50px 0 15px !important;
        display: none;
        background-color: $white !important;
        border-radius: 5px;
        border: 1px solid $color-orange;
        box-shadow: 0 0 5px rgba(234, 115, 81, 0.2);
        z-index: 2;

        &:focus {
            outline: none;
            box-shadow: 0 0px 5px rgba(234, 115, 81, 0.5);
            border-color: $color-orange;
        }
    }

    &.mc-active input {
        display: block;
    }

    &.mc-active i {
        right: 15px;
    }
}

/*

# POPUP
==========================================================

*/

.mc-popup {
    width: 340px;
    height: 360px;
    position: fixed;
    transform: translateX(-50%);
    bottom: 0;
    left: 0;
    display: none;

    &:after {
        content: "";
        background: url(../media/triangle.svg) no-repeat center center;
        background-size: contain;
        position: absolute;
        width: 20px;
        height: 15px;
        bottom: -11px;
        left: 50%;
        transform: translateX(-50%);
        margin-left: 1px;
    }

    &.mc-active {
        display: block;
    }

    .mc-header {
        display: flex;
        justify-content: space-between;
        padding: 10px 15px 15px 15px;
        line-height: 35px;
        height: 35px;

        .mc-search-btn > input {
            min-width: 243px;
        }

        .mc-title {
            font-weight: 500;
        }

        .mc-search-btn {
            height: 40px;

            > i {
                top: 7px;
            }

            &.mc-active {
                margin-top: -5px;

                > i {
                    top: 11px;
                }
            }
        }
    }
}

.mc-popup-active .mc-tooltip {
    visibility: hidden;
}

/*

# EMOJI
==========================================================

*/

.mc-popup.mc-emoji {
    bottom: 71px;
    left: 28px;
    height: 285px;

    .mc-emoji-list {
        height: calc(100% - 80px);
        padding-right: 15px;
        margin: 0 0 15px 15px;

        > ul {
            list-style: none;
            padding: 0;
            margin: 0 15px 0 0;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
            grid-gap: 8px;

            > li {
                overflow: hidden;
                min-width: 0;
                text-align: center;
                cursor: pointer;
                font-size: 23px;
                line-height: 28px;
                margin: 0;
                padding: 0;
                transition: all 0.2s;

                &:hover {
                    transform: scale(1.2);
                }

                &.mc-emoji-title {
                    grid-column: 1 / 9;
                    text-align: left;
                }
            }
        }
    }

    .mc-emoji-bar {
        position: absolute;
        top: 65px;
        bottom: 15px;
        right: 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        border-radius: 5px;
        overflow: hidden;

        > div {
            width: 4px;
            height: 100%;
            background: rgb(236, 236, 236);
            margin: 0;
            cursor: pointer;

            &.mc-active,
            &:hover {
                background: $color-orange;
                border-radius: 5px;
            }
        }
    }

    img + img {
        display: none !important;
    }
}

/*

# SELECT AND MENU
==========================================================

*/

.mc-select {
    position: relative;
    color: $color-black;
    font-size: 14px;
    height: 35px;
    line-height: 35px;
    display: inline-block;

    p {
        position: relative;
        padding: 0 20px 0 0;
        margin: 0;
        cursor: pointer;
        font-weight: 500;
        letter-spacing: .3px;
        font-size: 13px !important;
        line-height: 35px !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        user-select: none;
        transition: $transition;

        span {
            font-weight: 400;
            opacity: 0.7;
        }

        &:after {
            content: "\61";
            font-family: "Masi Chat Icons";
            position: absolute;
            top: 0;
            right: 1px;
            font-size: 9px;
            font-style: normal;
            font-weight: normal;
            line-height: 35px;
        }

        &.mc-error {
            color: $color-red;
        }
    }

    ul {
        padding: 10px 0 !important;
        display: none;
        overflow: hidden;
    }

    &.mc-loading {
        overflow: visible;
    }

    &:not(.mc-hide) + .mc-select {
        margin-left: 15px;
    }

    &:not(.mc-disabled) p:hover {
        color: $color-blue;
    }

    &.mc-disabled {
        > p {
            cursor: default !important;
        }

        > p:after, > ul {
            display: none !important;
        }
    }
}

div ul.mc-menu, .mc-select ul {
    position: absolute;
    margin: 0;
    z-index: 5;

    &.mc-active {
        display: block;
        margin-bottom: 15px;
    }

    &.mc-scroll-area {
        height: auto;
        max-height: 300px;
        overflow-y: scroll;
    }

    li {
        cursor: pointer;
        padding: 6px 25px 6px 12px;
        margin: 0;
        font-weight: 500;
        font-size: 13px;
        letter-spacing: .3px;
        line-height: 20px;
        white-space: nowrap;
        list-style: none;
        user-select: none;
        transition: all 0.1s;

        &.mc-active {
            color: $color-blue;
        }

        &:hover {
            background-color: $color-blue;
            color: $white;
        }
    }
}

/*

# RICH MESSAGES
==========================================================

*/

.mc-rich-message {
    letter-spacing: 0.3px;

    .mc-top, .mc-title {
        font-weight: 500;
        font-size: 15px;
        line-height: 25px;
        margin-bottom: 10px;
    }

    .mc-text {
        color: rgb(86, 96, 105);
        font-size: 13px;
        line-height: 21px;

        & + div {
            margin-top: 20px;
        }
    }

    .mc-content:empty {
        display: none;
    }

    img {
        max-width: 100%;
    }

    .mc-input {
        position: relative;
        box-sizing: content-box;

        > span {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            border-radius: 5px;
            font-size: 13px;
            line-height: 44px;
            font-weight: 400;
            width: 100%;
            cursor: text;
            padding: 0 10px;
            box-sizing: border-box;
            z-index: 8;
            transition: top 0.2s;

            &.mc-active {
                font-size: 12px;
                line-height: 10px;
                height: 10px;
                top: -3px;
                left: 5px;
                right: auto;
                background: linear-gradient(180deg, rgb(245, 245, 245) 0%, rgb(245, 245, 245) 40%, rgb(255, 255, 255) 100%) !important;
                color: $color-blue;
                width: auto;
                min-width: 0;
                padding: 0 5px;
            }

            &.mc-filled {
                color: rgb(143, 143, 143);
            }

            & + input, & + select {
                padding-top: 5px;
            }
        }

        > input, select, textarea {
            background: $white;
        }

        &[data-type=select] {
            select {
                padding-left: 6px;
            }

            > span {
                cursor: default;
            }
        }

        &[data-type=select] > span:not(.mc-filled):not(.mc-active) + select {
            background: none;
            z-index: 2;
        }

        iframe {
            max-width: 100%;
            border-radius: 4px;
        }
    }

    .mc-input-image {
        background: $white;
        border: 1px solid $border-color;
        border-radius: 4px;

        > span,
        > span.mc-active {
            position: relative;
            min-width: 50px;
            top: 0;
            left: 0;
            font-size: 13px;
            line-height: 42px;
            background: none !important;
            height: auto;
            padding: 0 10px;
            color: $color-gray;
            width: calc(100% - 20px);
        }
    }

    .mc-input-image > .image {
        background-color: $white;
        min-width: 65px;
        border: 1px solid $white;
    }

    .mc-input-btn > span {
        width: calc(100% - 42px);
    }

    .mc-info {
        margin: 15px 0 0 0;
        font-size: 12px;
        line-height: 23px;
        letter-spacing: 0.5px;
        font-weight: 500;
        color: $color-red;

        &:empty {
            display: none;
        }
    }

    .mc-buttons {
        .mc-btn {
            margin-bottom: 10px;
        }

        .mc-btn:last-child {
            margin-bottom: 0;
        }
    }

    &.mc-success {
        .mc-top {
            display: none;
        }
    }

    .mc-link-area {
        font-size: 12px;
        line-height: 20px;
        color: rgb(128, 128, 128);
        padding: 15px 0;

        a {
            color: rgb(128, 128, 128);
            text-decoration: underline;
        }
    }

    .mc-form + .mc-buttons {
        margin-top: 15px;
    }

    .mc-card, .mc-slider {
        .mc-card-img {
            height: 175px;
            background-size: cover;
            background-position: center center;
            margin: -12px -12px 20px -12px;
            border-top-right-radius: 6px;
            border-top-left-radius: 6px;
            display: block;
        }

        .mc-card-header {
            font-weight: 500;
            font-size: 15px;
            line-height: 25px;
            color: $color-black;
        }

        .mc-card-extra {
            display: inline-block;
            background: rgba(0, 0, 0, 0.59);
            color: rgb(255, 255, 255);
            font-size: 13px;
            line-height: 13px;
            padding: 5px 7px;
            border-radius: 3px;
            margin-top: 15px;
        }

        .mc-card-img + div + .mc-card-extra {
            position: absolute;
            top: 15px;
            left: 15px;
            margin: 0;
        }

        .mc-card-description {
            margin-top: 15px;
        }

        .mc-card-btn {
            color: $color-orange;
            display: block;
            text-decoration: none;
            margin: 20px -15px 0 -15px;
            padding: 0 15px 7px 15px;
            font-weight: 500;
            transition: $transition;
            position: relative;
            background: none;

            &:hover {
                color: $color-pink;
                background: none !important;
            }

            &.mc-loading:before {
                margin-top: -9px;
            }

            &[class*="mc-icon-"]:before {
                top: 19px;
                right: 15px;
                position: absolute;
            }
        }
    }

    .mc-slider {
        margin: -13px -12px 0 -12px;
        overflow: hidden;

        > div {
            display: flex;
        }

        > div > div {
            position: relative;
            min-width: 100%;
            transition: margin-left 1s;
        }

        .mc-card-img {
            margin: 0 0 20px 0;
        }

        .mc-card-header, .mc-card-description {
            margin-left: 15px;
            margin-right: 15px;
        }

        .mc-card-description {
            height: 63px;
            overflow: hidden;
        }

        .mc-card-btn {
            margin: 20px 0 -3px 0;
        }
    }

    .mc-slider-images {
        margin: -12px;
        border-radius: 4px;
        background: #000;
        cursor: pointer;

        .mc-card-img {
            margin: 0;
            transition: $transition;

            &:hover {
                opacity: 0.8;
            }
        }
    }

    &[data-type="slider-images"] .mc-slider-arrow {
        top: calc(50% - 15px);
    }

    .mc-slider-arrow {
        position: absolute;
        top: 160px;
        left: -10px;
        margin: 0;
        background: rgb(255, 255, 255);
        width: 30px;
        height: 30px;
        border-radius: 50%;
        text-align: center;
        line-height: 35px;
        font-size: 11px;
        box-shadow: 0 0 7px rgba(0, 0, 0, .3);
        cursor: pointer;
        transition: $transition;
        display: none;

        &:hover {
            color: $color-orange;
        }

        &.mc-active {
            display: block;
        }

        &:before {
            line-height: 30px;
        }
    }

    .mc-slider-arrow.mc-icon-arrow-right {
        left: auto;
        right: -10px;
    }

    .mc-btn {
        width: auto;
        display: block;
        border-radius: 4px;
    }

    &[disabled] {
        .mc-buttons .mc-btn {
            background: none;
            border: 1px solid $border-color;
            color: $color-black !important;
            font-weight: 400;
            cursor: default;
        }

        .mc-input-text {
            border: 1px solid $border-color !important;
            border-radius: 4px;

            span {
                color: $color-black;
                position: static;
                height: auto;
                padding: 0 10px;
                background: none !important;
                font-size: 13px;
                line-height: 44px;
            }

            input {
                display: none;
            }
        }

        .mc-select {
            p {
                display: none;
            }

            ul {
                display: block;
                box-shadow: none;
                padding: 0 !important;
                margin: 0;
                background: none;
                border: 1px solid $border-color !important;
                border-radius: 4px;
                position: static;
            }

            li {
                font-weight: 400;
                font-size: 13px;
                line-height: 25px;
                background: none !important;
                color: $color-black !important;
            }
        }

        &.mc-rich-inputs .mc-submit {
            display: none;
        }

        &[data-type="chips"] .mc-content {
            display: none;
        }
    }

    &.mc-rich-card {
        padding: 0;
    }
}

.mc-list .mc-rich-registration .mc-buttons, .mc-list .mc-rich-login .mc-buttons {
    text-align: center;
}

.mc-rich-success {
    display: block;

    & + .mc-rich-success {
        padding-top: 10px;
    }

    > span + div {
        padding-top: 12px;
    }
}

.mc-rich-loading {
    width: 30px;
    height: 30px;
}

.mc-list {
    .mc-rich-select {
        p {
            height: 35px;
            line-height: 35px;
            min-width: 100px;
            border-radius: 4px;
            border: 1px solid rgb(207, 207, 207);
            background: $white;
            padding: 0 25px 0 15px;

            &:after {
                right: 8px;
            }

            &:hover {
                border: 1px solid $color-orange;
                box-shadow: 0 0 5px rgba(234, 115, 81, 0.2);
            }
        }

        ul {
            margin-bottom: 25px;
            min-width: 140px;
        }
    }

    .mc-rich-table {
        overflow: hidden;

        .mc-content {
            overflow-x: scroll;
        }
    }
}

.mc-timetable {
    > div {
        > div:first-child {
            font-weight: 500;
        }

        > div[data-time] {
            display: block;
        }

        > div:last-child {
            span {
                padding: 0 5px;
            }
        }

        margin-bottom: 10px;
    }

    > span {
        position: relative;
        padding-left: 20px;
        white-space: nowrap;
        overflow: hidden;
        line-height: 13px;
        font-size: 11px;
        margin-top: 20px;
        display: block;

        i {
            position: absolute;
            left: 0;
            top: 0;
            font-size: 12px;
            line-height: 12px;
        }
    }
}

[data-id="rating"] {
    .mc-input-textarea {
        margin: 0 15px 15px 15px;
    }
}

.mc-rating {
    display: flex;
    align-items: center;
    justify-content: center;

    > span {
        font-weight: 500;
        font-size: 13px;
        letter-spacing: .3px;
    }

    > div {
        display: flex;
        padding-left: 10px;
    }

    .mc-input-textarea {
        display: none;

        & + div {
            padding: 0;
        }
    }

    i {
        position: relative;
        overflow: visible;
        margin: 0 10px;
        font-size: 15px;
        line-height: 14px;

        span {
            position: absolute;
            opacity: 0;
            white-space: nowrap;
            font-style: normal;
            background: $color-black;
            color: $white;
            font-size: 13px;
            letter-spacing: .3px;
            padding: 7px 10px;
            top: -40px;
            left: 50%;
            border-radius: 4px;
            transform: translate(-50%, -20px);
        }

        &:hover span {
            opacity: 1;
            transform: translate(-50%, 0);
            transition: all .5s;
        }

        &.mc-loading span {
            display: none;
        }
    }

    .mc-loading {
        width: 20px;
        height: 20px;
    }

    [data-rating] i {
        margin-left: 0;
    }

    div.mc-submit + .mc-submit {
        margin-left: 15px;
    }

    [data-rating="positive"] {
        cursor: pointer;

        span {
            background-color: $color-green;
        }

        &:hover {
            color: $color-green;
        }
    }

    [data-rating="negative"] {
        cursor: pointer;

        span {
            background-color: $color-red;
        }

        &:hover {
            color: $color-red;
        }
    }
}

.mc-rating-message {
    > div:first-child {
        color: $color-red;
        font-size: 13px;
        letter-spacing: .3px;
        font-weight: 500;

        i {
            font-size: 15px;
            margin-right: 10px;
            transform: translateY(3px);
            display: inline-block;
        }

        & + div {
            margin-top: 10px;
        }
    }

    &.mc-rating-positive > div:first-child {
        color: $color-green;
    }
}

.mc-articles {
    text-align: left;

    > div {
        letter-spacing: .3px;
        cursor: pointer;

        > div {
            line-height: 25px;
            max-height: 50px;
            font-weight: 500;
            font-size: 13px;
            overflow: hidden;
            transition: $transition;
        }

        > span {
            font-weight: 400;
            padding-top: 5px;
            display: block;
            height: 17px;
            font-size: 13px;
            line-height: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            letter-spacing: 0.3px;
            color: $color-gray;

            &:empty {
                display: none;
            }
        }

        &:hover > div {
            color: $color-orange;
        }

        & + div {
            margin-top: 18px;
            padding-top: 15px;
            border-top: 1px solid rgb(213, 213, 213);
        }

        & + .mc-title {
            border-top: none;
        }

        &.mc-title {
            font-weight: 500;
            font-size: 17px;
            color: $color-orange;
            cursor: default;

            & + div {
                border-top: none;
                padding-top: 0;
            }
        }
    }

    > p {
        font-size: 14px;
        letter-spacing: 0.3px;
        color: $color-gray;
    }
}

.mc-rich-articles .mc-articles {
    margin: 25px 0;
}

.mc-rich-cnt {
    &[data-type="chips"] {
        box-shadow: none !important;
        border: none !important;
        background: none !important;
        padding: 0;

        .mc-top {
            margin-bottom: 6px;
        }

        .mc-text {
            background-color: rgb(245, 245, 245);
            border-radius: 6px;
            padding: 13px 15px;
        }

        .mc-content {
            margin-top: 11px;
        }

        .mc-buttons {
            margin-bottom: -8px;
            display: flex;
            flex-wrap: wrap;

            .mc-btn {
                overflow: hidden;
                margin: 0 10px 10px 0;
            }
        }

        .mc-time {
            margin-top: 5px !important;
        }
    }

    &[data-type="video"] {
        padding: 0;

        .mc-rich-message {
            padding: 0;
            border-radius: 4px;
        }

        .mc-text {
            background: none;
            margin: 10px 10px -10px 10px;
        }

        iframe {
            display: block;
            border-radius: 4px;
        }

        .mc-time {
            margin-top: 10px !important;
        }
    }
}

.mc-rich-chips .mc-btn, .mc-rich-buttons .mc-btn {
    height: auto;
    line-height: 19px;
    padding: 8px 15px;
}

.mc-social-buttons {
    margin: -5px;

    div {
        top: 0;
        position: relative;
        color: #FFF;
        font-size: 12px;
        font-weight: 400;
        line-height: 30px;
        height: 26px;
        width: 60px;
        min-width: 60px;
        margin: 5px;
        text-align: center;
        display: inline-block;
        background: #000;
        border-radius: 3px;
        cursor: pointer;
        transition: $transition;

        &:hover {
            opacity: .8;
            top: -4px;
        }
    }

    .mc-icon-social-fb {
        background: #4267B2;
    }

    .mc-icon-social-tw {
        background: #55ACEE;
    }

    .mc-icon-social-li {
        background: #0077B5;
    }

    .mc-icon-social-pi {
        background: #CB2027;
    }

    .mc-icon-social-wa {
        background: #25D366;
    }
}

/*

# MISCELLANEOUS
==========================================================

*/

.mc-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr;
    grid-gap: 30px;

    > div {
        margin: 0 !important;
    }
}

.mc-grid-3 {
    grid-template-columns: 1fr 1fr 1fr;
}
