<?php

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

/*
 * ==========================================================
 * FUNCTIONS_EMAIL.PHP
 * ==========================================================
 *
 * Email functions file. © 2017-2025 app.masichat.com. All rights reserved.
 *
 * ----------------------------------------------------------
 * EMAIL
 * ----------------------------------------------------------
 *
 * 1. Create the email contents
 * 2. Create the email contents secondary function
 * 3. Send an email to the given address
 * 4. Send an email to the address of the given user ID
 * 5. Send a test email
 * 6. Check if the active user can send the requested email
 * 7. Email piping function
 * 8. Send the successfull subscription email
 * 9. Append the email header and the signature to an email content
 * 10. Manage email attachments
 * 11. Return the conversation messges as HTML code
 * 12. Remove an email notification from the email cron job list
 * 13. Convert the text formatting of Masi Chat to HTML
 * 14. Remove the text formatting of Masi Chat
 * 15. Newsletter
 * 16. Email cron job notifications
 * 17. Convert rich messages to HTML
 *
 */

function mc_email_create($recipient_id, $sender_name, $sender_profile_image, $message, $attachments = [], $department = false, $conversation_id = false, $cc = false) {
    $recipient = false;
    $recipient_name = '';
    $recipient_email = '';
    $recipient_user_type = 'agent';
    if ($recipient_id == 'email-test') {
        $recipient_name = 'Test user';
    } else if ($recipient_id == -1 || $recipient_id == 'agents' || $recipient_id == 'all-agents' || strpos($recipient_id, 'department-') !== false) {
        $department = $department ? $department : (strpos($recipient_id, 'department-') !== false ? substr($recipient_id, 11) : false);
        $agents = mc_db_get('SELECT id, first_name, last_name, email FROM mc_users WHERE (user_type = "agent"' . (mc_get_setting('stop-notify-admins') ? '' : ' OR user_type = "admin"') . ') ' . (empty($department) || $department == -1 ? ($recipient_id == 'agents' ? ' AND (department IS NULL OR department = "")' : '') : ' AND department = ' . mc_db_escape($department)), false);
        $online_agents_ids = mc_get_online_user_ids(true);

        for ($i = 0; $i < count($agents); $i++) {
            if (!in_array($agents[$i]['id'], $online_agents_ids)) {
                $recipient_name .= mc_get_user_name($agents[$i]) . ', ';
                $recipient_email .= $agents[$i]['email'] . ',';
                if ($i == 0) {
                    $recipient_id = $agents[$i]['id'];
                }
            }
        }
        $recipient_name = mb_substr($recipient_name, 0, -2);
        $recipient_email = substr($recipient_email, 0, -1);
    } else {
        if (!mc_email_security($recipient_id) && mc_get_active_user_ID() != $recipient_id) {
            return mc_error('security-error', 'mc_email_create');
        }
        $recipient = mc_get_user($recipient_id);
        if (!$recipient || !$recipient['email']) {
            return new MCValidationError('email-not-found');
        }
        $recipient_name = mc_get_user_name($recipient);
        $recipient_email = $recipient['email'];
        $recipient_user_type = $recipient['user_type'];
    }
    if (defined('MC_DIALOGFLOW') && strpos($message, '<div style') === false) {
        $message = mc_google_translate_auto($message, $recipient_id);
    }

    $subject_and_body = mc_email_get_subject_and_body($recipient_user_type, $recipient_id);
    $email = mc_email_create_content($subject_and_body[0], $subject_and_body[1], $attachments, ['conversation_url_parameter' => ($recipient && $conversation_id ? ('https://app.masichat.com/?conversation=' . $conversation_id . '&token=' . $recipient['token']) : ''), 'message' => $message, 'recipient_name' => $recipient_name, 'sender_name' => $sender_name, 'sender_profile_image' => str_replace('user.svg', 'user.png', $sender_profile_image), 'conversation_id' => $conversation_id]);
    $piping = mc_email_piping_suffix($conversation_id);
    $delimiter_text = 'Please type your reply above this line';
    $piping_delimiter = $piping && mc_get_multi_setting('email-piping', 'email-piping-delimiter') ? ('<div style="color:#b5b5b5">### ' . (is_numeric($recipient_id) ? mc_t($delimiter_text, mc_get_user_language($recipient_id)) : mc_($delimiter_text)) . ' ###</div><br><br>') : '';

    mc_webhooks('MCEmailSent', ['recipient_id' => $recipient_id, 'message' => $message, 'attachments' => $attachments]);

    $result = mc_email_send($recipient_email, ($piping ? 'Re: ' . $conversation_id . ' | ' : '') . $email[0], $piping_delimiter . $email[1], $piping, $cc);

    return $result;
}

function mc_email_get_subject_and_body($recipient_user_type, $recipient_id = false) {
    $is_agent = mc_is_agent($recipient_user_type);
    $suffix = $is_agent ? 'agent' : 'user';
    $settings = mc_get_multilingual_setting('emails', 'email-' . $suffix, mc_get_user_language(is_numeric($recipient_id) ? $recipient_id : false));
    $body = trim($settings['email-' . $suffix . '-content']);
    if (empty($body) && defined($is_agent ? 'MC_CLOUD_EMAIL_BODY_AGENTS' : 'MC_CLOUD_EMAIL_BODY_USERS')) {
        $body = $is_agent ? MC_CLOUD_EMAIL_BODY_AGENTS : MC_CLOUD_EMAIL_BODY_USERS;
        if (!$is_agent && defined('DIRECT_CHAT_URL')) {
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            $body = str_replace('{conversation_link}', DIRECT_CHAT_URL . '/' . account_chat_id(account()['user_id']) . '?chat=open', $body);
        }
    }
    return [$settings['email-' . $suffix . '-subject'], $body];
}

function mc_email_create_content($subject, $body, $attachments, $replacements) {
    if (empty($attachments)) {
        $attachments = [];
    }
    if (!$subject) {
        $subject = 'New message from {sender_name}';
    }
    if (!$body) {
        $body = 'Hello {recipient_name}!<br />{message}{attachments}';
    }
    $subject = str_replace(['{recipient_name}', '{sender_name}', '{conversation_id}'], [$replacements['recipient_name'], mc_isset($replacements, 'sender_name'), mc_isset($replacements, 'conversation_id')], $subject);
    $body = str_replace(['{conversation_url_parameter}', '{recipient_name}', '{sender_name}', '{sender_profile_image}', '{message}', '{attachments}', '{conversation_link}', '{conversation_id}'], ['conversation_url_parameter' => mc_isset($replacements, 'conversation_url_parameter', ''), $replacements['recipient_name'], mc_isset($replacements, 'sender_name'), str_replace('user.svg', 'user.png', mc_isset($replacements, 'sender_profile_image')), $replacements['message'], mc_email_attachments_code($attachments), (mc_is_cloud() ? CLOUD_URL : 'https://app.masichat.com/admin.php') . (isset($replacements['conversation_id']) ? '?conversation=' . $replacements['conversation_id'] : ''), mc_isset($replacements, 'conversation_id')], $body);
    return [$subject, $body];
}

function mc_email_send($to, $subject, $body, $sender_suffix = '', $cc = false) {
    $settings = mc_get_setting('email-server');
    $host = mc_isset($settings, 'email-server-host');
    if (!$host && mc_is_cloud()) {
        $settings = ['email-server-host' => CLOUD_SMTP_HOST, 'email-server-user' => CLOUD_SMTP_USERNAME, 'email-server-password' => CLOUD_SMTP_PASSWORD, 'email-server-from' => CLOUD_SMTP_SENDER, 'email-sender-name' => CLOUD_SMTP_SENDER_NAME, 'email-server-port' => CLOUD_SMTP_PORT];
        $host = CLOUD_SMTP_HOST;
    }
    if (empty($to)) {
        return false;
    }
    if ($host) {
        // Use the PHPMailer wrapper to prevent duplicate class declarations
        require_once (__DIR__ . '/../../phpmailer_wrapper.php');

        $port = str_replace("\xEF\xBB\xBF", '', $settings['email-server-port']);
        $mail = new PHPMailer;

        // Ensure line breaks are properly handled for HTML emails
        // First normalize all line breaks to \n
        $body = str_replace(["\r\n", "\r"], "\n", $body);

        // Process the body with text formatting
        $body = trim(mc_text_formatting_to_html($body));

        // If the body doesn't already have HTML tags for line breaks, add them
        if (strpos($body, '<br') === false && strpos($body, '<p') === false && strpos($body, '<div') === false) {
            $body = nl2br($body);
        }

        $mail->CharSet = 'UTF-8';
        $mail->Encoding = 'base64';
        $mail->isSMTP();
        $mail->Host = $host;
        $mail->SMTPAuth = true;
        $mail->Username = str_replace("\xEF\xBB\xBF", '', $settings['email-server-user']);
        $mail->Password = str_replace("\xEF\xBB\xBF", '', $settings['email-server-password']);
        $mail->SMTPSecure = $port == 25 ? '' : ($port == 465 ? 'ssl' : 'tls');
        $mail->Port = $port;
        $mail->setFrom($settings['email-server-from'], mc_isset($settings, 'email-sender-name', '') . $sender_suffix);
        $mail->isHTML(true);
        $mail->Subject = trim($subject);
        $mail->Body = $body;
        $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $body));
        $mail->SMTPOptions = ['ssl' => ['verify_peer' => false, 'verify_peer_name' => false, 'allow_self_signed' => true]];
        if (mc_is_cloud()) {
            $mail->addCustomHeader('List-Unsubscribe', '<' . MC_URL . '?setting=notifications>');
            if ($settings['email-server-host'] == CLOUD_SMTP_HOST) {
                require_once(MC_CLOUD_PATH . '/account/functions.php');
                $response = cloud_email_limit();
                if ($response !== true) {
                    return $response;
                }
            }
        }
        if (strpos($to, ',') > 0) {
            $emails = explode(',', $to);
            for ($i = 0; $i < count($emails); $i++) {
                $mail->addAddress($emails[$i]);
            }
        } else {
            $mail->addAddress($to);
        }
        if ($cc) {
            if (is_string($cc)) {
                $cc = explode(',', $cc);
            }
            for ($i = 0; $i < count($cc); $i++) {
                $mail->AddCC($cc[$i]);
            }
        }
        if (!$mail->send()) {
            return mc_error('email-error', 'mc_email_send', $mail->ErrorInfo);
        } else {
            return true;
        }
    } else {
        return mail($to, $subject, $body);
    }
}

function mc_email($recipient_id, $message, $attachments = [], $sender_id = -1) {
    if (!$recipient_id || empty($message)) {
        return new MCValidationError('missing-user-id-or-message');
    }
    if (!mc_email_security($recipient_id)) {
        return mc_error('security-error', 'mc_email');
    }
    $sender = $sender_id == -1 ? mc_get_active_user() : mc_get_user($sender_id);
    $user = mc_get_user($recipient_id);
    if ($sender && $user && isset($sender['id']) && isset($user['id'])) {
        if (!$user['email']) {
            return new MCValidationError('user-email-not-found');
        }
        $subject_and_body = mc_email_get_subject_and_body($user['user_type'], $recipient_id);
        $email = mc_email_create_content($subject_and_body[0], $subject_and_body[1], $attachments, ['message' => $message, 'recipient_name' => mc_get_user_name($user), 'sender_name' => mc_get_user_name($sender), 'sender_profile_image' => str_replace('user.svg', 'user.png', $sender['profile_image'])]);

        // Use cloud_merge_field_username if available
        if (mc_is_cloud() && function_exists('cloud_merge_field_username')) {
            require_once(MC_CLOUD_PATH . '/account/functions.php');

            // Prepare username - handle empty names properly
            $username = '';
            if ($user && !empty($user['first_name'])) {
                $username = mc_get_user_name($user);
            } else if (strpos($user['email'], '@') !== false) {
                // If no user found but we have an email, use the part before @ as a fallback name
                $username_parts = explode('@', $user['email']);
                $username = ucfirst(str_replace(['.', '_', '-'], ' ', $username_parts[0]));
            }

            return mc_email_send($user['email'], $email[0], cloud_merge_field_username($email[1], $username));
        } else {
            return mc_email_send($user['email'], $email[0], $email[1]);
        }
    } else {
        return mc_error('user-or-sender-not-found', 'mc_email');
    }
}

function mc_email_send_test($to, $email_type) {
    $user = mc_get_active_user();
    $name = '';

    // Properly handle username
    if ($user && !empty($user['first_name'])) {
        $name = mc_get_user_name($user);
    } else if (strpos($to, '@') !== false) {
        // If no user found but we have an email, use the part before @ as a fallback name
        $name_parts = explode('@', $to);
        $name = ucfirst(str_replace(['.', '_', '-'], ' ', $name_parts[0]));
    }

    $image = MC_URL . '/media/user.png';
    $attachments = [['Example link', $image], ['Example link two', $image]];
    $subject_and_body = mc_email_get_subject_and_body($email_type);
    $email = mc_email_create_content($subject_and_body[0], $subject_and_body[1], $attachments, ['message' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam', 'recipient_name' => $name, 'sender_name' => $name, 'sender_profile_image' => $user ? $user['profile_image'] : $image]);

    // Use cloud_merge_field_username if available
    if (mc_is_cloud() && function_exists('cloud_merge_field_username')) {
        require_once(MC_CLOUD_PATH . '/account/functions.php');
        return mc_email_send($to, $email[0], cloud_merge_field_username($email[1], $name));
    } else {
        return mc_email_send($to, $email[0], $email[1]);
    }
}

function mc_email_security($user_id) {
    if (mc_is_agent() || !empty($GLOBALS['MC_FORCE_ADMIN'])) {
        return true;
    } else {
        $user = mc_db_get('SELECT user_type FROM mc_users WHERE id = ' . $user_id);
        return !mc_is_error($user) && isset($user['user_type']) && mc_is_agent($user['user_type']);
    }
}

function mc_email_piping($force = false) {
    if (mc_is_cloud()) {
        mc_cloud_load_by_url();
        mc_cloud_membership_validation(true);
    }
    $settings_repeater = mc_get_setting('email-piping');
    $error = false;
    if (isset($settings_repeater['email-piping-active'])) { // Deprecated
        $settings_repeater = [$settings_repeater]; // Deprecated
    } // Deprecated
    for ($j = 0; $j < count($settings_repeater); $j++) {
        $settings = $settings_repeater[$j];
        if (!empty($settings['email-piping-active'])) {
            $port = $settings['email-piping-port'];
            $host = $settings['email-piping-host'];
            $all_emails = mc_isset($settings, 'email-piping-all');
            $today = date('d F Y');
            $last_check = mc_get_external_setting('email-piping-check');
            ini_set('default_socket_timeout', 5);
            imap_timeout(IMAP_OPENTIMEOUT, 5);
            $inbox = imap_open('{' . $host . ':' . $port . '/' . ($port == 143 || $port == 993 ? 'imap' : 'pop3') . ($port == 995 || $port == 993 ? '/ssl' : '') . ($port == 995 || $port == 993 ? '/novalidate-cert' : '') . '}INBOX', $settings['email-piping-user'], $settings['email-piping-password']);
            if (function_exists('ini_restore')) {
                ini_restore('default_socket_timeout');
            }
            $attachments_path = mc_upload_path(false, true) . '/';
            $attachments_url = mc_upload_path(true, true) . '/';
            $is_s3 = mc_get_multi_setting('amazon-s3', 'amazon-s3-active') || defined('MC_CLOUD_AWS_S3');
            if ($inbox) {
                set_time_limit(mc_is_cloud() ? 100 : 1000);
                $emails = imap_search($inbox, 'ALL SINCE "' . (empty($last_check) ? $today : $last_check) . '"');
                if ($emails) {
                    $department_id = mc_isset($settings, 'email-piping-department');
                    $history = mc_get_external_setting('email-piping-history', []);
                    $history_new = [];
                    rsort($emails);
                    foreach ($emails as $email_number) {
                        $overview = imap_headerinfo($inbox, $email_number, 0);
                        $follow_up = false;
                        if (!$overview || (!isset($overview->senderaddress) && !isset($overview->from))) {
                            $overview = imap_fetch_overview($inbox, $email_number, 0)[0];
                            $to = $overview->to;
                            $from = $overview->from;
                            $follow_up = strpos($to, '| MC') ? $to : false;
                        } else {
                            $to = isset($overview->senderaddress) ? $overview->senderaddress : $overview->from[0]->mailbox . '@' . $overview->from[0]->host;
                            $from = isset($overview->reply_toaddress) ? $overview->reply_toaddress : $overview->fromaddress;
                            $toaddress = $overview->toaddress;
                            if (strpos($toaddress, '?UTF-8') || strpos($toaddress, '7C_')) {
                                $toaddress = iconv_mime_decode($overview->toaddress, 0, 'UTF-8');
                            }
                            $follow_up = strpos($toaddress, '| MC') ? $toaddress : false;
                        }
                        if ($all_emails || $follow_up) {
                            $conversation_id = false;
                            $id = false;
                            if ($follow_up) {
                                $conversation_id = substr($follow_up, strpos($follow_up, '| MC') + 4);
                                $conversation_id = substr($conversation_id, 0, strpos($conversation_id, '<') - 1);
                                $conversation_id = explode('-', $conversation_id);
                                $id = hash('sha1', $conversation_id[1] . $overview->date);
                                $conversation_id = mc_db_escape($conversation_id[0], true);
                                if (!$conversation_id || !mc_db_get('SELECT id FROM mc_conversations WHERE id = ' . $conversation_id)) {
                                    $follow_up = false;
                                    $conversation_id = false;
                                }
                            }
                            if (!$follow_up) {
                                $id = hash('sha1', $from . $overview->date);
                            }
                            if (!in_array($id, $history) && !in_array($id, $history_new)) {
                                $from_email = mb_strpos($from, '<') ? trim(mb_substr($from, mb_strpos($from, '<') + 1, -1)) : $from;
                                $from_name = mb_strpos($from, '<') && mb_strpos($from, '=') === false && mb_strpos($from, '?') === false ? trim(mb_substr($from, 0, mb_strpos($from, '<'))) : '';
                                if (!$from_name) {
                                    $from_name = preg_match_all('/[a-z0-9_\-\+\.]+@[a-z0-9\-]+\.([a-z]{2,4})(?:\.[a-z]{2})?/i', $from, $matches);
                                    $from_name = count($matches) ? $matches[0] : $from;
                                    if (is_array($from_name) && count($from_name)) {
                                        $from_name = $from_name[0];
                                    }
                                }
                                $sender = mc_db_get('SELECT * FROM mc_users WHERE email = "' . mc_db_escape($from_email) . '" LIMIT 1');
                                if (!$sender) {
                                    $name = mc_split_name(str_replace(['"', 'Re:', '_'], ['', '', ' '], $from_name));
                                    $sender = mc_add_user(['email' => $from_email, 'first_name' => $name[0], 'last_name' => $name[1]]);
                                    $sender = mc_db_get('SELECT * FROM mc_users WHERE id = ' . $sender);
                                }
                                if ($sender && ($follow_up || !mc_is_agent($sender))) {
                                    $message = imap_fetchbody($inbox, $email_number, 1);
                                    $structure = imap_fetchstructure($inbox, $email_number);
                                    $agent = mc_is_agent($sender);

                                    // Message decoding
                                    $message_temp = false;
                                    $position = strpos($message, ': multipart/alternative');
                                    if ($position) {
                                        $message_temp = substr($message, strpos($message, ': text/plain'));
                                        $position = strpos($message_temp, 'Content-Type:');
                                        if ($position) {
                                            $message_temp = substr($message_temp, 0, $position);
                                        }
                                        $message_temp = substr($message_temp, strpos($message_temp, ': text/plain'));
                                        if ($message_temp) {
                                            $message = $message_temp;
                                        }
                                    }
                                    $position = strpos($message, ': base64');
                                    if ($position) {
                                        $message_temp = substr($message, $position + 8);
                                        $position = strpos($message_temp, ': base64');
                                        if ($position) {
                                            $message_temp = substr($message_temp, 0, mc_mb_strpos_reverse($message_temp, PHP_EOL, $position));
                                        }
                                        $position = strpos($message_temp, '--');
                                        if ($position) {
                                            $message_temp = substr($message_temp, 0, $position);
                                        }
                                        $message_temp = str_replace(["\r", "\n"], '', $message_temp);
                                        $message_temp = imap_base64($message_temp);
                                        if ($message_temp) {
                                            $message = $message_temp;
                                        }
                                    }
                                    if (strpos($message, 'quoted-printable')) {
                                        $message = quoted_printable_decode($message);
                                    } else {
                                        $encoding = isset($structure->parts) && count($structure->parts) ? $structure->parts[0]->encoding : ($structure->encoding && mb_detect_encoding($message) != 'UTF-8' ? $structure->encoding : -1);
                                        if ($encoding) {
                                            switch ($encoding) {
                                                case 0:
                                                case 1:
                                                    $message = imap_8bit($message);
                                                    break;
                                                case 2:
                                                    $message = imap_binary($message);
                                                    break;
                                                case 3:
                                                    $message = imap_base64($message);
                                                    break;
                                                case 4:
                                                    $message = imap_qprint($message);
                                                    break;
                                                default:
                                                    if (strpos($message, ' =E')) {
                                                        $message = mb_convert_encoding($message, 'UTF-8', mb_detect_encoding($message, 'ISO-8859-1, ISO-8859-2'));
                                                        if (strpos($message, '=')) {
                                                            $message = str_replace(['=AD', '=01', '=02', '=03', '=04', '=05', '=06', '=07', '=08', '=09', '=0A', '=0B', '=0C', '=0D', '=0E', '=0F', '=10', '=11', '=12', '=13', '=14', '=15', '=16', '=17', '=18', '=19', '=1A', '=1B', '=1C', '=1D', '=1E', '=1F', '=7F', '=80', '=81', '=82', '=83', '=84', '=85', '=86', '=87', '=88', '=89', '=8A', '=8B', '=8C', '=8D', '=8E', '=8F', '=90', '=91', '=92', '=93', '=94', '=95', '=96', '=97', '=98', '=99', '=9A', '=9B ', '=9C ', '=9D ', '=9E ', '=9F'], '', $message);
                                                            $message = str_replace(['=A0', '=20'], ' ', $message);
                                                            $message = str_replace(['=21', '=22', '=23', '=24', '=25', '=26', '=27', '=28', '=29', '=2A', '=2B', '=2C', '=2D', '=2E', '=2F', '=30', '=39', '=3A', '=3B', '=3C', '=3D', '=3E', '=3F', '=40', '=41', '=5A', '=5B', '=5C', '=5D', '=5E', '=5F', '=60', '=61', '=7A', '=7B', '=7C', '=7D', '=7E', '=A1', '=A2', '=A3', '=A4', '=A5', '=A6', '=A7', '=A8', '=A9', '=AA', '=AB', '=AC', '=AE', '=AF', '=B0', '=B1', '=B2', '=B3', '=B4', '=B5', '=B6', '=B7', '=B8', '=B9', '=BA', '=BB', '=BC', '=BD', '=BE', '=BF', '=C0', '=C1', '=C2', '=C3', '=C4', '=C5', '=C6', '=C7', '=C8', '=C9', '=CA', '=CB', '=CC', '=CD', '=CE', '=CF', '=D0', '=D1', '=D2', '=D3', '=D4', '=D5', '=D6', '=D7', '=D8', '=D9', '=DA', '=DB', '=DC', '=DD', '=DE', '=DF', '=E0', '=E1', '=E2', '=E3', '=E4', '=E5', '=E6', '=E7', '=E8', '=E9', '=EA', '=EB', '=EC', '=ED', '=EE', '=EF', '=F0', '=F1', '=F2', '=F3', '=F4', '=F5', '=F6', '=F7', '=F8', '=F9', '=FA', '=FB', '=FC', '=FD', '=FE', '=FF'], ['!', '"', '#', '$', '%', '&', '\'', '(', ')', '*', '+', '', '', '-', '.', '/', '0', '9', ':', ';', '<', '=', '>', '?', '@', 'A', 'Z', '[', '\\', ']', '^', '_', '`', 'a', 'z', '{', '|', '}', '~', '¡', '¢', '£', '¤', '¥', '¦', '§', '¨', '©', 'ª', '«', '¬', '®', '¯', '°', '±', '²', '³', '´', 'µ', '¶', '·', '¸', '¹', 'º', '»', '¼', '½', '¾', '¿', 'À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'Æ', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Í', 'Î', 'Ï', 'Ð', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', '×', 'Ø', 'Ù', 'Ú', 'Û', 'Ü', 'Ý', 'Þ', 'ß', 'à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è', 'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ð', 'ñ', 'ò', 'ó', 'ô', 'õ', 'ö', '÷', 'ø', 'ù', 'ú', 'û', 'ü', 'ý', 'þ', 'ÿ'], $message);
                                                        }
                                                    }
                                                    $message = quoted_printable_decode($message);
                                                    break;
                                            }
                                        }
                                    }
                                    if (mb_detect_encoding($message) != 'UTF-8') {
                                        $encoding = mb_detect_encoding($message);
                                        if ($encoding) {
                                            $message = mb_convert_encoding($message, 'UTF-8', $encoding);
                                        }
                                    }

                                    // Message formatting
                                    $message = str_replace(['<br>', '<br/>', '<br />'], PHP_EOL, $message);
                                    $position = mb_strpos($message, "|\r\nMC");
                                    if ($position) {
                                        $message = mb_substr($message, 0, mc_mb_strpos_reverse($message, PHP_EOL, $position));
                                    }
                                    $position = mb_strpos($message, ' | MC');
                                    if ($position) {
                                        $message = mb_substr($message, 0, mc_mb_strpos_reverse($message, PHP_EOL, $position));
                                    }
                                    $position = mb_strpos($message, $from_name . ' <');
                                    if ($position) {
                                        $message = mb_substr($message, 0, mc_mb_strpos_reverse($message, PHP_EOL, $position));
                                    }
                                    $position = mb_strpos($message, 'Content-Type:');
                                    if ($position) {
                                        $message = mb_substr($message, mb_strpos(mb_substr($message, $position), PHP_EOL) + $position);
                                        $position = mb_strpos($message, 'Content-Type:');
                                        if ($position) {
                                            $message = mb_substr($message, 0, $position);
                                        }
                                    }
                                    $position = mb_strpos($message, '______________________________');
                                    if ($position) {
                                        $message = mb_substr($message, 0, $position);
                                    }
                                    $position = mb_strpos($message, 'Outlook');
                                    if ($position) {
                                        $message = mb_substr($message, 0, mb_strrpos(mb_substr($message, 0, $position), "\n"));
                                    }
                                    $position = mb_strpos($message, 'Content-Transfer-Encoding:');
                                    $position_2 = mb_strpos($message, 'Content-Type: text/plain');
                                    if ($position) {
                                        if ($position_2 && $position_2 < $position) {
                                            $message = mb_substr($message, mb_strpos($message, "\n", $position_2), mb_strpos($message, "\n", $position));
                                        } else {
                                            $message = mb_substr($message, mb_strpos($message, "\n", $position));
                                        }
                                    }
                                    $strings_check = ['>:', '> wrote:', '--0'];
                                    for ($i = 0; $i < count($strings_check); $i++) {
                                        if (mb_strpos($message, $strings_check[$i])) {
                                            $message = mb_substr($message, 0, mc_mb_strpos_reverse($message, PHP_EOL, mb_strpos($message, $strings_check[$i])));
                                        }
                                    }
                                    $message = str_replace(['wrote:' . PHP_EOL, 'wrote:'], '', $message);
                                    if ($settings['email-piping-delimiter'] && mb_strpos($message, '### ')) {
                                        $message = str_replace('> ###', '###', $message);
                                        $message = mb_substr($message, 0, mb_strpos($message, '### '));
                                    }
                                    if (!empty($message)) {
                                        $message = preg_replace('/(<(script|style)\b[^>]*>).*?(<\/\2>)/is', "$1$3", $message);
                                        $message = strip_tags($message);
                                        $message = preg_replace("/\[image[\s\S]+?\]/", '', $message);
                                        $message = str_replace('&nbsp;', ' ', $message);
                                        while (mb_strpos($message, PHP_EOL . '> ')) {
                                            $message = mb_substr($message, 0, mb_strpos($message, PHP_EOL . '> ') - 2);
                                        }
                                        while (strpos($message, ' ' . PHP_EOL) !== false || strpos($message, PHP_EOL . ' ') !== false) {
                                            $message = str_replace([' ' . PHP_EOL, PHP_EOL . ' '], PHP_EOL, $message);
                                        }
                                        while (strpos($message, PHP_EOL . PHP_EOL . PHP_EOL) !== false) {
                                            $message = str_replace(PHP_EOL . PHP_EOL . PHP_EOL, PHP_EOL . PHP_EOL, $message);
                                        }
                                        $message = trim($message);
                                        $message = preg_replace("/(\n){3,}/", "\n\n", str_replace(["\r", "\t"], "\n", str_replace('  ', ' ', $message)));
                                        while (strpos($message, "\n ") !== false) {
                                            $message = str_replace("\n ", "\n", $message);
                                        }
                                        while (strpos($message, "\n\n\n") !== false) {
                                            $message = str_replace("\n\n\n", "\n\n", $message);
                                        }
                                    }

                                    // Attachments
                                    $attachments = mc_email_piping_attachments($structure, $inbox, $email_number);
                                    $attachments_2 = [];
                                    if (count($attachments) && !file_exists($attachments_path)) {
                                        mkdir($attachments_path, 0755, true);
                                    }
                                    for ($i = 0; $i < count($attachments); $i++) {
                                        $file_name_attachment = mc_sanatize_file_name($attachments[$i]['filename']);
                                        $file_name = rand(100000, 999999999) . '_' . $file_name_attachment;
                                        $file_path = $attachments_path . $file_name;
                                        if (mc_is_allowed_extension(pathinfo($file_name, PATHINFO_EXTENSION))) {
                                            $file_url = $attachments_url . $file_name;
                                            mc_file($file_path, $attachments[$i]['attachment']);
                                            if ($is_s3 && file_exists($file_path)) {
                                                $url_aws = mc_aws_s3($file_path);
                                                if (strpos($url_aws, 'http') === 0) {
                                                    $file_url = $url_aws;
                                                    unlink($file_path);
                                                }
                                            }
                                            array_push($attachments_2, [$file_name_attachment, $file_url]);
                                        } else {
                                            unlink($file_path);
                                        }
                                    }

                                    // Send message
                                    if (!empty($message)) {
                                        $GLOBALS['MC_FORCE_ADMIN'] = true;
                                        if (!$follow_up) {
                                            $subject = mc_string_slug(trim(mb_decode_mimeheader($overview->subject)), 'string');
                                            if (in_array(mb_substr($subject, 0, 1), ['?'])) {
                                                $subject = mb_substr($subject, 1);
                                            }
                                            if (mb_substr($subject, 0, 3) == 'Re:') {
                                                $subject = mb_substr($subject, 3);
                                            }
                                            $cc = isset($overview->cc) ? $overview->cc : false;
                                            $cc = $cc ? implode(',', array_map(function ($item) {
                                                return $item->mailbox . '@' . $item->host;
                                            }, $cc)) : '';
                                            $conversation_id = mc_isset(mc_new_conversation($sender['id'], 2, $subject, $department_id, -1, 'em', $cc), 'details', [])['id'];
                                        }
                                        $message_id = mc_send_message($sender['id'], $conversation_id, $message, $attachments_2, ($agent ? 1 : 2))['id'];

                                        // Process the message with OpenAI if it's a question
                                        if (!$agent && mc_chatbot_active(false, true)) {
                                            $GLOBALS['MC_FORCE_ADMIN'] = true;
                                            $extra_params = ['source' => 'em'];

                                            $response = mc_open_ai_message($message, false, false, $conversation_id, $extra_params, false, []);

                                            // Log OpenAI API key status
                                            $api_key = mc_get_multi_setting('open-ai', 'open-ai-key');
                                            file_put_contents($log_file, date('Y-m-d H:i:s') . " - OpenAI API key status: " .
                                                (empty($api_key) ? "MISSING" : "PRESENT (length: " . strlen($api_key) . ")") . "\n", FILE_APPEND);

                                            // Log OpenAI settings
                                            $openai_settings = mc_get_setting('open-ai');
                                            file_put_contents($log_file, date('Y-m-d H:i:s') . " - OpenAI settings: " .
                                                "Active: " . (mc_isset($openai_settings, 'open-ai-active') ? "YES" : "NO") . ", " .
                                                "Model: " . mc_isset($openai_settings, 'open-ai-model', 'Not set') . "\n", FILE_APPEND);

                                            // Check if OpenAI returned a valid response
                                            if ($response[0] && !empty($response[1])) {
                                                $bot_message = $response[1];

                                                // Check validation
                                                $is_valid = mc_open_ai_is_valid($bot_message, true);

                                                if ($is_valid) {
                                                    // Create the email response
                                                    $email_result = mc_email_create($sender['id'], mc_get_setting('bot-name', 'Chatbot'), mc_get_setting('bot-image'), $bot_message, [], false, $conversation_id);

                                                    // Send the message to the conversation
                                                    $message_result = mc_send_message(mc_get_bot_id(), $conversation_id, $bot_message, [], 1);

                                                    // Send notifications to agents
                                                    mc_send_agents_notifications($bot_message, false, $conversation_id, [], $sender);
                                                }
                                            } else {
                                                // Try to get a smart reply instead
                                                // Only try smart reply if we have a valid message
                                                if (!empty(trim($message))) {
                                                    $smart_reply = mc_open_ai_smart_reply($message, $conversation_id);

                                                    if (!mc_is_error($smart_reply) && isset($smart_reply['suggestions']) && !empty($smart_reply['suggestions'])) {
                                                        $bot_message = $smart_reply['suggestions'][0]; // Use the first suggestion

                                                        // Create the email response
                                                        $email_result = mc_email_create($sender['id'], mc_get_setting('bot-name', 'Chatbot'), mc_get_setting('bot-image'), $bot_message, [], false, $conversation_id);

                                                        // Send the message to the conversation
                                                        $message_result = mc_send_message(mc_get_bot_id(), $conversation_id, $bot_message, [], 1);

                                                        // Send notifications to agents
                                                        mc_send_agents_notifications($bot_message, false, $conversation_id, [], $sender);
                                                    }
                                                }
                                            }
                                            $GLOBALS['MC_FORCE_ADMIN'] = false;
                                        }

                                        // Notifications
                                        $recipient = mc_get_user_from_conversation($conversation_id, !$agent);
                                        if (isset($recipient['id']) && !mc_is_user_online($recipient['id']) && (!mc_get_setting('stop-notify-admins') || !mc_is_agent($recipient, true, true))) {
                                            if (($agent && mc_get_setting('notify-user-email')) || (!$agent && mc_get_setting('notify-agent-email'))) {
                                                mc_email_create($recipient['id'], mc_get_user_name($sender), $sender['profile_image'], $message, $attachments_2, false, $conversation_id);
                                            }
                                            if (($agent && mc_get_multi_setting('sms', 'sms-active-users')) || (!$agent && mc_get_multi_setting('sms', 'sms-active-agents'))) {
                                                $phone = mc_get_user_extra($recipient['id'], 'phone');
                                                if ($phone) {
                                                    mc_send_sms($message, $phone, true, $conversation_id, $attachments_2);
                                                }
                                            }
                                        } else if (!$follow_up && mc_get_setting('notify-agent-email')) {
                                            mc_send_agents_notifications($message, false, $conversation_id, $attachments_2);
                                        }

                                        // Dialogflow and Slack
                                        if (!$agent) {
                                            if (defined('MC_DIALOGFLOW') && mc_get_setting('dialogflow-email-piping')) {
                                                mc_messaging_platforms_functions($conversation_id, $message, $attachments_2, $sender, ['source' => 'em', 'user_id' => $sender['id'], 'conversation_id' => $conversation_id]);
                                                mc_db_query('DELETE FROM mc_messages WHERE conversation_id = ' . $conversation_id . ' AND payload LIKE "%NO_MATCH%" AND creation_time < "' . mc_gmt_now(-60) . '" ORDER BY id DESC LIMIT 1');
                                            } else if (defined('MC_SLACK') && mc_slack_can_send($conversation_id)) {
                                                mc_send_slack_message($sender['id'], mc_get_user_name($sender), $sender['profile_image'], $message, $attachments_2, $conversation_id);
                                            }
                                        }

                                        $GLOBALS['MC_FORCE_ADMIN'] = false;
                                    }
                                    array_push($history_new, $id);
                                }
                            }
                        }
                    }
                    if ($last_check != $today) {
                        $history = [];
                    }
                    mc_save_external_setting('email-piping-history', array_merge($history, $history_new));
                }
                if ($last_check != $today) {
                    mc_save_external_setting('email-piping-check', $today);
                }
                imap_close($inbox);
            } else {
                $error = imap_last_error();
                $error = 'Connection error (' . $settings['email-piping-user'] . ')' . ($error ? ': ' . $error : '');
            }
        }
    }
    return $error ? $error : true;
}

function mc_email_piping_attachments($structure, &$inbox, &$email_number, $part_index = false) {
    $attachments = [];
    $count = isset($structure->parts) ? count($structure->parts) : 0;
    for ($i = 0; $i < $count; $i++) {
        $part = $structure->parts[$i];
        $attachment = false;
        $parameters = $part->ifdparameters ? $part->dparameters : ($part->ifparameters ? $part->parameters : []);
        foreach ($parameters as $object) {
            if (in_array(strtolower($object->attribute), ['name', 'filename'])) {
                $attachment = ['filename' => $object->value];
            }
        }
        if ($attachment) {
            $index = (($part_index ? $part_index : $i) + 1);
            $attachment_temp = imap_fetchbody($inbox, $email_number, $index . '.' . ($i + 1));
            if (!$attachment_temp)
                $attachment_temp = imap_fetchbody($inbox, $email_number, $index);
            if ($part->encoding == 3) {
                $attachment_temp = base64_decode($attachment_temp);
            } else if ($part->encoding == 4) {
                $attachment_temp = quoted_printable_decode($attachment_temp);
            }
            $attachment['attachment'] = $attachment_temp;
            array_push($attachments, $attachment);
        }
        if (property_exists($part, 'parts') && $part->parts) {
            array_merge($attachments, mc_email_piping_attachments($part, $inbox, $email_number, $i));
        }
    }
    return $attachments;
}

function mc_email_piping_suffix($conversation_id) {
    return $conversation_id && mc_email_piping_is_active() ? (' | MC' . $conversation_id . '-' . rand(100, 9999)) : '';
}

function mc_email_piping_is_active() {
    $settings = mc_get_setting('email-piping', []);
    if (isset($settings['email-piping-active'])) { // Deprecated
        $settings = [$settings]; // Deprecated
    } // Deprecated
    for ($i = 0; $i < count($settings); $i++) {
        if ($settings[$i]['email-piping-active']) {
            return true;
        }
    }
    return false;
}

function mc_subscribe_email($email) {
    $settings = mc_get_multilingual_setting('emails', 'email-subscribe');
    $subject = $settings['email-subscribe-subject'];
    $content = $settings['email-subscribe-content'];
    if ($settings && !empty($subject) && !empty($content)) {
        // Get user information if available
        $user = mc_db_get('SELECT first_name, last_name FROM mc_users WHERE email = "' . mc_db_escape($email) . '" LIMIT 1');

        // Prepare username - handle empty names properly
        $username = '';
        if ($user && !empty($user['first_name'])) {
            $username = trim($user['first_name'] . ' ' . $user['last_name']);
        } else if (strpos($email, '@') !== false) {
            // If no user found but we have an email, use the part before @ as a fallback name
            $username_parts = explode('@', $email);
            $username = ucfirst(str_replace(['.', '_', '-'], ' ', $username_parts[0]));
        }

        // Use cloud_merge_field_username to replace variables in the content
        if (function_exists('cloud_merge_field_username')) {
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            return mc_email_send($email, mc_merge_fields($subject), cloud_merge_field_username($content, $username));
        } else {
            return mc_email_send($email, mc_merge_fields($subject), mc_merge_fields($content));
        }
    }
    return false;
}

function mc_email_default_parts($body, $user_id = false) {
    $lang = $user_id ? mc_get_user_language($user_id) : 'en';
    return mc_get_multilingual_setting('emails', 'email-header', $lang) . PHP_EOL . $body . PHP_EOL . mc_get_multilingual_setting('emails', 'email-signature', $lang);
}

function mc_email_attachments_code($attachments) {
    $code = '';
    for ($i = 0; $i < count($attachments); $i++) {
        $code .= '<a style="display:block;text-decoration:none;line-height:25px;color:rgb(102, 102, 102);" href="' . str_replace(' ', '%20', $attachments[$i][1]) . '">' . $attachments[$i][0] . '</a>';
    }
    if ($code) {
        $code = '<div style="margin-top: 30px">' . $code . '</div>';
    }
    return $code;
}

function mc_email_get_conversation_code($conversation_id, $count = false, $is_recipient_agent = false) {
    $conversation_id = mc_db_escape($conversation_id, true);
    $messages = mc_db_get('SELECT A.user_id, A.message, A.payload, A.attachments, A.creation_time, B.first_name, B.last_name, B.profile_image, B.user_type FROM mc_messages A, mc_users B WHERE A.conversation_id = ' . $conversation_id . ' AND A.user_id = B.id ORDER BY A.id ASC', false);
    $count_messages = count($messages);
    $start = $count ? ($count_messages - $count > 0 ? $count_messages - $count : 0) : 0;
    $code = '';
    $count_final = 0;
    $translate = false;
    $utc_offset = mc_get_setting('timetable-utc', 0);
    if (defined('MC_DIALOGFLOW') && mc_get_multi_setting('google', 'google-translation')) {
        $recipient_id = false;
        $sender_id = false;
        for ($i = $count_messages - 1; $i > -1; $i--) {
            if (($is_recipient_agent && mc_is_agent($messages[$i], true)) || (!$is_recipient_agent && !mc_is_agent($messages[$i], true))) {
                $recipient_id = $messages[$i]['user_id'];
                break;
            }
        }
        for ($i = $count_messages - 1; $i > -1; $i--) {
            if (($is_recipient_agent && !mc_is_agent($messages[$i])) || (!$is_recipient_agent && mc_is_agent($messages[$i], true))) {
                $sender_id = $messages[$i]['user_id'];
                break;
            }
        }
        if ($is_recipient_agent && !$recipient_id) {
            $recipient_id = mc_db_get('SELECT id FROM mc_users WHERE user_type = "admin" LIMIT 1')['id'];
        }
        if ($recipient_id && $sender_id) {
            $recipient_language = mc_get_user_language($recipient_id);
            $sender_language = mc_get_user_language($sender_id);
            $translate = $recipient_language && $sender_language && $recipient_language != $sender_language ? $recipient_language : false;
        }
    }
    for ($i = $start; $i < $count_messages; $i++) {
        $message = $messages[$i];
        $message_text = $message['message'];
        $attachments = mc_isset($message, 'attachments', []);
        if (!empty($message_text) || count($attachments)) {
            if ($translate && $message_text) {
                $message = mc_google_get_message_translation($message);
                if ($message['message'] != $message_text) {
                    $message_text = $message['message'];
                } else {
                    $translation = mc_google_translate([$message_text], $translate)[0];
                    if (count($translation)) {
                        $translation = trim($translation[0]);
                        if (!empty($translation)) {
                            $message_text = $translation;
                        }
                    }
                }
            }
            $message_text = mc_rich_messages_to_html($message_text);
            $css = ($is_recipient_agent && mc_is_agent($messages[$i])) || (!$is_recipient_agent && !mc_is_agent($messages[$i])) ? ['right', '0 0 20px 50px', '#E6F2FC'] : ['left', '0 50px 20px 0', '#F0F0F0'];
            $code .= '<div style="float:' . $css[0] . ';text-align:' . $css[0] . ';clear:both;margin:' . $css[1] . ';"><span style="background-color:' . $css[2] . ';padding:10px 15px;display:inline-block;border-radius:4px;margin:0;">' . $message_text . '</span>';
            if ($attachments) {
                $code .= '<br>';
                $attachments = json_decode($attachments, true);
                for ($j = 0; $j < count($attachments); $j++) {
                    $code .= '<br><a style="color:#626262;text-decoration:underline;" href="' . $attachments[$j][1] . '">' . $attachments[$j][0] . '</a>';
                }
            }
            $code .= '<br><span style="color:rgb(168,168,168);font-size:12px;display:block;margin:10px 0 0 0;">' . $message['first_name'] . ' ' . $message['last_name'] . ' | ' . mc_gmt_date_to_local($message['creation_time'], $utc_offset) . '</span></div>';
            $count_final++;
        }
    }
    return '<div style="max-width:600px;clear:both;">' . ($start ? '<div style="clear:both;width:100%;opacity:.7;padding-bottom:20px;text-align:left;">' . str_replace('{R}', $count, mc_('Only the most recent {R} messages are shown...')) . '</div>' : '') . $code . '<div style="clear:both;"></div></div>';
}

function mc_remove_email_cron($conversation_id) {
    if (mc_get_setting('notify-email-cron')) {
        if (mc_conversation_security_error($conversation_id)) {
            return mc_error('security-error', 'mc_remove_email_cron');
        }
        $cron_job_emails = mc_get_external_setting('cron-email-notifications', []);
        if (isset($cron_job_emails[$conversation_id])) {
            $is_user_notification = $cron_job_emails[$conversation_id][5];
            if (($is_user_notification && !mc_is_agent()) || (!$is_user_notification && mc_is_agent())) {
                unset($cron_job_emails[$conversation_id]);
                return mc_save_external_setting('cron-email-notifications', $cron_job_emails);
            }
        }
    }
    return false;
}

function mc_text_formatting_to_html($message, $clear = false) {
    // Remove unnecessary backslashes before colons
    $message = preg_replace('/\\\\:/', ':', $message);

    // Basic text formatting
    $regex = $clear ? [
        ['/\*(.*?)\*/', '', ''],
        ['/__(.*?)__/', '', ''],
        ['/~(.*?)~/', '', ''],
        ['/```(.*?)```/', '', ''],
        ['/`(.*?)`/', '', '']
    ] : [
        ['/\*(.*?)\*/', '<b>', '</b>'],
        ['/__(.*?)__/', '<em>', '</em>'],
        ['/~(.*?)~/', '<del>', '</del>'],
        ['/```(.*?)```/', '<code style="display:block;background-color:#f4f4f4;padding:10px;border-radius:4px;font-family:monospace;">', '</code>'],
        ['/`(.*?)`/', '<code style="background-color:#f4f4f4;padding:2px 4px;border-radius:3px;font-family:monospace;">', '</code>']
    ];

    for ($i = 0; $i < count($regex); $i++) {
        $values = [];
        if (preg_match_all($regex[$i][0], $message, $values, PREG_SET_ORDER)) {
            for ($j = 0; $j < count($values); $j++) {
                $message = str_replace($values[$j][0], $regex[$i][1] . $values[$j][1] . $regex[$i][2], $message);
            }
        }
    }

    // Process list shortcodes
    if (!$clear) {
        $message = mc_process_lists($message);
    }

    // Pre-process links with #mc- format to ensure they work in emails
    $message = preg_replace_callback('/https?:\/\/[^\s#<>]+#mc-([^#\s<>]+)/', function($matches) {
        $url = substr($matches[0], 0, strpos($matches[0], '#mc-'));
        $text = str_replace('--', ' ', $matches[1]);
        return '<a href="' . $url . '" style="color:#0000EE;text-decoration:underline;">' . $text . '</a>';
    }, $message);

    // Convert any remaining URLs to clickable links (if not already in an HTML tag)
    if (!$clear) {
        $message = preg_replace_callback(
            '/(?<!href="|src="|>)(https?:\/\/[^\s<>]+)(?!<\/a>)/',
            fn($matches) => '<a href="' . $matches[1] . '" style="color:#0000EE;text-decoration:underline;">' . $matches[1] . '</a>',
            $message
        );
    }

    return $message;
}

function mc_clear_text_formatting($message) {
    return mc_text_formatting_to_html($message, true);
}

function mc_newsletter($email, $first_name = '', $last_name = '') {
    $settings = mc_get_setting('newsletter');
    if ($settings && $settings['newsletter-active']) {
        $post_fields = '';
        $header = ['Content-Type: application/json', 'Accept: application/json'];
        $url = false;
        $list_id = $settings['newsletter-list-id'];
        $key = $settings['newsletter-key'];
        $type = 'POST';
        switch ($settings['newsletter-service']) {
            case 'mailchimp':
                $url = 'https://' . substr($key, strpos($key, '-') + 1) . '.api.mailchimp.com/3.0/lists/' . $list_id . '/members/';
                $post_fields = ['email_address' => $email, 'status' => 'subscribed', 'merge_fields' => ['FNAME' => $first_name, 'LNAME' => $last_name]];
                array_push($header, 'Authorization: Basic ' . base64_encode('user:' . $key));
                break;
            case 'sendinblue':
                $url = 'https://api.brevo.com/v3/contacts';
                $post_fields = ['email' => $email, 'listIds' => [intval($list_id)], 'updateEnabled' => false, 'attributes' => ['FIRSTNAME' => $first_name, 'LASTNAME' => $last_name]];
                array_push($header, 'api-key: ' . $key);
                break;
            case 'sendgrid':
                $url = 'https://api.sendgrid.com/v3/marketing/contacts';
                $post_fields = ['list_ids' => [$list_id], 'contacts' => [['email' => $email, 'first_name' => $first_name, 'last_name' => $last_name]]];
                array_push($header, 'Authorization: Bearer ' . $key);
                $type = 'PUT';
                break;
            case 'elasticemail':
                $url = 'https://api.elasticemail.com/v2/contact/add?email=' . $email . '&publicAccountID=' . $key . '&listName=' . urlencode($list_id) . '&firstName=' . urlencode($first_name) . '&lastName=' . urlencode($last_name) . '&sendActivation=false';
                $type = 'GET';
                break;
            case 'campaignmonitor':
                $url = 'https://api.createsend.com/api/v3.2/subscribers/' . $list_id . '.json';
                $post_fields = ['EmailAddress' => $email, 'name' => $first_name . ' ' . $last_name, 'ConsentToTrack' => 'Yes', 'Resubscribe' => true, 'RestartSubscriptionBasedAutoresponders' => true, 'CustomFields' => []];
                array_push($header, 'Authorization: Basic ' . base64_encode($key));
                break;
            case 'hubspot':
                array_push($header, 'Authorization: Bearer ' . $key);
                $contact_id = mc_isset(mc_curl('https://api.hubapi.com/crm/v3/objects/contacts', json_encode(['properties' => ['email' => $email, 'firstname' => $first_name, 'lastname' => $last_name]]), $header), 'id');
                if ($contact_id && $list_id) {
                    $url = 'https://api.hubapi.com/contacts/v1/lists/' . $list_id . '/add';
                    $post_fields = ['vids' => [$contact_id]];
                }
                break;
            case 'moosend':
                $url = 'https://api.moosend.com/v3/subscribers/' . $list_id . '/subscribe.json?apikey=' . $key;
                $post_fields = ['Email' => $email, 'Name' => $first_name . ' ' . $last_name];
                break;
            case 'getresponse':
                $url = 'https://api.getresponse.com/v3/contacts';
                $post_fields = ['email' => $email, 'name' => $first_name . ' ' . $last_name, 'campaign' => ['campaignId' => $list_id]];
                array_push($header, 'X-Auth-Token: api-key ' . $key);
                break;
            case 'convertkit':
                $url = 'https://api.convertkit.com/v3/forms/' . $list_id . '/subscribe';
                $post_fields = ['api_secret' => $key, 'first_name' => $first_name . ' ' . $last_name, 'email' => $email];
                break;
            case 'activecampaign':
                $list_id = explode(':', $list_id);
                array_push($header, 'Api-Token: ' . $key);
                $contact_id = mc_isset(mc_curl('https://' . $list_id[0] . '.api-us1.com/api/3/contacts', json_encode(['contact' => ['email' => $email, 'firstName' => $first_name, 'lastName' => $last_name]]), $header), 'contact');
                if ($contact_id) {
                    $url = 'https://' . $list_id[0] . '.api-us1.com/api/3/contactLists';
                    $post_fields = ['contactList' => ['list' => $list_id[1], 'contact' => $contact_id['id'], 'status' => 1]];
                }
                break;
            case 'mailerlite':
                $url = 'https://api.mailerlite.com/api/v2/groups/' . $list_id . '/subscribers';
                $post_fields = ['email' => $email, 'apiKey' => $key, 'name' => $first_name, 'id' => $list_id, 'fields' => ['last_name' => $last_name]];
                break;
            case 'mailjet':
                $url = 'https://api.mailjet.com/v3/REST/contactslist/' . $list_id . '/managecontact';
                $post_fields = ['Email' => $email, 'Properties' => ['Name' => mc_get_user_name(['first_name' => $first_name, 'last_name' => $last_name])], 'Action' => 'addforce'];
                $key = explode(',', $key);
                array_push($header, 'Authorization: Basic ' . base64_encode(trim($key[0]) . ':' . trim($key[1])));
                break;
            case 'sendy':
                $list_id = explode('|', $list_id);
                $url = $list_id[0] . '/subscribe';
                $header = [];
                $post_fields = ['email' => $email, 'name' => mc_get_user_name(['first_name' => $first_name, 'last_name' => $last_name]), 'list' => $list_id[1], 'api_key' => $key];
                break;
            case 'sendfox':
                array_push($header, 'Authorization: Bearer ' . $key);
                $url = 'https://api.sendfox.com/contacts';
                $post_fields = ['email' => $email, 'first_name' => $first_name, 'last_name' => $last_name, 'lists' => [$list_id]];
                break;
        }
        if ($url) {
            $response = mc_curl($url, empty($header) ? $post_fields : json_encode($post_fields), $header, $type);
            return $response;
        }
    }
    return false;
}

function mc_cron_email_notifications() {
    // Email cron job functionality disabled for SaaS version
    return true;
}

// Process list shortcodes for email formatting
function mc_process_lists($message) {
    // First pattern: [list numeric="true" values="..."]
    $message = preg_replace_callback('/\[list(\s+numeric="true")?\s+values="([^"]*?)"\]/s', function($matches) {
        $is_numeric = !empty($matches[1]);
        $values_raw = $matches[2];

        // Handle escaped commas and special characters
        $values_raw = str_replace('\\,', '{{ESCAPED_COMMA}}', $values_raw);

        // Split by commas, but preserve newlines for processing
        $values = explode(',', $values_raw);

        // Group items by their parent
        $grouped_items = [];
        $current_parent_index = -1;

        foreach ($values as $value) {
            $value = trim($value);
            if (empty($value)) continue;

            // Restore escaped commas
            $value = str_replace('{{ESCAPED_COMMA}}', ',', $value);

            // Check if this is an inner item (prefixed with "- ")
            $is_inner = strpos($value, '- ') === 0;

            if ($is_inner) {
                // This is an inner item, add it to the current parent
                $value = substr($value, 2); // Remove the "- " prefix

                if (isset($grouped_items[$current_parent_index])) {
                    $grouped_items[$current_parent_index]['inner_items'][] = $value;
                }
            } else {
                // This is a parent item, create a new group
                $current_parent_index++;
                $grouped_items[$current_parent_index] = [
                    'parent' => $value,
                    'inner_items' => []
                ];
            }
        }

        // Start HTML list with appropriate list style
        $list_style = $is_numeric ? 'decimal' : 'disc';
        $output = '<' . ($is_numeric ? 'ol' : 'ul') . ' style="list-style-type: ' . $list_style . '; padding-left: 20px; margin: 10px 0;">';

        // Process each group
        foreach ($grouped_items as $group) {
            // Add the parent item
            $output .= '<li style="margin-bottom: 5px;">' . $group['parent'];

            // If this group has inner items, add a nested list
            if (!empty($group['inner_items'])) {
                // For numeric lists, use decimal sub-numbering
                if ($is_numeric) {
                    // For numeric lists, use custom sub-numbering (e.g., 2.1, 2.2)
                    $output .= '<ol style="list-style-type: none; margin: 5px 0 5px 0; padding-left: 0;">';

                    $parent_index = array_search($group, $grouped_items) + 1;
                    $sub_index = 1;

                    foreach ($group['inner_items'] as $inner_item) {
                        $output .= '<li style="margin-bottom: 5px;"><span style="display:inline-block;min-width:30px;">' .
                                   $parent_index . '.' . $sub_index . '.</span> ' . $inner_item . '</li>';
                        $sub_index++;
                    }

                    $output .= '</ol>';
                } else {
                    // For bullet lists, use circle bullets
                    $output .= '<ul style="list-style-type: circle; margin: 5px 0 5px 20px;">';

                    foreach ($group['inner_items'] as $inner_item) {
                        $output .= '<li style="margin-bottom: 5px;">' . $inner_item . '</li>';
                    }

                    $output .= '</ul>';
                }
            }

            // Close the parent item
            $output .= '</li>';
        }

        $output .= '</' . ($is_numeric ? 'ol' : 'ul') . '>';
        return $output;
    }, $message);

    // Second pattern: [list values="..." numeric="true"]
    $message = preg_replace_callback('/\[list\s+values="([^"]*?)"(\s+numeric="true")?\]/s', function($matches) {
        $values_raw = $matches[1];
        $is_numeric = !empty($matches[2]);

        // Handle escaped commas and special characters
        $values_raw = str_replace('\\,', '{{ESCAPED_COMMA}}', $values_raw);

        // Split by commas, but preserve newlines for processing
        $values = explode(',', $values_raw);

        // Group items by their parent
        $grouped_items = [];
        $current_parent_index = -1;

        foreach ($values as $value) {
            $value = trim($value);
            if (empty($value)) continue;

            // Restore escaped commas
            $value = str_replace('{{ESCAPED_COMMA}}', ',', $value);

            // Check if this is an inner item (prefixed with "- ")
            $is_inner = strpos($value, '- ') === 0;

            if ($is_inner) {
                // This is an inner item, add it to the current parent
                $value = substr($value, 2); // Remove the "- " prefix

                if (isset($grouped_items[$current_parent_index])) {
                    $grouped_items[$current_parent_index]['inner_items'][] = $value;
                }
            } else {
                // This is a parent item, create a new group
                $current_parent_index++;
                $grouped_items[$current_parent_index] = [
                    'parent' => $value,
                    'inner_items' => []
                ];
            }
        }

        // Start HTML list with appropriate list style
        $list_style = $is_numeric ? 'decimal' : 'disc';
        $output = '<' . ($is_numeric ? 'ol' : 'ul') . ' style="list-style-type: ' . $list_style . '; padding-left: 20px; margin: 10px 0;">';

        // Process each group
        foreach ($grouped_items as $group) {
            // Add the parent item
            $output .= '<li style="margin-bottom: 5px;">' . $group['parent'];

            // If this group has inner items, add a nested list
            if (!empty($group['inner_items'])) {
                // For numeric lists, use decimal sub-numbering
                if ($is_numeric) {
                    // For numeric lists, use custom sub-numbering (e.g., 2.1, 2.2)
                    $output .= '<ol style="list-style-type: none; margin: 5px 0 5px 0; padding-left: 0;">';

                    $parent_index = array_search($group, $grouped_items) + 1;
                    $sub_index = 1;

                    foreach ($group['inner_items'] as $inner_item) {
                        $output .= '<li style="margin-bottom: 5px;"><span style="display:inline-block;min-width:30px;">' .
                                   $parent_index . '.' . $sub_index . '.</span> ' . $inner_item . '</li>';
                        $sub_index++;
                    }

                    $output .= '</ol>';
                } else {
                    // For bullet lists, use circle bullets
                    $output .= '<ul style="list-style-type: circle; margin: 5px 0 5px 20px;">';

                    foreach ($group['inner_items'] as $inner_item) {
                        $output .= '<li style="margin-bottom: 5px;">' . $inner_item . '</li>';
                    }

                    $output .= '</ul>';
                }
            }

            // Close the parent item
            $output .= '</li>';
        }

        $output .= '</' . ($is_numeric ? 'ol' : 'ul') . '>';
        return $output;
    }, $message);

    // Third pattern: [list values='...' numeric="true"]
    $message = preg_replace_callback('/\[list\s+values=\'([^\']*?)\'(\s+numeric="true")?\]/s', function($matches) {
        $values_raw = $matches[1];
        $is_numeric = !empty($matches[2]);

        // Handle escaped commas and special characters
        $values_raw = str_replace('\\,', '{{ESCAPED_COMMA}}', $values_raw);

        // Split by commas, but preserve newlines for processing
        $values = explode(',', $values_raw);

        // Group items by their parent
        $grouped_items = [];
        $current_parent_index = -1;

        foreach ($values as $value) {
            $value = trim($value);
            if (empty($value)) continue;

            // Restore escaped commas
            $value = str_replace('{{ESCAPED_COMMA}}', ',', $value);

            // Check if this is an inner item (prefixed with "- ")
            $is_inner = strpos($value, '- ') === 0;

            if ($is_inner) {
                // This is an inner item, add it to the current parent
                $value = substr($value, 2); // Remove the "- " prefix

                if (isset($grouped_items[$current_parent_index])) {
                    $grouped_items[$current_parent_index]['inner_items'][] = $value;
                }
            } else {
                // This is a parent item, create a new group
                $current_parent_index++;
                $grouped_items[$current_parent_index] = [
                    'parent' => $value,
                    'inner_items' => []
                ];
            }
        }

        // Start HTML list with appropriate list style
        $list_style = $is_numeric ? 'decimal' : 'disc';
        $output = '<' . ($is_numeric ? 'ol' : 'ul') . ' style="list-style-type: ' . $list_style . '; padding-left: 20px; margin: 10px 0;">';

        // Process each group
        foreach ($grouped_items as $group) {
            // Add the parent item
            $output .= '<li style="margin-bottom: 5px;">' . $group['parent'];

            // If this group has inner items, add a nested list
            if (!empty($group['inner_items'])) {
                // For numeric lists, use decimal sub-numbering
                if ($is_numeric) {
                    // For numeric lists, use custom sub-numbering (e.g., 2.1, 2.2)
                    $output .= '<ol style="list-style-type: none; margin: 5px 0 5px 0; padding-left: 0;">';

                    $parent_index = array_search($group, $grouped_items) + 1;
                    $sub_index = 1;

                    foreach ($group['inner_items'] as $inner_item) {
                        $output .= '<li style="margin-bottom: 5px;"><span style="display:inline-block;min-width:30px;">' .
                                   $parent_index . '.' . $sub_index . '.</span> ' . $inner_item . '</li>';
                        $sub_index++;
                    }

                    $output .= '</ol>';
                } else {
                    // For bullet lists, use circle bullets
                    $output .= '<ul style="list-style-type: circle; margin: 5px 0 5px 20px;">';

                    foreach ($group['inner_items'] as $inner_item) {
                        $output .= '<li style="margin-bottom: 5px;">' . $inner_item . '</li>';
                    }

                    $output .= '</ul>';
                }
            }

            // Close the parent item
            $output .= '</li>';
        }

        $output .= '</' . ($is_numeric ? 'ol' : 'ul') . '>';
        return $output;
    }, $message);

    // Fourth pattern: [list values="..." numeric="true"] - exact match from debug logs
    $message = preg_replace_callback('/\[list values="([^"]*?)" numeric="true"\]/s', function($matches) {
        $values_raw = $matches[1];
        // This is a numeric list by definition of the pattern
        $is_numeric = true;

        // Handle escaped commas and special characters
        $values_raw = str_replace('\\,', '{{ESCAPED_COMMA}}', $values_raw);

        // Split by commas, but preserve newlines for processing
        $values = explode(',', $values_raw);

        // Group items by their parent
        $grouped_items = [];
        $current_parent_index = -1;

        foreach ($values as $value) {
            $value = trim($value);
            if (empty($value)) continue;

            // Restore escaped commas
            $value = str_replace('{{ESCAPED_COMMA}}', ',', $value);

            // Check if this is an inner item (prefixed with "- ")
            $is_inner = strpos($value, '- ') === 0;

            if ($is_inner) {
                // This is an inner item, add it to the current parent
                $value = substr($value, 2); // Remove the "- " prefix

                if (isset($grouped_items[$current_parent_index])) {
                    $grouped_items[$current_parent_index]['inner_items'][] = $value;
                }
            } else {
                // This is a parent item, create a new group
                $current_parent_index++;
                $grouped_items[$current_parent_index] = [
                    'parent' => $value,
                    'inner_items' => []
                ];
            }
        }

        // Start HTML list with appropriate list style
        $output = '<ol style="list-style-type: decimal; padding-left: 20px; margin: 10px 0;">';

        // Process each group
        foreach ($grouped_items as $group) {
            // Add the parent item
            $output .= '<li style="margin-bottom: 5px;">' . $group['parent'];

            // If this group has inner items, add a nested list
            if (!empty($group['inner_items'])) {
                // For numeric lists, use custom sub-numbering (e.g., 2.1, 2.2)
                $output .= '<ol style="list-style-type: none; margin: 5px 0 5px 0; padding-left: 0;">';

                $parent_index = array_search($group, $grouped_items) + 1;
                $sub_index = 1;

                foreach ($group['inner_items'] as $inner_item) {
                    $output .= '<li style="margin-bottom: 5px;"><span style="display:inline-block;min-width:30px;">' .
                               $parent_index . '.' . $sub_index . '.</span> ' . $inner_item . '</li>';
                    $sub_index++;
                }

                $output .= '</ol>';
            }

            // Close the parent item
            $output .= '</li>';
        }

        $output .= '</ol>';
        return $output;
    }, $message);

    return $message;
}

function mc_rich_messages_to_html($message) {
    // Process lists first
    $message = mc_process_lists($message);

    // Process URL links with #mc- format - improved pattern to catch more variations
    $message = preg_replace_callback('/https?:\/\/[^\s#<>]+#mc-([^#\s<>]+)/', function($matches) {
        $url = substr($matches[0], 0, strpos($matches[0], '#mc-'));
        $text = str_replace('--', ' ', $matches[1]);
        return '<a href="' . $url . '" style="color:#0000EE;text-decoration:underline;">' . $text . '</a>';
    }, $message);

    // Process duplicate URLs that might occur in email formatting
    $message = preg_replace('/(https?:\/\/[^\s<>]+)\/\/\1/', '$1', $message);

    // Additional pass for URLs that might have been missed
    $message = preg_replace_callback('/(https?:\/\/[^\s#<>]+)#mc-([^#\s<>]+)/', function($matches) {
        return '<a href="' . $matches[1] . '" style="color:#0000EE;text-decoration:underline;">' .
               str_replace('--', ' ', $matches[2]) . '</a>';
    }, $message);

    $shortcodes = mc_get_shortcode($message);
    $extra_values = [];
    $div_button_start = '<div>';
    $div_button = '<div style="background-color:#028BE5;color:#FFF;border-radius:4px;padding:3px 6px;float:left;margin-right:5px;cursor:default">';
    $div_button_end = '<div style="width:100%;clear:both"></div></div>';
    $div_input = '<div style="margin-top:5px;border:1px solid #999999;background:#FFF;color:#c2c2c2;font-size:13px;line-height:14px;border-radius:4px;padding:6px 10px;cursor:text;">';
    for ($j = 0; $j < count($shortcodes); $j++) {
        $shortcode = $shortcodes[$j];
        $shortcode_name = $shortcode['shortcode_name'];
        $message = trim(str_replace($shortcode['shortcode'], '', $message) . (empty($shortcode['title']) ? '' : '<b>' . mc_($shortcode['title']) . '</b><br>') . mc_isset($shortcode, 'message', ''));
        if ($message) {
            $message .= '<br><br>';
        }
        if ($shortcode_name == 'registration' || $shortcode_name == 'timetable') {
            $message .= '[' . $shortcode_name . ']';
        }
        switch ($shortcode_name) {
            case 'slider-images':
                $message .= $div_button_start;
                $extra_values = explode(',', $shortcode['images']);
                for ($i = 0; $i < count($extra_values); $i++) {
                    $message .= '<img src="' . $extra_values[$i] . '" style="float:left;width:20%;min-width:100px;" />';
                }
                $message .= $div_button_end;
                break;
            case 'slider':
            case 'card':
                $suffix = '';
                if ($shortcode_name == 'slider') {
                    $suffix = '-1';
                }
                $message .= '<div style="width:300px;margin:-10px -15px;border-radius:4px;overflow:hidden;"><img style="max-width:100%" src="' . $shortcode['image' . $suffix] . '" /><div style="padding:15px"><b>' . mc_($shortcode['header' . $suffix]) . '</b><br>' . (empty($shortcode['description' . $suffix]) ? '' : '<div style="padding-top:5px">' . $shortcode['description' . $suffix] . '</div>') . (empty($shortcode['extra' . $suffix]) ? '' : '<div style="padding-top:5px">' . $shortcode['extra' . $suffix] . '</div>') . (empty($shortcode['link-text' . $suffix]) ? '' : '<br>' . $div_button . $shortcode['link-text' . $suffix] . '</div>') . $div_button_end . '</div>';
                break;
            case 'select':
            case 'buttons':
            case 'chips':
                $values = explode(',', $shortcode['options']);
                $message .= $div_button_start;
                for ($i = 0; $i < count($values); $i++) {
                    $message .= $div_button . $values[$i] . '</div>';
                }
                $message .= $div_button_end;
                break;
            case 'inputs':
                $values = explode(',', $shortcode['values']);
                for ($i = 0; $i < count($values); $i++) {
                    $message .= $div_input . $values[$i] . '</div>';
                }
                break;
            case 'email':
                $fields = ['placeholder', 'name', 'last-name', 'phone'];
                for ($i = 0; $i < count($fields); $i++) {
                    $field = $shortcode[$fields[$i]];
                    $is_true = $field == 'true' || $field === true;
                    if ($is_true || ($field != 'false' && $field !== false)) {
                        $message .= $div_input . ($is_true ? mc_string_slug($fields[$i], 'string') : $field) . '</div>';
                    }
                }
                break;
            case 'button':
                $message .= $div_button_start . $div_button . $shortcode['link'] . '</div>' . $div_button_end;
                break;
            case 'video':
                $message .= ($shortcode['type'] == 'youtube' ? 'https://www.youtube.com/embed/' : 'https://player.vimeo.com/video/') . $shortcode['id'];
                break;
            case 'image':
                $message .= '<img src="' . $shortcode['url'] . '" style="max-width:300px;border-radius:4px;margin:-10px -15px;display:block;" />';
                break;
            case 'list-image':
            case 'list':
                $index = 0;
                $is_list_image = $shortcode_name == 'list-image';
                $is_numeric = isset($shortcode['numeric']) && $shortcode['numeric'] == 'true';

                if ($is_list_image) {
                    $shortcode['values'] = str_replace('://', '//', $shortcode['values']);
                    $index = 1;
                }

                // Handle escaped commas in list values
                $values_raw = $shortcode['values'];
                $values_raw = str_replace('\\,', '{{ESCAPED_COMMA}}', $values_raw);
                $values = explode(',', $values_raw);

                // Start HTML list with appropriate list style
                $list_style = $is_numeric ? 'decimal' : 'disc';
                $message .= '<' . ($is_numeric ? 'ol' : 'ul') . ' style="list-style-type: ' . $list_style . '; padding-left: 20px; margin: 10px 0;">';

                if (strpos($values[0], ':')) {
                    // This is a list with title:description format
                    for ($i = 0; $i < count($values); $i++) {
                        $value = explode(':', $values[$i]);
                        $value_text = isset($value[$index]) ? trim($value[$index]) : '';
                        $value_description = isset($value[$index + 1]) ? trim($value[$index + 1]) : '';

                        // Restore escaped commas
                        $value_text = str_replace('{{ESCAPED_COMMA}}', ',', $value_text);
                        $value_description = str_replace('{{ESCAPED_COMMA}}', ',', $value_description);

                        $message .= '<li style="margin-bottom: 5px;">' . ($is_list_image ? '<img style="border-radius:4px;width:30px;height:30px;transform:translateY(10px);margin-right: 10px;" src="' . str_replace('//', '://', $value[0]) . '" />' : '') . '<strong>' . $value_text . '</strong> ' . $value_description . '</li>';
                    }
                } else {
                    // Regular list or list with inner items
                    // Group items by their parent
                    $grouped_items = [];
                    $current_parent_index = -1;

                    foreach ($values as $value) {
                        $value = trim($value);
                        if (empty($value)) continue;

                        // Restore escaped commas
                        $value = str_replace('{{ESCAPED_COMMA}}', ',', $value);

                        // Check if this is an inner item (prefixed with "- ")
                        $is_inner = strpos($value, '- ') === 0;

                        if ($is_inner) {
                            // This is an inner item, add it to the current parent
                            $value = substr($value, 2); // Remove the "- " prefix

                            if (isset($grouped_items[$current_parent_index])) {
                                $grouped_items[$current_parent_index]['inner_items'][] = $value;
                            }
                        } else {
                            // This is a parent item, create a new group
                            $current_parent_index++;
                            $grouped_items[$current_parent_index] = [
                                'parent' => $value,
                                'inner_items' => []
                            ];
                        }
                    }

                    // Process each group
                    foreach ($grouped_items as $group) {
                        // Add the parent item
                        $message .= '<li style="margin-bottom: 5px;">' . $group['parent'];

                        // If this group has inner items, add a nested list
                        if (!empty($group['inner_items'])) {
                            // For numeric lists, use decimal sub-numbering
                            if ($is_numeric) {
                                // For numeric lists, use custom sub-numbering (e.g., 2.1, 2.2)
                                $message .= '<ol style="list-style-type: none; margin: 5px 0 5px 0; padding-left: 0;">';

                                $parent_index = array_search($group, $grouped_items) + 1;
                                $sub_index = 1;

                                foreach ($group['inner_items'] as $inner_item) {
                                    $message .= '<li style="margin-bottom: 5px;"><span style="display:inline-block;min-width:30px;">' .
                                               $parent_index . '.' . $sub_index . '.</span> ' . $inner_item . '</li>';
                                    $sub_index++;
                                }

                                $message .= '</ol>';
                            } else {
                                // For bullet lists, use circle bullets
                                $message .= '<ul style="list-style-type: circle; margin: 5px 0 5px 20px;">';

                                foreach ($group['inner_items'] as $inner_item) {
                                    $message .= '<li style="margin-bottom: 5px;">' . $inner_item . '</li>';
                                }

                                $message .= '</ul>';
                            }
                        }

                        // Close the parent item
                        $message .= '</li>';
                    }
                }

                $message .= '</' . ($is_numeric ? 'ol' : 'ul') . '>';
                break;
            case 'rating':
                $message .= $div_button_start . $div_button . mc_($shortcode['label-positive']) . '</div>' . $div_button . mc_($shortcode['label-negative']) . '</div>' . $div_button_end;
                break;
            case 'articles':
                $message .= '<b>' . mc_(mc_get_setting('articles-title', 'Help center')) . '</b><br>' . mc_isset($shortcode, 'link');
                break;
        }
    }
    return mc_text_formatting_to_html($message);
}

