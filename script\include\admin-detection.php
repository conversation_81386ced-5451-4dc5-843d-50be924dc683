<?php
/**
 * Admin Detection Helper
 *
 * This file provides functions to detect admin status by leveraging both
 * the script/ and account/ folder implementations.
 */

// Check if we're in a cloud environment
if (!function_exists('mc_is_cloud')) {
    function mc_is_cloud() {
        return defined('MC_CLOUD');
    }
}

// Function to check if user is admin using both script and account methods
function is_admin_user() {
    // First try the script method
    $is_admin = false;

    // Check if mc_is_agent function exists (script method)
    if (function_exists('mc_is_agent')) {
        $is_admin = mc_is_agent(false, true, true);
    }

    // If not admin and we're in cloud environment, try account method
    if (!$is_admin && mc_is_cloud() && defined('MC_CLOUD_PATH')) {
        // Include account functions if not already included
        $account_functions_path = MC_CLOUD_PATH . '/account/functions.php';
        if (file_exists($account_functions_path) && !function_exists('account')) {
            require_once($account_functions_path);
        }

        // Check if account function exists
        if (function_exists('account')) {
            $account_data = account();
            if ($account_data && isset($account_data['is_super_admin']) && $account_data['is_super_admin']) {
                $is_admin = true;
            }
        }

        // Check if super_admin function exists
        if (!$is_admin && function_exists('super_admin')) {
            $is_admin = super_admin() ? true : false;
        }
    }

    return $is_admin;
}

// Function to check if user is super admin
function is_super_admin_user() {
    $is_super_admin = false;

    // If we're in cloud environment, try account method
    if (mc_is_cloud() && defined('MC_CLOUD_PATH')) {
        // Include account functions if not already included
        $account_functions_path = MC_CLOUD_PATH . '/account/functions.php';
        if (file_exists($account_functions_path) && !function_exists('account')) {
            require_once($account_functions_path);
        }

        // Check if account function exists
        if (function_exists('account')) {
            $account_data = account();
            if ($account_data && isset($account_data['is_super_admin']) && $account_data['is_super_admin']) {
                $is_super_admin = true;
            }
        }

        // Check if super_admin function exists
        if (!$is_super_admin && function_exists('super_admin')) {
            $is_super_admin = super_admin() ? true : false;
        }
    }

    return $is_super_admin;
}

// Function to set JavaScript admin variables
function set_admin_js_variables() {
    $is_admin = is_admin_user();
    $is_super_admin = is_super_admin_user();

    // Output JavaScript variables
    if ($is_admin) {
        echo "<script>var IS_ADMIN = true;</script>\n";
    } else {
        echo "<script>var IS_ADMIN = false;</script>\n";
    }

    if ($is_super_admin) {
        echo "<script>var IS_SUPER = true;</script>\n";
    } else {
        echo "<script>var IS_SUPER = false;</script>\n";
    }

    return $is_admin;
}
