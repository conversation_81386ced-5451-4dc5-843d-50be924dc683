﻿/*
*
* ===================================================================
* CLOUD MAIN JS FILE
* ===================================================================
*
* © 2017-2025 app.masichat.com. All rights reserved.
*
*/

'use strict';

(function ($) {
    let body;
    let box_account;
    let box_registration;
    let box_super;
    let box_loading;
    let lightbox_profile;
    let URL = document.location.href;
    let URL_NO_PARS = URL;
    let account;
    let pusher;
    let pusher_channel;
    let responsive = $(window).width() < 465;
    let razorpay = PAYMENT_PROVIDER == 'razorpay';
    let stripe = PAYMENT_PROVIDER == 'stripe';
    let rapyd = PAYMENT_PROVIDER == 'rapyd';
    let paystack = PAYMENT_PROVIDER == 'paystack'; // Added: Initialize paystack variable
    let membership_type_ma = MEMBERSHIP_TYPE == 'messages-agents';
    let messages = {
        password_length: mc_('The password must be at least 8 characters long.'),
        password_match: mc_('The passwords do not match.'),
        email: mc_('The email address is not valid.')
    };
    // Added: Paystack endpoint to map
    let endpoints = {
        stripe: 'stripe-create-session',
        paystack: 'paystack-create-session',
        rapyd: 'rapyd-checkout',
        verifone: 'verifone-checkout',
        razorpay: 'razorpay-create-subscription',
        yoomoney: 'yoomoney-create-subscription'
    };

    $(document).ready(function () {
        body = $('body');
        box_account = body.find('.mc-account-box');
        box_registration = box_account.length ? false : body.find('.mc-registration-box');
        box_super = body.find('.mc-super-box');
        lightbox_profile = body.find('.mc-profile-edit-box');
        box_loading = body.find('.mc-loading-global');
        body.removeClass('on-load');

        // Initialize Facebook SDK
        initFacebookSDK();

        // Global
        body.on('click', '.mc-nav li', function () {
            $(this).siblings().mcActive(false);
            $(this).mcActive(true);
            $(this).closest('.mc-tab').find('> .mc-content > div').mcActive(false).eq($(this).index()).mcActive(true);
        });

        body.on('click', '.mc-lightbox .mc-close', function () {
            $(this).closest('.mc-lightbox').mcActive(false);
            body.find('.mc-lightbox-overlay').mcActive(false);
        });

        body.on('click', '.mc-lightbox .mc-info', function () {
            $(this).mcActive(false);
        });

        body.on('click', '.banner > i', function () {
            $(this).parent().remove();
        });

        if (URL.includes('?')) {
            URL_NO_PARS = URL.substring(0, URL.indexOf('?'));
        }

        if (URL.includes('reload=true')) {
            box_account.startLoading();
            setTimeout(() => {
                document.location = URL.replace('&reload=true', '').replace('?reload=true', '');
            }, 2000);
            return;
        }

        // Responsive
        if (responsive) {
            body.on('click', '.mc-nav,.mc-menu-wide', function () {
                $(this).setClass('mc-active', !$(this).hasClass('mc-active'));
            });

            body.on('click', '.mc-nav li,.mc-menu-wide li', function () {
                $(this).parents().eq(1).find(' > div').html($(this).html());
            });
        }

        // Account
        if (box_account.length) {
            let chart;
            let chart_cnt = box_account.find('#chart-usage');
            let tabs = ['installation', 'membership', 'invoices', 'profile'];
            let encrypted_code;
            let profile_keys = box_account.find('#tab-profile .mc-input:not(#password)').map(function () { return $(this).attr('id') }).get();
            let menu = box_account.find('.plans-box-menu');

            // Check for payment success and show appropriate message
            if (URL.includes('payment_status=success') || URL.includes('payment_status=success_pending_webhook')) {
                // Remove any existing banners first to avoid duplicates
                $('.banner').remove();

                // Debug the URL to see what parameters are present
                console.log('Payment success URL:', URL);
                console.log('Contains payment_type=credits:', URL.includes('payment_type=credits'));
                console.log('Contains #credits:', URL.includes('#credits'));

                // Check if it's a credit purchase - check payment_type parameter, URL hash, and last_payment_type
                // First check the URL parameters which are most reliable
                if (URL.includes('payment_type=credits') || URL.includes('#credits')) {
                    console.log('Detected credits purchase success from URL parameters');
                    // Set a flag to indicate this is a credits purchase
                    window.isCreditsPayment = true;
                    setTimeout(() => {
                        // Force remove any banners that might have been added by other code
                        $('.banner').remove();
                        banner_success('Credits purchase successful! Your credits have been added to your account.');
                    }, 500);
                } else {
                    // If not in URL, check membership data for last_payment_type
                    ajax('account-membership-details', {}, (response) => {
                        if (response && response.membership && response.membership.last_payment_type === 'credits') {
                            console.log('Detected credits purchase from last_payment_type');
                            // Set a flag to indicate this is a credits purchase
                            window.isCreditsPayment = true;
                            // Force remove any banners that might have been added by other code
                            $('.banner').remove();
                            banner_success('Credits purchase successful! Your credits have been added to your account.');
                        } else {
                            // Assume it's a plan purchase
                            console.log('Detected plan purchase success');
                            // Set a flag to indicate this is NOT a credits purchase
                            window.isCreditsPayment = false;
                            // Force remove any banners that might have been added by other code
                            $('.banner').remove();
                            banner_success('Your plan has been updated successfully!');
                        }
                    });
                }

                // Refresh membership data to update the UI without full page reload
                setTimeout(() => {
                    ajax('account-membership-details', {}, (response) => {
                        if (response && !response.status) {
                            // Update membership data in the UI
                            if (response.membership) {
                                // Update quota display
                                $('.membership-quota').first().text(response.membership.quota == 9999 ? '∞' : response.membership.quota);

                                // Update active membership name and price
                                $('.membership-name').text(response.membership.name);

                                // Update credits display if available
                                if (response.membership.credits !== undefined) {
                                    $('#credits .credits-amount').text(response.membership.credits);
                                }

                                // Update active plan in the plans list
                                $('#plans > div').removeAttr('data-active-membership').removeAttr('data-expired');
                                $('#plans > div[data-id="' + response.membership.id + '"]').attr('data-active-membership', 'true');

                                // Remove the active-membership-info divs and add a new one to the active plan
                                $('.active-membership-info').remove();
                                $('#plans > div[data-id="' + response.membership.id + '"]').prepend('<div class="active-membership-info">' + mc_('Active Membership') + '</div>');

                                console.log('Membership data updated successfully');
                            }
                        }
                    });
                }, 1000);
            }

            if (URL.includes('#credits')) {
                setTimeout(() => {
                    box_account.find('#credits')[0].scrollIntoView();
                }, 500);
            }

            ajax('account-user-details', {}, (response) => {
                account = response;
                box_account.find('#embed-code').val(`<!-- ${BRAND_NAME} -->\n<script id="chat-init" src="${CLOUD_URL}/account/js/init.js?id=${response.chat_id}"></script>`);
                box_account.stopLoading();
                for (var i = 0; i < profile_keys.length; i++) {
                    if (profile_keys[i] in response) {
                        box_account.find(`#${profile_keys[i]} input`).val(response[profile_keys[i]]);
                    }
                }
                for (var i = 0; i < 2; i++) {
                    let name = i ? 'email' : 'phone';
                    if (response[name + '_confirmed'] == 1 || (!i && !TWILIO_SMS)) {
                        box_account.find('#' + name).removeClass('mc-type-input-button').find('.mc-btn').remove();
                    }
                }
                banners('verify');
            });

            if (menu.length) {
                let selected = menu.find('li').eq(0).mcActive(true);
                box_account.find('#plans > div').removeClass('mc-visible');
                box_account.find(`#plans > [data-menu="${selected.attr('data-type')}"]`).addClass('mc-visible');
                $(menu).on('click', 'li', function () {
                    menu.find('li').mcActive(false);
                    box_account.find('#plans > div').removeClass('mc-visible');
                    box_account.find(`#plans > [data-menu="${$(this).attr('data-type')}"]`).addClass('mc-visible');
                    $(this).mcActive(true);
                });
                if (box_account.find('#membership-appsumo').length) {
                    menu.find('ul').append(`<li><a href="https://appsumo.com/account/products/" target="_blank" style="color:#028be5;text-decoration:none">AppSumo</a></li>`);
                }
            } else {
                box_account.find('#plans > div').addClass('mc-visible');
            }

            box_account.on('click', ' > .mc-tab > .mc-nav li', function () {
                let tab_id = $(this).attr('id').replace('nav-', '');
                if (tab_id == 'membership') {
                    if (chart_cnt.isLoading()) {
                        $.getScript(CLOUD_URL + '/script/vendor/chart.min.js', () => {
                            chart_cnt.stopLoading();
                            chart = new Chart(chart_cnt, {
                                type: 'bar',
                                data: {
                                    labels: [mc_('January'), mc_('February'), mc_('March'), mc_('April'), mc_('May'), mc_('June'), mc_('July'), mc_('August'), mc_('September'), mc_('October'), mc_('November'), mc_('December')],
                                    datasets: [{
                                        data: messages_volume,
                                        backgroundColor: '#009BFC'
                                    }],
                                }, options: {
                                    legend: {
                                        display: false
                                    }
                                }
                            });
                        });
                    }
                    setTimeout(() => { banners('suspended') }, 300);
                }
                if (tab_id == 'profile') {
                    setTimeout(() => { banners('verify') }, 1000);
                }
                if (tab_id == 'logout') {
                    MCF.logout(false);
                    setTimeout(() => { document.location = location.href.substring(0, location.href.indexOf('/account')) + '?login' }, 300);
                }
                window.history.replaceState(null, null, '?tab=' + tab_id + (location.href.includes('debug') ? '&debug' : ''));
            });

            box_account.on('click', '.btn-verify-email,.btn-verify-phone', function () {
                let value = $(this).parent().find('input').val();
                if (!value || loading(this)) return;
                let data = {};
                let is_email = $(this).hasClass('btn-verify-email');
                data[is_email ? 'email' : 'phone'] = $(this).parent().find('input').val();
                ajax('verify', data, (response) => {
                    // Check if response is an error object
                    if (response && typeof response === 'object' && response.status === 'error') {
                        // More user-friendly error message with guidance
                        banner_error(response.message || 'We couldn\'t send your verification code. Please check your contact information and try again in a few minutes.');
                        $(this).stopLoading();
                        return;
                    }

                    // Handle success response with message property (new format)
                    if (response && typeof response === 'object' && response.status === 'success' && response.message) {
                        console.log('Received verification code in new format');
                        encrypted_code = response.message;
                    } else {
                        // Store the encrypted code for later use (old format - direct string)
                        encrypted_code = response;
                    }

                    console.log('Verification code sent successfully');

                    // Show banner with input for verification code
                    banner(`We sent you a secret code`, `We sent you a secret code, please enter it below to verify your ${is_email ? 'email address' : 'phone number'}`, `<div data-type="text" class="mc-input mc-type-input-button"><input type="text"><a id="btn-verify-code" class="mc-btn">${mc_('Complete verification')}</a></div>`);
                    $(this).stopLoading();
                });
            });

            box_account.on('click', '.banner #btn-verify-code', function () {
                let code = $(this).parent().find('input').val();
                if (!code || loading(this)) return;

                // Log the data being sent for debugging
                console.log('Sending verification code:', { encrypted_code_type: typeof encrypted_code, code_type: typeof code });

                // Make sure encrypted_code is a string
                if (typeof encrypted_code !== 'string') {
                    // More user-friendly error message with guidance
                    banner_error('There was a problem with your verification request. Please try requesting a new code by clicking the verify button again.');
                    $(this).stopLoading();
                    return;
                }

                // Send the verification request
                ajax('verify', { 'code_pairs': [encrypted_code, code] }, (response) => {
                    // Log the response for debugging
                    console.log('Verification response:', response);

                    // First check if response is an error object
                    if (response && typeof response === 'object' && response.status === 'error') {
                        // More user-friendly error message with guidance
                        banner_error(response.message || 'The verification code you entered is incorrect or has expired. Please check the code and try again, or request a new code.');
                        $(this).stopLoading();
                        return;
                    }

                    // Check if response is valid and has the expected structure for success
                    if (response && Array.isArray(response) && response.length >= 1) {
                        let email = response[0] === 'email';

                        // Check if response[1] exists and has the expected structure
                        if (Array.isArray(response[1]) && response[1].length >= 2) {
                            setLogin(response[1][0], response[1][1]);
                            let setting = $(box_account).find(email ? '#email' : '#phone');
                            setting.removeClass('mc-type-input-button').find('.mc-btn').remove();
                            box_account.find('.banner').remove();
                            banner_success(`Thank you! Your ${email ? 'email address' : 'phone number'} has been verified.`);
                        } else {
                            // More user-friendly error message with guidance
                            banner_error('We encountered an issue while verifying your information. Please try again or contact our support team if the problem persists.');
                        }
                    } else {
                        // If we get here, it's not an error object and not a valid success response
                        // More user-friendly error message with guidance
                        banner_error('We couldn\'t complete your verification at this time. Please try again later or contact support if the problem persists.');
                    }
                    $(this).stopLoading();
                });
            });

            box_account.on('click', '#save-profile', function () {
                if (loading(this)) return;
                let details = {};
                let error = false;
                box_account.find('#tab-profile .mc-input input').each((e, element) => {
                    let id = $(element).parent().attr('id');
                    let value = $.trim($(element).val());
                    if (!value && id !== 'password') { // Password can be empty if not changing
                        banner_error('All fields except password are required.');
                        error = true;
                    }
                    if (id == 'password' && value && value.length < 8) { // Only check length if password is provided
                        banner_error(messages.password_length);
                        error = true;
                    }
                    if (id == 'email' && (!value.includes('@') || !value.includes('.'))) {
                        banner_error(messages.email);
                        error = true;
                    }
                    details[id] = value;
                });
                if (!error) {
                    ajax('account-save', { 'details': details }, (response) => {
                        // Added: Check for error object from improved ajax helper
                        if (response && response.status === 'error') {
                            // More user-friendly error message with guidance
                            banner_error(response.message || 'We couldn\'t save your profile information. Please check your entries and try again. If the problem persists, contact support.');
                            $(this).stopLoading();
                            return;
                        }
                        if (Array.isArray(response)) {
                            setLogin(response[0], response[1]);
                            banner_success('Your profile information has been updated successfully.');
                             if (details['password']) { // Clear password field after successful save if it was changed
                                 box_account.find('#password input').val('');
                            }
                        } else {
                            banner_error(response); // Handle specific errors returned as strings (e.g., duplicate email)
                        }
                        $(this).stopLoading();
                    });
                } else {
                    $(this).stopLoading();
                }
            });

            box_account.on('click', '#nav-invoices', function () {
                let tab = box_account.find('#tab-invoices');
                if (tab.isLoading()) {
                    ajax('get-payments', {}, (response) => {
                        // Added: Check for error object from improved ajax helper
                        if (response && response.status === 'error') {
                            // More user-friendly error message with guidance
                            tab.append(`<p>${mc_('We couldn\'t load your payment history at this time. Please refresh the page or try again later.')}</p>`);
                            tab.stopLoading();
                            return;
                        }
                        let code = '';
                        for (var i = 0; i < response.length; i++) {
                            code += `<tr><td><i class="mc-icon-file"></i>${get_invoice_string(response[i])}</td></tr>`;
                        }
                        if (code) {
                            tab.find('tbody').html(code);
                        } else {
                             // Added: Paystack check for invoice message
                            tab.append(`<p>${mc_(`There are no ${stripe || paystack ? 'invoices' : 'payments'} yet.`)}</p>`);
                        }
                        tab.stopLoading();
                    });
                }
            });

            box_account.on('click', '.mc-invoice-row', function () {
                    let $row = $(this);

    if ($row.isLoading()) return;

    $row.startLoading();
    ajax('get-invoice', { payment_id: $(this).attr('data-id') }, (response) => {
    $row.stopLoading(); // Stop loading when response arrives

    // --- REVISED LOGIC ---
    // 1. Check for overall response validity and PHP-level error
    if (!response || typeof response !== 'object' || response.status === 'error') {
        console.error("Error getting invoice:", response ? response.message : 'Invalid response received');
        banner_error((response && response.message) || 'Failed to retrieve invoice details.');
        return;
    }

    // 2. Extract the filename from the 'message' property
    let filename = response.message;

    // 3. Validate the extracted filename
    // (Optional: You might not need the .trim().replace() anymore if PHP sends clean data)
    if (typeof filename === 'string') {
        filename = filename.trim().replace(/^"|"$/g, ''); // Keep cleanup just in case
    }

    if (!filename || typeof filename !== 'string' || filename.includes('/') || filename.includes('..') || !filename.endsWith('.pdf')) { // Added endsWith check
         // More user-friendly error message with guidance
         banner_error('We couldn\'t generate your invoice. Please try again or contact our support team for assistance.');
         return;
    }
    // --- END REVISED LOGIC ---


    // 4. Open the URL with the validated filename
    window.open(CLOUD_URL + '/script/uploads/invoices/' + filename);

    // 5. Request deletion using the validated filename
    setTimeout(() => {
        ajax('delete-invoice', { file_name: filename }, (deleteResponse) => {
            // Optional: Log success/failure of delete request
            if (deleteResponse && deleteResponse.status === 'error') {
                console.warn("Failed to delete temporary invoice file:", filename, deleteResponse.message);
            } else {
                console.log("Temporary invoice delete request sent for:", filename);
            }
        });
    }, 1500);
                });
            });


            box_account.on('click', '#plans > div', function (e) {
                e.preventDefault();
                if ($(this).attr('data-active-membership') && !$(this).attr('data-expired') || loading(this)) {
                    return;
                }
                if (PAYMENT_PROVIDER == 'manual') {
                    $(this).stopLoading();
                    return document.location = PAYMENT_MANUAL_LINK;
                }
                 // Modified: Use updated endpoints map which includes Paystack
                ajax(external_integration === 'shopify' ? 'shopify-subscription' : endpoints[PAYMENT_PROVIDER], {
                    price_id: $(this).attr('data-id'),
                    cloud_user_id: CLOUD_USER_ID
                }, (response) => {
                     // Added: Check for error object from improved ajax helper
                    if (response && response.status === 'error') {
                        // More user-friendly error message with guidance
                        banner_error(response.message || 'We couldn\'t process your payment request. Please check your payment information and try again, or contact support if the issue persists.');
                        $(this).stopLoading();
                        return;
                    }
                     if (response && response.url) { // Check if URL exists
                        document.location = response.url;
                     } else {
                          // More user-friendly error message with guidance
                          banner_error('We couldn\'t redirect you to the payment page. Please try again or contact support if the problem continues.');
                          $(this).stopLoading();
                     }
                });
            });

            box_account.on('click', '#purchase-white-label', function (e) {
                e.preventDefault();
                if ($(this).hasClass('mc-plan-active') || loading(this)) {
                    return;
                }
                if (PAYMENT_PROVIDER == 'manual') {
                    $(this).stopLoading();
                    return document.location = PAYMENT_MANUAL_LINK;
                }
                ajax('purchase-white-label', { external_integration: external_integration }, (response) => {
                     // Added: Check for error object from improved ajax helper
                    if (response && response.status === 'error') {
                        // More user-friendly error message with guidance
                        banner_error(response.message || 'We couldn\'t process your white-label purchase request. Please try again later or contact support for assistance.');
                        $(this).stopLoading();
                        return;
                    }
                    if (response && response.url) {
                         document.location = response.url;
                    } else {
                         // More user-friendly error message with guidance
                         banner_error('We couldn\'t redirect you to the white-label payment page. Please try again or contact our support team for help.');
                         $(this).stopLoading();
                    }
                });
            });

            box_account.on('change', '#add-credits select', function (e) {
                let value = $(this).val();
                e.preventDefault();
                if (!value || loading($(this).parent())) {
                    return;
                }
                if (PAYMENT_PROVIDER == 'manual') {
                    $(this).parent().stopLoading();
                    return document.location = PAYMENT_MANUAL_LINK;
                }
                ajax('purchase-credits', { amount: value, external_integration: external_integration }, (response) => {
                     // Added: Check for error object from improved ajax helper
                    if (response && response.status === 'error') {
                        // More user-friendly error message with guidance
                        banner_error(response.message || 'We couldn\'t process your credit purchase. Please verify the amount and try again, or contact support if the issue persists.');
                         $(this).parent().stopLoading(); // Stop loading on error
                        return;
                    }
                     // Original response check (might be {error:..} or {url:...})
                    if (response.error) {
                        // More user-friendly error message with guidance
                        banner_error('We couldn\'t complete your credit purchase. Please try again later or contact our support team for assistance.');
                         $(this).parent().stopLoading();
                    } else if (response.url) {
                        document.location = response.url;
                         // Don't stop loading here, redirection handles it
                    } else {
                         // More user-friendly error message with guidance
                         banner_error('We encountered an issue while processing your credit purchase. Please try again or contact our support team for assistance with your purchase.');
                         $(this).parent().stopLoading();
                    }
                });
            });

            box_account.on('click', '.mc-custom-addon', function (e) {
                if (loading(this)) {
                    return;
                }
                ajax('purchase-addon', { index: $(this).attr('data-index') }, (response) => {
                    // Added: Check for error object from improved ajax helper
                    if (response && response.status === 'error') {
                        // More user-friendly error message with guidance
                        banner_error(response.message || 'We couldn\'t process your addon purchase. Please try again later or contact our support team for assistance.');
                        $(this).stopLoading();
                        return;
                    }
                     if (response && response.url) {
                        document.location = response.url;
                     } else {
                          // More user-friendly error message with guidance
                          banner_error('We couldn\'t redirect you to the addon payment page. Please try again in a few minutes or contact our support team for assistance with your purchase.');
                          $(this).stopLoading();
                     }
                });
            });

            box_account.on('click', '#credits-recharge input', function (e) {
                ajax('set-auto-recharge-credits', { enabled: $(this).is(':checked') }, (response) => {
                    // Added: Check for error object from improved ajax helper
                    if (response && response.status === 'error') {
                        console.error("Error setting auto-recharge:", response.message);
                        banner_error(response.message || 'Failed to save auto-recharge setting.');
                        // Revert checkbox state? Maybe not necessary, but consider UX.
                        // $(this).prop('checked', !$(this).is(':checked'));
                        return;
                    }
                    // Assuming success if no error object
                    banner_success('Settings saved.');
                });
            });

            // --- Start: Paystack Cancellation Handler (from user's version) ---
            box_account.on('click', '#cancel-subscription', function() {
                if (PAYMENT_PROVIDER === 'paystack') { // Only run this for Paystack
                    var subscriptionCode = $(this).data('subscription-code');
                    var emailToken = $(this).data('email-token'); // Ensure this is being set in index.php

                    if (subscriptionCode) {
                        if (confirm(mc_('Are you sure you want to cancel your subscription?'))) {
                            let button = $(this); // Reference the button
                            button.addClass('mc-loading').html('<i class="mc-icon-loading"></i> ' + mc_('Cancelling...')); // Use an icon

                            $.ajax({
                                url: 'ajax.php', // Verify this path is correct relative to your HTML file
                                method: 'POST',
                                data: {
                                    function: 'cancel_paystack_subscription', // Match the case name in ajax.php
                                    subscription_code: subscriptionCode,
                                    email_token: emailToken
                                },
                                dataType: 'json' // Expect JSON response
                            })
                            .done(function(response) {
                                console.log("Cancellation response:", response);
                                if (response && response.status === 'manage_link_generated' && response.link) {
                                    // Redirect user to Paystack manage link
                                    alert(response.message || mc_('Redirecting to Paystack to manage your subscription...'));
                                    window.location.href = response.link;
                                    // No page reload needed here, button state remains loading until redirect
                                } else if (response && (response.status === 'success' || response.status === 'canceled_locally')) {
                                    // Cancellation processed (fully or locally)
                                    alert(response.message || mc_('Subscription cancellation requested. Please reload the page to see the changes.'));
                                    // Replace button with a refresh link
                                    button.replaceWith('<a href="#" id="mc-refresh-after-cancel" class="mc-btn-text mc-icon"><i class="mc-icon-refresh"></i> ' + mc_('Refresh to update status') + '</a>');
                                } else {
                                    // Handle errors
                                    alert(response.message || mc_('Subscription cancellation failed. Please try again or contact support.'));
                                    // Restore button state on failure
                                    button.removeClass('mc-loading').html('<i class="mc-icon-close"></i>' + mc_('Cancel subscription'));
                                }
                            })
                            .fail(function(jqXHR, textStatus, errorThrown) {
                                console.error("Cancellation AJAX failed:", textStatus, errorThrown, jqXHR.responseText);
                                alert(mc_('Unable to process subscription cancellation. Please check console (F12) for details.'));
                                // Restore button state on AJAX failure
                                button.removeClass('mc-loading').html('<i class="mc-icon-close"></i>' + mc_('Cancel subscription'));
                            });
                        }
                    } else {
                        console.error("Missing subscription code for cancellation.", {code: subscriptionCode});
                        alert(mc_('Subscription details not found. Cannot proceed with cancellation.'));
                    }
                 } else { // --- Fallback for other providers (Original Logic) ---
                    // This part handles Stripe, Rapyd etc. cancellation
                     e.preventDefault();
                     if (confirm(mc_('Are you sure?'))) {
                         if (loading(this)) return;
                         let button = $(this); // Reference button
                         ajax((external_integration ? external_integration : PAYMENT_PROVIDER) + '-cancel-subscription', {}, (response) => {
                              // Added: Check for error object from improved ajax helper
                              if (response && response.status === 'error') {
                                  console.error("Error cancelling subscription:", response.message);
                                  banner_error(response.message || 'Failed to cancel subscription.');
                                  button.stopLoading();
                                  return;
                              }
                             if (response == 'no-subscriptions') {
                                 banner('Subscription already cancelled', 'You do not have any active subscription.', '', false, false, true);
                                 button.remove(); // Remove button if already cancelled
                             } else if (response && (response.status == 'canceled' || response === true)) { // Allow boolean true for generic success
                                 banner('Subscription cancelled', 'The subscription has ben cancelled sucessfully.', '', false, false, true);
                                 button.replaceWith('<a href="#" id="mc-refresh-after-cancel" class="mc-btn-text mc-icon"><i class="mc-icon-refresh"></i> ' + mc_('Refresh to update status') + '</a>'); // Use refresh link standardly
                             } else {
                                 // Attempt to stringify if it's an object, otherwise show raw response
                                 let errorMsg = typeof response === 'object' ? JSON.stringify(response) : response;
                                 banner('Error', 'Cancellation failed: ' + errorMsg, '', false, true);
                             }
                             button.stopLoading(); // Stop loading if not replaced
                         });
                     }
                 } // --- End Fallback ---
            });

            // --- Added: Refresh Handler (Delegated) ---
            // Ensure box_account is defined and available in this scope
            console.log('Attaching refresh handler to box_account');
            box_account.on('click', '#mc-refresh-after-cancel', function(e){
                e.preventDefault(); // Prevent default anchor link behavior
                $(this).html('<i class="mc-icon-loading"></i> Refreshing...'); // Provide user feedback with icon
                location.reload(); // Reload the current page
            });
            // --- End Refresh Handler ---

            // Check for welcome message
            if (URL.includes('welcome') && SETTINGS.text_welcome_title) {
                banner(SETTINGS.text_welcome_title, SETTINGS.text_welcome, '', SETTINGS.text_welcome_image);
                window.history.replaceState({}, document.title, URL_NO_PARS);
                // Set the dashboard button to open in the same tab (remove target attribute)
                box_account.find('.mc-btn-dashboard').addClass('animation-button').attr('href', '../?welcome');
            }

            // Check for Paystack payment status - moved to document ready for earlier processing
            if (URL.includes('payment_status=')) {
                let status = MCF.getURL('payment_status');
                let error = MCF.getURL('psk_error');

                if (status === 'success_pending_webhook') {
                    // Keep the loading screen active while we check for updates
                    box_account.startLoading();

                    // Make an AJAX call to check if the membership has been updated
                    ajax('membership', {}, (response) => {
                        // Clean URL first to prevent loops if page refreshes
                        window.history.replaceState({}, document.title, URL_NO_PARS + '?tab=membership');

                        // Check if the membership data is available and valid
                        if (response && typeof response === 'object' && response.id) {
                            // Update the membership data without refreshing
                            membership = response;

                            // Update the UI with the new membership data
                            updateMembershipUI(response);

                            // Show success message based on payment type
                            // Debug the URL and our flag
                            console.log('Webhook success - URL:', URL);
                            console.log('Webhook success - isCreditsPayment flag:', window.isCreditsPayment);
                            console.log('Webhook success - Contains payment_type=credits:', URL.includes('payment_type=credits'));
                            console.log('Webhook success - Contains #credits:', URL.includes('#credits'));

                            // Check if it's a credit purchase using our flag or URL parameters
                            if (window.isCreditsPayment === true || URL.includes('payment_type=credits') || URL.includes('#credits')) {
                                // Remove any existing banners first
                                $('.banner').remove();
                                console.log('Webhook showing CREDITS success message');
                                banner_success('Credits purchase successful! Your credits have been added to your account.');
                            } else {
                                // Remove any existing banners first
                                $('.banner').remove();
                                console.log('Webhook showing PLAN success message');
                                banner_success('Your plan has been updated successfully!');
                            }
                            box_account.stopLoading();
                        } else {
                            // If we can't get updated data, refresh the page
                            // Show success message based on payment type
                            // Debug the URL and our flag
                            console.log('Fallback - URL:', URL);
                            console.log('Fallback - isCreditsPayment flag:', window.isCreditsPayment);
                            console.log('Fallback - Contains payment_type=credits:', URL.includes('payment_type=credits'));
                            console.log('Fallback - Contains #credits:', URL.includes('#credits'));

                            // Check if it's a credit purchase using our flag or URL parameters
                            if (window.isCreditsPayment === true || URL.includes('payment_type=credits') || URL.includes('#credits')) {
                                // Remove any existing banners first
                                $('.banner').remove();
                                console.log('Fallback showing CREDITS success message');
                                banner_success('Credits purchase successful! Updating your account...');
                            } else {
                                // Remove any existing banners first
                                $('.banner').remove();
                                console.log('Fallback showing PLAN success message');
                                banner_success('Payment successful! Updating your plan...');
                            }
                            setTimeout(() => {
                                location.reload();
                            }, 500);
                        }
                    });
                } else if (status === 'failed') {
                    // Clean URL first
                    window.history.replaceState({}, document.title, URL_NO_PARS + '?tab=membership');
                    banner_error('Payment verification failed: ' + (error || 'Unknown error'));
                } else if (status === 'error') {
                    // Clean URL first
                    window.history.replaceState({}, document.title, URL_NO_PARS + '?tab=membership');
                    banner_error('Error processing payment: ' + (error || 'Unknown error'));
                }
            }

            if (URL.includes('tab=')) {
                for (var i = 0; i < tabs.length; i++) {
                    if (URL.includes('tab=' + tabs[i])) {
                        let nav = box_account.find(' > .mc-tab > .mc-nav');
                        nav.find('li').eq(i).click();
                        nav.mcActive(false);
                        break;
                    }
                }
            }

            box_account.on('click', '#delete-account', function (e) {
                e.preventDefault();
                if (confirm(mc_('Are you sure? Your account, along with all its users and conversations, will be deleted permanently.'))) {
                    if (loading(this)) return;
                    ajax('account-delete', {}, (response) => { // Added response handling
                        // Added: Check for error object from improved ajax helper
                        if (response && response.status === 'error') {
                            console.error("Error deleting account:", response.message);
                            banner_error(response.message || 'We couldn\'t delete your account. Please try again later or contact our support team for assistance.');
                            $(this).stopLoading();
                            return;
                        }
                        // Assume success otherwise (original code had no explicit success check)
                        MCF.cookie('mc-login', '', '', false);
                        MCF.cookie('mc-cloud', '', '', false);
                        MCF.storage('open-conversation', '');
                        MCF.storage('login', '');
                        setTimeout(() => { location.reload() }, 500);
                    });
                }
            });

            box_account.on('click', '#delete-agents-quota', function () {
                if (confirm(mc_('Are you sure?'))) {
                    ajax('account-delete-agents-quota', {}, (response) => { // Added response handling
                         // Added: Check for error object from improved ajax helper
                         if (response && response.status === 'error') {
                            console.error("Error deleting agents quota:", response.message);
                            banner_error(response.message || 'We couldn\'t update your agent quota. Please try again later or contact our support team for assistance.');
                            return; // Don't redirect on error
                         }
                         // Assume success otherwise
                        location.href = CLOUD_URL;
                    });
                }
            });

            box_account.on('click', '#save-payment-information', function () {
                ajax('save-referral-payment-information', { method: box_account.find('#payment_method').val(), details: box_account.find('#payment_information').val() }, (response) => { // Added response handling
                     // Added: Check for error object from improved ajax helper
                     if (response && response.status === 'error') {
                         console.error("Error saving payment info:", response.message);
                         banner_error(response.message || 'Failed to save payment information.');
                         return;
                     }
                     // Assume success otherwise
                    banner_success('Settings saved.');
                    scrollTop(); // Kept scrollTop call
                });
            });

            box_account.on('click', '#nav-referral', function () {
                let payment_method = box_account.find('#payment_method');
                if (!payment_method.attr('data-loaded')) {
                    ajax('get-referral-payment-information', {}, (response) => {
                         // Added: Check for error object from improved ajax helper
                        if (response && response.status === 'error') {
                            console.error("Error getting payment info:", response.message);
                            // Optionally display an error message in the tab
                            return;
                        }
                        response = response ? response.split('|') : ['', ''];
                        payment_method.attr('data-loaded', 'true');
                        payment_method.val(response[0]);
                        box_account.find('#payment_information').val(response[1]);
                        box_account.find('#payment_information_label').html(mc_(response[0] == 'bank' ? 'Bank details' : 'PayPal email'));
                    });
                }
            });

            box_account.on('change', '#payment_method', function () {
                box_account.find('#payment_information_label').html(mc_($(this).val() == 'bank' ? 'Bank details' : 'PayPal email'));
            });

            banners('suspended');
        } else { // Added fallback for refresh handler if box_account not found immediately
             // Fallback if box_account is not available (e.g., on super admin page)
             console.warn("box_account not defined or found, using document delegation for #mc-refresh-after-cancel.");
             $(document).on('click', '#mc-refresh-after-cancel', function(e){
                  e.preventDefault();
                  $(this).html('<i class="mc-icon-loading"></i> Refreshing...');
                  location.reload();
             });
        }

        // Registration and login
        if (box_registration.length) {
            let box_login = body.find('.mc-login-box');
            let box_reset_password = body.find('.mc-reset-password-box');

            if (MCF.getURL('login_email')) {
                setTimeout(() => {
                    box_login.find('#email input').val(MCF.getURL('login_email'));
                    box_login.find('#password input').val(MCF.getURL('login_password'));
                    active(box_login, true);
                    active(box_registration, false);
                    box_login.find('.btn-login').click();
                }, 300);
            }

            $(box_registration).on('click', '.btn-register', function (e) {
                e.preventDefault(); // Moved preventDefault up
                if (loading(this)) return;
                let details = {};
                let errors = false;
                let errors_area = box_registration.find('.mc-errors-area');
                errors_area.html('');
                box_registration.find('[id].mc-input').each(function () {
                    let input = $(this).find('input');
                    if ($.trim(input.val())) {
                        input.removeClass('mc-error');
                    } else {
                        input.addClass('mc-error');
                        errors = true;
                    }
                    details[$(this).attr('id')] = $.trim(input.val());
                });
                if (errors) {
                    errors_area.html(mc_('Please fill in all required fields to complete your registration. All fields are necessary to create your account.'));
                } else if (details['password'].length < 8) {
                    errors_area.html(mc_('Your password must be at least 8 characters long. Please choose a stronger password for better security.'));
                    errors = true;
                } else if (details['password'] != details['password_2']) {
                    errors_area.html(mc_('The passwords you entered do not match. Please make sure both passwords are identical.'));
                    errors = true;
                } else if (!details['email'].includes('@') || !details['email'].includes('.')) {
                    errors_area.html(mc_('Please enter a valid email address. This will be used for account verification and important notifications.'));
                    errors = true;
                } else {
                    setLogin('', '');
                    if (URL.includes('ref=')) {
                        cookie('mc-referral', MCF.getURL('ref'), 180);
                    }
                    ajax('registration', { 'details': details }, (response) => {
                         // Added: Check for error object from improved ajax helper
                        if (response && response.status === 'error') {
                            // More user-friendly error message with guidance
                            errors_area.html(response.message || 'We couldn\'t complete your registration. Please check that all your information is correct and try again. If the problem persists, please contact our support team for assistance.');
                            $(this).stopLoading();
                            return;
                        }
                        if (response == 'duplicate-email') {
                            errors_area.html(mc_('This email address is already registered. Please use a different email address or try to log in if you already have an account. If you forgot your password, you can reset it from the login page.'));
                        } else if (Array.isArray(response)) { // Check if response is the expected array
                            setLogin(response[0], response[1]);
                            ajax('account-welcome'); // Send welcome email/notification
                            setTimeout(() => { document.location = MCF.getURL('redirect') ? MCF.getURL('redirect') : CLOUD_URL + '/account?welcome' }, 300);
                         } else {
                            // Handle unexpected successful response format with user-friendly message
                             errors_area.html('This email address is already registered. Please use a different email address or try to log in if you already have an account. If you forgot your password, you can reset it from the login page.');
                         }
                        $(this).stopLoading();
                    });
                }
                if (errors) {
                    $(this).stopLoading();
                }
                // Removed return false; as preventDefault is used
            });

            $(box_login).on('click', '.btn-login', function (e) {
                e.preventDefault(); // Moved preventDefault up
                let email = box_login.find('#email input').val();
                let password = box_login.find('#password input').val();
                let errors_area = box_login.find('.mc-errors-area');
                if (!email || !password || loading(this)) {
                     if(!email || !password) errors_area.html(mc_('Email and password are required.')); // Add message if fields empty
                    return;
                }
                errors_area.html('');
                ajax('login', { 'email': email, 'password': password }, (response) => {
                     // Added: Check for error object from improved ajax helper
                    if (response && response.status === 'error') {
                        // More user-friendly error message with guidance
                        errors_area.html(response.message || 'We couldn\'t log you in. Please check your email and password and try again.');
                        $(this).stopLoading();
                        return;
                    }
                    if (response === false) {
                        errors_area.html(mc_('The email or password you entered is incorrect. Please check your credentials and try again, or use the "Forgot Password" link if you need to reset your password.'));
                    } else if (response === 'ip-ban') {
                        errors_area.html(mc_('For security reasons, your account has been temporarily locked due to too many failed login attempts. Please wait a few hours before trying again, or contact our support team for immediate assistance.'));
                    } else if (Array.isArray(response)) { // Check if response is the expected array
                        setLogin(response[0], response[1]);
                        document.location = MCF.getURL('redirect') ? MCF.getURL('redirect') : CLOUD_URL;
                     } else {
                         // Handle unexpected successful response format with user-friendly message
                         errors_area.html('Your login was processed, but we encountered an issue. Please try again or contact our support team for assistance.');
                     }
                    $(this).stopLoading();
                });
                 // Removed return false; as preventDefault is used
            });

            $(box_login).on('click', '.btn-registration-box', function () {
                active(box_login, false);
                active(box_registration, true);
            });

            $(box_registration).on('click', '.mc-btn-login-box', function () {
                active(box_registration, false);
                active(box_login, true);
            });

            $(box_reset_password).on('click', '.btn-reset-password', function () {
                let email = $.trim(box_reset_password.find('#reset-password-email').val());
                if (email && email.includes('@') && email.includes('.')) {
                    ajax('account-reset-password', { 'email': email }, (response) => { // Added response handler
                         // Added: Check for error object from improved ajax helper
                         if (response && response.status === 'error') {
                             console.error("Error requesting password reset:", response.message);
                             // Optionally show an error message here, but the text below is generic enough
                         }
                         // Update UI regardless of success/failure on backend, as per original logic
                         box_reset_password.html(`<div class="mc-top-bar"><div class="mc-title">${mc_('Check your email')}</div><div class="mc-text">${mc_('If an account linked to the email provided exists you will receive an email with a link to reset your password.')}</div></div>`);
                    });
                } else {
                     // Add feedback if email is invalid
                     box_reset_password.find('.mc-errors-area').html(messages.email).mcActive(true); // Assuming an error area exists
                }
            });

            $(box_reset_password).on('click', '.btn-cancel-reset-password', function () {
                active(box_login, true);
                active(box_reset_password, false);
            });

            $(box_login).on('click', '.btn-forgot-password', function () {
                active(box_registration, false);
                active(box_login, false);
                active(box_reset_password, true);
            });

            if (URL.includes('reset=')) {
                let box_reset_password_2 = body.find('.mc-reset-password-box-2');
                let info = box_reset_password_2.find('.mc-info');
                $(box_reset_password_2).on('click', '.btn-reset-password-2', function () {
                    let password = box_reset_password_2.find('#reset-password-1').val();
                    info.html('').mcActive(false);
                    if (!password) {
                         info.html(mc_('Please enter a new password to complete the reset process. Your password should be at least 8 characters long for better security.')).mcActive(true);
                        return;
                    }
                    if (password != box_reset_password_2.find('#reset-password-2').val()) {
                        info.html(mc_('The passwords you entered do not match. Please make sure both passwords are identical.')).mcActive(true);
                        return;
                    }
                    if (password.length < 8) {
                        info.html(mc_('Your password must be at least 8 characters long. Please choose a stronger password for better security.')).mcActive(true);
                        return;
                    }
                    if (loading(this)) return;
                    ajax('account-reset-password', { 'email': MCF.getURL('email'), 'token': MCF.getURL('reset'), 'password': password }, (response) => {
                         // Added: Check for error object from improved ajax helper
                         if (response && response.status === 'error') {
                             // More user-friendly error message with guidance
                             info.html(response.message || 'We couldn\'t reset your password. The reset link may have expired. Please request a new password reset link and try again.').mcActive(true);
                             $(this).stopLoading();
                             return;
                         }
                         // Assume success if no error object (original code had no explicit check)
                        active(box_login, true);
                        active(box_reset_password_2, false);
                        $(this).stopLoading();
                    });
                });
            }

            $(window).keydown(function (e) {
                if (e.which == 13) {
                    // Trigger login only if login box is active
                    if (box_login.mcActive()) {
                        box_login.find('.btn-login').click();
                    }
                    // Trigger registration only if registration box is active
                    else if (box_registration.mcActive()) {
                         box_registration.find('.btn-register').click();
                    }
                }
            });
        }

        // Super
        if (box_super.length) {
            if (box_super.find('.table-customers').length) {
                ajax('super-get-customers', {}, (response) => {
                    // Added: Check for error object from improved ajax helper
                    if (response && response.status === 'error') {
                        // Log minimal information for debugging
                        console.log("Could not load customer list");
                        box_super.find('#tab-customers').html(`<p>${mc_('We couldn\'t load the customer list. Please refresh the page and try again.')}</p>`).stopLoading();
                        return;
                    }
                    let code = '';
                    for (var i = 0; i < response.length; i++) {
                        let user = response[i];
                        // Make sure get_membership exists and handles potential undefined IDs
                        let membershipInfo = get_membership(user.membership) || { name: 'Unknown' };
                        code += `<tr data-customer-id="${user.id}"><td data-id="id">${user.id}</td><td data-id="name">${user.first_name || ''} ${user.last_name || ''}</td><td data-id="email">${user.email || ''}</td><td data-id="phone">${user.phone || ''}</td><td data-id="membership">${membershipInfo.name}</td><td data-id="token">${user.token || ''}</td><td data-id="creation_time">${user.creation_time || ''}</td></tr>`;
                    }
                    box_super.find('.table-customers tbody').html(code);
                    box_super.find('#tab-customers').stopLoading();
                });
            }

            // --- Start: Super Admin Login (User's Version with direct $.ajax) ---
            $(box_super).on('click', '.btn-login', function (e) {
                e.preventDefault();
                let email = box_super.find('#email input').val();
                let password = box_super.find('#password input').val();
                let errors_area = box_super.find('.mc-errors-area');
                if (!email || !password || loading(this)) {
                     if(!email || !password) errors_area.html(mc_('Email and password are required.'));
                     return;
                }
                errors_area.html(''); // Clear previous errors

                $.ajax({
                    method: 'POST',
                    url: 'ajax.php',
                    data: {
                        function: 'super-login',
                        email: email,
                        password: password
                    },
                    dataType: 'json' // Expect JSON response from our modified ajax_response
                })
                // Inside $(box_super).on('click', '.btn-login', ...
// ... AJAX call setup ...
.done(function(response) {
    console.log('Super Login response:', response);

    // --- START FIX ---
    // Check if the response is an object with status 'success' and a message (the token)
    if (response && typeof response === 'object' && response.status === 'success' && typeof response.message === 'string' && response.message.length > 10) {
        // Extract the token from the message property
        let token = response.message;
        cookie('mc-super', token, 3650); // Save the token
        document.location = URL_NO_PARS + '?login=success'; // Redirect
    // --- END FIX ---

    } else if (response && typeof response === 'object' && response.status === 'error') {
        // Handle explicit error object from PHP/helper
        errors_area.html(response.message || 'Invalid email or password.');
    } else if (response === 'ip-ban') { // Handle specific string response
        errors_area.html('Too many login attempts. Please retry again in a few hours.');
    } else if (response === false) { // Handle explicit false response
        errors_area.html('Invalid email or password.');
    } else {
        // Handle other unexpected responses with user-friendly message
        errors_area.html('The email or password you entered is incorrect. Please check your credentials and try again, or use the "Forgot Password" link if you need to reset your password.');
    }
})
// ... .fail() and .always() parts remain the same ...
                .fail(function(jqXHR, textStatus, errorThrown) {
                     // More user-friendly error message with guidance
                     errors_area.html('We couldn\'t connect to the server. Please check your internet connection and try again. If the problem persists, please contact our support team for assistance.');
                })
                .always(function() {
                     // Make sure 'this' refers to the button if loading() was used, otherwise select directly
                     box_super.find('.btn-login').stopLoading(); // Ensure loading stops
                });
            });
             // --- End: Super Admin Login ---


            $(box_super).on('click', '.table-customers td', function (e) {
                e.preventDefault(); // Prevent default link behavior if any
                box_loading.mcActive(true);
                ajax('super-get-customer', { 'customer_id': $(this).parent().attr('data-customer-id') }, (response) => {
                     // Added: Check for error object from improved ajax helper
                    if (response && response.status === 'error') {
                        // More user-friendly error message with guidance
                        banner_error(response.message || 'We couldn\'t load the customer details. Please try again or contact support if the problem persists.');
                        box_loading.mcActive(false); // Hide loading on error
                        return;
                    }

                    let fields_editable = ['first_name', 'last_name', 'email', 'phone', 'password', 'credits'];
                    // Added: Ensure all expected keys exist in response, provide defaults
                    let fields_readonly = ['id', 'lifetime_value', 'token', 'creation_time', 'customer_id', 'database', 'count_users', 'count_agents', 'membership_expiration'];
                    let code = '';

                     // Ensure response is an object before proceeding
                     if (typeof response !== 'object' || response === null) {
                         // More user-friendly error message with guidance
                         banner_error('We couldn\'t load the customer information. Please refresh the page and try again. If the problem persists, please contact our support team for assistance.');
                         box_loading.mcActive(false);
                         return;
                     }


                    for (var i = 0; i < fields_editable.length; i++) {
                        let slug = fields_editable[i];
                         // Provide empty string default for missing fields, handle password specifically
                         let value = (slug === 'password') ? '' : (response[slug] || '');
                        code += `<div data-type="text" class="mc-input"><span>${slugToString(slug)}</span><input id="${slug}" type="${slug === 'password' ? 'password' : 'text'}" value="${value}" placeholder="${slug === 'password' ? 'Enter new password or leave blank' : ''}" ${slug !== 'phone' && slug !== 'password' ? 'required' : ''} /></div>`;
                    }

                    // Handle extra fields safely
                     if (response.extra_fields && Array.isArray(response.extra_fields)) {
                        for (var i = 0; i < response.extra_fields.length; i++) {
                            let item = response.extra_fields[i];
                             if (item && item.slug && !['payment', 'active_membership_cache', 'notifications_credits_count', 'marketing_email_30', 'marketing_email_7', 'email_limit'].includes(item.slug)) {
                                 // White label logic from original needs to be added if it was removed
                                 let isWhiteLabel = item.slug === 'white-label' || item.slug === 'white_label'; // Allow both slugs
                                 code += `<div data-type="${isWhiteLabel ? 'select' : 'text'}" class="mc-input"><span>${slugToString(item.slug)}</span>`;
                                 if (isWhiteLabel) {
                                     // Ensure value is set for the current option
                                     let currentValue = item.value || '';
                                     code += `<select id="white_label" data-extra="true">`;
                                     // Add current value as an option if it's not one of the standard actions
                                     if (currentValue && !['renew', 'disable', 'activate'].includes(currentValue)) {
                                        code += `<option value="${currentValue}" selected>${slugToString(currentValue)}</option>`; // Display current value nicely
                                     } else {
                                          code += `<option value="" ${currentValue === '' ? 'selected': ''}>Not Active</option>`; // Default empty option
                                     }
                                     code += `<option value="activate" ${currentValue === 'activate' ? 'selected': ''}>Activate</option>`;
                                     code += `<option value="renew" ${currentValue === 'renew' ? 'selected': ''}>Manual renewal</option>`;
                                     code += `<option value="disable" ${currentValue === 'disable' ? 'selected': ''}>Disable</option>`;
                                     code += `</select></div>`;
                                 } else {
                                     code += `<input id="${item.slug}" type="text" value="${item.value || ''}" data-extra="true" /></div>`;
                                 }
                            }
                        }
                         // Add White Label select if it wasn't in extra_fields
                         if (!lightbox_profile.find('#white_label').length && !code.includes('id="white_label"')) {
                            code += `<div data-type="select" class="mc-input"><span>White label</span><select id="white_label" data-extra="true"><option value="" selected>Not Active</option><option value="activate">Activate</option></select></div>`;
                         }
                    } else {
                         // Add default White Label if extra_fields are missing entirely
                         code += `<div data-type="select" class="mc-input"><span>White label</span><select id="white_label" data-extra="true"><option value="" selected>Not Active</option><option value="activate">Activate</option></select></div>`;
                    }


                    code += `<div data-type="text" class="mc-input"><span>Membership</span><select id="membership" required>`;
                    for (var i = 0; i < MEMBERSHIPS.length; i++) {
                        code += `<option value="${MEMBERSHIPS[i].id}"${MEMBERSHIPS[i].id == response.membership ? ' selected' : ''}>${MEMBERSHIPS[i].name}${MEMBERSHIPS[i].period ? (' | ' + slugToString(MEMBERSHIPS[i].period)) : ''}</option>`; // Use slugToString for period
                    }
                    code += '<option value="manual_membership_renewal">Manual membership renewal</option></select></div>';
                    lightbox_profile.find('.mc-edit-box').html(code);

                    code = ''; // Reset code for readonly section
                    for (var i = 0; i < fields_readonly.length; i++) {
                         let value = response[fields_readonly[i]] || ''; // Default to empty string
                         // Format date if it's creation_time or expiration
                         if (['creation_time', 'membership_expiration'].includes(fields_readonly[i]) && value) {
                              // Check if it's a timestamp (likely needs * 1000) or already a date string
                              let timestamp = parseInt(value);
                              if (!isNaN(timestamp) && timestamp > 1000000) { // Basic check for likely timestamp
                                   value = new Date(timestamp * 1000).toLocaleString();
                              } // Otherwise assume it's already formatted or not a date
                         }
                        code += `<div data-type="readonly" class="mc-input"><span>${slugToString(fields_readonly[i])}</span><input id="${fields_readonly[i]}" type="text" value="${value}" readonly /></div>`;
                    }
                    lightbox_profile.find('.mc-readonly-box').html(code);

                    code = ''; // Reset code for invoices
                     if (response.invoices && Array.isArray(response.invoices)) {
                        for (var i = 0; i < response.invoices.length; i++) {
                            code += get_invoice_string_payment_gateway(response.invoices[i]); // Use the helper
                        }
                    }
                    lightbox_profile.find('.mc-sales-box').html(code ? code : '<div>No payment data available</div>');

                    code = ''; // Reset code for volume
                     if (response.monthly_volume && Array.isArray(response.monthly_volume)) {
                        for (var i = 0; i < response.monthly_volume.length; i++) {
                             let volume_item = response.monthly_volume[i];
                            code += `<div>${volume_item.date || 'N/A'} | ${volume_item.count || 0} messages</div>`;
                        }
                    }
                    lightbox_profile.find('.mc-volume-box').html(code ? code : '<div>No volume data available</div>');
                    lightbox_profile.find('.mc-name').html(`${response.first_name || ''} ${response.last_name || ''}`);
                    lightbox_profile.find('.mc-delete-box input').val('');
                    lightbox_profile.attr('data-customer-id', response.id || ''); // Ensure ID is set
                    lightbox_profile.lightbox(); // Call lightbox function (defined later)
                });
                // Removed return false;
            });


            $(lightbox_profile).on('click', '.mc-save', function (e) {
                e.preventDefault(); // Moved up
                if (loading(this)) return;
                let details = {};
                let extra_details = {};
                let error = false;
                let info_box = $(lightbox_profile).find('.mc-info'); // Reference info box
                info_box.html('').mcActive(false); // Clear previous errors

                lightbox_profile.find('.mc-edit-box input:not([readonly]), .mc-edit-box select').each((idx, input) => { // Target only editable inputs/selects
                    let $input = $(input); // jQuery object
                    let value = $.trim($input.val());
                    let id = $input.attr('id');

                    if (id) { // Ensure input has an ID
                         // Check required fields (excluding password if blank)
                        if (!value && $input.attr('required') && id !== 'password') {
                            info_box.html(mc_('Please fill in all required fields.')).mcActive(true);
                            $input.addClass('mc-error'); // Highlight the specific field
                            error = true;
                        } else {
                             $input.removeClass('mc-error'); // Remove error class if valid
                        }

                         // Password validation (only if a value is entered)
                         if (id === 'password' && value && value.length < 8) {
                            info_box.html(messages.password_length).mcActive(true);
                            $input.addClass('mc-error');
                            error = true;
                         }

                        // Don't validate membership_expiration here, handle in PHP if needed for specific gateways
                        // if (!free && id == 'membership_expiration') { ... }

                        if ($input.attr('data-extra')) {
                            extra_details[id] = value;
                        } else {
                            details[id] = value;
                        }
                    }
                });

                if (error) {
                    $(this).stopLoading();
                    return; // Stop if client-side validation failed
                }

                let customer_id = $(this).closest('[data-customer-id]').attr('data-customer-id');
                ajax('super-save-customer', { 'customer_id': customer_id, 'details': details, 'extra_details': extra_details }, (response) => {
                     // Added: Check for error object from improved ajax helper
                    if (response && response.status === 'error') {
                        console.error("Error saving customer:", response.message);
                        info_box.html(response.message || 'We couldn\'t save the customer details. Please check all fields and try again.').mcActive(true);
                        $(this).stopLoading();
                        return;
                    }
                    // Handle specific string errors returned from PHP
                    if (response === 'duplicate-phone-or-email') {
                         info_box.html('This email or phone number is already in use. Please use a different email or phone number.').mcActive(true);
                         $(this).stopLoading();
                         return;
                    }
                     if (response !== true && response !== 1 && response !== 'true') { // Check for explicit non-success if needed
                         console.warn("Unexpected success response from super-save-customer:", response);
                         // Optionally show a generic error or warning
                     }

                    // Update table row on success
                    let row = box_super.find(`.table-customers [data-customer-id="${customer_id}"]`);
                    if (row.length) { // Ensure row exists
                        let keys = ['name', 'email', 'phone', 'membership'];
                        details.name = details.first_name + ' ' + details.last_name;
                         // Update membership name in the details object for display
                         let selectedMembership = MEMBERSHIPS.find(m => m.id == details.membership);
                         details.membership = selectedMembership ? selectedMembership.name : details.membership; // Fallback to ID if not found

                        for (var i = 0; i < keys.length; i++) {
                            row.find(`[data-id="${keys[i]}"]`).html(details[keys[i]] || ''); // Update cell, default empty
                        }
                    }
                    banner_success('Settings saved.'); // Show success banner
                    body.find('.mc-lightbox,.mc-lightbox-overlay').mcActive(false); // Close lightbox
                    $(this).stopLoading();
                });
                // Removed return false;
            });

            $(lightbox_profile).on('click', '.mc-delete-box .mc-btn-text', function () {
                if ($(this).parent().find('input').val().toUpperCase() === 'DELETE') {
                    let customer_id = $(this).closest('[data-customer-id]').attr('data-customer-id');
                    ajax('super-delete-customer', { 'customer_id': customer_id }, (response) => { // Added response handler
                        // Added: Check for error object from improved ajax helper
                         if (response && response.status === 'error') {
                             console.error("Error deleting customer:", response.message);
                             lightbox_profile.lightboxError(response.message || 'Failed to delete customer.'); // Show error in lightbox
                             return;
                         }
                         // Assume success otherwise
                        box_super.find(`.table-customers [data-customer-id="${customer_id}"]`).remove();
                        body.find('.mc-lightbox,.mc-lightbox-overlay').mcActive(false);
                    });
                } else {
                     // Add feedback if input is wrong
                     $(this).parent().find('input').addClass('mc-error');
                     lightbox_profile.lightboxError('Please type DELETE to confirm.');
                }
            });

            $(box_super).on('click', '#save-emails, #save-settings', function (e) {
                e.preventDefault(); // Moved up
                if (loading(this)) return;
                let settings = {};
                let email = $(this).attr('id') == 'save-emails';
                box_super.find(email ? '#tab-emails' : '#tab-settings').find(' .mc-setting textarea,.mc-setting input,.mc-setting select').each((e, input) => {
                    input = $(input);
                    settings[input.attr('id')] = input.is(':checkbox') ? input.is(':checked') : $.trim(input.val());
                });
                ajax(email ? 'super-save-emails' : 'super-save-settings', { settings: settings }, (response) => {
                        // --- START FIX ---
    // Check for specific success object structure OR standard true values
    let isSuccess = is_true(response) || (response && typeof response === 'object' && response.status === 'success');

    if (isSuccess) {
        banner_success('Settings saved successfully.');
    } else if (response && response.status === 'error') {
        // Handle explicit error object with user-friendly message
        banner_error(response.message || `We couldn't save your ${email ? 'emails' : 'settings'}. Please try again or contact support if the problem persists.`);
    } else {
        // Handle other unexpected non-success responses with user-friendly message
        banner_error(`We couldn't save your ${email ? 'emails' : 'settings'}. Please try again or contact support if the problem persists.`);
    }
    // --- END FIX ---
    $(this).stopLoading();
});
                // Removed return false;
            });

            // --- Start: Super Admin Tab Loader (User's Version with HTML handling) ---
            $(function() { // Ensure DOM is ready
    var body = $('body');

    // --- Final Navigation Click Handler ---
    body.on('click', '.mc-nav li[id^="nav-"]', function (e) {
        e.preventDefault();

        let $navItem = $(this);
        let id = $navItem.attr('id');
        let areaId = id.replace('nav', 'tab');
        let $area = $('#' + areaId); // The content area div for the clicked tab

        console.log(`Nav click: ${id}, Target area: #${areaId}`);

        // --- 1. Handle Active States ---
        if (!$navItem.hasClass('mc-active')) {
            console.log('Switching active tab...');
            $('.mc-nav li').removeClass('mc-active');
            $('.mc-content > div').removeClass('mc-active');
            $navItem.addClass('mc-active');
            $area.addClass('mc-active');
        } else {
            console.log('Clicked already active tab.');
        }

        // --- 2. Determine Content Type and Load if Necessary ---
        let email = (id === 'nav-emails');
        let settings = (id === 'nav-settings');
        let membership = (id === 'nav-membership-plans');
        let customers = (id === 'nav-customers'); // Added for completeness
        let affiliates = (id === 'nav-affiliates'); // Added for completeness

        // --- Load Membership Plans Content (HTML, Load Once) ---
        if (membership) {
            console.log('Membership tab selected.');
            let $container = $area.find('#membership-plans-container');

            if ($container.length === 0) {
                console.error('CRITICAL: #membership-plans-container not found!');
                $area.html('<p style="color:red;">Error: Container div missing in HTML.</p>');
                return;
            }

            // Load HTML content only if marker class is NOT present
            if (!$container.hasClass('content-loaded')) {
                console.log('#membership-plans-container needs loading...');
                $area.addClass('mc-loading');

                $.ajax({ /* ... AJAX call for super-membership-plans as before ... */
                    method: 'POST',
                    url: 'ajax.php',
                    data: { function: 'super-membership-plans' },
                    dataType: 'html',
                    timeout: 15000
                })
                .done(function(response_html) {
                    console.log('AJAX success for super-membership-plans.');
                    if (response_html && response_html.trim().startsWith('<')) {
                        $container.html(response_html);
                        $container.addClass('content-loaded'); // MARK AS LOADED
                        console.log('Membership content loaded and marked.');

                        // Re-apply period selections
                         var paystack = (typeof PAYMENT_PROVIDER !== 'undefined' && PAYMENT_PROVIDER === 'paystack');
                         var stripe = (typeof PAYMENT_PROVIDER !== 'undefined' && PAYMENT_PROVIDER === 'stripe');
                         var razorpay = (typeof PAYMENT_PROVIDER !== 'undefined' && PAYMENT_PROVIDER === 'razorpay');
                         if (!stripe && !razorpay && !paystack) {
                             $container.find('.mc-input [data-period]').each(function () {
                                 let $parentDiv = $(this).closest('[data-period]');
                                 let periodValue = $parentDiv.attr('data-period');
                                 $(this).find('select.period').val(periodValue);
                             });
                         }
                    } else { /* ... error handling ... */
                        console.error("Received invalid HTML for membership plans:", response_html);
                        $container.html('<div class="mc-msg mc-error"><h2>Loading Error</h2><p>Invalid HTML received.</p></div>');
                    }
                })
                .fail(function(jqXHR, textStatus, errorThrown) { /* ... error handling ... */
                    // More user-friendly error message with guidance
                    $container.html(`<div class="mc-msg mc-error"><h2>Connection Error</h2><p>We couldn't load the membership plans. Please check your internet connection and try again. If the problem persists, please contact our support team for assistance.</p></div>`);
                })
                .always(function() {
                    $area.removeClass('mc-loading');
                    console.log('Membership AJAX complete.');
                });
            } else {
                 console.log('#membership-plans-container already loaded. Skipping AJAX.');
            }

        // --- Load Emails or Settings Data (JSON, Load Once) ---
        } else if (email || settings) {
            console.log(`${email ? 'Emails' : 'Settings'} tab selected.`);

            // Load data ONLY if marker class is NOT present on the main tab area ($area)
            if (!$area.hasClass('data-loaded')) {
                 console.log(`#${areaId} needs data loading...`);
                 $area.addClass('mc-loading'); // Show loading on the whole tab

                 // Use the custom 'ajax' helper
                 ajax(email ? 'super-get-emails' : 'super-get-settings', {}, (response) => {

                    if (!response || typeof response !== 'object' || response.status === 'error') {
                        // Log minimal information for debugging
                        console.log(`Could not load ${email ? 'emails' : 'settings'}`);
                        // Display user-friendly error message
                        $area.prepend(`<div class="mc-msg mc-error mc-transient"><h2>Loading Error</h2><p>We couldn't load the ${email ? 'email' : 'settings'} information. Please refresh the page and try again. If the problem persists, please contact our support team.</p></div>`); // Prepend error
                        // Do NOT mark as loaded if error occurs
                    } else {
                        // Successfully received data - Populate existing fields
                        for (var key in response) {
                            if (response.hasOwnProperty(key)) {
                                let input = $area.find('#' + key);
                                if (input.length) {
                                    if (input.is(':checkbox')) {
                                        input.prop('checked', response[key] !== false && response[key] !== 'false' && response[key] !== 0 && response[key] !== '0');
                                    } else {
                                        input.val(response[key]);
                                    }
                                }
                            }
                        }
                         $area.addClass('data-loaded'); // *** MARK AS LOADED ***
                         console.log(`${email ? 'Emails' : 'Settings'} data loaded and marked.`);
                         // Remove any transient error messages if load succeeds now
                          $area.find('.mc-msg.mc-transient').remove();
                    }
                     // Remove loading indicator AFTER processing
                     $area.removeClass('mc-loading');
                     console.log(`${email ? 'Emails' : 'Settings'} AJAX complete.`);

                }); // End custom ajax callback
            } else {
                 console.log(`#${areaId} already marked as data-loaded. Skipping AJAX.`);
                 // Ensure loading spinner isn't stuck if we skipped AJAX
                 $area.removeClass('mc-loading');
            }

        // --- Placeholder for Customers/Affiliates Tabs ---
        } else if (customers || affiliates) {
            console.log(`Tab ${id} selected. Assuming specific JS (e.g., MCF.admin.get) handles data loading.`);
            // Add specific load-once logic here if needed, similar to above
            // Example: Check if table body is empty or has a marker class
            // if ($area.find('table tbody').is(':empty') && !$area.hasClass('data-loaded')) {
            //    $area.addClass('data-loaded');
            //    // Trigger the specific function like MCF.admin.get(...)
            // }
        }

    }); // End click handler

}); // End document ready
            // --- End: Super Admin Tab Loader ---

            $(box_super).on('click', '#save-membership-plans', function () {
                 if (loading(this)) return;
                 let button = $(this);
                 let plans = [];
                 let is_error = false;
                 let error_message = ''; // Store specific error message

                 box_super.find('#membership-plans > div').each(function () {
                     let $planDiv = $(this);
                     let item = {
                         id: $planDiv.attr('data-id'),
                         // Added: Paystack check for price/currency/period retrieval
                         price: stripe || razorpay || paystack ? $planDiv.attr('data-price') : $planDiv.find('.price').val(),
                         currency: stripe || razorpay || paystack ? $planDiv.attr('data-currency') : CURRENCY,
                         period: stripe || razorpay || paystack ? $planDiv.attr('data-period') : $planDiv.find('.period').val(),
                         name: $planDiv.find('.name').val().trim(),
                         quota: $planDiv.find('.quota').val().trim()
                     };

                     // Default currency if needed (should ideally come from data-currency or constant)
                     if (!item.currency) item.currency = 'usd';

                     if (membership_type_ma) {
                         item['quota_agents'] = $planDiv.find('.quota-agents').val().trim();
                     }

                     // Validation
                     // Inside the .each loop for plans:
                        let planName = item.name || `Plan ID ${item.id}`;

                        if (!item.name) { error_message = `Plan name is required for ${planName}.`; is_error = true; return false; }

                        // VALIDATION MODIFIED: Only check if quota is numeric, allow negative/zero
                        if (item.quota === '' || !$.isNumeric(item.quota)) {
                            error_message = `Quota must be a valid number (can be 0 or negative) for ${planName}.`; is_error = true; return false;
                        }

                        // VALIDATION MODIFIED: Only check if agent quota is numeric, allow negative/zero
                        if (membership_type_ma && (item.quota_agents === '' || !$.isNumeric(item.quota_agents))) {
                            error_message = `Agent Quota must be a valid number (can be 0 or negative) for ${planName}.`; is_error = true; return false;
                        }

                        if (item.id !== 'free') { // Paid plans still require price and period if quota > 0 (or maybe always?)
                            // Decide if price/period are needed even for hidden plans (quota <= 0)
                            // If YES, keep these checks. If NO, wrap them in `if (parseInt(item.quota) > 0)`
                            if (!item.price || !$.isNumeric(item.price) || parseFloat(item.price) < 0) {
                                error_message = `Price must be a non-negative number for ${planName}.`; is_error = true; return false;
                            }
                            if (!item.period) {
                                 error_message = `Period (monthly/yearly) is required for ${planName}.`; is_error = true; return false;
                            }
                        }
                        // End of modified validation}

                     plans.push(item);
                 });

                 if (is_error) {
                     banner_error(error_message || 'Error in plan configuration. Please check all fields.');
                     button.stopLoading();
                     return; // Stop if validation failed
                 }

                 if (window.confirm('Are you sure to update the membership plans? The changes will be live instantaneously.')) {
                    ajax('super-save-membership-plans', { 'plans': plans }, (response) => {
        button.stopLoading(); // Stop loading regardless of outcome

        // --- START REVISED FIX ---
        // Check for explicit error object FIRST
        if (response && typeof response === 'object' && response.status === 'error') {
            console.error("Error saving membership plans:", response.message);
            banner_error(response.message || 'Failed to save membership plans.');
        }
        // Check for explicit success object SECOND
        else if (response && typeof response === 'object' && response.status === 'success') {
             banner_success('Membership plans saved successfully.');
        }
        // Check for standard true values THIRD
        else if (is_true(response)) {
            banner_success('Membership plans saved successfully.');
        }
        // If none of the above, treat as an unexpected response/error
        else {
            let errorMsg = (typeof response === 'object') ? JSON.stringify(response) : String(response);
            console.warn("Unexpected response saving membership plans:", response);
            banner_error('Failed to save membership plans. Unexpected response: ' + errorMsg);
        }
        // --- END REVISED FIX ---
                         button.stopLoading();
                     });
                 } else {
                     button.stopLoading(); // Stop loading if user cancels confirm dialog
                 }
             });

            $(box_super).on('click', '#save-white-label', function () {
                if (loading(this)) return;
                ajax('super-save-white-label', { 'price': box_super.find('.super-white-label input').val() }, (response) => {
                     // Added: Check for error object from improved ajax helper
                     if (response && response.status === 'error') {
                        console.error("Error saving white label:", response.message);
                        banner_error(response.message || 'Failed to save white label price.');
                     } else if (is_true(response)) {
                        banner_success('White label price saved successfully.');
                     } else {
                        banner_error('Error saving white label price: ' + response);
                     }
                    $(this).stopLoading();
                });
            });

            $(box_super).on('click', '#logout', function () {
                cookie('mc-super', '', 0);
                document.location = URL_NO_PARS + '?logout=true';
            });

            $(box_super).on('click', '#membership-plans > div > i.mc-icon-close', function () { // Make selector more specific
                 if (confirm('Are you sure you want to remove this membership plan?')) {
                     $(this).parent().remove();
                 }
            });

            $(box_super).on('click', '#add-membership', function () {
                let newPlanId = random(); // Generate random ID
                let newPlanHtml = `
                    <div data-id="${newPlanId}" data-price="" data-period="" data-currency="${CURRENCY || 'usd'}">
                        <i class="mc-icon-close"></i> <!-- Close icon at top -->
                        <div class="mc-input">
                             <h5>ID</h5>
                             <input type="text" value="${newPlanId}" readonly style="background:#eee;"> <!-- Display ID -->
                            <h5>Name</h5>
                            <input class="name" type="text" value="" placeholder="Insert plan name..." required>
                            <h5>Quota (Messages/Month)</h5>
                            <input type="number" class="quota" placeholder="0" value="" required min="0">
                            ${membership_type_ma ? '<h5>Quota (Agents)</h5><input type="number" class="quota-agents" placeholder="0" value="" required min="0">' : ''}
                            <h5>Price</h5>
                            <input type="number" class="price" placeholder="0.00" value="" required min="0" step="0.01">
                             <h5>Currency</h5>
                             <input type="text" class="currency" value="${CURRENCY || 'usd'}" readonly style="background:#eee;"> <!-- Display Currency -->
                            <h5>Period</h5>
                            <select class="period" required>
                                 <option value="" disabled selected>Select period...</option> <!-- Added default option -->
                                <option value="month">Monthly</option>
                                <option value="year">Yearly</option>
                            </select>
                        </div>
                    </div>`;
                 // Append and potentially scroll into view
                 let $newPlan = $(newPlanHtml);
                 $(box_super).find('#membership-plans').append($newPlan);
                 $newPlan[0].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
             });

            $(box_super).on('click', '#nav-affiliates', function () {
                let area = $(body).find('#tab-affiliates');
                if (area.isLoading()) {
                    ajax('super-get-affiliates', {}, (response) => {
                        // Added: Check for error object from improved ajax helper
                        if (response && response.status === 'error') {
                            console.error("Error getting affiliates:", response.message);
                            area.html(`<p>${mc_('Error loading affiliate list.')}</p>`).stopLoading();
                            return;
                        }
                        let code = '';
                        if (Array.isArray(response)) { // Check if response is array
                            for (var i = 0; i < response.length; i++) {
                                let row = response[i];
                                code += `<tr><td>${row.id || 'N/A'}</td><td>${row.first_name || ''} ${row.last_name || ''}</td ><td>${row.email || ''}</td><td>${(CURRENCY || '').toUpperCase()} <span>${row.value || 0}</span></td><td><div class="mc-btn mc-btn-payment-details" data-id="${row.id}">Details</div></td><td><div class="mc-btn mc-btn-reset-affiliate" data-id="${row.id}">Reset to zero</div></td></tr>`; // Added class mc-btn-reset-affiliate
                            }
                         }
                        area.find('tbody').html(code || `<tr><td colspan="6">${mc_('No affiliates found.')}</td></tr>`); // Add message if no affiliates
                        area.stopLoading();
                    });
                }
            });

            $(box_super).on('click', '.table-affiliates .mc-btn', function () {
                let affiliate_id = $(this).attr('data-id');
                if (!affiliate_id) return; // Stop if no ID

                let attributes = { affiliate_id: affiliate_id };
                if ($(this).hasClass('mc-btn-payment-details')) {
                    box_loading.mcActive(true);
                    ajax('super-get-affiliate-details', attributes, (response) => {
                        // Added: Check for error object from improved ajax helper
                         if (response && response.status === 'error') {
                             console.error("Error getting affiliate details:", response.message);
                             banner_error(response.message || 'Failed to load affiliate details.');
                             box_loading.mcActive(false);
                             return;
                         }
                        let panel = body.find('.mc-generic-box'); // Assuming a generic lightbox exists
                         if (panel.length === 0) { // Create if doesn't exist
                              $('body').append('<div class="mc-lightbox mc-generic-box"><div class="mc-info"></div><div class="mc-top-bar"><div></div><i class="mc-close mc-icon-close"></i></div><div class="mc-main mc-scroll-area"></div></div>');
                              panel = body.find('.mc-generic-box');
                         }
                        panel.find('.mc-top-bar > div:first-child').html('Payment details of ' + $(this).closest('tr').find('td').eq(1).html());
                        let detailsHtml = 'The user has not provided the payment details yet.';
                         if (Array.isArray(response) && response.length >= 2 && response[0]) {
                             detailsHtml = `<b>Payment method</b><br>${response[0] ? response[0].toUpperCase() : ''}<br><br><b>Payment details</b><br>${response[1] ? response[1].replace(/\n/g, '<br>') : ''}`;
                         }
                        panel.find('.mc-main').html(detailsHtml);
                        panel.lightbox(); // Requires lightbox function to be defined
                    });
                } else if ($(this).hasClass('mc-btn-reset-affiliate')) { // Use specific class
                    if (confirm('Are you sure you want to reset this affiliate\'s balance to zero?')) {
                        ajax('super-reset-affiliate', attributes, (response) => { // Added response handler
                             // Added: Check for error object from improved ajax helper
                             if (response && response.status === 'error') {
                                 console.error("Error resetting affiliate:", response.message);
                                 banner_error(response.message || 'Failed to reset affiliate balance.');
                                 return;
                             }
                             // Assume success otherwise
                            $(this).closest('tr').find('td').eq(3).find('span').text('0'); // Update balance in table
                             banner_success('Affiliate balance reset successfully.');
                        });
                    }
                }
            });


            $(box_super).on('click', '#mc-get-emails', function () {
                let code = '';
                 // Ensure table exists before iterating
                 let customerTable = box_super.find('.table-customers');
                 if (customerTable.length) {
                     customerTable.find('tbody [data-customer-id]').each(function () { // Target tbody rows
                         let membershipCell = $(this).find('[data-id="membership"]');
                         let emailCell = $(this).find('[data-id="email"]');
                         // Check if cells exist and membership is not 'Free'
                         if (membershipCell.length && emailCell.length && membershipCell.html().trim().toLowerCase() !== 'free') {
                             code += emailCell.html().trim() + ', ';
                         }
                     });
                 }
                 // Ensure response area exists
                 let responseArea = $('#mc-response-area');
                 if (responseArea.length === 0) {
                     // Create the area if it doesn't exist, perhaps after the button
                     $(this).after('<div id="mc-response-area" style="margin-top: 10px; word-break: break-all;"></div>');
                     responseArea = $('#mc-response-area');
                 }
                 responseArea.html(code ? code.substring(0, code.length - 2) : 'No paying customer emails found.'); // Remove trailing comma and space or show message
            });

             // Removed mc-btn-update-saas handler as it wasn't in the user's provided file.
             // If it was intended to be kept, it needs to be added back from the original file.

        } // End Super Admin section
    }); // End document ready

    // --- Enhanced AJAX Helper with Improved Error Handling ---
    function ajax(function_name, data = {}, onSuccess = false) {
        // Add the function name to the data
        $.extend(data, { function: function_name });

        // Track the request for debugging
        console.log(`AJAX Request: ${function_name}`);

        // Set a timeout for the request
        let ajaxTimeout = 30000; // 30 seconds

        // Make the AJAX request
        $.ajax({
            method: 'POST',
            url: 'ajax.php',
            data: data,
            timeout: ajaxTimeout,
            // We don't explicitly set dataType here, letting jQuery decide based on Content-Type
            beforeSend: function(xhr) {
                // Add a custom header for tracking
                xhr.setRequestHeader('X-Request-Function', function_name);
            }
        }).done((response, textStatus, jqXHR) => {
            // Check if we have a callback function
            if (onSuccess) {
                let processedResponse;

                // Handle different response types
                if (response === null || response === undefined) {
                    // Handle null or undefined response
                    processedResponse = {
                        status: 'error',
                        message: 'The server returned an empty response. Please try again later.',
                        error_type: 'empty-response'
                    };
                } else if (response === false) {
                    // Handle explicit false response (often means authentication failure)
                    processedResponse = {
                        status: 'error',
                        message: 'Authentication failed. Please check your credentials and try again.',
                        error_type: 'auth-error'
                    };
                } else if (response === 'ip-ban') {
                    // Handle IP ban response
                    processedResponse = {
                        status: 'error',
                        message: 'For security reasons, your account has been temporarily locked due to too many failed attempts. Please try again later or contact support.',
                        error_type: 'ip-ban'
                    };
                } else if (typeof response === 'string') {
                    // Check if the string is a JSON string
                    if (response.trim().startsWith('{') || response.trim().startsWith('[')) {
                        try {
                            // Try to parse the JSON string
                            processedResponse = JSON.parse(response);
                        } catch (e) {
                            // JSON parsing failed
                            console.warn('JSON parsing failed for response:', response.substring(0, 100) + (response.length > 100 ? '...' : ''));

                            // Check for common error patterns in the string
                            if (response.includes('500') || response.includes('Internal Server Error')) {
                                processedResponse = {
                                    status: 'error',
                                    message: 'The server encountered an internal error. Please try again later or contact support if the problem persists.',
                                    error_type: 'server-error'
                                };
                            } else if (response.includes('404') || response.includes('Not Found')) {
                                processedResponse = {
                                    status: 'error',
                                    message: 'The requested resource was not found. Please check your request and try again.',
                                    error_type: 'not-found'
                                };
                            } else if (response.includes('timeout') || response.includes('timed out')) {
                                processedResponse = {
                                    status: 'error',
                                    message: 'The request timed out. Please try again later.',
                                    error_type: 'timeout'
                                };
                            } else {
                                // Generic error for other cases
                                processedResponse = {
                                    status: 'error',
                                    message: 'We encountered an issue processing the server response. Please try again or contact support if the problem persists.',
                                    error_type: 'parse-error'
                                };
                            }
                        }
                    } else {
                        // Handle non-JSON strings
                        // Check for common error strings
                        if (response.includes('error') || response.includes('failed') || response.includes('invalid')) {
                            processedResponse = {
                                status: 'error',
                                message: response,
                                error_type: 'string-error'
                            };
                        } else {
                            // Use the string as-is for success responses
                            processedResponse = response;
                        }
                    }
                } else if (typeof response === 'object') {
                    // Use the object as-is
                    processedResponse = response;

                    // Add error_type if it's an error but doesn't have error_type
                    if (processedResponse.status === 'error' && !processedResponse.error_type) {
                        processedResponse.error_type = 'api-error';
                    }
                } else {
                    // For other types (like numbers), convert to string
                    processedResponse = response.toString();
                }

                // Log errors for debugging
                if (processedResponse && processedResponse.status === 'error') {
                    console.warn(`AJAX Error (${function_name}):`, processedResponse.error_type || 'unknown', processedResponse.message);
                }

                // Call the success callback with the processed response
                onSuccess(processedResponse);
            }
        }).fail((jqXHR, textStatus, errorThrown) => {
            // Handle AJAX failures
            console.warn(`AJAX Request Failed (${function_name}):`, textStatus, errorThrown);

            let errorMessage = 'We couldn\'t connect to the server. Please check your internet connection and try again.';
            let errorType = 'connection-error';

            // Provide more specific error messages based on the error type
            if (textStatus === 'timeout') {
                errorMessage = 'The request timed out. The server might be experiencing high load. Please try again later.';
                errorType = 'timeout-error';
            } else if (textStatus === 'parsererror') {
                errorMessage = 'We encountered an issue processing the server response. Please try again or contact support if the problem persists.';
                errorType = 'parse-error';
            } else if (textStatus === 'abort') {
                errorMessage = 'The request was aborted. Please try again.';
                errorType = 'abort-error';
            } else if (jqXHR.status === 404) {
                errorMessage = 'The requested resource was not found. Please check your request and try again.';
                errorType = 'not-found';
            } else if (jqXHR.status === 403) {
                errorMessage = 'You don\'t have permission to access this resource. Please log in again or contact support.';
                errorType = 'permission-error';
            } else if (jqXHR.status === 500) {
                errorMessage = 'The server encountered an internal error. Please try again later or contact support if the problem persists.';
                errorType = 'server-error';
            } else if (jqXHR.status === 0 && errorThrown === '') {
                errorMessage = 'We couldn\'t connect to the server. Please check your internet connection and try again.';
                errorType = 'network-error';
            }

            // Call the success callback with the error object
            if (onSuccess) {
                onSuccess({
                    status: 'error',
                    message: errorMessage,
                    error_type: errorType,
                    http_status: jqXHR.status
                });
            }
        });
    }
    // --- End: Enhanced AJAX Helper ---


    function cookie(name, value, expiration_days) {
        let date = new Date();
        let expires = "";
        if (expiration_days) {
             // Handle expiration in days
             date.setTime(date.getTime() + (expiration_days * 24 * 60 * 60 * 1000));
             expires = "; expires=" + date.toUTCString();
        } else if (expiration_days === 0) {
             // Handle immediate expiration
             expires = "; expires=Thu, 01 Jan 1970 00:00:01 GMT";
        }
        // Always set path, SameSite, and Secure for modern browsers
        document.cookie = name + "=" + (value || "") + expires + "; path=/; SameSite=None; Secure";
    }


    function setLogin(cloud, mc) {
        cookie('mc-cloud', cloud, 3650);
        cookie('mc-login', mc, 3650);
    }

    function loading(element) {
        let $element = $(element); // Ensure it's a jQuery object
        if ($element.hasClass('mc-loading')) {
            return true;
        } else {
            $element.addClass('mc-loading');
            // Optionally disable the element while loading
            // $element.prop('disabled', true);
        }
        return false;
    }

    // Added stopLoading counterpart to re-enable element
    $.fn.stopLoading = function () {
        $(this).removeClass('mc-loading');
         // Optionally re-enable
         // $(this).prop('disabled', false);
        return this;
    }

    $.fn.startLoading = function () {
         $(this).addClass('mc-loading');
          // Optionally disable
         // $(this).prop('disabled', true);
         return this;
     }

     $.fn.isLoading = function () {
         return $(this).hasClass('mc-loading');
     }

    function banner(title, message, code = '', image = false, error = false, success = false) {
        // Use a more specific container if possible, fallback to body
        let container = $('.mc-tab > .mc-content > .mc-active');
        if (!container.length) {
            // Try finding the active tab in super admin layout
             container = box_super.find('.mc-admin-tab.mc-active');
             if (!container.length) {
                 container = body.find('.mc-tab > .mc-content').first(); // Fallback further
                  if (!container.length) container = body; // Last resort
             }
        }
         // Ensure title is a string for slug generation
         let safeTitle = String(title || 'banner-' + random()); // Use random if no title
        let id = 'banner-' + stringToSlug(safeTitle); // Generate ID based on title or random

        body.find(`#${id}`).remove(); // Remove existing banner with same ID globally

         // Prepend the banner to the identified container
        container.prepend(
            `<div id="${id}" class="banner${image ? ' banner-img' : ''}${error ? ' banner-error' : ''}${success ? ' banner-success' : ''}">
                ${image ? `<img src="${image}" alt="Notification Image"/>` : ''}
                <h2>${mc_(title)}</h2>
                <p>${mc_(message)}</p>
                <div>${code}</div>
                <i class="mc-btn-icon mc-icon mc-icon-close"></i>
            </div>`
        );
        scrollTop(container); // Scroll the specific container
    }

    function banner_success(message) {
        banner('Success', message, '', false, false, true); // Provide default title
    }

    function banner_error(message) {
        // Make error messages more user-friendly
        if (message.includes('Error') || message.includes('error') || message.includes('failed') || message.includes('invalid')) {
            // Replace with a more user-friendly message
            message = 'We encountered an issue while processing your request. Please try again or contact support if the problem persists.';
        } else if (message.includes('duplicate-email')) {
            message = 'This email address is already registered. Please use a different email address or try to log in if you already have an account.';
        } else if (message.includes('duplicate-phone')) {
            message = 'This phone number is already registered. Please use a different phone number or try to log in if you already have an account.';
        } else if (message.includes('invalid-password')) {
            message = 'The password you entered is incorrect. Please try again or use the "Forgot Password" link if you need to reset your password.';
        } else if (message.includes('invalid-login')) {
            message = 'The email or password you entered is incorrect. Please check your credentials and try again.';
        } else if (message.includes('ip-ban')) {
            message = 'For security reasons, your account has been temporarily locked due to too many failed login attempts. Please try again later.';
        }
        banner('Error', message, '', false, true, false); // Provide default title
    }


    function banners(type) {
        // Ensure membership object exists before accessing properties
        if (typeof membership === 'undefined' || membership === null) {
             console.warn('Membership data not available for banner check:', type);
             return;
        }

        switch (type) {
            case 'suspended':
                // Safely check if membership has the required properties
                let hasQuotaAgents = membership_type_ma && membership && typeof membership === 'object' && 'quota_agents' in membership;
                let hasCountAgents = membership && typeof membership === 'object' && 'count_agents' in membership;
                let hasQuota = membership && typeof membership === 'object' && 'quota' in membership;
                let hasCount = membership && typeof membership === 'object' && 'count' in membership;

                // Only check quota exceeded if all required properties exist
                let agents_quota_exceeded = hasQuotaAgents && hasCountAgents && membership.count_agents > membership.quota_agents;
                let quota_exceeded = hasQuota && hasCount && membership.count > membership.quota;

                // Check count > quota OR expired OR agents quota exceeded
                if (quota_exceeded || (membership && membership.expired) || agents_quota_exceeded) {
                    let title = (typeof SETTINGS !== 'undefined' && SETTINGS && SETTINGS.text_suspended_title) || 'Your account needs attention';
                    let baseMessage = (typeof SETTINGS !== 'undefined' && SETTINGS && SETTINGS.text_suspended) || mc_('Your website visitors can still use the chat, but you cannot view messages or reply to visitors because your account requires attention. Please renew your subscription below or upgrade to a higher plan to restore full access to your account.');
                    let agentMessage = agents_quota_exceeded ? ' ' + mc_('You have exceeded your agent limit. You can manage your team members and restore your account by clicking {R}.').replace('{R}', '<a href="#" id="delete-agents-quota">' + mc_('here') + '</a>') : ''; // Use # href for JS handling
                    banner(title, baseMessage + agentMessage, '', false, true, false); // Suspended is an error state
                }
                break;
            case 'verify':
                // Check if box_account exists before trying to find elements within it
                if (!box_account || !box_account.length) {
                    // Silent failure - no need to show error to user
                    return;
                }

                let verify_email = box_account.find('.btn-verify-email').length;
                let verify_phone = box_account.find('.btn-verify-phone').length;
                let text = verify_email && verify_phone ? 'email and phone number' : (verify_email ? 'email' : 'phone number');
                if ((verify_email || verify_phone) && !URL.includes('welcome')) {
                    banner(`Verify your ${text}`, `Please verify your ${text} from the profile area.`, '', false, true);
                }
                break;
        }
    }

    function is_true(value) {
        return value === true || value == 1 || String(value).toLowerCase() === 'true';
    }

    function mc_(text) {
        // Ensure MC_TRANSLATIONS exists and is an object
        return (typeof MC_TRANSLATIONS !== 'undefined' && MC_TRANSLATIONS !== null && typeof MC_TRANSLATIONS === 'object' && text in MC_TRANSLATIONS) ? MC_TRANSLATIONS[text] : text;
    }

    function get_membership(id) {
         // Ensure MEMBERSHIPS exists and is an array
         if (typeof MEMBERSHIPS === 'undefined' || !Array.isArray(MEMBERSHIPS)) {
             console.error("MEMBERSHIPS data is missing or invalid.");
             return { id: id || 'unknown', name: 'Unknown Plan' }; // Return a default object
         }
        for (var i = 0; i < MEMBERSHIPS.length; i++) {
            if (MEMBERSHIPS[i].id == id) return MEMBERSHIPS[i];
        }
        // Return the 'free' plan or the first one as a fallback if ID not found
        return MEMBERSHIPS.find(m => m.id === 'free') || MEMBERSHIPS[0] || { id: id || 'unknown', name: 'Unknown Plan' };
    }

    function get_invoice_string(item) {
        try {
            let values = JSON.parse(item.value);
            // Basic validation of parsed values
             if (!Array.isArray(values) || values.length < 6) {
                 console.error("Invalid invoice data structure:", item.value);
                 return `<div class="mc-invoice-row" data-id="${item.id || ''}"><span>Error loading invoice</span></div>`;
             }
             let dateString = 'Invalid Date';
             // Ensure timestamp is valid before creating Date
             let timestamp = parseInt(values[5]);
             if (!isNaN(timestamp)) {
                 dateString = new Date(timestamp * 1000).toLocaleString();
             }
             // Provide defaults for currency and amount
             let currency = CLOUD_CURRENCY || '';
             let amount = values[0] || 0;
             let planName = values[1] ? slugToString(values[1]) : 'Unknown Plan'; // Handle missing plan name

            return `<div class="mc-invoice-row" data-id="${item.id || ''}"><span>INV-${values[5]}-${CLOUD_USER_ID || ''}</span><span>${currency} ${amount}</span><span>${planName}</span><span>${dateString}</span></div>`;
        } catch (e) {
            console.error("Error parsing invoice data:", e, item.value);
            return `<div class="mc-invoice-row" data-id="${item.id || ''}"><span>Error loading invoice</span></div>`;
        }
    }

    // Updated get_invoice_string_payment_gateway to handle both payment provider data and local payment data
    function get_invoice_string_payment_gateway(item) {
         // Basic check if item is valid
         if (!item || typeof item !== 'object') return 'Invalid invoice item<br>';

         // Check if this is local payment data (has 'value' field with JSON)
         if (item.value && item.slug === 'payment') {
             try {
                 let values = JSON.parse(item.value);
                 if (Array.isArray(values) && values.length >= 6) {
                     let amount = values[0] || 0;
                     let planName = values[1] ? slugToString(values[1]) : 'Payment';
                     let timestamp = parseInt(values[5]);
                     let date = !isNaN(timestamp) ? new Date(timestamp * 1000).toISOString().slice(0, 10) : 'Unknown Date';
                     let currency = (CLOUD_CURRENCY || '').toUpperCase();
                     let invoiceNumber = `INV-${timestamp}-${item.user_id || item.id || 'N/A'}`;
                     return `${date} | ${currency} ${amount} | ${invoiceNumber} (${planName})<br>`;
                 }
             } catch (e) {
                 console.error("Error parsing local payment data:", e, item.value);
                 return `Local Payment | ${item.id || 'N/A'}<br>`;
             }
         }

         // Stripe / Razorpay / Paystack (assuming similar structure for basic display)
         if ((stripe || razorpay || paystack) && item.created && item.currency) {
            let amount = item.amount_paid || item.amount; // Use amount_paid first
            let currency = item.currency.toUpperCase();
             // Handle zero-decimal currencies (Add more if needed)
             let divisor = ['BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF'].includes(currency) ? 1 : 100;
             let formattedAmount = (amount / divisor).toFixed(2); // Format to 2 decimal places
             let date = new Date(item.created * 1000).toISOString().slice(0, 10);
             let number = item.number || item.id || item.reference || 'N/A'; // Get invoice number or ID/reference
            return `${date} | ${currency} ${formattedAmount} | ${number}<br>`;
        }
         // Rapyd
         if (rapyd && item.paid_at && item.currency_code) {
            let date = new Date(item.paid_at * 1000).toISOString().slice(0, 10);
            let method = item.payment_method_type ? item.payment_method_type.replace(/_/g, ' ').toUpperCase() : 'N/A';
            return `${item.currency_code.toUpperCase()} ${item.amount || 0} | ${date} | ${method}<br>`;
        }
         // Verifone / Default / Others (Adjust based on actual structure)
         if (item.OrderDate && item.Currency && item.NetPrice) {
             return `${item.Currency.toUpperCase()} ${item.NetPrice} | ${item.OrderDate} | ${item.RefNo || 'N/A'}<br>`;
         }
         // Fallback if no known structure matches - provide generic format
         return `Invoice | ${item.id || 'N/A'}<br>`;
    }

    function stringToSlug(string) {
         if (typeof string !== 'string') return ''; // Handle non-strings
        string = string.trim().toLowerCase();
        //\u00E0-\u00E5 = àáâãäå, \u00E8-\u00EB = èéêë, etc.
        const from = "àáâãäåāçćčèéêëēęîïíīìłñńôöòóõøōřßşśšșțûüúùūůŵýÿŷžźż·/_,:;";
        const to =   "aaaaaaaacceeeeeeiíiiilnnooooooorsssssttuuuuuuwyyyzzz------";
        for (let i = 0, l = from.length; i < l; i++) {
            string = string.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
        }
        return string
            .replace(/[^a-z0-9 -]/g, '') // remove invalid chars
            .replace(/\s+/g, '-') // collapse whitespace and replace by -
            .replace(/-+/g, '-') // collapse dashes
            .replace(/^-+/, '') // trim - from start of text
            .replace(/-+$/, ''); // trim - from end of text
    }


    function slugToString(string) {
         if (typeof string !== 'string') return ''; // Handle non-strings
        string = string.replace(/_/g, ' ').replace(/-/g, ' ');
        return string.charAt(0).toUpperCase() + string.slice(1);
    }

    function random() {
        let chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let result = '';
        for (var i = 5; i > 0; --i) result += chars[Math.floor(Math.random() * chars.length)]; // Use length property
        return result;
    }

    // Initialize Facebook SDK
    function initFacebookSDK() {
        if (typeof FB_APP_ID === 'undefined' || !FB_APP_ID) return;

        // Check if Facebook SDK is already loaded
        if (typeof FB !== 'undefined') {
            console.log('Facebook SDK already initialized');
            return;
        }

        // Set window.fbAsyncInit before loading the SDK
        window.fbAsyncInit = function() {
            FB.init({
                appId: FB_APP_ID,
                cookie: true,
                xfbml: true,
                version: 'v18.0',
                status: true,
                autoLogAppEvents: true
            });

            // Set app name for login dialog and log page view
            FB.AppEvents.logPageView();
        };

        // Load Facebook SDK
        (function(d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) return;
            js = d.createElement(s); js.id = id;
            js.src = "https://connect.facebook.net/en_US/sdk.js";
            fjs.parentNode.insertBefore(js, fjs);
        }(document, 'script', 'facebook-jssdk'));

        // Add click handlers for Facebook login buttons
        body.on('click', '.mc-btn-facebook', function() {
            let button = $(this);
            if (loading(button)) return;

            // Check if FB is defined
            if (typeof FB === 'undefined') {
                console.error('Facebook SDK not loaded');
                banner_error('Facebook login is not available at the moment. Please try again later or use regular login.');
                button.stopLoading();
                return;
            }

            // Use auth_type: rerequest to force permission dialog
            // Pass access_token directly to API calls instead of using global token
            FB.login(function(response) {
                if (response.authResponse) {
                    const accessToken = response.authResponse.accessToken;
                    // Get user info from Facebook using the token directly
                    FB.api('/me', {
                        fields: 'name,email,id',
                        access_token: accessToken // Pass token directly to avoid global override
                    }, function(userInfo) {
                        if (!userInfo) {
                            banner('Facebook Error', 'Could not get information from Facebook. Please try again or use regular login.', '', false, false, true);
                            button.stopLoading();
                            return;
                        }

                        // If email is missing but we have Facebook ID, proceed anyway
                        // The server will handle finding the user by Facebook ID
                        if (!userInfo.email && userInfo.id) {
                            console.log('Facebook did not provide email, but we have ID: ' + userInfo.id);
                            userInfo.email = 'fb_' + userInfo.id + '@facebook.com';
                        }

                        // Add the access token to the user info for potential server-side use
                        userInfo.access_token = accessToken;

                        // Send data to server for login/registration
                        ajax('facebook-login', {fb_data: userInfo}, function(response) {
                            if (response && response.status === 'error') {
                                banner('Login Error', response.message || 'Facebook login failed', '', false, false, true);
                                button.stopLoading();
                                return;
                            }

                            if (Array.isArray(response) && response.length >= 2) {
                                // Login successful - use the same flow as normal login
                                setLogin(response[0], response[1]);

                                // Check if this is a new registration (welcome flag)
                                if (response.length > 3 && response[3] === 'welcome') {
                                    // Redirect to welcome page
                                    setTimeout(function() {
                                        document.location = CLOUD_URL + '/account?welcome';
                                    }, 300);
                                } else {
                                    // Existing user login
                                    setTimeout(function() {
                                        document.location = CLOUD_URL;
                                    }, 300);
                                }
                            } else {
                                banner('Login Error', 'Login failed. Please try again.', '', false, false, true);
                                button.stopLoading();
                            }
                        });
                    });
                } else {
                    // User cancelled login or did not fully authorize
                    button.stopLoading();
                }
            }, {
                scope: 'email,public_profile',
                auth_type: 'rerequest',
                display: 'popup',
                return_scopes: true,
                enable_profile_selector: true,
                locale: 'en_US'
            });
        });
    }

    function active(element, set_active) {
         let $element = $(element); // Ensure jQuery object
        if (set_active === false || set_active === true) {
            $element.setClass('active', set_active); // Use setClass method defined below
            return $element;
        }
        return $element.hasClass('active');
    }

    // Modified: Use the original scrollTop that handles both user and super admin
     function scrollTop(container = null) {
         let scrollTarget = null;
         if (container && container instanceof jQuery && container.length) {
             scrollTarget = container[0]; // Scroll specific container if provided
         } else if (box_super && box_super.length) {
             // Find scrollable area within super admin content
             scrollTarget = box_super.find('.mc-admin-tab.mc-active .mc-scroll-area')[0] || box_super.find('.mc-scroll-area')[0];
         } else if (box_account && box_account.length) {
             scrollTarget = box_account.find('> .mc-tab > .mc-content.mc-active')[0] || box_account.find('> .mc-tab > .mc-content')[0];
         }

         if (scrollTarget) {
             scrollTarget.scrollTop = 0;
         } else {
              // Fallback to window scroll if no specific target found
              window.scrollTo(0, 0);
         }
     }

    $.fn.lightbox = function () {
        let $lightbox = $(this);
        // Center lightbox after ensuring it's visible (CSS might transition display)
        $lightbox.mcActive(true); // Show it first
        body.find('.mc-lightbox-overlay').mcActive(true);
        box_loading.mcActive(false); // Hide global loading

        // Use setTimeout to allow rendering before calculating dimensions
        setTimeout(() => {
            $lightbox.css({
                'margin-top': ($lightbox.outerHeight() / -2) + 'px',
                'margin-left': ($lightbox.outerWidth() / -2) + 'px',
                 'opacity': 1 // Ensure visible after positioning
            });
        }, 50); // Small delay

        return this;
    }


    $.fn.lightboxError = function (error_message) {
        let $lightbox = $(this);
        let area = $lightbox.find(' > .mc-info');
        if (area.length === 0) { // Create info area if it doesn't exist
             $lightbox.prepend('<div class="mc-info"></div>');
             area = $lightbox.find(' > .mc-info');
        }
        area.html(error_message).mcActive(true);
        // Auto-hide after a delay
        setTimeout(() => {
            area.mcActive(false); // Use mcActive to potentially animate out
        }, 10000);
        return this; // Allow chaining
    }


    $.fn.setClass = function (class_name, add = true) {
        // Check if it's a valid class name (basic check)
        if (typeof class_name === 'string' && class_name.trim() !== '') {
             if (add) {
                 $(this).addClass(class_name);
             } else {
                 $(this).removeClass(class_name);
             }
        }
        return this;
    }

    // Function to update the membership UI without refreshing
    function updateMembershipUI(membershipData) {
        if (!membershipData || !box_account) return false;

        try {
            // Update the membership quota display
            let quotaElement = box_account.find('.membership-quota');
            if (quotaElement.length) {
                quotaElement.each(function() {
                    // Check if this is the agents quota or regular quota
                    let isAgentsQuota = $(this).closest('div').text().toLowerCase().includes('agent');
                    if (isAgentsQuota && 'quota_agents' in membershipData) {
                        $(this).text(membershipData.quota_agents == 9999 ? '∞' : membershipData.quota_agents);
                    } else if (!isAgentsQuota && 'quota' in membershipData) {
                        $(this).text(membershipData.quota);
                    }
                });
            }

            // Update the membership name
            let nameElement = box_account.find('.membership-name');
            if (nameElement.length && 'name' in membershipData) {
                nameElement.text(mc_(membershipData.name));
            }

            // Update the membership price
            let priceElement = box_account.find('.membership-price');
            if (priceElement.length && 'price' in membershipData && 'currency' in membershipData && 'period' in membershipData) {
                let priceString = '';
                if (membershipData.price > 0) {
                    priceString = mb_strtoupper(membershipData.currency) + ' ' +
                                 membershipData.price + ' ' +
                                 membership_get_period_string(membershipData.period);
                }
                priceElement.text(priceString);
                if (membershipData.currency) {
                    priceElement.attr('data-currency', membershipData.currency);
                }
            }

            // Update the active plan in the plans section
            if ('id' in membershipData) {
                box_account.find('#plans > div').removeClass('mc-plan-active').removeAttr('data-active-membership data-expired');
                let activePlan = box_account.find('#plans > div[data-id="' + membershipData.id + '"]');
                if (activePlan.length) {
                    activePlan.attr('data-active-membership', 'true');
                    if (membershipData.expired) {
                        activePlan.attr('data-expired', 'true');
                    }

                    // Add the active membership info div if it doesn't exist
                    if (activePlan.find('.active-membership-info').length === 0) {
                        let infoText = mc_('Active Membership');
                        if (membershipData.expired) {
                            infoText += ' ' + mc_('Expired');
                        }
                        activePlan.prepend('<div class="active-membership-info">' + infoText + '</div>');
                    }
                }
            }

            // Update the credits display if available
            if ('credits' in membershipData) {
                let creditsElement = box_account.find('.maso-box-credits .box-black:first-child div');
                if (creditsElement.length) {
                    creditsElement.text(membershipData.credits || '0');
                }
            }

            return true;
        } catch (e) {
            console.error('Error updating membership UI:', e);
            return false;
        }
    }

    // Helper function to convert string to uppercase
    function mb_strtoupper(str) {
        return str ? str.toUpperCase() : '';
    }

    // Helper function to get period string
    function membership_get_period_string(period) {
        if (!period) return '';

        switch(period.toLowerCase()) {
            case 'month': return mc_('per month');
            case 'year': return mc_('per year');
            default: return period;
        }
    }

    // stopLoading, startLoading, isLoading are already defined above near loading()

    $.fn.mcActive = function (show = -1) {
        let $element = $(this); // Cache $(this)
        if (show === -1) return $element.hasClass('mc-active');

        // Use setClass for consistency
        $element.setClass('mc-active', show);

        // Optional: Add ARIA attributes for accessibility
        if (show) {
            $element.attr('aria-hidden', 'false');
        } else {
            $element.attr('aria-hidden', 'true');
        }

        return $element; // Return jQuery object for chaining
    };

}(jQuery));
