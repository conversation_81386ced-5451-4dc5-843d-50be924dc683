<?php

/*
 * ==========================================================
 * GOOGLE.PHP
 * ==========================================================
 *
 * Google synchronization.
 * � 2017-2025 app.masichat.com. All rights reserved.
 *
 */

require('functions.php');

if (isset($_GET['code'])) {
    $info = mc_google_key();
    if (empty($info[0]) || empty($info[1])) {
        die('<br><br>Please enter Client ID and Client Secret in Masi Chat > Settings > Artificial Intelligence > Google.');
    }
    $query = '{ code: "' . $_GET['code'] . '", grant_type: "authorization_code", client_id: "' . $info[0] . '", client_secret: "' . $info[1] . '", redirect_uri: "' . MC_URL . '/include/google.php" }';
    $response = mc_curl('https://accounts.google.com/o/oauth2/token', $query, ['Content-Type: application/json', 'Content-Length: ' . strlen($query)]);
    if ($response && isset($response['refresh_token'])) {
        die('<br><br>Copy the refresh token below and paste it in Masi Chat > Settings > Artificial Intelligence > Google > Refresh token.<br><br><b>' . $response['refresh_token'] . '</b>');
    } else {
        echo '<br><br>Error: ' . print_r($response, true);
    }
}

if (isset($_GET['refresh-token'])) {
    $info = mc_google_key();
    $query = '{ refresh_token: "' . $_GET['refresh-token'] . '", grant_type: "refresh_token", client_id: "' . $info[0] . '", client_secret: "' . $info[1] . '" }';
    die(json_encode(mc_curl('https://accounts.google.com/o/oauth2/token', $query, ['Content-Type: application/json', 'Content-Length: ' . strlen($query)])));
}

?>