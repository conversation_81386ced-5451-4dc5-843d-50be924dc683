<?php

/*
 * ==========================================================
 * AJAX.PHP
 * ==========================================================
 *
 * AJAX functions. This file must be executed only via AJAX. © 2017-2025 app.masichat.com. All rights reserved.
 *
 */

header('Access-Control-Allow-Headers: *');

if (file_exists('../config.php')) {
    require_once('../config.php');
}
if (defined('MC_CROSS_DOMAIN') && MC_CROSS_DOMAIN) {
    header('Access-Control-Allow-Origin: *');
}
if (!isset($_POST['function'])) {
    die('true');
}
$GLOBALS['MC_LANGUAGE'] = mc_post('language');
require_once('functions.php');
if (mc_is_cloud()) {
    if (mc_cloud_ajax_function_forbidden($_POST['function'])) {
        die('cloud_function_forbidden');
    }
    mc_cloud_load();
}
if ($_POST['function'] == 'ajax_calls') {
    $response = [];
    $calls = $_POST['calls'];
    $GLOBALS['MC_JSON_RAW'] = true;
    for ($i = 0; $i < count($calls); $i++) {
        $_POST = array_merge($_POST, $calls[$i]);
        array_push($response, mc_ajax_execute());
    }
    die(json_encode($response, JSON_INVALID_UTF8_IGNORE));
} else {
    die(mc_ajax_execute());
}

function mc_ajax_execute() {
    $function = $_POST['function'];
    if (!defined('MC_API')) {
        if (mc_security($function) !== true) {
            die(mc_json_response(mc_error('security-error', function_exists($function) ? $function : '')));
        }
        if (!in_array($function, ['verification-cookie', 'on-close', 'installation']) && mc_get_active_user(mc_post('login-cookie'))) {
            if (!mc_post('login-cookie')) {
                die(mc_json_response(mc_error('login-data-error', 'The login-cookie data is no present in the request.')));
            }
            if (isset($_COOKIE['mc-login']) && $_COOKIE['mc-login'] != mc_post('login-cookie') && urldecode($_COOKIE['mc-login']) != urldecode(mc_post('login-cookie'))) {
                die(mc_json_response(mc_error('login-data-error', 'The login-cookie different from cookie value.')));
            }
        }
    }
    if ($function == 'get-front-settings' && mc_get_setting('front-auto-translations') && mc_post('language') && mc_post('language')[0] != 'en') {
        $GLOBALS['MC_LANGUAGE'] = [mc_get_user_language(), 'front'];
    }
    switch ($function) {
        case 'get-user-by':
            return mc_json_response(mc_get_user_by($_POST['by'], $_POST['value']));
        case 'update-bot':
            return mc_json_response(mc_update_bot(mc_post('name'), mc_post('profile_image')));
        case 'get-last-agent-in-conversation':
            return mc_json_response(mc_get_last_agent_in_conversation($_POST['conversation_id']));
        case 'get-last-message':
            return mc_json_response(mc_get_last_message($_POST['conversation_id'], mc_post('exclude_message'), mc_post('user_id')));
        case 'delete-attachments':
            return mc_json_response(mc_delete_attachments(mc_post('conversation_id'), mc_post('message_id')));
        case 'execute-bot-message':
            return mc_json_response(mc_execute_bot_message($_POST['name'], $_POST['conversation_id'], mc_post('last_user_message'), mc_post('check')));
        case 'messaging-platforms-send-message':
            return mc_json_response(mc_messaging_platforms_functions($_POST['conversation_id'], $_POST['message'], mc_post('attachments', []), $_POST['user'], $_POST['source']));
        case 'translate-string':
            return mc_json_response(mc_t($_POST['string'], $_POST['language_code']));
        case 'get-external-setting':
            return mc_json_response(mc_get_external_setting($_POST['name'], mc_post('default')));
        case 'save-external-setting':
            return mc_json_response(mc_save_external_setting($_POST['name'], $_POST['value']));
        case 'get-multi-setting':
            return mc_json_response(mc_get_multi_setting($_POST['id'], $_POST['sub_id'], mc_post('default')));
        case 'newsletter':
            return mc_json_response(mc_newsletter($_POST['email'], mc_post('first_name'), mc_post('last_name')));
        case 'upload-path':
            return mc_json_response(mc_upload_path(mc_post('email'), mc_post('date')));
        case 'is-allowed-extension':
            return mc_json_response(mc_is_allowed_extension($_POST['extension']));
        case 'logs':
            return mc_json_response(mc_logs($_POST['string'], mc_post('user')));
        case 'aws-s3':
            return mc_json_response(mc_aws_s3($_POST['file_path'], mc_post('action', 'PUT')));
        case 'automations-is-sent':
            return mc_json_response(mc_automations_is_sent($_POST['user_id'], $_POST['automation_id'], mc_post('repeat_id')));
        case 'get-agent-department':
            return mc_json_response(mc_get_agent_department());
        case 'emoji':
            return file_get_contents(MC_PATH . '/resources/json/emoji.json');
        case 'saved-replies':
            return mc_json_response(mc_get_setting('saved-replies'));
        case 'save-settings':
            return mc_json_response(mc_save_settings($_POST['settings'], mc_post('external_settings', []), mc_post('external_settings_translations', [])));
        case 'get-settings':
            return mc_json_response(mc_get_settings());
        case 'get-all-settings':
            return mc_json_response(mc_get_all_settings());
        case 'get-front-settings':
            return mc_json_response(mc_get_front_settings());
        case 'get-block-setting':
            return mc_json_response(mc_get_block_setting($_POST['value']));
        case 'add-user':
            return mc_json_response(mc_add_user($_POST['settings'], mc_post('settings_extra', []), true, mc_is_agent()));
        case 'add-user-and-login':
            return mc_json_response(mc_add_user_and_login(mc_post('settings', []), mc_post('settings_extra', [])));
        case 'get-user':
            return mc_json_response(mc_get_user($_POST['user_id'], mc_post('extra')));
        case 'get-users':
            return mc_json_response(mc_get_users(mc_post('sorting', ['creation_time', 'DESC']), mc_post('user_types', []), mc_post('search', ''), mc_post('pagination'), mc_post('extra'), mc_post('users_id'), mc_post('department'), mc_post('tag'), mc_post('source')));
        case 'get-new-users':
            return mc_json_response(mc_get_new_users($_POST['datetime']));
        case 'get-user-extra':
            return mc_json_response(mc_get_user_extra($_POST['user_id'], mc_post('slug'), mc_post('default')));
        case 'get-user-language':
            return mc_json_response(mc_get_user_language(mc_post('user_id')));
        case 'get-user-from-conversation':
            return mc_json_response(mc_get_user_from_conversation($_POST['conversation_id'], mc_post('agent')));
        case 'get-online-users':
            return mc_json_response(mc_get_online_users(mc_post('sorting', 'creation_time'), mc_post('agents')));
        case 'search-users':
            return mc_json_response(mc_search_users($_POST['search']));
        case 'get-active-user':
            return mc_json_response(mc_get_active_user(mc_post('login-cookie'), mc_post('db'), mc_post('login_app'), mc_post('user_token')));
        case 'get-agent':
            return mc_json_response(mc_get_agent($_POST['agent_id']));
        case 'delete-user':
            return mc_json_response(mc_delete_user($_POST['user_id']));
        case 'delete-users':
            return mc_json_response(mc_delete_users($_POST['user_ids']));
        case 'update-user':
            return mc_json_response(mc_update_user($_POST['user_id'], mc_post('settings', []), mc_post('settings_extra', [])));
        case 'count-users':
            return mc_json_response(mc_count_users());
        case 'get-users-with-details':
            return mc_json_response(mc_get_users_with_details($_POST['details'], mc_post('user_ids')));
        case 'update-user-to-lead':
            return mc_json_response(mc_update_user_to_lead($_POST['user_id']));
        case 'get-conversations':
            return mc_json_response(mc_get_conversations(mc_post('pagination', 0), mc_post('status_code', 0), mc_post('department'), mc_post('source'), mc_post('tag')));
        case 'get-new-conversations':
            return mc_json_response(mc_get_new_conversations($_POST['datetime'], mc_post('department'), mc_post('source'), mc_post('tag')));
        case 'get-conversation':
            return mc_json_response(mc_get_conversation(mc_post('user_id'), $_POST['conversation_id']));
        case 'search-conversations':
            return mc_json_response(mc_search_conversations($_POST['search']));
        case 'search-user-conversations':
            return mc_json_response(mc_search_user_conversations($_POST['search'], mc_post('user_id')));
        case 'new-conversation':
            return mc_json_response(mc_new_conversation($_POST['user_id'], mc_post('status_code'), mc_post('title', ''), mc_post('department', -1), mc_post('agent_id', -1), mc_post('source'), mc_post('extra'), mc_post('extra_2'), mc_post('extra_3'), mc_post('tags')));
        case 'get-user-conversations':
            return mc_json_response(mc_get_user_conversations($_POST['user_id'], mc_post('exclude_id', -1), mc_post('agent')));
        case 'get-new-user-conversations':
            return mc_json_response(mc_get_new_user_conversations($_POST['user_id'], mc_isset($_POST, 'datetime', 0)));
        case 'update-conversation-status':
            return mc_json_response(mc_update_conversation_status($_POST['conversation_id'], $_POST['status_code']));
        case 'update-conversation-department':
            return mc_json_response(mc_update_conversation_department($_POST['conversation_id'], $_POST['department'], mc_post('message')));
        case 'update-conversation-agent':
            return mc_json_response(mc_update_conversation_agent($_POST['conversation_id'], $_POST['agent_id'], mc_post('message')));
        case 'update-conversation-extra':
            return mc_json_response(mc_update_conversation_extra($_POST['conversation_id'], mc_post('extra'), mc_post('extra_2'), mc_post('extra_3')));
        case 'queue':
            return mc_json_response(mc_queue(mc_post('conversation_id'), mc_post('department')));
        case 'update-users-last-activity':
            return mc_json_response(mc_update_users_last_activity($_POST['user_id'], mc_post('return_user_id', -1), mc_post('check_slack')));
        case 'is-typing':
            return mc_json_response(mc_is_typing($_POST['user_id'], $_POST['conversation_id']));
        case 'is-agent-typing':
            return mc_json_response(mc_is_agent_typing($_POST['conversation_id']));
        case 'set-typing':
            return mc_json_response(mc_set_typing(mc_post('user_id'), mc_post('conversation_id'), mc_post('source')));
        case 'login':
            return mc_json_response(mc_login(mc_post('email', ''), mc_post('password', ''), mc_post('user_id', ''), mc_post('user_token', mc_post('token', ''))));
        case 'logout':
            return mc_json_response(mc_logout());
        case 'update-login':
            return mc_json_response(mc_update_login(mc_post('profile_image', ''), mc_post('first_name', ''), mc_post('last_name', ''), mc_post('email', ''), mc_post('department', ''), mc_post('user_id', '')));
        case 'get-new-messages':
            return mc_json_response(mc_get_new_messages($_POST['user_id'], $_POST['conversation_id'], $_POST['datetime'], mc_post('last_id')));
        case 'send-message':
            return mc_json_response(mc_send_message($_POST['user_id'], $_POST['conversation_id'], mc_post('message', ''), mc_post('attachments', []), mc_post('conversation_status_code', -1), mc_post('payload'), mc_post('queue'), mc_post('recipient_id')));
        case 'send-slack-message':
            return mc_json_response(mc_send_slack_message($_POST['user_id'], $_POST['full_name'], mc_post('profile_image'), mc_post('message', ''), mc_post('attachments', []), mc_post('conversation_id'), mc_post('channel')));
        case 'update-message':
            return mc_json_response(mc_update_message($_POST['message_id'], mc_post('message'), mc_post('attachments'), mc_post('payload')));
        case 'delete-message':
            return mc_json_response(mc_delete_message($_POST['message_id']));
        case 'close-message':
            return mc_json_response(mc_close_message($_POST['conversation_id'], $_POST['bot_id']));
        case 'update-messages-status':
            return mc_json_response(mc_update_messages_status($_POST['message_ids'], mc_post('user_id')));
        case 'csv-users':
            return mc_json_response(mc_csv_users(mc_post('users_id')));
        case 'transcript':
            return mc_json_response(mc_transcript($_POST['conversation_id'], mc_post('type')));
        case 'update-user-and-message':
            return mc_json_response(mc_update_user_and_message($_POST['user_id'], mc_post('settings', []), mc_post('settings_extra', []), mc_post('message_id', ''), mc_post('message', ''), mc_post('payload')));
        case 'get-rich-message':
            return mc_json_response(mc_get_rich_message($_POST['name'], mc_post('settings')));
        case 'create-email':
            return mc_json_response(mc_email_create($_POST['recipient_id'], $_POST['sender_name'], $_POST['sender_profile_image'], $_POST['message'], mc_post('attachments', []), mc_post('department'), mc_post('conversation_id')));
        case 'send-email':
            return mc_json_response(mc_email($_POST['recipient_id'], $_POST['message'], mc_post('attachments', []), mc_post('sender_id', -1)));
        case 'send-custom-email':
            return mc_json_response(mc_email_send($_POST['to'], $_POST['subject'], mc_email_default_parts($_POST['message'], mc_post('recipient_id'))));
        case 'send-test-email':
            return mc_json_response(mc_email_send_test($_POST['to'], $_POST['email_type']));
        case 'slack-users':
            return mc_json_response(mc_slack_users());
        case 'archive-slack-channels':
            return mc_json_response(mc_archive_slack_channels(mc_post('conversation_user_id')));
        case 'slack-presence':
            return mc_json_response(mc_slack_presence(mc_post('agent_id'), mc_post('list')));
        case 'slack-channels':
            return mc_json_response(mc_slack_get_channels(mc_post('code')));
        case 'clean-data':
            return mc_json_response(mc_clean_data());
        case 'user-autodata':
            return mc_json_response(mc_user_autodata($_POST['user_id']));
        case 'current-url':
            return mc_json_response(mc_current_url(mc_post('user_id'), mc_post('url')));
        case 'get-translations':
            return mc_json_response(mc_get_translations());
        case 'get-translation':
            return mc_json_response(mc_get_translation($_POST['language_code']));
        case 'save-translations':
            return mc_json_response(mc_save_translations($_POST['translations']));
        case 'dialogflow-message':
            return mc_json_response(mc_dialogflow_message(mc_post('conversation_id'), $_POST['message'], mc_post('token', -1), mc_post('dialogflow_language'), mc_post('attachments', []), mc_post('event'), mc_post('parameters'), mc_post('project_id'), mc_post('session_id'), mc_post('audio')));
        case 'dialogflow-get-intents':
            return mc_json_response(mc_dialogflow_get_intents(mc_post('intent_name'), mc_post('language')));
        case 'dialogflow-create-intent':
            return mc_json_response(mc_dialogflow_create_intent($_POST['expressions'], $_POST['response'], mc_post('agent_language', ''), mc_post('conversation_id'), mc_post('services')));
        case 'dialogflow-update-intent':
            return mc_json_response(mc_dialogflow_update_intent($_POST['intent_name'], $_POST['expressions'], mc_post('agent_language', ''), mc_post('services') != 'dialogflow' ? $_POST['response'] : 'dialogflow'));
        case 'dialogflow-entity':
            return mc_json_response(mc_dialogflow_create_entity($_POST['entity_name'], $_POST['synonyms'], mc_post('agent_language', '')));
        case 'dialogflow-get-entity':
            return mc_json_response(mc_dialogflow_get_entity(mc_post('entity_id', 'all'), mc_post('agent_language', '')));
        case 'dialogflow-get-token':
            return mc_json_response(mc_dialogflow_get_token());
        case 'dialogflow-get-agent':
            return mc_json_response(mc_dialogflow_get_agent());
        case 'dialogflow-set-active-context':
            return mc_json_response(mc_dialogflow_set_active_context($_POST['context_name'], mc_post('parameters', [])));
        case 'dialogflow-human-takeover':
            return mc_json_response(mc_dialogflow_human_takeover($_POST['conversation_id'], mc_post('auto_messages')));
        case 'dialogflow-curl':
            return mc_json_response(mc_dialogflow_curl($_POST['url_part'], $_POST['query'], mc_post('language'), mc_post('type', 'POST')));
        case 'dialogflow-smart-reply':
            return mc_json_response(mc_dialogflow_smart_reply($_POST['message'], mc_post('dialogflow_languages'), mc_post('token'), mc_post('conversation_id')));
        case 'dialogflow-knowledge':
            return mc_json_response(mc_dialogflow_knowledge_articles(mc_post('articles')));
        case 'dialogflow-saved-replies':
            return mc_json_response(mc_dialogflow_saved_replies());
        case 'set-rating':
            return mc_json_response(mc_set_rating($_POST['conversation_id'], $_POST['user_id'], $_POST['rating'], mc_post('message'), mc_post('agent_id')));
        case 'get-rating':
            return mc_json_response(mc_get_rating($_POST['user_id']));
        case 'init-articles-admin':
            return mc_json_response(mc_init_articles_admin());
        case 'save-article':
            return mc_json_response(mc_save_article($_POST['article']));
        case 'get-articles':
            return mc_json_response(mc_get_articles(mc_post('id'), mc_post('count'), mc_post('full'), mc_post('categories'), mc_post('language'), mc_post('skip_language')));
        case 'get-articles-categories':
            return mc_json_response(mc_get_articles_categories(mc_post('category_type')));
        case 'save-articles-categories':
            return mc_json_response(mc_save_articles_categories($_POST['categories']));
        case 'search-articles':
            return mc_json_response(mc_search_articles($_POST['search'], mc_post('language')));
        case 'article-ratings':
            return mc_json_response(mc_article_ratings($_POST['article_id'], mc_post('rating')));
        case 'installation':
            return mc_json_response(mc_installation($_POST['details']));
        case 'get-versions':
            return mc_json_response(mc_get_versions());
        case 'update':
            return mc_json_response(mc_update());
        case 'app-activation':
            return mc_json_response(mc_app_activation($_POST['app_name'], $_POST['key']));
        case 'app-disable':
            return mc_json_response(mc_app_disable($_POST['app_name']));
        case 'app-get-key':
            return mc_json_response(mc_app_get_key($_POST['app_name']));
        case 'wp-sync':
            return mc_json_response(mc_wp_synch());
        case 'webhooks':
            return mc_json_response(mc_webhooks($_POST['function_name'], $_POST['parameters']));
        case 'system-requirements':
            return mc_json_response(mc_system_requirements());
        case 'get-departments':
            return mc_json_response(mc_get_departments());
        case 'push-notification':
            return mc_json_response(mc_push_notification(mc_post('title'), mc_post('message'), mc_post('icon'), mc_post('interests'), mc_post('conversation_id'), mc_post('user_id')));
        case 'delete-leads':
            return mc_json_response(mc_delete_leads());
        case 'cron-jobs':
            return mc_json_response(mc_cron_jobs());
        case 'path':
            return mc_json_response(MC_PATH);
        case 'subscribe-email':
            return mc_json_response(mc_subscribe_email($_POST['email']));
        case 'agents-online':
            return mc_json_response(mc_agents_online());
        case 'woocommerce-get-customer':
            return mc_json_response(mc_woocommerce_get_customer(mc_post('session_key')));
        case 'woocommerce-get-user-orders':
            return mc_json_response(mc_woocommerce_get_user_orders($_POST['user_id']));
        case 'woocommerce-get-product':
            return mc_json_response(mc_woocommerce_get_product($_POST['product_id']));
        case 'woocommerce-get-taxonomies':
            return mc_json_response(mc_woocommerce_get_taxonomies($_POST['type'], mc_post('language', '')));
        case 'woocommerce-get-attributes':
            return mc_json_response(mc_woocommerce_get_attributes(mc_post('type'), mc_post('language', '')));
        case 'woocommerce-get-product-id-by-name':
            return mc_json_response(mc_woocommerce_get_product_id_by_name($_POST['name']));
        case 'woocommerce-get-product-images':
            return mc_json_response(mc_woocommerce_get_product_images($_POST['product_id']));
        case 'woocommerce-get-product-taxonomies':
            return mc_json_response(mc_woocommerce_get_product_taxonomies($_POST['product_id']));
        case 'woocommerce-get-attribute-by-term':
            return mc_json_response(mc_woocommerce_get_attribute_by_term($_POST['term_name']));
        case 'woocommerce-get-attribute-by-name':
            return mc_json_response(mc_woocommerce_get_attribute_by_name($_POST['name']));
        case 'woocommerce-is-in-stock':
            return mc_json_response(mc_woocommerce_is_in_stock($_POST['product_id']));
        case 'woocommerce-coupon':
            return mc_json_response(mc_woocommerce_coupon($_POST['discount'], $_POST['expiration'], mc_post('product_id', ''), mc_post('user_id', '')));
        case 'woocommerce-coupon-check':
            return mc_json_response(mc_woocommerce_coupon_check($_POST['user_id']));
        case 'woocommerce-coupon-delete-expired':
            return mc_json_response(mc_woocommerce_coupon_delete_expired());
        case 'woocommerce-get-url':
            return mc_json_response(mc_woocommerce_get_url($_POST['type'], mc_post('name', ''), mc_post('language', '')));
        case 'woocommerce-get-session':
            return mc_json_response(mc_woocommerce_get_session(mc_post('session_key')));
        case 'woocommerce-get-session-key':
            return mc_json_response(mc_woocommerce_get_session_key(mc_post('user_id')));
        case 'woocommerce-payment-methods':
            return mc_json_response(mc_woocommerce_payment_methods());
        case 'woocommerce-shipping-locations':
            return mc_json_response(mc_woocommerce_shipping_locations(mc_post('country_code')));
        case 'woocommerce-get-conversation-details':
            return mc_json_response(mc_woocommerce_get_conversation_details($_POST['user_id']));
        case 'woocommerce-returning-visitor':
            return mc_json_response(mc_woocommerce_returning_visitor());
        case 'woocommerce-get-products':
            return mc_json_response(mc_woocommerce_get_products(mc_post('filters'), mc_post('pagination'), mc_post('user_language', '')));
        case 'woocommerce-search-products':
            return mc_json_response(mc_woocommerce_search_products($_POST['search']));
        case 'woocommerce-products-popup':
            return mc_json_response([mc_woocommerce_get_products([], 0, mc_post('user_language')), mc_woocommerce_get_taxonomies('category', mc_post('user_language'))]);
        case 'woocommerce-waiting-list':
            return mc_json_response(mc_woocommerce_waiting_list($_POST['product_id'], mc_post('conversation_id'), $_POST['user_id'], mc_post('action', 'request'), mc_post('token')));
        case 'woocommerce-get-order':
            return mc_json_response(mc_woocommerce_get_order($_POST['order_id']));
        case 'ump-get-conversation-details':
            return mc_json_response(mc_ump_get_conversation_details($_POST['user_id']));
        case 'armember-get-conversation-details':
            return mc_json_response(mc_armember_get_conversation_details($_POST['wp_user_id']));
        case 'perfex-sync':
            return mc_json_response(mc_perfex_sync());
        case 'perfex-articles-sync':
            return mc_json_response(mc_perfex_articles_sync());
        case 'whmcs-get-conversation-details':
            return mc_json_response(mc_whmcs_get_conversation_details($_POST['whmcs_id']));
        case 'whmcs-articles-sync':
            return mc_json_response(mc_whmcs_articles_sync());
        case 'whmcs-sync':
            return mc_json_response(mc_whmcs_sync());
        case 'aecommerce-get-conversation-details':
            return mc_json_response(mc_aecommerce_get_conversation_details($_POST['aecommerce_id']));
        case 'aecommerce-sync-sellers':
        case 'aecommerce-sync-admins':
        case 'aecommerce-sync':
            return mc_json_response(mc_aecommerce_sync($function == 'aecommerce-sync-admins' ? 'admin' : ($function == 'aecommerce-sync-sellers' ? 'seller' : 'customer')));
        case 'aecommerce-cart':
            return mc_json_response(mc_aecommerce_cart($_POST['cart']));
        case 'email-piping':
            return mc_json_response(mc_email_piping(mc_post('force')));
        case 'reports':
            return mc_json_response(mc_reports($_POST['name'], mc_post('date_start'), mc_post('date_end'), mc_post('timezone')));
        case 'reports-update':
            return mc_json_response(mc_reports_update($_POST['name'], mc_post('value'), mc_post('external_id'), mc_post('extra')));
        case 'reports-export':
            return mc_json_response(mc_reports_export($_POST['name'], mc_post('date_start'), mc_post('date_end'), mc_post('timezone')));
        case 'pusher-trigger':
            return mc_json_response(mc_pusher_trigger($_POST['channel'], $_POST['event'], mc_post('data')));
        case 'is-online':
            return mc_json_response(mc_is_user_online($_POST['user_id']));
        case 'get-notes':
            return mc_json_response(mc_get_notes($_POST['conversation_id']));
        case 'add-note':
            return mc_json_response(mc_add_note($_POST['conversation_id'], $_POST['user_id'], $_POST['name'], $_POST['message']));
        case 'update-note':
            return mc_json_response(mc_update_note($_POST['conversation_id'], $_POST['user_id'], $_POST['note_id'], $_POST['message']));
        case 'delete-note':
            return mc_json_response(mc_delete_note($_POST['conversation_id'], $_POST['note_id']));
        case 'messenger-unsubscribe':
            return mc_json_response(mc_messenger_unsubscribe());
        case 'messenger-send-message':
            return mc_json_response(mc_messenger_send_message($_POST['psid'], $_POST['facebook_page_id'], mc_post('message', ''), mc_post('attachments', []), mc_post('metadata', []), mc_post('message_id')));
        case 'whatsapp-send-message':
            return mc_json_response(mc_whatsapp_send_message($_POST['to'], mc_post('message', ''), mc_post('attachments', []), mc_post('phone_id')));
        case 'whatsapp-get-templates':
            return mc_json_response(mc_whatsapp_get_templates(mc_post('business_account_id'), mc_post('template_name'), mc_post('template_langauge')));
        case 'whatsapp-send-template':
            return mc_json_response(mc_whatsapp_send_template($_POST['to'], mc_post('language', ''), mc_post('conversation_url_parameter', ''), mc_post('user_name', ''), mc_post('user_email', ''), mc_post('template_name'), mc_post('phone_id'), mc_post('parameters'), mc_post('template_languages'), mc_post('user_id')));
        case 'whatsapp-360-synchronization':
            return mc_json_response(mc_whatsapp_360_synchronization($_POST['token'], mc_post('cloud_token')));
        case 'telegram-send-message':
            return mc_json_response(mc_telegram_send_message($_POST['chat_id'], mc_post('message', ''), mc_post('attachments', []), mc_post('conversation_id')));
        case 'telegram-synchronization':
            return mc_json_response(mc_telegram_synchronization($_POST['token'], mc_post('cloud_token'), mc_post('is_additional_number')));
        case 'viber-send-message':
            return mc_json_response(mc_viber_send_message($_POST['viber_id'], mc_post('message', ''), mc_post('attachments', [])));
        case 'viber-synchronization':
            return mc_json_response(mc_viber_synchronization($_POST['token'], mc_post('cloud_token')));
        case 'zalo-send-message':
            return mc_json_response(mc_zalo_send_message($_POST['zalo_id'], mc_post('message', ''), mc_post('attachments', [])));
        case 'twitter-send-message':
            return mc_json_response(mc_twitter_send_message($_POST['twitter_id'], mc_post('message', ''), mc_post('attachments', [])));
        case 'twitter-subscribe':
            return mc_json_response(mc_twitter_subscribe(mc_post('cloud_token')));
        case 'line-send-message':
            return mc_json_response(mc_line_send_message($_POST['line_id'], mc_post('message', ''), mc_post('attachments', []), mc_post('conversation_id')));
        case 'wechat-send-message':
            return mc_json_response(mc_wechat_send_message($_POST['open_id'], mc_post('message', ''), mc_post('attachments', []), mc_post('token')));
        case 'send-sms':
            return mc_json_response(mc_send_sms($_POST['message'], $_POST['to'], mc_post('template', true), mc_post('conversation_id'), mc_post('attachments')));
        case 'direct-message':
            return mc_json_response(mc_direct_message($_POST['user_ids'], $_POST['message']));
        case 'automations-get':
            return mc_json_response(mc_automations_get());
        case 'automations-save':
            return mc_json_response(mc_automations_save(mc_post('automations'), mc_post('translations')));
        case 'automations-run':
            return mc_json_response(mc_automations_run($_POST['automation'], mc_post('validate')));
        case 'automations-run-all':
            return mc_json_response(mc_automations_run_all());
        case 'automations-validate':
            return mc_json_response(mc_automations_validate($_POST['automation']));
        case 'chat-css':
            return mc_json_response(mc_css(mc_post('color_1'), mc_post('color_2'), mc_post('color_3'), true));
        case 'get-avatar':
            return mc_json_response(mc_get_avatar($_POST['first_name'], mc_post('last_name')));
        case 'get-agents-ids':
            return mc_json_response(mc_get_agents_ids(mc_post('admins', true)));
        case 'get-agents-in-conversation':
            return mc_json_response(mc_get_agents_in_conversation($_POST['conversation_id']));
        case 'count-conversations':
            return mc_json_response(mc_count_conversations(mc_post('status_code')));
        case 'updates-available':
            return mc_json_response(mc_updates_available());
        case 'google-translate':
            return mc_json_response(mc_google_translate($_POST['strings'], $_POST['language_code'], mc_post('token'), mc_post('message_ids'), mc_post('conversation_id')));
        case 'google-language-detection-update-user':
            return mc_json_response(mc_google_language_detection_update_user($_POST['string'], mc_post('user_id'), mc_post('token')));
        case 'google-troubleshoot':
            return mc_json_response(mc_google_troubleshoot());
        case 'export-settings':
            return mc_json_response(mc_export_settings());
        case 'import-settings':
            return mc_json_response(mc_import_settings($_POST['file_url']));
        case 'delete-file':
            return mc_json_response(mc_file_delete($_POST['path']));
        case 'check-conversations-assignment':
            return mc_json_response(mc_check_conversations_assignment($_POST['conversation_ids'], mc_post('agent_id'), mc_post('department')));
        case 'verification-cookie':
            return mc_json_response(mc_verification_cookie($_POST['code'], $_POST['domain']));
        case 'on-close':
            return mc_json_response(mc_on_close());
        case 'zendesk-get-conversation-details':
            return mc_json_response(mc_zendesk_get_conversation_details($_POST['user_id'], $_POST['conversation_id'], mc_post('zendesk_id'), mc_post('email'), mc_post('phone')));
        case 'zendesk-create-ticket':
            return mc_json_response(mc_zendesk_create_ticket($_POST['conversation_id']));
        case 'zendesk-update-ticket':
            return mc_json_response(mc_zendesk_update_ticket($_POST['conversation_id'], $_POST['zendesk_ticket_id']));
        case 'recaptcha':
            return mc_json_response(mc_tickets_recaptcha($_POST['token']));
        case 'martfury-session':
            return mc_json_response(mc_martfury_save_session());
        case 'martfury-get-conversation-details':
            return mc_json_response(mc_martfury_get_conversation_details($_POST['user_id'], $_POST['martfury_id']));
        case 'martfury-sync':
        case 'martfury-sync-sellers':
            return mc_json_response(mc_martfury_import_users($function == 'martfury-sync-sellers'));
        case 'open-ai-message':
            return mc_json_response(mc_open_ai_message($_POST['message'], mc_post('max_tokens'), mc_post('model'), mc_post('conversation_id'), mc_post('extra'), mc_post('audio'), mc_post('attachments', []), mc_post('context')));
        case 'open-ai-user-expressions':
            return mc_json_response(mc_open_ai_user_expressions($_POST['message']));
        case 'open-ai-user-expressions-intents':
            return mc_json_response(mc_open_ai_user_expressions_intents());
        case 'open-ai-smart-reply':
            return mc_json_response(mc_open_ai_smart_reply($_POST['message'], $_POST['conversation_id']));
        case 'open-ai-generate-embeddings':
            return mc_json_response(mc_open_ai_embeddings_generate($_POST['paragraphs'], mc_post('save_source')));
        case 'open-ai-file-training':
            return mc_json_response(mc_open_ai_file_training($_POST['url']));
        case 'open-ai-url-training':
            return mc_json_response(mc_open_ai_url_training($_POST['url']));
        case 'open-ai-qea-training':
            return mc_json_response(mc_open_ai_qea_training(mc_post('questions_answers', []), mc_post('language'), mc_post('reset'), mc_post('update_index')));
        case 'open-ai-articles-training':
            return mc_json_response(mc_open_ai_articles_training());
        case 'open-ai-embeddings-delete':
            return mc_json_response(mc_open_ai_embeddings_delete($_POST['sources_to_delete']));
        case 'open-ai-source-file-to-paragraphs':
            return mc_json_response(mc_open_ai_source_file_to_paragraphs($_POST['url']));
        case 'open-ai-troubleshoot':
            return mc_json_response(mc_open_ai_troubleshoot());
        case 'open-ai-html-to-paragraphs':
            return mc_json_response(mc_open_ai_html_to_paragraphs($_POST['url']));
        case 'open-ai-get-training-files':
            return mc_json_response(mc_open_ai_get_training_source_names());
        case 'open-ai-get-qea-training':
            return mc_json_response(mc_get_external_setting('embedding-texts', []));
        case 'open-ai-get-information':
            return mc_json_response(mc_open_ai_embeddings_get_information());
        case 'open-ai-playground-message':
            return mc_json_response(mc_open_ai_playground_message($_POST['messages']));
        case 'open-ai-flows-save':
            return mc_json_response(mc_flows_save($_POST['flows']));
        case 'open-ai-flows-get':
            return mc_json_response(mc_flows_get(mc_post('name')));
        case 'run-flow-on-load':
            return mc_json_response(mc_flows_run_on_load($_POST['message'], $_POST['conversation_id'], mc_post('language')));
        case 'open-ai-get-conversation-embeddings':
            return mc_json_response(mc_open_ai_embeddings_get_conversations());
        case 'open-ai-save-conversation-embeddings':
            return mc_json_response(mc_open_ai_embeddings_save_conversations($_POST['qea']));
        case 'open-ai-send-fallback-message':
            return mc_json_response(mc_open_ai_send_fallback_message($_POST['conversation_id']));
        case 'remove-email-cron':
            return mc_json_response(mc_remove_email_cron($_POST['conversation_id']));
        case 'envato':
            return mc_json_response(mc_envato_purchase_code_validation($_POST['purchase_code'], true));
        case 'get-html':
            return mc_json_response(mc_curl($_POST['url'], '', [], 'GET-SC'));
        case 'get-sitemap-urls':
            return mc_json_response(mc_get_sitemap_urls($_POST['sitemap_url']));
        case 'update-tags':
            return mc_json_response(mc_tags_update($_POST['conversation_id'], mc_post('tags', []), mc_post('add')));
        case 'audio-clip':
            return mc_json_response(mc_audio_clip($_POST['audio']));
        case 'audio-to-text':
            return mc_json_response(mc_open_ai_audio_to_text($_POST['path'], mc_post('audio_language'), mc_post('user_id'), mc_post('message_id'), mc_post('conversation_id')));
        case 'opencart-panel':
            return mc_json_response(mc_opencart_panel($_POST['opencart_id'], mc_post('store_url')));
        case 'opencart-order-details':
            return mc_json_response(mc_opencart_order_details($_POST['order_id']));
        case 'opencart-sync':
            return mc_json_response(mc_opencart_sync());
        case 'update-sw':
            return mc_json_response(mc_update_sw($_POST['url']));
        case 'data-scraping':
            return mc_json_response(mc_open_ai_data_scraping($_POST['conversation_id'], $_POST['prompt_id']));
        case 'assign-conversations-active-agent':
            return mc_json_response(mc_routing_assign_conversations_active_agent());
        case 'whatsapp-clear-flows':
            return mc_json_response(mc_save_external_setting('wa-flows', []));
        case 'generate-sitemap':
            return mc_json_response(mc_generate_sitemap($_POST['url']));
        case 'get-select-phone':
            return mc_json_response(mc_select_phone());
        case 'import-users':
            return mc_json_response(mc_import_users($_POST['file_url']));
        case 'otp':
            return mc_json_response(mc_otp($_POST['email'], mc_post('otp')));
        case 'shopify-cart-sync':
            return mc_json_response(mc_save_external_setting('shopify_cart_' . mc_get_active_user_id(), $_POST['cart']));
        case 'shopify-get-conversation-details':
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            return mc_json_response(shopify_get_conversation_details(mc_post('shopify_id'), $_POST['user_id']));
        case 'shopify-get-product-link':
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            return mc_json_response(shopify_get_product_link($_POST['product_id']));
        case 'shopify-get-products':
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            return mc_json_response(shopify_get_products(mc_post('products_id'), mc_post('key'), mc_post('collection'), mc_post('search'), mc_post('pagination')));
        default:
            return '["error", "Error: No functions found with the given name."]';
    }
}

function mc_json_response($result) {
    if (mc_is_error($result)) {
        return defined('MC_API') ? mc_api_error($result, false) : json_encode(['error', $result->code(), $result->function_name(), $result->message()], JSON_INVALID_UTF8_IGNORE);
    } else {
        $response = defined('MC_API') ? mc_api_success($result) : (mc_is_validation_error($result) ? ['validation-error', $result->code()] : ['success', $result]);
        return empty($GLOBALS['MC_JSON_RAW']) ? json_encode($response, JSON_INVALID_UTF8_IGNORE) : $response;
    }
}

function mc_post($key, $default = false) {
    return isset($_POST[$key]) ? ($_POST[$key] === 'false' ? false : ($_POST[$key] === 'true' ? true : $_POST[$key])) : $default;
}

function mc_security($function) {
    $security = [
        'admin_db' => ['open-ai-source-file-to-paragraphs', 'save-settings', 'update-user', 'get-conversation'],
        'admin' => ['open-ai-save-conversation-embeddings', 'open-ai-get-conversation-embeddings', 'import-users', 'messenger-unsubscribe', 'open-ai-flows-save', 'open-ai-playground-message', 'open-ai-get-information', 'open-ai-url-training', 'open-ai-file-training', 'open-ai-get-training-files', 'update-sw', 'open-ai-articles-training', 'open-ai-embeddings-delete', 'get-sitemap-urls', 'open-ai-source-file-to-paragraphs', 'open-ai-generate-embeddings', 'get-user-by', 'get-agent-department', 'upload-path', 'get-multi-setting', 'save-external-setting', 'get-external-setting', 'update-bot', 'get-last-message', 'delete-attachments', 'open-ai-user-expressions-intents', 'slack-channels', 'twitter-subscribe', 'whatsapp-360-synchronization', 'telegram-synchronization', 'viber-synchronization', 'import-settings', 'export-settings', 'updates-available', 'automations-save', 'path', 'reports-export', 'reports', 'aecommerce-sync-admins', 'aecommerce-sync-sellers', 'aecommerce-sync', 'whmcs-sync', 'whmcs-articles-sync', 'perfex-articles-sync', 'perfex-sync', 'woocommerce-get-session', 'woocommerce-get-attributes', 'woocommerce-get-taxonomies', 'woocommerce-dialogflow-intents', 'woocommerce-dialogflow-entities', 'dialogflow-curl', 'delete-leads', 'system-requirements', 'save-settings', 'get-settings', 'get-all-settings', 'delete-user', 'delete-users', 'app-get-key', 'app-activation', 'app-disable', 'wp-sync'],
        'agent' => ['shopify-get-product-link', 'shopify-get-conversation-details', 'update-conversation-extra', 'generate-sitemap', 'open-ai-html-to-paragraphs', 'google-troubleshoot', 'open-ai-troubleshoot', 'whatsapp-clear-flows', 'assign-conversations-active-agent', 'init-articles-admin', 'data-scraping', 'opencart-order-details', 'opencart-panel', 'open-ai-get-qea-training', 'open-ai-qea-training', 'get-tags', 'add-user', 'get-html', 'envato', 'whatsapp-get-templates', 'automations-is-sent', 'logs', 'newsletter', 'get-last-agent-in-conversation', 'messaging-platforms-send-message', 'open-ai-user-expressions', 'whatsapp-send-template', 'martfury-sync', 'martfury-sync-sellers', 'martfury-get-conversation-details', 'zendesk-update-ticket', 'zendesk-create-ticket', 'zendesk-get-conversation-details', 'dialogflow-knowledge', 'save-articles-categories', 'on-close', 'check-conversations-assignment', 'delete-file', 'dialogflow-smart-reply', 'dialogflow-update-intent', 'dialogflow-get-intents', 'ump-get-conversation-details', 'armember-get-conversation-details', 'count-conversations', 'reports-update', 'get-agents-ids', 'send-custom-email', 'get-users-with-details', 'direct-message', 'messenger-send-message', 'wechat-send-message', 'whatsapp-send-message', 'telegram-send-message', 'viber-send-message', 'zalo-send-message', 'line-send-message', 'twitter-send-message', 'get-user-language', 'get-notes', 'add-note', 'update-note', 'delete-note', 'user-online', 'get-user-from-conversation', 'aecommerce-get-conversation-details', 'whmcs-get-conversation-details', 'woocommerce-get-order', 'woocommerce-coupon-delete-expired', 'woocommerce-coupon-check', 'woocommerce-coupon', 'woocommerce-is-in-stock', 'woocommerce-get-attribute-by-name', 'woocommerce-get-attribute-by-term', 'woocommerce-get-product-taxonomies', 'woocommerce-get-product-images', 'woocommerce-get-product-id-by-name', 'woocommerce-get-user-orders', 'woocommerce-get-product', 'woocommerce-get-customer', 'dialogflow-get-agent', 'dialogflow-get-entity', 'woocommerce-products-popup', 'woocommerce-search-products', 'woocommerce-get-products', 'woocommerce-get-data', 'is-agent-typing', 'close-message', 'count-users', 'get-users', 'get-new-users', 'get-online-users', 'search-users', 'get-conversations', 'get-new-conversations', 'search-conversations', 'csv-users', 'send-test-email', 'slack-users', 'clean-data', 'save-translations', 'dialogflow-intent', 'dialogflow-create-intent', 'dialogflow-entity', 'get-rating', 'save-article', 'update', 'archive-slack-channels'],
        'user' => ['shopify-cart-sync', 'open-ai-send-fallback-message', 'open-ai-flows-get', 'audio-to-text', 'update-tags', 'execute-bot-message', 'remove-email-cron', 'aws-s3', 'is-allowed-extension', 'translate-string', 'dialogflow-human-takeover', 'google-language-detection-update-user', 'google-translate', 'get-agents-in-conversation', 'update-conversation-agent', 'update-conversation-department', 'get-avatar', 'slack-presence', 'woocommerce-waiting-list', 'dialogflow-set-active-context', 'search-user-conversations', 'update-login', 'update-user', 'get-user', 'get-user-extra', 'update-user-to-lead', 'new-conversation', 'get-user-conversations', 'get-new-user-conversations', 'send-slack-message', 'slack-unarchive', 'update-message', 'delete-message', 'update-user-and-message', 'get-conversation', 'get-new-messages', 'set-rating', 'create-email', 'send-email']
    ];
    $user_id = mc_post('user_id', -1);
    $active_user = mc_get_active_user(mc_post('login-cookie'));

    // No check
    $no_check = true;
    foreach ($security as $key => $value) {
        if (in_array($function, $security[$key])) {
            $no_check = false;
            break;
        }
    }
    if ($no_check) {
        return true;
    }

    // Check
    if ($active_user && isset($active_user['user_type'])) {
        $user_type = $active_user['user_type'];
        $current_user_id = mc_isset($active_user, 'id', -2);
        if ($user_id == -1) {
            $user_id = $current_user_id;
            $_POST['user_id'] = $current_user_id;
        }

        // Admin db
        if (mc_is_agent($active_user, true) && in_array($function, $security['admin_db'])) {
            if (!mc_get_active_user(mc_post('login-cookie'), true)) {
                die('invalid-session');
            }
        }

        // User check
        if (in_array($function, $security['user']) && (mc_is_agent($user_type) || $user_id == $current_user_id)) {
            return true;
        }

        // Agent check
        if (in_array($function, $security['agent']) && mc_is_agent($user_type)) {
            return true;
        }

        // Admin check
        if (in_array($function, $security['admin']) && $user_type == 'admin') {
            return true;
        }
    }
    return false;
}

?>

