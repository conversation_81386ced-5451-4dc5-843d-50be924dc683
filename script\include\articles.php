<?php

/*
 * ==========================================================
 * ARTICLES.PHP
 * ==========================================================
 *
 * Articles page.
 * � 2017-2025 app.masichat.com. All rights reserved.
 *
 */

if (defined('MC_CROSS_DOMAIN') && MC_CROSS_DOMAIN) {
    header('Access-Control-Allow-Origin: *');
}
require_once('functions.php');
mc_cloud_load();
$query_category_id = mc_sanatize_string(mc_isset($_GET, 'category'));
$query_article_id = mc_sanatize_string(mc_isset($_GET, 'article_id'));
$query_search = mc_sanatize_string(mc_isset($_GET, 'search'));
$language = mc_sanatize_string(mc_isset($_GET, 'lang', mc_get_user_language()));
$code = '<div class="' . ($query_category_id ? 'mc-subcategories' : ($query_search ? 'mc-articles-search' : 'mc-grid mc-grid-3')) . '">';
$code_script = '';
$css = 'mc-articles-parent-categories-cnt';
$articles_page_url = mc_get_articles_page_url();
$articles_page_url_slash = $articles_page_url . (substr($articles_page_url, -1) == '/' ? '' : '/');
$url_rewrite = $articles_page_url && mc_is_articles_url_rewrite();
$cloud_url_part = defined('ARTICLES_URL') && isset($_GET['chat_id']) ? mc_sanatize_string($_GET['chat_id']) . '/' : '';
$code_breadcrumbs = $articles_page_url ? '<div class="mc-breadcrumbs"><a href="' . $articles_page_url . ($cloud_url_part ? '/' : '') . substr($cloud_url_part, 0, -1) . '">' . mc_t('All categories', $language) . '</a>' : '';
if ($query_category_id) {
    $category = mc_get_article_category($query_category_id);
    if ($category) {
        $category = mc_get_article_category_language($category, $language, $query_category_id);
        $css = 'mc-articles-category-cnt';
        $image = mc_isset($category, 'image');
        if ($code_breadcrumbs) {
            $code_breadcrumbs .= '<i class="mc-icon-arrow-right"></i><a>' . $category['title'] . '</a></div>';
        }
        $code .= $code_breadcrumbs . '<div class="mc-parent-category-box">' . ($image ? '<img src="' . $image . '" />' : '') . '<div><h1>' . $category['title'] . '</h1><p>' . trim(mc_isset($category, 'description', '')) . '</p>' . '</div></div>';
        $articles = mc_get_articles(false, false, false, $query_category_id, $language);
        $articles_by_category = [];
        for ($j = 0; $j < count($articles); $j++) {
            $category = mc_isset($articles[$j], 'category');
            $key = $category && $category != $query_category_id ? $category : 'no-category';
            $articles_by_category_single = mc_isset($articles_by_category, $key, []);
            array_push($articles_by_category_single, $articles[$j]);
            $articles_by_category[$key] = $articles_by_category_single;
        }
        foreach ($articles_by_category as $key => $articles) {
            $category = mc_get_article_category($key);
            $code .= '<div class="mc-subcategory-box">' . ($category ? '<a href="' . ($url_rewrite ? $articles_page_url_slash . $cloud_url_part . 'category/' . $category['id'] : $articles_page_url . '?category=' . $category['id'] . $cloud_url_part) . '" class="mc-subcategory-title"><h2>' . $category['title'] . '</h2><p>' . trim(mc_isset($category, 'description', '')) . '</p></a>' : '') . '<div class="mc-subcategory-articles">';
            for ($j = 0; $j < count($articles); $j++) {
                $code .= '<a class="mc-icon-arrow-right" href="' . ($url_rewrite ? $articles_page_url_slash . $cloud_url_part . mc_isset($articles[$j], 'slug', $articles[$j]['id']) : $articles_page_url . '?article_id=' . $articles[$j]['id'] . $cloud_url_part) . '">' . $articles[$j]['title'] . '</a>';
            }
            $code .= '</div></div>';
        }
    }
} else if ($query_article_id) {
    $css = 'mc-article-cnt';
    $article = mc_get_articles($query_article_id, false, true);
    if ($article) {
        $article = $article[0];
        if ($code_breadcrumbs) {
            $article_categories = [mc_isset($article, 'parent_category'), mc_isset($article, 'category')];
            for ($i = 0; $i < 2; $i++) {
                if ($article_categories[$i]) {
                    $category = mc_get_article_category_language(mc_get_article_category($article_categories[$i]), $language, $article_categories[$i]);
                    $code_breadcrumbs .= '<i class="mc-icon-arrow-right"></i><a href="' . ($url_rewrite ? $articles_page_url_slash . $cloud_url_part . 'category/' . $article_categories[$i] : $articles_page_url . '?category=' . $article_categories[$i] . $cloud_url_part) . '">' . $category['title'] . '</a>';
                }
            }
            $code_breadcrumbs .= '<i class="mc-icon-arrow-right"></i><a>' . $article['title'] . '</a></div>';
        }
        $code = $code_breadcrumbs . '<div data-id="' . $article['id'] . '" class="mc-article"><div class="mc-title">' . $article['title'] . '</div>';
        $code .= '<div class="mc-content">' . $article['content'] . '</div>';
        if (!empty($article['link'])) {
            $code .= '<a href="' . $article['link'] . '" target="_blank" class="mc-btn-text"><i class="mc-icon-plane"></i>' . mc_t('Read more', $language) . '</a>';
        }
        $code .= '<div class="mc-rating mc-rating-ext"><span>' . mc_t('Rate and review', $language) . '</span><div>';
        $code .= '<i data-rating="positive" class="mc-submit mc-icon-like"><span>' . mc_t('Helpful', $language) . '</span></i>';
        $code .= '<i data-rating="negative" class="mc-submit mc-icon-dislike"><span>' . mc_t('Not helpful', $language) . '</span></i>';
        $code .= '</div></div></div>';
        $code_script = 'let user_rating = false; $(document).on(\'MCInit\', function () { user_rating = MCF.storage(\'article-rating-' . $article['id'] . '\'); if (user_rating) $(\'.mc-article\').attr(\'data-user-rating\', user_rating); });  $(\'.mc-article\').on(\'click\', \'.mc-rating-ext [data-rating]\', function (e) { MCChat.articleRatingOnClick(this); e.preventDefault(); return false; });';
    }
} else if ($query_search) {
    $css = 'mc-article-search-cnt';
    $articles = mc_search_articles($query_search, $language);
    $count = count($articles);
    $code .= '<h2 class="mc-articles-search-title">' . mc_t('Search results for:', $language) . ' <span>' . $query_search . '</span></h2><div class="mc-search-results">';
    for ($i = 0; $i < $count; $i++) {
        $code .= '<a href="' . ($url_rewrite ? $articles_page_url_slash . $cloud_url_part . mc_isset($articles[$i], 'slug', $articles[$i]['id']) : $articles_page_url . '?article_id=' . $articles[$i]['id'] . $cloud_url_part) . '"><h3>' . $articles[$i]['title'] . '</h3><p>' . $articles[$i]['content'] . '</p></a>';
    }
    if (!$count) {
        $code .= '<p>' . mc_t('No results found.', $language) . '</p>';
    }
    $code .= '</div>';
} else {
    $categories = mc_get_articles_categories('parent');
    $count = count($categories);
    if ($count) {
        for ($i = 0; $i < count($categories); $i++) {
            $category = $categories[$i];
            $image = mc_isset($category, 'image');
            $title = mc_isset($category, 'title');
            $description = mc_isset($category, 'description');
            if ($language) {
                $translations = mc_isset(mc_isset($category, 'languages', []), $language);
                if ($translations) {
                    $title = mc_isset($translations, 'title', $title);
                    $description = mc_isset($translations, 'description', $description);
                }
            }
            $code .= '<a href="' . ($url_rewrite ? $articles_page_url_slash . $cloud_url_part . 'category/' . $category['id'] : $articles_page_url . '?category=' . $category['id'] . $cloud_url_part) . '">' . ($image ? '<img src="' . $image . '" />' : '') . '<h2>' . $title . '</h2><p>' . $description . '</p></a>';
        }
    } else {
        $code .= '<p>' . mc_t('No results found.', $language) . '</p>';
    }
}
if (mc_get_setting('rtl') || in_array(mc_get_user_language(), ['ar', 'he', 'ku', 'fa', 'ur'])) {
    $css .= ' mc-rtl';
}
$code .= '</div>';

function mc_get_article_category_language($category, $language, $category_id) {
    if (isset($category['languages'][$language])) {
        return $category['languages'][$language];
    }
    if (mc_get_multi_setting('google', 'google-multilingual-translation')) {
        $translations = [$category['title']];
        if (isset($category['description'])) {
            array_push($translations, $category['description']);
        }
        $translations = mc_google_translate($translations, $language);
        $category['title'] = $translations[0][0];
        $category['description'] = mc_isset($translations[0], 1, '');
        $articles_categories = mc_get_articles_categories();
        for ($i = 0; $i < count($articles_categories); $i++) {
            if ($articles_categories[$i]['id'] == $category_id) {
                $articles_categories[$i]['languages'][$language] = $category;
                mc_save_articles_categories($articles_categories);
            }
        }
    }
    return $category;
}

?>

<div class="mc-articles-page <?php echo $css ?>">
    <div class="mc-articles-header">
        <div>
            <h1>
                <?php mc_e(mc_get_setting('articles-title', 'Help Center')) ?>
            </h1>
            <div class="mc-input mc-input-btn">
                <input placeholder="<?php mc_e('Search for articles...') ?>" autocomplete="off" />
                <div class="mc-search-articles mc-icon-search"></div>
            </div>
        </div>
    </div>
    <div class="mc-articles-body">
        <?php echo $code ?>
    </div>
</div>
<?php mc_js_global() ?>
<script>
    $('.mc-search-articles').on('click', function () {
        document.location.href = '<?php echo ($articles_page_url ? $articles_page_url : '') . (defined('ARTICLES_URL') && isset($_GET['chat_id']) ? (substr($articles_page_url, -1) == '/' ? '' : '/') . $_GET['chat_id'] . '/' : '') . '?search=\' + $(this).prev().val();' ?>
    });
    <?php echo $code_script ?>
</script>
