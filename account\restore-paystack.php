<?php
/**
 * PayStack Integration Restoration Script
 * 
 * This script restores PayStack integration files from a backup.
 */

// Include the main functions file
require_once('functions.php');

// Check if user is super admin
if (!super_admin()) {
    die('Access denied. Super admin privileges required.');
}

// Process restoration if requested
$restore_result = null;
$backup_dirs = [];

// Get all backup directories
$base_path = MC_CLOUD_PATH;
$items = scandir($base_path);
foreach ($items as $item) {
    if (is_dir($base_path . '/' . $item) && strpos($item, 'paystack_backup_') === 0) {
        $backup_dirs[] = $item;
    }
}

// Sort backup directories by date (newest first)
usort($backup_dirs, function($a, $b) {
    return strcmp($b, $a);
});

// Process restoration if requested
if (isset($_POST['action']) && $_POST['action'] === 'restore' && isset($_POST['backup_dir'])) {
    $backup_dir = $_POST['backup_dir'];
    
    // Security check - make sure the backup directory is valid
    if (strpos($backup_dir, 'paystack_backup_') !== 0 || !is_dir($base_path . '/' . $backup_dir)) {
        die('Invalid backup directory');
    }
    
    // Get list of files in the backup directory
    $backed_up_files = [];
    scan_directory($base_path . '/' . $backup_dir, $backed_up_files, strlen($base_path . '/' . $backup_dir) + 1);
    
    // Restore files
    $restore_result = restore_files($backed_up_files, $base_path . '/' . $backup_dir, $base_path);
}

/**
 * Recursively scan a directory and get all files
 */
function scan_directory($dir, &$results, $prefix_len) {
    $items = scandir($dir);
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }
        
        $path = $dir . '/' . $item;
        if (is_dir($path)) {
            scan_directory($path, $results, $prefix_len);
        } else {
            $results[] = substr($path, $prefix_len);
        }
    }
}

/**
 * Restore files from backup
 */
function restore_files($files, $backup_dir, $target_dir) {
    $restored_files = [];
    $failed_files = [];
    
    foreach ($files as $file) {
        $backup_file_path = $backup_dir . '/' . $file;
        $target_file_path = $target_dir . '/' . $file;
        
        // Create directory structure if it doesn't exist
        $target_file_dir = dirname($target_file_path);
        if (!file_exists($target_file_dir)) {
            if (!mkdir($target_file_dir, 0755, true)) {
                $failed_files[] = ['file' => $file, 'reason' => 'Failed to create directory'];
                continue;
            }
        }
        
        // Copy the file from backup to target
        if (copy($backup_file_path, $target_file_path)) {
            $restored_files[] = $file;
        } else {
            $failed_files[] = ['file' => $file, 'reason' => 'Failed to copy file'];
        }
    }
    
    return [
        'status' => empty($failed_files) ? 'success' : 'partial',
        'restored' => $restored_files,
        'failed' => $failed_files
    ];
}

// Output the result
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restore PayStack Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .warning {
            background-color: #fcf8e3;
            border-left: 4px solid #f0ad4e;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success {
            background-color: #dff0d8;
            border-left: 4px solid #5cb85c;
            padding: 15px;
            margin-bottom: 20px;
        }
        .partial {
            background-color: #fcf8e3;
            border-left: 4px solid #f0ad4e;
            padding: 15px;
            margin-bottom: 20px;
        }
        .error {
            background-color: #f2dede;
            border-left: 4px solid #d9534f;
            padding: 15px;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-danger {
            background-color: #e74c3c;
        }
        .btn-danger:hover {
            background-color: #c0392b;
        }
        select {
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            width: 100%;
            margin-bottom: 20px;
            font-size: 16px;
        }
        .file-list {
            margin-bottom: 20px;
        }
        .file-list h3 {
            margin-bottom: 10px;
        }
        .file-list ul {
            list-style-type: none;
            padding-left: 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .file-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .file-list li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Restore PayStack Integration</h1>
        
        <?php if ($restore_result): ?>
            <?php if ($restore_result['status'] === 'success'): ?>
                <div class="success">
                    <h2>Restoration Completed Successfully!</h2>
                    <p>All PayStack integration files have been restored successfully.</p>
                </div>
            <?php elseif ($restore_result['status'] === 'partial'): ?>
                <div class="partial">
                    <h2>Restoration Partially Completed</h2>
                    <p>Some files could not be restored. See details below.</p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h2>Restoration Failed</h2>
                    <p>Error: <?php echo htmlspecialchars($restore_result['message']); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="file-list">
                <h3>Successfully Restored Files</h3>
                <?php if (empty($restore_result['restored'])): ?>
                    <p>No files were restored successfully.</p>
                <?php else: ?>
                    <ul>
                        <?php foreach ($restore_result['restored'] as $file): ?>
                            <li><?php echo htmlspecialchars($file); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            
            <?php if (!empty($restore_result['failed'])): ?>
                <div class="file-list">
                    <h3>Failed Restorations</h3>
                    <ul>
                        <?php foreach ($restore_result['failed'] as $failure): ?>
                            <li>
                                <?php echo htmlspecialchars($failure['file']); ?>: 
                                <?php echo htmlspecialchars($failure['reason']); ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <p>
                <a href="super.php" class="btn">Return to Admin Panel</a>
            </p>
        <?php else: ?>
            <?php if (empty($backup_dirs)): ?>
                <div class="error">
                    <h2>No Backups Found</h2>
                    <p>No PayStack integration backups were found. Please create a backup first.</p>
                </div>
                
                <p>
                    <a href="backup-paystack.php" class="btn">Create Backup</a>
                    <a href="super.php" class="btn" style="margin-left: 10px;">Return to Admin Panel</a>
                </p>
            <?php else: ?>
                <div class="warning">
                    <h2>Important: Choose Backup to Restore</h2>
                    <p>This will restore PayStack integration files from the selected backup. Any current files will be overwritten.</p>
                    <p><strong>It's recommended to create a new backup of your current files before restoring, in case you need to revert back.</strong></p>
                </div>
                
                <form method="post" onsubmit="return confirm('Are you sure you want to restore from this backup? Current files will be overwritten.');">
                    <input type="hidden" name="action" value="restore">
                    
                    <label for="backup_dir"><strong>Select Backup to Restore:</strong></label>
                    <select name="backup_dir" id="backup_dir">
                        <?php foreach ($backup_dirs as $dir): ?>
                            <option value="<?php echo htmlspecialchars($dir); ?>">
                                <?php 
                                // Format the date from the directory name
                                $date_str = str_replace('paystack_backup_', '', $dir);
                                $date_parts = explode('_', $date_str);
                                if (count($date_parts) === 2) {
                                    $date = $date_parts[0];
                                    $time = str_replace('-', ':', $date_parts[1]);
                                    echo htmlspecialchars($date . ' ' . $time);
                                } else {
                                    echo htmlspecialchars($dir);
                                }
                                ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    
                    <button type="submit" class="btn btn-danger">Restore from Selected Backup</button>
                    <a href="super.php" style="margin-left: 10px; color: #3498db;">Cancel</a>
                </form>
                
                <p style="margin-top: 20px;">
                    <a href="backup-paystack.php" class="btn">Create New Backup</a>
                </p>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>
