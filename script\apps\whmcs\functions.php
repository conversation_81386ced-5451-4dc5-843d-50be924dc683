<?php

/*
 * ==========================================================
 * WHMCS APP
 * ==========================================================
 *
 * WHMCS app. � 2017-2025 app.masichat.com. All rights reserved.
 *
 */

define('MC_WHMCS', '1.0.4');

/*
 * ----------------------------------------------------------
 * DATABASE
 * ----------------------------------------------------------
 *
 */

function mc_whmcs_db_connect() {
    return mc_external_db('connect', 'whmcs');
}

function mc_whmcs_db_get($query, $single = true) {
    return mc_external_db('read', 'whmcs', $query, $single);
}

function mc_whmcs_db_query($query, $return = false) {
    return mc_external_db('write', 'whmcs', $query, $return);
}

/*
 * -----------------------------------------------------------
 * PANEL DATA
 * -----------------------------------------------------------
 *
 * Return the user details for the conversations panel
 *
 */

function mc_whmcs_get_conversation_details($whmcs_id) {
    $services_count = 0;
    $total = 0;
    $products = [];
    $addons = [];
    $domains = [];
    $whmcs_id = mc_db_escape($whmcs_id);
    $client_id = mc_isset(mc_whmcs_db_get('SELECT A.id FROM tblclients A, tblusers B WHERE A.email = B.email and B.id = ' . $whmcs_id), 'id', $whmcs_id);

    // Total
    $invoices = mc_whmcs_db_get('SELECT subtotal FROM tblinvoices WHERE status = "Paid" AND userid = ' . $whmcs_id, false);
    for ($i = 0; $i < count($invoices); $i++) {
        $total += floatval($invoices[$i]['subtotal']);
    }

    // Services
    $products = mc_whmcs_db_get('SELECT B.name FROM tblhosting A, tblproducts B WHERE A.packageid = B.id AND A.userid = ' . $whmcs_id, false);
    $addons = mc_whmcs_db_get('SELECT B.name FROM tblhostingaddons A, tbladdons B WHERE A.addonid = B.id AND A.userid = ' . $whmcs_id, false);
    $domains = mc_whmcs_db_get('SELECT domain AS `name` FROM tbldomains WHERE userid = ' . $whmcs_id, false);
    $services_count = count($products) + count($addons) + count($domains);

    return ['total' => round($total, 2), 'services_count' => $services_count, 'products' => $products, 'addons' => $addons, 'domains' => $domains, 'currency_symbol' => mc_get_setting('whmcs-currency-symbol', ''), 'client-id' => $client_id];
}

/*
 * -----------------------------------------------------------
 * USERS
 * -----------------------------------------------------------
 *
 * 1. Return an admin
 * 2. Return a user
 * 3. Get the active WHMCS user and register it if required
 * 4. Function used internally by mc_get_active_user()
 * 5. Get all users
 * 6. Sync users
 *
 */

function mc_whmcs_get_admin($admin_id) {
    return mc_whmcs_db_get('SELECT id, username, password, firstname AS `first_name`, lastname AS `last_name`, email FROM tbladmins WHERE id = ' . mc_db_escape($admin_id));
}

function mc_whmcs_get_user($user_id) {
    return mc_whmcs_db_get('SELECT id, first_name, last_name, email, password FROM tblusers WHERE id = ' . mc_db_escape($user_id));
}

function mc_whmcs_get_active_user($user_id) {
    $user = mc_whmcs_get_user($user_id);
    $query = '';
    if ($user && isset($user['email'])) {
        $query = 'SELECT id, token FROM mc_users WHERE email ="' . $user['email'] . '" LIMIT 1';
        $user_db = mc_db_get($query);
        if ($user_db === '') {
            $settings_extra = ['whmcs-id' => [$user['id'], 'Whmcs ID']];
            $active_user = mc_get_active_user();
            if ($active_user && ($active_user['user_type'] == 'lead' || $active_user['user_type'] == 'visitor')) {
                mc_update_user($active_user['id'], $user, $settings_extra, false);
            } else {
                mc_add_user($user, $settings_extra, false);
            }
            $user = mc_db_get($query);
        } else {
            $user = $user_db;
        }
        if (mc_is_error($user) || !isset($user['token']) || !isset($user['id'])) {
            return false;
        } else {
            return mc_login('', '', $user['id'], $user['token']);
        }
    } else {
        return false;
    }
}

function mc_whmcs_get_active_user_function($return, $login_app) {
    if ($return === false) {
        $return = mc_whmcs_get_active_user($login_app);
    } else {
        $user = mc_whmcs_get_user($login_app);
        if (mc_is_error($user))
            die($user);
        if (isset($user['email']) && $user['email'] != $return['email']) {
            $return = mc_whmcs_get_active_user($login_app);
        }
    }
    if (isset($return[1])) {
        $return = array_merge($return[0], ['cookie' => $return[1]]);
    }
    return $return;
}

function mc_whmcs_get_all_users() {
    return mc_whmcs_db_get('SELECT id, first_name, last_name, email, password FROM tblusers', false);
}

function mc_whmcs_sync() {
    $users = mc_whmcs_get_all_users();
    if (mc_is_error($users))
        return $users;
    for ($i = 0; $i < count($users); $i++) {
        mc_add_user($users[$i], ['whmcs-id' => [$users[$i]['id'], 'Whmcs ID']], false);
    }
    return true;
}

/*
 * ----------------------------------------------------------
 * KNOWLEDGE BASE ARTICLES
 * ----------------------------------------------------------
 *
 * Import articles into Masi Chat
 *
 */

function mc_whmcs_articles_sync() {
    $articles = mc_get_articles(false, false, true);
    $article_titles = [];
    for ($i = 0; $i < count($articles); $i++) {
        array_push($article_titles, $articles[$i]['title']);
    }
    $whmcs_articles = mc_whmcs_db_get('SELECT title, article FROM tblknowledgebase', false);
    if (mc_is_error($whmcs_articles)) {
        return $whmcs_articles;
    }
    for ($i = 0; $i < count($whmcs_articles); $i++) {
        if (!in_array($whmcs_articles[$i]['title'], $article_titles)) {
            mc_save_article(['title' => $whmcs_articles[$i]['title'], 'content' => $whmcs_articles[$i]['article']]);
        }
    }
    return true;
}

?>