<?php

/*
 * ==========================================================
 * OPENCART APP
 * ==========================================================
 *
 * OpenCart app. � 2017-2025 app.masichat.com. All rights reserved.
 *
 */

define('MC_OPENCART', '1.0.0');

function mc_opencart_panel($opencart_id, $store_url = false) {
    $total = 0;
    $customer_id = explode('|', $opencart_id)[0];
    $cart = mc_opencart_curl('api/mc/cart', '&customer_id=' . $customer_id);
    $orders = mc_opencart_curl('api/mc/orders', '&customer_id=' . $customer_id);
    $count = count($cart);
    $count_orders = count($orders);
    for ($i = 0; $i < count($orders); $i++) {
        $total += round($orders[$i]['total'], 2);
    }
    $code = '<i class="mc-icon-refresh"></i><h3>OpenCart</h3><div><div class="mc-split"><div><div class="mc-title">' . mc_('Number of orders') . '</div><span>' . $count_orders . ' ' . mc_('orders') . '</span></div><div><div class="mc-title">' . mc_('Total spend') . '</div><span>' . mc_get_multi_setting('opencart-details', 'opencart-details-currency') . ' ' . $total . '</span></div></div><div class="mc-title">' . mc_('Cart') . '</div><div class="mc-list-items mc-list-links mc-opencart-cart">';
    if ($count) {
        for ($i = 0; $i < $count; $i++) {
            $product = $cart[$i];
            $id = $product['product_id'];
            $url = $product['url'];
            if ($store_url && strpos($url, $store_url) === false) {
                $url = 'https://' . $store_url . '/index.php?route=product/product&product_id=' . $id;
            }
            $code .= '<a href="' . $url . '" target="_blank" data-id="' . $id . '"><span>#' . $id . '</span> <span>' . $product['name'] . '</span> <span>x ' . $product['quantity'] . '</span></a>';
        }
    } else {
        $code .= '<p>' . mc_('The cart is currently empty.') . '</p>';
    }
    $code .= '</div>';
    if ($count_orders) {
        $code .= '<div class="mc-title">' . mc_('Orders') . '</div><div class="mc-list-items mc-list-links mc-opencart-orders">';
        for ($i = 0; $i < $count_orders; $i++) {
            $order = $orders[$i];
            $code .= '<a data-id="' . $order['order_id'] . '"><span>#' . $order['order_id'] . '</span> <span>' . $order['date_added'] . '</span> <span>' . $order['currency_code'] . ' ' . round($order['total']) . '</span></a>';
        }
        $code .= '</div>';
    }
    return $code;
}

function mc_opencart_order_details($order_id) {
    $order = mc_opencart_curl('api/mc/orderdetails', '&order_id=' . $order_id);
    $code = '<div class="mc-bold-list"><p>';
    $products = $order['products'];
    $count = count($products);
    unset($order['products']);
    unset($order['user_agent']);
    unset($order['accept_language']);
    foreach ($order as $key => $value) {
        if (!empty($value) && $value != '[]') {
            $code .= '<b>' . mc_string_slug($key, 'string') . '</b> ' . $value . '<br />';
        }
    }
    if ($count) {
        $code .= '<b class="oc-b-products">' . mc_('Products') . '</b><span class="mc-panel-details"><span class="mc-list-items mc-list-links">';
        for ($i = 0; $i < $count; $i++) {
            $product = $products[$i];
            $code .= '<a href="' . $product['url'] . '" target="_blank" data-id="' . $product['product_id'] . '"><span>#' . $product['product_id'] . '</span> <span>' . $product['name'] . '</span> <span>x ' . $product['quantity'] . '</span></a>';
        }
        $code . '</span></span></p>';
    }
    return $code . '</div>';
}

function mc_opencart_curl($route, $parameters = '', $post_fields = []) {
    $open_cart = mc_get_setting('opencart-details');
    $token = mc_get_external_setting('opencart-token');
    $url = mc_isset($open_cart, 'opencart-details-url');
    if (!$open_cart || !$url) {
        return mc_error('OpenCart details not found', 'mc_opencart_curl', 'Enter the details in Settings > OpenCart > OpenCart details.', true);
    }
    if (substr($url, -1) == '/') {
        $url = substr($url, 0, -1);
    }
    if (!$token) {
        $response = mc_curl($url . '/index.php?route=api/login', ['username' => mc_isset($open_cart, 'opencart-details-user', 'Default'), 'key' => $open_cart['opencart-details-api-key']], ['Content-Type: multipart/form-data']);
        $token = mc_isset($response, 'api_token');
        if ($token) {
            mc_save_external_setting('opencart-token', $token);
        } else {
            return mc_error('Invalid API token', 'mc_opencart_curl', json_encode($response), true);
        }
    }
    return mc_curl($url . '/index.php?route=' . $route . '&api_token=' . $token . $parameters, $post_fields, ['Content-Type: multipart/form-data']);
}

function mc_opencart_sync() {
    $customers = mc_opencart_curl('api/mc/customers');
    for ($i = 0; $i < count($customers); $i++) {
        $customer = $customers[$i];
        if (!mc_get_user_by('email', $customer['email'])) {
            mc_add_user(['first_name' => $customer['firstname'], 'last_name' => $customer['lastname'], 'email' => $customer['email']], ['phone' => [$customer['telephone'], 'Phone'], 'opencart_id' => [$customer['customer_id'], 'OpenCart ID'], 'opencart_store' => [$customer['name'], 'OpenCart Store'], 'opencart_store_url' => [$customer['url'], 'OpenCart Store URL']]);
        }
    }
    return true;
}

?>