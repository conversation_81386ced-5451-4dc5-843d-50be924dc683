<?php

/*
 * ==========================================================
 * VIBER POST.PHP
 * ==========================================================
 *
 * Viber response listener. This file receive the messages sent to the Viber bot. This file requires the Viber App.
 * © 2017-2025 app.masichat.com. All rights reserved.
 *
 */

$raw = file_get_contents('php://input');
flush();
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
}
$response = json_decode($raw, true);
if ($response) {
    require('../../include/functions.php');
    if (isset($response['event']) && $response['event'] == 'message') {
        $GLOBALS['MC_FORCE_ADMIN'] = true;
        mc_cloud_load_by_url();
        $sender = $response['sender'];
        $viber_id = $sender['id'];
        $message = $response['message'];
        $message_text = mc_isset($message, 'text', '');
        $attachments = [];
        $token = mc_get_multi_setting('viber', 'viber-token');
        $user_id = false;

        // User and conversation
        $user = mc_get_user_by('viber-id', $viber_id);
        if (!$user) {
            $extra = ['viber-id' => [$viber_id, 'Viber ID']];
            if (!empty($sender['country'])) {
                $country_codes = mc_get_json_resource('json/country_codes.json');
                if (isset($country_codes[$sender['country']])) {
                    $extra['country'] = [$country_codes[$sender['country']], 'Country'];
                }
            }
            if (!empty($sender['language'])) {
                $extra['language'] = [mc_language_code($sender['language']), 'Language'];
            } else if ($message_text && defined('MC_DIALOGFLOW')) $extra['language'] = mc_google_language_detection_get_user_extra($message_text);
            $user_id = mc_add_user(['first_name' => $sender['name'], 'last_name' => '', 'profile_image' => empty($sender['avatar']) ? '' : mc_download_file($sender['avatar']), 'user_type' => 'lead'], $extra);
            $user = mc_get_user($user_id);
        } else {
            $user_id = $user['id'];
            $conversation_id = mc_isset(mc_db_get('SELECT id FROM mc_conversations WHERE source = "vb" AND user_id = ' . $user_id . ' ORDER BY id DESC LIMIT 1'), 'id');
        }
        $GLOBALS['MC_LOGIN'] = $user;
        if (!$conversation_id) {
            $conversation_id = mc_isset(mc_new_conversation($user_id, 2, '', mc_get_setting('viber-department'), -1, 'vb', $chat_id), 'details', [])['id'];
        }

        // Attachments
        switch ($message['type']) {
            case 'file':
            case 'video':
        	case 'picture':
                array_push($attachments, [$message['file_name'], mc_download_file($message['media'], $message['file_name'])]);
                break;
            case 'sticker':
                array_push($attachments, ['', $message['media']]);
                break;
            case 'location':
                $message_text .= ($message_text ? PHP_EOL  : '') . 'https://www.google.com/maps/search/?api=1&query=' . $message['location']['lat'] . ',' . $message['location']['lon'];
                break;
        }

        // Send message
        $response = mc_send_message($user_id, $conversation_id, $message_text, $attachments, false, json_encode(['message_token' => $response['message_token']]));

        // Dialogflow, Notifications, Bot messages
        $response_external = mc_messaging_platforms_functions($conversation_id, $message_text, $attachments, $user, ['source' => 'vb', 'viber_id' => $viber_id]);

        // Queue
        if (mc_get_multi_setting('queue', 'queue-active')) {
            mc_queue($conversation_id, mc_get_setting('viber-department'));
        }

        // Online status
        mc_update_users_last_activity($user_id);

        $GLOBALS['MC_FORCE_ADMIN'] = false;
    }
}
die();

?>