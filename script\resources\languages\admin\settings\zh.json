{"{product_name} has no {product_attribute_name} variants.": "{product_name}缺少{product_attribute_name}属性。", "360dialog settings": "360对话框设置", "360dialog template": "360度对话框模板", "Abandoned cart notification": "取消购物车通知", "Abandoned cart notification - Admin email": "取消购物车通知-管理员邮件", "Abandoned cart notification - First email": "取消购物车通知-第一封邮件", "Abandoned cart notification - Second email": "取消购物车通知-第二封邮件", "Accept button text": "接受按钮文本", "Account SID": "账号SID", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "管理员后台启用从右到左(RTL)文字布局", "Activate the Right-To-Left (RTL) reading layout.": "前端启用从右到左(RTL)文字布局", "Activate the Slack integration.": "启用Slack支持", "Activate the Zendesk integration": "激活 Zendesk 集成", "Activate this option if you don't want to translate the settings area.": "如果您不想翻译设置区域，请激活此选项。", "Active": "启用", "Active - admin": "活动 - 管理员", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce CMS网站链接，例如：https://shop.com/", "Active eCommerce URL": "Active eCommerce链接", "Active for agents": "为客服启用", "Active for users": "为用户启用", "Active webhooks": "活动网络挂钩", "Add a delay (ms) to the bot's responses. Default is 2000.": "为机器人的响应添加延迟（毫秒）。默认值为 2000。", "Add and manage additional support departments.": "增加和管理额外的支持部门", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "添加和管理保存的回复，可供代理在聊天编辑器中使用。可以通过键入 # 后跟回复名称加空格来打印保存的回复。使用 \\n 进行换行。", "Add and manage tags.": "添加和管理标签。", "Add comma separated WordPress user roles. The Masi Chat administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "增加WordPress用户角色，以英文逗号(,)分隔。新增的角色将可以访问客服系统管理后台。默认可访问管理后台的角色是：editor, administrator, author", "Add custom fields to the new ticket form.": "为[创建工单]表单增加自定义字段", "Add custom fields to the user profile details.": "为用户资料增加自定义字段", "Add Intents": "添加 Intents", "Add Intents to saved replies": "将 Intents 添加到已保存的回复中", "Add WhatsApp phone number details here.": "在此处添加 WhatsApp 电话号码详细信息。", "Adjust the chat button position. Values are in px.": "调整对话按钮位置。数值单位为像素px", "Admin icon": "管理员图标", "Admin IDs": "管理员 ID", "Admin login logo": "管理员登录页LOGO", "Admin login message": "管理员登录消息", "Admin notifications": "管理员通知", "Admin title": "管理员标题", "Agent area": "客服面板", "Agent details": "客服详情", "Agent email notifications": "客服邮件通知", "Agent ID": "代理编号", "Agent linking": "代理链接", "Agent message template": "客服消息模板", "Agent notification email": "客服通知邮箱", "Agent privileges": "代理权限", "Agents": "客服", "Agents and admins tab": "代理和管理员选项卡", "Agents menu": "客服分流菜单", "Agents only": "仅限客服", "All": "全部", "All channels": "所有频道", "All messages": "全部消息", "All questions": "所有问题", "Allow only extended licenses": "仅允许扩展许可证", "Allow only one conversation": "只允许进行一个对话", "Allow only one conversation per user.": "每个用户只允许进行一个对话", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "如果答案已知并且电子邮件管道处于活动状态，则允许聊天机器人回复用户的电子邮件。", "Allow the chatbot to reply to the user's text messages if the answer is known.": "如果答案已知，则允许聊天机器人回复用户的短信。", "Allow the user to archive a conversation and hide archived conversations.": "允许用户存档对话并隐藏存档的对话。", "Allow users to contact you via their favorite messaging apps.": "允许用户通过他们最喜欢的消息应用与您联系。", "Allow users to select a product on ticket creation.": "允许用户在创建工单时选择产品。", "Always all messages": "始终允许所有消息", "Always incoming messages only": "始终只允许接收消息", "Always sort conversations by date in the admin area.": "始终在管理区域中按日期对对话进行排序。", "API key": "API密钥", "Append the registration user details to the success message.": "在注册成功提示内附加用户账号详情", "Apply a custom background image for the header area.": "在页面头部应用自定义背景图片", "Apply changes": "应用更改", "Apply to": "应用到", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "归档 Slack 应用程序中的所有用户频道。此操作可能需要很长时间才能完成。重要提示：您的所有闲置频道都将被存档。", "Archive automatically the conversations marked as read every 24h.": "每24小时自动将已读对话移动至归档对话", "Archive channels": "存档频道", "Archive channels now": "立即存档频道", "Articles": "知识库", "Articles area": "文章区", "Articles button link": "文章按钮链接", "Articles page URL": "文章页面 URL", "Artificial Intelligence": "人工智能", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "为从 Google Business Messages 发起的所有对话分配一个部门。输入部门 ID。", "Assign a department to all conversations started from Twitter. Enter the department ID.": "为从 Twitter 发起的所有对话分配一个部门。输入部门 ID。", "Assign a department to all conversations started from Viber. Enter the department ID.": "为从 Viber 发起的所有对话分配一个部门。输入部门 ID。", "Assign a department to all conversations started from WeChat. Enter the department ID.": "为所有从微信发起的对话分配一个部门。输入部门 ID。", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "将不同部门分配给从不同 Google Business Messages 位置发起的对话。此设置会覆盖默认部门。", "Assistant": "助手", "Assistant ID": "助理ID", "Attachments list": "附件清单", "Audio file URL - admin": "音频文件 URL - admin", "Automatic": "自动的", "Automatic human takeover": "自动人工接管", "Automatic translation": "自动翻译", "Automatic updates": "自动更新", "Automatically archive conversations": "自动归档对话", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "基于用户的套餐自动分配一个部门。没有套餐的用户请输入-1作为套餐ID", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "自动检查和安装更新。需输入有效的Envato购买代码和应用的许可证", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "自动折叠管理区域的对话详细信息面板和其他面板。", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "自动为每个网站创建一个部门并分流各网站的对话到对应部门。此设置需要开启WordPress多站点", "Automatically hide the conversation details panel.": "自动隐藏对话详情面板。", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "自动向有购物车商品的用户发送提醒。你可以在消息中插入下列模板字段：{coupon}, {discount_price}, {original_price}, {product_names}, {user_name}。", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "自动与 {R} 同步 Zendesk 客户、查看 Zendesk 票证或创建新票证，而无需离开 {R}。", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "自动同步产品、分类、标签等信息到Dialogflow，并启用店铺机器人自动应答。", "Automatically translate admin area": "自动翻译管理员后台", "Automatically translate the admin area to match the agent profile language or browser language.": "自动翻译管理员后台以适配客服个人档案设置语言或浏览器语言", "Avatar image": "头像图片", "Away mode": "离开模式", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "在开始聊天之前，用户必须接受隐私消息才能获得访问权限。", "Birthday": "生日", "Body variables": "身体变量", "Bot name": "机器人名称", "Bot profile image": "机器人头像", "Bot response delay": "机器人回复延迟", "Bottom": "底部", "Brand": "品牌语", "Built-in chat button icons": "内置聊天按钮图标", "Business Account ID": "企业帐户 ID", "Button action": "按键操作", "Button name": "按键名称", "Button text": "按键文本", "Button variables": "按钮变量", "Cancel button text": "取消按键文本", "Cart": "大车", "Cart follow up message": "购物车提醒消息", "Catalogue details": "目录详情", "Catalogue ID": "目录编号", "Change the chat button image with a custom one.": "更改为自定义按键图像", "Change the default field names.": "更改默认工单字段的名称", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "更改聊天窗口顶部显示的消息文本。这段文本将在收到第一条回复后被客服问候语取代。", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "更改聊天窗口顶部标题文本。这个文本将在收到第一条回复后被客服的名称取代。默认显示为[<PERSON><PERSON> Chat]", "Channel ID": "频道 ID", "Channels": "频道", "Channels filter": "频道过滤器", "Chat": "聊天", "Chat and admin": "聊天和管理", "Chat background": "聊天背景", "Chat button icon": "聊天按钮图标", "Chat button offset": "聊天按钮偏移", "Chat message": "聊天消息", "Chat only": "仅聊天", "Chat position": "聊天位置", "Chatbot": "聊天机器人", "Chatbot mode": "聊天机器人模式", "Check Requirements": "检查系统要求", "Check the server configurations and make sure it has all the requirements.": "检查服务器配置以确认符合全部要求", "Checkout": "查看", "Choose a background texture for the chat header and conversation area.": "为聊天界面和聊天页头选择背景纹理", "Choose where to display the chat. Enter the values separated by commas.": "选择显示聊天的位置。输入以逗号分隔的值。", "Choose which fields to disable from the tickets area.": "选择在工单界面禁用的字段", "Choose which fields to include in the new ticket form.": "选择在新建工单表格中包含的字段", "Choose which fields to include in the registration form. The name field is included by default.": "选择注册表单中要包含的字段。默认包含姓名字段。", "Choose which user system the front-end chat will use to register and log in users.": "选择用于注册和登录前端聊天工具的账号系统", "City": "城市", "Clear flows": "清晰的流程", "Click the button to start the Dialogflow synchronization.": "点击按钮开始Dialogflow同步", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "点击按钮开始Slack同步。本地环境无法接收消息。", "Client email": "客户电子邮件", "Client ID": "客户编号", "Client token": "客户令牌", "Close chat": "关闭聊天", "Close message": "关闭消息", "Cloud API numbers": "云 API 编号", "Cloud API settings": "云 API 设置", "Cloud API template fallback": "云 API 模板回退", "Code": "代码", "Collapse panels": "折叠面板", "Color": "颜色", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "直接从Slack中和你的用户沟通。支持发送/接收消息和附件、使用表情包等。", "Company": "公司", "Concurrent chats": "同时进行的聊天", "Configuration URL": "配置URL", "Confirm button text": "确认按钮文本", "Confirmation message": "确认信息", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "连接智能机器人并通过先进的人工智能表单实现对话自动化。", "Connect stores to agents.": "将商店连接到代理商。", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "将您的 Telegram 机器人连接到 {R}，以阅读并回复直接在 {R} 中发送到 Telegram 机器人的所有消息。", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "将您的 Viber 机器人连接到 {R}，以阅读并回复直接在 {R} 中发送到您的 Viber 机器人的所有消息。", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "将您的 Zalo 官方账号连接到 {R}，以便直接在 {R} 中阅读和回复发送到您的 Zalo 官方账号的所有消息。", "Content": "内容", "Content template SID": "内容模板SID", "Conversation profile": "对话详情资料", "Conversations data": "对话数据", "Convert all emails": "转换所有邮箱", "Cookie domain": "<PERSON><PERSON>域名", "Country": "国家", "Coupon discount (%)": "优惠券折扣率(%)", "Coupon expiration (days)": "优惠券过期时间(日)", "Coupon expiration (seconds)": "优惠券过期时间(秒)", "Create a WordPress user upon registration.": "注册时创建一个 WordPress 用户。", "Create Intents now": "创建意向", "Currency symbol": "货币符号", "Custom CSS": "自定义CSS", "Custom fields": "自定义字段", "Custom JS": "自定义JS", "Custom model ID": "自定义模型 ID", "Custom parameters": "自定义参数", "Customize the link for the 'All articles' button.": "自定义“所有文章”按钮的链接。", "Dashboard display": "管理面板显示", "Dashboard title": "管理面板标题", "Database details": "数据库详情", "Database host": "数据库主机", "Database name": "数据库名", "Database password": "数据库密码", "Database prefix": "数据库前缀", "Database user": "数据库用户名", "Decline button text": "拒绝按钮文本", "Declined message": "对话被拒提示消息", "Default": "默认", "Default body text": "默认正文", "Default conversation name": "默认对话名称", "Default department": "默认部门", "Default department ID": "默认部门 ID", "Default form": "默认表单", "Default header text": "默认标题文本", "Delay (ms)": "延迟(毫秒)", "Delete all leads and all messages and conversations linked to them.": "删除全部潜在客户和与之相关的消息与对话", "Delete conversation": "删除对话", "Delete leads": "删除潜在客户", "Delete message": "删除留言", "Delete the built-in flows.": "删除内置流。", "Delimiter": "分隔符", "Department": "部门", "Department ID": "部门ID", "Departments": "部门", "Departments settings": "部门设置", "Desktop notifications": "桌面通知", "Dialogflow - Department linking": "Dialogflow - 部门链接", "Dialogflow chatbot": "Dialogflow 聊天机器人", "Dialogflow edition": "Dialogflow版本", "Dialogflow Intent detection confidence": "Dialogflow 意图检测置信度", "Dialogflow location": "Dialogflow 位置", "Dialogflow spelling correction": "Dialogflow 拼写更正", "Dialogflow welcome Intent": "Dialogflow 欢迎意图", "Disable agents check": "Dialogflow客服检查", "Disable and hide the chat widget if all agents are offline.": "当所有客服离线时停用并隐藏聊天窗口", "Disable and hide the chat widget outside of scheduled office hours.": "当处于非上班时间时停用并隐藏聊天窗口", "Disable any features that you don't need.": "禁用任何您不需要的功能。", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "停用聊天窗口自动启动。当此设置开启时你必须手动通过自定义JavaScript API初始化聊天窗口。如果当启用此设置之后聊天窗未显示，请停用此选项。", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "停用工单界面自动初始化。当此设置开启时你必须手动通过自定义JavaScript API初始化工单界面。如果当启用此设置之后工单界面未显示，请停用此选项。", "Disable chatbot": "禁用聊天机器人", "Disable cron job": "禁用自带的Cron定时任务", "Disable dashboard": "停用管理面板", "Disable during office hours": "上班时间停用", "Disable features": "停用功能", "Disable features you don't use and improve the chat performance.": "停用不需要的功能以提升聊天窗口性能。", "Disable file uploading capabilities within the chat.": "停用聊天窗内文件上传功能。", "Disable for messaging channels": "禁用消息通道", "Disable for the tickets area": "为工单窗口停用此功能", "Disable invitation": "禁用邀请", "Disable online status check": "禁用在线状态检查", "Disable outside of office hours": "非上班时间停用", "Disable password": "停用密码", "Disable registration during office hours": "上班时间停用注册功能", "Disable registration if agents online": "客服在线时停用注册功能", "Disable the automatic invitation of agents to the channels.": "禁用自动邀请代理加入频道。", "Disable the channels filter.": "禁用通道过滤器。", "Disable the chatbot for the tickets area.": "停用工单界面的聊天机器人", "Disable the chatbot for this channel only.": "仅禁用此频道的聊天机器人。", "Disable the dashboard, and allow only one conversation per user.": "停用管理面板，且每个用户只允许建立一个对话", "Disable the login and remove the password field from the registration form.": "停用登录并从注册表单中去除密码字段", "Disable uploads": "停用上传", "Disable voice message capabilities within the chat.": "禁用聊天中的语音消息功能。", "Disable voice messages": "禁用语音消息", "Disabled": "已停用", "Display a brand image in the header area. This only applies for the 'brand' header type.": "在页头显示品牌图像。这只应用于品牌类型的页头。", "Display categories": "显示类别", "Display images": "显示图片", "Display in conversation list": "显示在对话列表中", "Display in dashboard": "在管理面板中显示", "Display online agents only": "仅显示在线坐席", "Display the articles section in the right area.": "在右侧显示知识库面板", "Display the dashboard instead of the chat area on initialization.": "启动后默认显示管理面板而不是聊天窗口。", "Display the feedback form to rate the conversation when it is archived.": "在存档对话时显示反馈表以对其进行评级。", "Display the user full name in the left panel instead of the conversation title.": "在左侧面板显示用户全名而不是对话标题", "Display the user's profile image within the chat.": "在聊天窗口中显示用户头像", "Display user name in header": "在页头显示用户名称", "Display user's profile image": "显示用户头像", "Displays additional columns in the user table. Enter the name of the fields to add.": "显示用户表中的其他列。输入要添加的字段的名称。", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "客服按比例分配对话并通知他们的等待位数。回复时间以分钟为单位。你可以使用下列模板字段：{position}, {minutes}。他们将在对话中显示为实际等待时间和位置。", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "客服按比例分配对话，并禁止客服查看其他客服的对话。", "Do not send email notifications to admins": "不要向管理员发送电子邮件通知", "Do not show tickets in chat": "不要在聊天中显示门票", "Do not translate settings area": "不翻译设置区域", "Download": "下载", "Edit profile": "编辑个人资料", "Edit user": "编辑用户", "Email address": "邮箱", "Email and ticket": "电子邮件和票证", "Email header": "邮件头", "Email notification delay (hours)": "邮件通知延迟(小时)", "Email notifications via cron job": "通过 cron 作业发送电子邮件通知", "Email only": "仅限电子邮件", "Email piping": "邮件接管", "Email piping server information and more settings.": "邮件接管服务器信息和更多设置", "Email request message": "邮件请求消息", "Email signature": "邮件签名", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "客服人员回复时发送给用户的电子邮件模板。您可以使用文本、HTML 和以下合并字段：{conversation_url_parameter}、{recipient_name}、{sender_name}、{sender_profile_image}、{message}、{attachments}。", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "用户发送新消息时发送给代理的电子邮件模板。您可以使用文本、HTML 和以下合并字段：{conversation_link}、{recipient_name}、{sender_name}、{sender_profile_image}、{message}、{attachments}。", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "用户通过后续消息表单提交电子邮件后，向其发送的电子邮件模板。您可以使用文本、HTML 和以下合并字段：{user_name}、{user_email}。", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "发送给用户以验证其电子邮件地址的电子邮件模板。在您的内容中包含 {code} 合并字段，它将被一次性代码替换。", "Email verification": "电子邮件验证", "Email verification content": "邮箱验证内容", "Enable email verification with OTP.": "使用 OTP 启用电子邮件验证。", "Enable logging of agent activity": "启用代理活动记录", "Enable logs": "启用日志", "Enable the chatbot outside of scheduled office hours only.": "只在非上班时间段启用聊天机器人", "Enable the registration only if all agents are offline.": "只在所有客服离线时允许注册", "Enable the registration outside of scheduled office hours only.": "只在非上班时间段允许注册", "Enable this option if email notifications are sent via cron job.": "如果通过 cron 作业发送电子邮件通知，请启用此选项。", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "只为订阅用户启用工单和聊天支持，在管理面板查看用户的账号和订阅详情", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "输入机器人令牌并单击按钮以同步 Telegram 机器人。 Localhost 无法接收消息。", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "输入机器人令牌并单击按钮以同步 Viber 机器人。本地主机无法接收消息。", "Enter the database details of the Active eCommerce CMS database.": "输入 Active eCommerce CMS 数据库的数据库详细信息。", "Enter the database details of the Martfury database.": "输入 Martfury 数据库的数据库详细信息。", "Enter the database details of the Perfex database.": "输入 Perfex 数据库的数据库详细信息。", "Enter the database details of the WHMCS database.": "输入 WHMCS 数据库的数据库详细信息。", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "当用户问题需要动态回答时，输入聊天机器人使用的默认消息。", "Enter the details of your Google Business Messages.": "输入您的 Google 商家信息的详细信息。", "Enter the details of your Twitter app.": "输入您的 Twitter 应用程序的详细信息。", "Enter the LINE details to start using it. Localhost cannot receive messages.": "输入 LINE 详细信息以开始使用它。本地主机无法接收消息。", "Enter the URL of a .css file, to load it automatically in the admin area.": "输入CSS文件的URL以自动加载到管理员菜单", "Enter the URL of a .js file, to load it automatically in the admin area.": "输入JS文件的URL以自动加载到管理员菜单", "Enter the URL of the articles page.": "输入文章页面的 URL。", "Enter the URLs of your shop": "输入您店铺的网址", "Enter the WeChat official account token. See the docs for more details.": "输入微信公众号令牌。有关详细信息，请参阅文档。", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "输入 Zalo 详细信息以开始使用它。本地主机无法接收消息。", "Enter your 360dialog account settings information.": "输入您的 360dialog 帐户设置信息。", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "输入Envato购买代码来激活自动升级和解锁全部功能", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "输入Twilio账号信息。你可以使用文本及下列模板字段：{message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}。", "Enter your Twilio account settings information.": "输入你的Twilio账号设置信息", "Enter your WeChat Official Account information.": "输入您的微信公众号信息。", "Enter your Zendesk information.": "输入您的 Zendesk 信息。", "Entities": "Entities", "Envato Purchase Code": "Envato购买代码", "Envato purchase code validation": "Envato 购买代码验证", "Exclude products": "排除产品", "Export all settings.": "导出全部设置", "Export settings": "导出设置", "Facebook pages": "Facebook Pages商户主页", "Fallback message": "后备消息", "Filters": "过滤器", "First chat message": "第一条聊天消息", "First reminder delay (hours)": "第一条提醒延迟（小时）", "First ticket form": "创建第一条工单的表单", "Flash notifications": "通知闪烁", "Follow up - Email": "跟进 - 邮件", "Follow up email": "跟进电子邮件", "Follow up message": "跟进消息", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "跟进人工客服和用户之间的对话并实时向客服提供回复建议", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "跟进邮件模板。你可以使用纯文本或HTML以及下列模板字段：{coupon}, {product_names}, {user_name}。", "Force language": "强制语言", "Force log out": "强制下线", "Force the chat to ignore the language preferences, and to use always the same language.": "强制聊天忽略语言偏好设置并始终使用同一语言。", "Force the loggout of Masi Chat agents if they are not logged in WordPress.": "当客服未登录WordPress时，将他们从客服系统中强制下线。", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "强制用户对每个商店使用不同的对话，并向商店管理员隐藏来自其他商店的对话。", "Force users to use only one phone country code.": "强制用户使用此唯一电话国家代码", "Form message": "表单消息", "Form title": "表单标题", "Frequency penalty": "频率损失", "Full visitor details": "完整访客详情", "Function name": "函数名称", "Generate conversations data": "生成对话数据", "Generate user questions": "生成用户问题", "Get configuration URL": "获取配置URL", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "从启用的eCommerce根目录的.env文件中获取APP_KEY值", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "从 Martfury 根目录下的 .env 文件的 APP_KEY 值中获取。", "Get Path": "获取路径", "Get Service Worker path": "获取 Service Worker 路径", "Get URL": "获取网址", "Google and Dialogflow settings.": "Google 和 Dialogflow 设置。", "Google search": "谷歌搜索", "Header": "标头", "Header background image": "页头背景", "Header brand image": "页头品牌图片", "Header message": "页头消息", "Header title": "页头标题", "Header type": "页头类型", "Header variables": "标头变量", "Hide": "隐藏", "Hide agent's profile image": "隐藏客服头像", "Hide archived tickets": "隐藏存档的票证", "Hide archived tickets from users.": "对用户隐藏存档的票证。", "Hide chat if no agents online": "无客服在线时隐藏聊天窗", "Hide chat outside of office hours": "非上班时间隐藏聊天窗", "Hide conversation details panel": "隐藏对话详情面板", "Hide conversations of other agents": "隐藏其他客服的对话", "Hide on mobile": "在移动端隐藏聊天窗", "Hide the agent's profile image within the chat.": "在聊天窗内隐藏客服头像", "Hide tickets from the chat widget and chats from the ticket area.": "隐藏聊天小部件中的工单和工单区域中的聊天。", "Hide timetable": "隐藏在线时间表", "Host": "主机", "Human takeover": "人工接管", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "如果在指定的时间间隔内没有代理响应，则会发送一条消息来请求用户的详细信息，例如他们的电子邮件。", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "如果聊天机器人不理解用户的问题，则将对话转发给代理。", "Image": "图片", "Import admins": "导入管理员", "Import all settings.": "导入所有设置", "Import articles": "导入知识库", "Import contacts": "导入联系人", "Import customers": "导入客户", "Import customers into Masi Chat. Only new customers will be imported.": "将客户导入 <PERSON><PERSON>。只会导入新客户。", "Import settings": "导入设置", "Import users": "导入用户", "Import users from a CSV file.": "从 CSV 文件导入用户。", "Import vendors": "进口供应商", "Import vendors into Masi Chat as agents. Only new vendors will be imported.": "将供应商作为代理导入 <PERSON><PERSON>。只会导入新的供应商。", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "通过Pusher和Websockets提升聊天窗性能。此设置将停止可能拖慢服务器的AJAX/HTTP实时请求并使用WebSockets进行连接。", "Include custom fields": "包含自定义字段", "Include custom fields in the registration form.": "在注册表单中包含自定义字段", "Include the password field in the registration form.": "在注册表单中包含密码", "Incoming conversations and messages": "收到的对话和消息", "Incoming conversations only": "仅收到的对话", "Incoming messages only": "仅收到的消息", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Masi Chat.": "通过将Active eCommerce对接客服系统将买卖双方实时连接来促进销售。", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Masi Chat.": "增加销售额，通过启用WooCommerce的客服支持系统来提供更好的客户支持和更快捷的解决方案。", "Info message": "信息消息", "Initialize and display the chat widget and tickets only for members.": "仅向会员启用和显示聊天窗和工单。", "Initialize and display the chat widget only when the user is logged in.": "仅当用户登录时初始化并显示聊天小部件。", "Instance ID": "示例ID", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "将 OpenCart 与 {R} 集成，以实时同步客户、访问订单历史记录和客户购物车可见性。", "Interval (sec)": "间隔（秒）", "IP banning": "IP 封禁", "Label": "标签", "Language": "语言", "Language detection": "语言检测", "Language detection message": "语言检测消息", "Last name": "姓氏", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "如果不了解此设置的作用请留空，错误的输入值将使聊天窗无法工作。设置聊天窗用于登录和对话的主域名，对话将在主域名和子域名之间共享。", "Left": "左侧", "Left panel": "左侧面板", "Left profile image": "左侧头像", "Let the bot to search on Google to find answers to user questions.": "让机器人在 Google 上搜索以找到用户问题的答案。", "Let the chatbot search on Google to find answers to user questions.": "让聊天机器人在 Google 上搜索以找到用户问题的答案。", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "让您的用户通过 Twitter 联系您。阅读并回复直接从 {R} 发送到您的 Twitter 帐户的消息。", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "让您的用户通过微信联系您。阅读并回复直接从 {R} 发送到您的微信公众号的所有消息。", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "让您的用户通过 WhatsApp 联系您。阅读并回复直接从 {R} 发送到您的 WhatsApp Business 帐户的所有消息。", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "将每个代理与相应的 Slack 用户链接，因此当代理通过 Slack 回复时，它将显示为分配的代理。", "Link name": "链接名字", "Login form": "登录表单", "Login initialization": "登录初始化", "Login verification URL": "登录验证网址", "Logit bias": "逻辑偏差", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "首先备份您的 Dialogflow 代理。此操作可能需要几分钟时间。", "Make the registration phone field mandatory.": "将注册手机号设为必填项", "Manage": "管理", "Manage here the departments settings.": "在此处管理部门设置", "Manage the tags settings.": "管理标签设置。", "Manifest file URL": "Web应用配置URL", "Manual": "手动的", "Manual initialization": "手动初始化", "Martfury root directory path, e.g. /var/www/": "Martfury根目录路径，例如/var/www/", "Martfury shop URL, e.g. https://shop.com": "Martfury 商店网址，例如 https://shop.com", "Max message limit": "最大消息限制", "Max tokens": "最大代币", "Members only": "仅限会员访问", "Members with an active paid plan only": "仅限付费会员访问", "Message": "消息", "Message area": "留言区", "Message rewrite button": "消息重写按钮", "Message template": "消息模板", "Message type": "消息类型", "Messaging channels": "消息渠道", "Messenger and Instagram settings": "Messenger 和 Instagram 设置", "Minify JS": "压缩JS文件", "Minimal": "最小", "Model": "模型", "Multilingual": "多语言", "Multilingual plugin": "多语言插件", "Multilingual via translation": "通过翻译多语言", "Multlilingual training sources": "多语言培训资源", "Name": "名称", "Namespace": "命名空间", "New conversation email": "新的对话电子邮件", "New conversation notification": "新对话通知", "New ticket button": "新建工单按钮", "Newsletter": "订阅邮件", "No delay": "无延迟", "No results found.": "未找到结果", "No, we don't ship in": "不，我们不寄往", "None": "无", "Note data scraping": "注意数据抓取", "Notes": "笔记", "Notifications icon": "通知图标", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "当消息在预定办公时间之外发送或所有客服人员均离线时通知用户。", "OA secret key": "OA密钥", "Offline message": "离线消息", "Offset": "偏移", "On chat open": "当聊天开启时", "On page load": "当页面加载时", "One conversation per agent": "每个代理一次对话", "One conversation per department": "每个部门一次对话", "Online users notification": "用户上线通知", "Only desktop": "仅桌面", "Only general questions": "仅一般性问题", "Only mobile devices": "仅移动端", "Only questions related to your sources": "仅与您的消息来源相关的问题", "Open automatically": "自动打开", "Open chat": "自动打开聊天窗口", "Open the chat window automatically when a new message is received.": "当收到新消息时自动打开聊天窗口", "OpenAI Assistants - Department linking": "OpenAI 助手 - 部门链接", "OpenAI settings.": "OpenAI 设置。", "Optional link": "可选链接", "Order webhook": "订购网络挂钩", "Other": "其他", "Outgoing SMTP server information.": "出站SMTP服务器信息", "Page ID": "商户主页ID", "Page IDs": "页面ID", "Page name": "商户主页名称", "Page token": "商户主页token", "Panel height": "面板高度", "Panel name": "面板名称", "Panel title": "面板标题", "Panels arrows": "面板箭头", "Password": "密码", "Perfex URL": "Perfex URL", "Performance optimization": "性能优化", "Phone": "电话", "Phone number ID": "电话号码 ID", "Phone required": "电话必填", "Place ID": "地点 ID", "Placeholder text": "占位符文本", "Play a sound for new messages and conversations.": "收到新消息或对话时播放声音。", "Popup message": "弹出消息", "Port": "端口", "Post Type slugs": "文章类型Slug后缀", "Presence penalty": "存在处罚", "Prevent admins from receiving email notifications.": "防止管理员收到电子邮件通知。", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "防止客服查看分配给其他客服的对话。 如果分流或队列启用中，则会自动启用此设置。", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "通过限制从一台设备发送到聊天机器人的消息数量，防止用户滥用。", "Primary color": "主要颜色", "Priority": "优先级", "Privacy link": "隐私链接", "Privacy message": "私密消息", "Private chat": "私人聊天", "Private chat linking": "私聊链接", "Private key": "私钥", "Product IDs": "产品编号", "Product removed notification": "产品已删除通知", "Product removed notification - Email": "产品已删除通知 - 邮件", "Profile image": "用户头像", "Project ID": "项目编号", "Project ID or Agent Name": "项目 ID 或代理名称", "Prompt": "迅速的", "Prompt - Message rewriting": "提示-消息重写", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "使用 Google reCAPTCHA 保护票务区域免受垃圾邮件和滥用。", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "通过在网页中极速加载包含工单界面和全部聊天功能的在线客服系统，为客户提供完善的在线支持", "Provider": "提供者", "Purchase button text": "购买按钮文本", "Push notifications": "推送通知", "Push notifications settings.": "推送通知设置。", "Queue": "队列", "Rating": "等级", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "直接在 {R} 中阅读和回复从 Google 搜索、地图和品牌拥有的渠道发送的消息。", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "阅读、管理和回复直接从 {R} 发送到您的 Facebook 页面和 Instagram 帐户的所有消息。", "Reconnect": "重新连接", "Redirect the user to the registration link instead of showing the registration form.": "将用户跳转至注册页面而不是显示注册表单", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "当用户未登录时，跳转至指定URL。当留空时使用默认注册表单", "Refresh token": "刷新令牌", "Register all visitors": "注册所有访客", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "自动注册所有访客。当此选项未启用时，只有开启对话的访客才会注册账号", "Registration / Login": "注册/登录", "Registration and login form": "注册和登录表单", "Registration fields": "注册字段", "Registration form": "注册表单", "Registration link": "注册链接", "Registration redirect": "注册后重定向", "Rename the chat bot. Default is 'Bot'.": "重命名聊天机器人。默认值为‘Bot’。", "Rename the visitor name prefix. Default is 'User'.": "重命名访客姓名前缀。默认值为‘User’。", "Repeat": "重复", "Repeat - admin": "重复 - 管理员", "Replace the admin login page message.": "更换管理员登录页的提示消息。", "Replace the brand logo on the admin login page.": "更改管理员登录页的品牌LOGO", "Replace the header title with the user's first name and last name when available.": "如果可用，将以用户的名字和姓氏替换页头标题。", "Replace the top-left brand icon on the admin area and the browser favicon.": "更换管理员面板左上角的品牌LOGO和浏览器标识。", "Reply to user emails": "回复用户电子邮件", "Reply to user text messages": "回复用户短信", "Reports": "报告", "Reports area": "报告区", "Request a valid Envato purchase code for registration.": "请求有效的 Envato 购买代码进行注册。", "Request the user to provide their email address and then send a confirmation email to the user.": "要求用户提供其电子邮件地址，然后向用户发送确认电子邮件。", "Require phone": "电话必填", "Require registration": "需要注册", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "用户需要注册或登录以开启对话。为了启用登录界面，密码字段必须包含在登录表单中。", "Require the user registration or login in order to use the tickets area.": "用户需要注册或登录以使用工单界面", "Required": "必需", "Response time": "响应时间", "Restrict chat access by blocking IPs. List IPs with commas.": "通过阻止 IP 来限制聊天访问。用逗号列出 IP。", "Returning visitor message": "回头访客消息", "Rich messages": "富媒体消息", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "富媒体消息是可以在聊天消息中使用的代码片段。它们可以包含HTML代码并在聊天中自动展现。可以通过以下语法使用富媒体消息：[rich-message-name]。有大量内置的富媒体消息可供选择。", "Right": "右侧", "Right panel": "右侧面板", "Routing": "自动分流", "Routing if offline": "客服离线时自动分流", "RTL": "从右到左文字方向", "Save useful information like user country and language also for visitors.": "保存访客的有效信息，例如国家和语言", "Saved replies": "快捷回复", "Scheduled office hours": "上班时间计划表", "Search engine ID": "搜索引擎 ID", "Second chat message": "第二条聊天消息", "Second reminder delay (hours)": "第二条提醒延时（小时）", "Secondary color": "次要颜色", "Secret key": "密钥", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "发送消息以允许客户在他们可以购买感兴趣但目前缺货的产品时收到通知。 您可以使用以下模板字段：{user_name}、{product_name}。", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "在新用户创建第一张票时向他们发送消息。 支持文本格式和模板字段。", "Send a message to new users when they visit the website for the first time.": "当新用户第一次访问网站时向他们发送消息。", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "当用户从购物车删除了一件商品后发送一条通知消息。你可以使用文本、HTML和下列模板字段：{coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}。", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "向完成购买的客户发送消息邀请他们分享刚购买的产品。 您可以使用以下模板字段：{product_name}、{user_name}。", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "向完成购买的客户发送消息。 您可以使用以下模板字段：{coupon}、{product_names}、{user_name}。", "Send a message to the user when the agent archive the conversation.": "当客服归档对话时向用户发送消息。", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "向至少24小时后再次访问该网站的用户发送消息。您可以使用如下和更多模板字段：{coupon}、{user_name}。有关更多详细信息，请参阅文档。", "Send a test agent notification email to verify email settings.": "发送客服通知测试邮件以验证邮件设置。", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "向您的 Slack 频道发送测试消息。这仅测试传出消息的发送功能。", "Send a test user notification email to verify email settings.": "发送用户通知测试邮件以验证邮件设置。", "Send a text message to the provided phone number.": "向提供的电话号码发送短信。", "Send a user email notification": "发送用户电子邮件通知", "Send a user text message notifcation": "发送用户短信通知", "Send a user text message notification": "发送用户短信通知", "Send an agent email notification": "发送代理电子邮件通知", "Send an agent text message notification": "发送代理短信通知", "Send an agent user text notification": "发送代理用户文本通知", "Send an email notification to the provided email address.": "向提供的电子邮件地址发送电子邮件通知。", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "当用户回复且客服离线时，向此客服发送邮件通知。当有新对话时，一封邮件会自动发送给所有客服。", "Send an email to the user when a new conversation is created.": "创建新对话时向用户发送电子邮件。", "Send an email to the user when a new conversation or ticket is created": "创建新对话或工单时向用户发送电子邮件", "Send an email to the user when an agent replies and the user is offline.": "当客服回复并且用户离线时向用户发送邮件通知。", "Send email": "发送邮件", "Send login details to the specified URL and allow access only if the response is positive.": "将登录详细信息发送到指定的 URL 并仅在响应为肯定时才允许访问。", "Send message": "发信息", "Send message to Slack": "向 Slack 发送消息", "Send message via enter button": "按ENTER键发送消息。", "Send text message": "发送短信", "Send the message template to a WhatsApp number.": "将消息模板发送到 WhatsApp 号码。", "Send the message via the ENTER keyboard button.": "按键盘上的ENTER键发送消息。", "Send the user details of the registration form and email rich messages to Dialogflow.": "将注册表单的用户详细信息和电子邮件丰富的消息发送到 Dialogflow。", "Send the WhatsApp order details to the URL provided.": "将 WhatsApp 订单详细信息发送到提供的 URL。", "Send to user's email": "发送到用户邮箱", "Send transcript to user's email": "发送聊天记录到用户邮箱", "Send user details": "发送用户详细信息", "Sender": "发件人", "Sender email": "发件人邮箱", "Sender name": "发件人名称", "Sender number": "发件人号码", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "如果发送WhatsApp消息失败，则发送短信。您可以使用文本和以下模板字段：{conversation_url_parameter}、{message}、{recipient_name}、{recipient_email}。", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "当WhatsApp信息发送失败时，发送一条WhatsApp模板通知消息。你可以使用文本、HTML和下列模板字段：{conversation_url_parameter}, {recipient_name}, {recipient_email}。", "Service": "服务", "Service Worker path": "Service Worker 路径", "Service Worker URL": "服务进程URL", "Set a dedicated Dialogflow agent for each department.": "为每个部门设置一个专用的 Dialogflow 代理。", "Set a dedicated OpenAI Assistants for each department.": "为每个部门设置专门的OpenAI助手。", "Set a dedicated Slack channel for each department.": "为每个部门设置一个专用的 Slack 频道。", "Set a profile image for the chat bot.": "为聊天机器人设置头像。", "Set the articles panel title. Default is 'Help Center'.": "设置文章面板标题。默认为“帮助中心”。", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "设置消息旁边显示的头像图像。它必须是 1024x1024px 的 JPG 图片，最大大小为 50KB。", "Set the chat language or translate it automatically to match the user language. Default is English.": "设置聊天语言或自动翻译以匹配用户语言。默认为英语。", "Set the currency symbol of the membership prices.": "设置会员价格的货币符号。", "Set the currency symbol used by your system.": "设置系统使用的货币符号。", "Set the default departments for all tickets. Enter the department ID.": "为所有工单设置默认部门。输入部门ID。", "Set the default email header that will be prepended to automated emails and direct emails.": "设置将附加到自动化邮件和直接邮件的默认邮件标头。", "Set the default email signature that will be appended to automated emails and direct emails.": "设置将附到自动化邮件和直接邮件内的默认邮件签名。", "Set the default form to display if the registraion is required.": "设置默认表单以在需要注册时显示。", "Set the default name to use for conversations without a name.": "设置默认名称以用于没有名称的对话。", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "设置默认通知图标。如果用户没有图标，则该图标将用作个人头像。", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "设置上班时间，此时客服显示为在线。这些设置也将应用于所有其他依赖办公时间的设置项。", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "设置默认用户名以在用户没有姓名时在机器人消息和邮件中使用。", "Set the header appearance.": "设置标题外观。", "Set the maximum height of the tickets panel.": "设置工单面板的最大高度。", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "设置您正在使用的多语言插件，如果您的网站只使用一种语言，则将其禁用。", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "当座席或管理员在管理区域保持非活动状态至少 10 分钟时，自动设置离线状态。", "Set the position of the chat widget.": "设置聊天窗口的位置。", "Set the primary color of the admin area.": "设置管理区域的主要颜色。", "Set the primary color of the chat widget.": "设置聊天窗口的主要颜色。", "Set the secondary color of the admin area.": "设置管理区域的辅助颜色。", "Set the secondary color of the chat widget.": "设置聊天窗口的次要颜色。", "Set the tertiary color of the chat widget.": "设置聊天窗口的第三颜色。", "Set the title of the administration area.": "设置管理面板的标题。", "Set the title of the conversations panel.": "设置对话面板的标题。", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "设置上班时间表的UTC偏移量。正确的值可以是负数，并且一旦您单击此输入框（如果值为空），它将自动生成。", "Set which actions to allow agents.": "设置允许代理的操作。", "Set which actions to allow supervisors.": "设置允许主管执行哪些操作。", "Set which user details to send to the main channel. Add comma separated values.": "设置要发送到主频道的用户详细信息。添加逗号分隔值。", "Settings area": "设置区", "settings information": "设置信息", "Shop": "店铺", "Show": "显示", "Show a browser tab notification when a new message is received.": "收到新消息时显示浏览器选项卡通知。", "Show a desktop notification when a new message is received.": "收到新消息时显示桌面通知。", "Show a notification and play a sound when a new user is online.": "当用户上线时显示通知并播放提示音", "Show a pop-up notification to all users.": "向所有用户显示弹出通知。", "Show profile images": "显示头像", "Show sender's name": "显示发件人姓名", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "在仪表板中显示代理菜单并强制用户选择代理以开始对话。", "Show the articles panel on the chat dashboard.": "在聊天仪表板上显示文章面板。", "Show the categories instead of the articles list.": "显示类别而不是文章列表。", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "当访客将商品添加到购物车时显示跟进消息。仅当用户尚未提供电子邮件时才会发送消息。", "Show the list of all Slack channels.": "显示所有 Slack 频道的列表。", "Show the profile image of agents and users within the conversation.": "在聊天中显示客服和用户的头像。", "Show the sender's name in every message.": "在每封邮件中显示发件人的姓名。", "Single label": "单一标签", "Single phone country code": "统一电话国家代码", "Site key": "站点密钥", "Slug": "Slug后缀", "Social share message": "社交分享消息", "Sort conversations by date": "按日期排序对话", "Sound": "提示音", "Sound settings": "声音设置", "Sounds": "提示音", "Sounds - admin": "声音 - 管理员", "Source links": "来源链接", "Speech recognition": "语音识别", "Spelling correction": "拼写更正", "Starred tag": "加星标的标签", "Start importing": "开始导入", "Store name": "店铺名称", "Subject": "主题", "Subscribe": "订阅", "Subscribe users to your preferred newsletter service when they provide an email.": "当用户提供邮箱后，将其加入指定的邮件订阅服务。", "Subtract the offset value from the height value.": "从高度值中减去偏移值。", "Success message": "成功提示消息", "Supervisors": "主管", "Masi Chat path": "<PERSON><PERSON> 路径", "Sync admin and staff accounts with Masi Chat. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "同步管理员和员工账号到客服系统。员工用户将注册为客服，管理员身份不变。只有新用户会被导入。", "Sync all contacts of all clients with Masi Chat. Only new contacts will be imported.": "用户所有客户联系信息到客服系统。只有新联系人会被导入", "Sync all users with Masi Chat. Only new users will be imported.": "同步全部用户到客服系统。只有新用户会被导入。", "Sync all WordPress users with Masi Chat. Only new users will be imported.": "同步所有WordPress用户到客服系统。只有新用户会被导入", "Sync knowledge base articles with Masi Chat. Only new articles will be imported.": "同步知识库文章到客服系统，只有新文章会被导入。", "Sync mode": "同步模式", "Synchronization": "同步", "Synchronize": "同步", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "同步客户，仅为订阅用户启用工单和聊天支持。在管理员界面查看订阅套餐。", "Synchronize emails": "同步邮箱", "Synchronize Entities": "同步实体", "Synchronize Entities now": "立刻同步实体", "Synchronize now": "现在同步", "Synchronize users": "同步用户", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "实时同步你的客户信息，与他们聊天并加强互动，以提供更快更好的支持。", "Synchronize your Messenger and Instagram accounts.": "同步您的 Messenger 和 Instagram 帐户。", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "实时同步你的Perfex用户信息，你可以通过聊天窗和他们实时对话，主动沟通等。", "Synchronize your WhatsApp Cloud API account.": "同步您的 WhatsApp Cloud API 帐户。", "System requirements": "系统要求", "Tags": "标签", "Tags settings": "标签设置", "Template default language": "模板默认语言", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "代理回复时发送给用户的电子邮件模板。您可以使用文本、HTML 和以下合并字段：{conversation_url_parameter}、{recipient_name}、{sender_name}、{sender_profile_image}、{message}、{attachments}。", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "创建新对话时发送给用户的电子邮件模板。您可以使用文本、HTML 和以下合并字段：{conversation_url_parameter}、{user_name}、{message}、{attachments}、{conversation_id}。", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "创建新对话或工单时发送给用户的电子邮件模板。您可以使用文本、HTML 和以下合并字段：{conversation_url_parameter}、{user_name}、{message}、{attachments}。", "Template languages": "模板语言", "Template name": "模板名称", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "管理员通知电子邮件的模板。您可以使用文本、HTML 和以下合并字段等：{carts}。在电子邮件地址字段中输入您要向其发送通知的电子邮件。", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "从购物车中删除产品后发送给客户的电子邮件模板。您可以使用文本、HTML 和以下合并字段等：{html_products_list}、{coupon}、{discount_price}、{original_price}、{product_names}、{user_name} .", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "第一封通知电子邮件的模板。您可以使用文本、HTML 和以下合并字段等：{html_products_list}、{coupon}、{discount_price}、{original_price}、{product_names}、{user_name} .", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "第二封通知电子邮件的模板。您可以使用文本、HTML 和以下合并字段等：{html_products_list}、{coupon}、{discount_price}、{original_price}、{product_names}、{user_name} .", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "等候名单通知电子邮件的模板。您可以使用文本、HTML 和以下合并字段等：{html_product_card}、{product_description}、{product_image}、{product_name}、{product_link}。", "Terms link": "用户条款链接", "Tertiary color": "第三颜色", "Test Slack": "测试松弛", "Test template": "测试模板", "Text": "文本", "Text message fallback": "短信备用消息", "Text message notifications": "短信通知", "Text messages": "短信", "The product is not in the cart.": "商品不在购物车中", "The workspace name you are using to synchronize Slack.": "用于Slack同步的工作区名称", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "这是您的主要 Slack 频道 ID，通常是 #general 频道。您将通过完成 Slack 同步获得此代码。", "This returns the Masi Chat path of your server.": "这将返回您服务器的 Ma<PERSON> 路径。", "Ticket custom fields": "工单自定义字段", "Ticket email": "工单确认邮件", "Ticket field names": "工单字段名称", "Ticket fields": "工单字段", "Ticket only": "仅限门票", "Ticket products selector": "票产品选择器", "Title": "标题", "Top": "顶部", "Top bar": "顶部栏", "Training via cron job": "通过 cron job 进行训练", "Transcript": "聊天记录", "Transcript settings.": "聊天记录设置", "Trigger": "触发条件", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "当欢迎消息启用时，为新访客触发Dialogflow欢迎语", "Troubleshoot": "故障排除", "Troubleshoot problems": "解决问题", "Twilio settings": "<PERSON><PERSON><PERSON>设置", "Twilio template": "<PERSON><PERSON><PERSON> 模板", "Unsubscribe": "取消订阅", "Upload attachments to Amazon S3.": "将附件上传到 Amazon S3。", "Usage Limit": "使用限制", "Use this option to change the PWA icon. See the docs for more details.": "使用此选项更改 PWA 图标。请参阅文档了解更多详细信息。", "User details": "用户详情", "User details in success message": "成功提示消息中的用户详情", "User email notifications": "用户邮件通知", "User login form information.": "用户登录表单信息。", "User message template": "用户消息", "User name as title": "用户名作为标题", "User notification email": "用户通知邮件", "User registration form information.": "用户注册表单信息", "User roles": "用户角色", "User system": "用户系统", "Username": "用户名", "Users and agents": "用户和客服", "Users area": "用户区", "Users only": "仅用户", "Users table additional columns": "用户列表的额外列", "UTC offset": "UTC 偏移量", "Variables": "变量", "View channels": "查看频道", "View unassigned conversations": "查看未分配客服的对话", "Visibility": "可见性", "Visitor default name": "访客默认名称", "Visitor name prefix": "访客名前缀", "Volume": "体积", "Volume - admin": "卷 - 管理员", "Waiting list": "等待队列", "Waiting list - Email": "等待队列-邮件", "Webhook URL": "网络挂钩网址", "Webhooks": "Webhook", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Webhook是当事件发生时，通过后台发送到一个特定的URL的一段信息。", "Website": "网址", "WeChat settings": "微信设置", "Welcome message": "欢迎消息", "Whmcs admin URL": "WHMCS管理员URL", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "WHMCS管理员URL。例如：https://example.com/whmcs/admin/", "WordPress registration": "WordPress注册", "Yes, we ship in": "是的，我们发货到", "You haven't placed an order yet.": "你还没有下单。", "You will get this code by completing the Dialogflow synchronization.": "当完成Dialogflow同步后将获得此代码。", "You will get this code by completing the Slack synchronization.": "当完成Slack同步后将获得此代码。", "You will get this information by completing the synchronization.": "当完成同步后将获得这些信息。", "Your cart is empty.": "你的购物车是空的。", "Your turn message": "轮到你时的消息", "Your username": "您的用户名", "Your WhatsApp catalogue details.": "您的 WhatsApp 目录详细信息。", "Zendesk settings": "Zendesk 设置", "Smart Reply": "智能回复", "This returns your Masi Chat URL.": "这将返回您的 <PERSON><PERSON> Chat URL。", "Update conversation department": "更新对话部门"}