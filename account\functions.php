<?php
use parallel\Events\Event\Type;

/*
 *
 * ===================================================================
 * CLOUD FUNCTIONS FILE
 * ===================================================================
 *
 * © 2017-2025 app.masichat.com. All rights reserved.
 *
 */

if (!defined('MC_VERSION')) {
    require_once(dirname(dirname(__FILE__)) . '/script/include/functions.php');
}
global $CLOUD_CONNECTION;
global $CUSTOMER_CONNECTION;
global $ACTIVE_ACCOUNT;

/*
 * -----------------------------------------------------------
 * DATABASE
 * -----------------------------------------------------------
 *
 */

function db_connect() {
    global $CLOUD_CONNECTION;
    if ($CLOUD_CONNECTION) {
        return true;
    }
    $CLOUD_CONNECTION = new mysqli(CLOUD_DB_HOST, CLOUD_DB_USER, CLOUD_DB_PASSWORD, CLOUD_DB_NAME);
    if ($CLOUD_CONNECTION->connect_error) {
        echo 'Connection error';
        return false;
    }
    return true;
}

function db_customer_connect($token) {
    global $CUSTOMER_CONNECTION;
    $database = get_config($token);
    $CUSTOMER_CONNECTION = new mysqli($database['MC_DB_HOST'], $database['MC_DB_USER'], $database['MC_DB_PASSWORD'], $database['MC_DB_NAME']);
    if ($CUSTOMER_CONNECTION->connect_error) {
        return false;
    }
    return true;
}

function db_get($query, $single = true, $customer_token = false) {
    $status = $customer_token ? db_customer_connect($customer_token) : db_connect();
    $connection = $GLOBALS[$customer_token ? 'CUSTOMER_CONNECTION' : 'CLOUD_CONNECTION'];
    $value = $single ? '' : [];
    if ($status) {
        $result = $connection->query($query);
        if ($result) {
            if ($result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    if ($single) {
                        $value = $row;
                    } else {
                        array_push($value, $row);
                    }
                }
            }
        }
    } else {
        return $status;
    }
    return $value;
}

function db_query($query, $return = false, $customer_token = false) {
    $status = $customer_token ? db_customer_connect($customer_token) : db_connect();
    $connection = $GLOBALS[$customer_token ? 'CUSTOMER_CONNECTION' : 'CLOUD_CONNECTION'];
    if ($status) {
        $result = $connection->query($query);
        if ($result) {
            if ($return) {
                if (isset($connection->insert_id) && $connection->insert_id > 0) {
                    return $connection->insert_id;
                } else
                    return false;
            } else {
                return true;
            }
        } else {
            return $connection->error;
        }
    } else {
        return $status;
    }
}

function db_escape($value, $numeric = -1) {
    if (is_numeric($value)) {
        return $value;
    } else if ($numeric === true) {
        return false;
    }
    global $CLOUD_CONNECTION;
    db_connect();
    $value = $CLOUD_CONNECTION->real_escape_string($value);
    $value = mc_sanatize_string($value);
    $value = htmlspecialchars($value, ENT_NOQUOTES | ENT_SUBSTITUTE, 'utf-8');
    return $value;
}

/*
 * -----------------------------------------------------------
 * ACCOUNT
 * -----------------------------------------------------------
 *
 */

function account_registration($details) {
    $now = gmdate('Y-m-d H:i:s');
    $token = bin2hex(openssl_random_pseudo_bytes(20));
    $response = false;
    $appsumo = isset($details['appsumo']);

    // Validation
    if (strlen($details['password']) < 8) {
        return 'password-length'; // Error message handled in cloud.js
    } else if (!strpos($details['email'], '@') || !strpos($details['email'], '.')) {
        return 'invalid-email'; // Error message handled in cloud.js
    } else if (!$appsumo && intval(db_get('SELECT COUNT(*) as count FROM users WHERE email = "' . db_escape($details['email']) . '"')['count']) > 0) {
        return 'duplicate-email'; // Error message handled in cloud.js
    }

    // Cloud user registration
    $membership = defined('MC_CLOUD_API') ? mc_isset($details, 'membership', '0') : '0';
    $membership_expiration = '';
    $extra = '';
    $credits = 0.05;
    $response = db_query('INSERT INTO users (first_name, last_name, email, phone, password, membership, membership_expiration, token, last_activity, creation_time, email_confirmed, phone_confirmed, customer_id, extra, credits) VALUES ("' . db_escape($details['first_name']) . '", "' . db_escape($details['last_name']) . '", "' . db_escape($details['email']) . '", NULL, "' . db_escape(password_hash($details['password'], PASSWORD_DEFAULT)) . '", "' . $membership . '", "' . $membership_expiration . '", "' . $token . '", "' . $now . '", "' . $now . '", ' . mc_isset($details, 'email_confirmed', '0') . ', ' . mc_isset($details, 'phone_confirmed', '0') . ', "", "' . $extra . '", ' . $credits . ')', true);
    if (is_int($response)) {
        $user_slug = 'mc_' . rand(1, ********);
        $db_password = 'mc' . bin2hex(openssl_random_pseudo_bytes(20));
        $databases = array_column(db_get('SHOW DATABASES', false), 'Database');
        $db_name = $user_slug;
        while (in_array($user_slug, $databases)) {
            $user_slug = 'mc_' . rand(1, ********);
        }

        // Save additional user details
        $main_ids = ['first_name', 'last_name', 'email', 'password', 'password_2'];
        $query = '';
        foreach ($details as $key => $value) {
            if (!in_array($key, $main_ids)) {
                $query .= '(' . $response . ', "' . db_escape($key) . '", "' . db_escape($value) . '"),';
            }
        }
        $response_2 = $query ? super_insert_user_data(substr($query, 0, -1)) : false;
        if ($appsumo) {
            membership_save_white_label($response);
        }

        // Masi Chat database creation
        $response = db_query('CREATE DATABASE `' . $user_slug . '` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;');
        if ($response === true) {
            if (CLOUD_URL === 'http://localhost/masi-chat/cloud') {
                $user_slug = 'root';
                $db_password = '';
            } else {
                $mysql_user = "'" . $user_slug . "'@'" . (defined('CLOUD_IP') ? CLOUD_IP : 'localhost') . "'";
                db_query('CREATE USER ' . $mysql_user . ' IDENTIFIED BY \'' . $db_password . '\'');
                db_query('GRANT ALL PRIVILEGES ON ' . $user_slug . '.* TO ' . $mysql_user . ' WITH GRANT OPTION');
                db_query('GRANT select, update, delete, insert, create, drop, index, alter, lock tables, execute, create temporary tables, execute, trigger, create view, show view, references, event ON ' . $user_slug . '.* TO ' . $mysql_user);
            }
            $raw = str_replace(['[url]', '[name]', '[user]', '[password]', '[host]', '[port]'], [CLOUD_URL . '/script', $db_name, $user_slug, $db_password, CLOUD_DB_HOST, ''], file_get_contents('../script/resources/config-source.php'));
            $file = fopen(MC_CLOUD_PATH . '/script/config/config_' . $token . '.php', 'w');
            fwrite($file, $raw);
            fclose($file);

            // Masi Chat installation
            $response = mc_installation(['db-name' => [$db_name], 'db-user' => [$user_slug], 'db-password' => [$db_password], 'db-host' => [CLOUD_DB_HOST], 'first-name' => [$details['first_name']], 'last-name' => [$details['last_name']], 'password' => [$details['password']], 'email' => [$details['email']], 'url' => CLOUD_URL . '/script']);
            db_query('INSERT INTO mc_settings(name, value) VALUES (\'active_apps\', \'["dialogflow","whatsapp","telegram","messenger","viber","tickets","line"]\')', false, $token);

            // Webhook and return
            cloud_webhook('user-registration', $details);
            return account_login($details['email'], $details['password']);
        }
    }
    return $response;
}

function account_login($email, $password = false, $token = false) {
    $mc_login = false;
    $email = db_escape($email);
    $cloud_user = db_get('SELECT id AS `user_id`, first_name, last_name, email, phone, password, token, email_confirmed, phone_confirmed, customer_id, extra FROM users WHERE email = "' . $email . '" LIMIT 1');
    if (!$cloud_user) {
        $agent = db_get('SELECT A.token, A.id FROM users A, agents B WHERE B.email = "' . $email . '" AND A.id = B.admin_id');
        if ($agent) {
            require_once(MC_PATH . '/config/config_' . $agent['token'] . '.php');
            $cloud_user = mc_db_get('SELECT * FROM mc_users WHERE email = "' . $email . '"');
            $cloud_user['token'] = $agent['token'];
            $cloud_user['user_id'] = $agent['id'];
        }
    } else {
        $cloud_user['owner'] = true;
    }
    if ($cloud_user) {
        if (($password && password_verify($password, $cloud_user['password'])) || ($token && $token == $cloud_user['token'])) {
            require_once(MC_PATH . '/config/config_' . $cloud_user['token'] . '.php');
            if ($password) {
                $mc_login = mc_login($email, $password);
            } else {
                $active_user = mc_get_user_by('email', $email);
                if ($active_user) {
                    $mc_login = mc_login(false, false, $active_user['id'], $active_user['token']);
                } else {
                    return false;
                }
            }
            if ($mc_login === 'ip-ban') {
                return $mc_login;
            }
            return [mc_encryption(json_encode($cloud_user)), $mc_login[1], $mc_login[0]['id']];
        }
    }
    return false;
}

function account_login_get_user($email, $password) {
    $login = account_login($email, $password);
    if ($login && is_array($login)) {
        $user = mc_get_user($login[2], true);
        $_COOKIE['mc-cloud'] = $login[0];
        $details = membership_get_active();
        if (isset($details['name'])) {
            $user['details']['membership'] = [$details['name'], 'Membership'];
        }
        return $user;
    }
    return false;
}

function account() {
    global $ACTIVE_ACCOUNT;
    if ($ACTIVE_ACCOUNT) {
        return $ACTIVE_ACCOUNT;
    }
    if (empty($_COOKIE['mc-cloud']) && empty($_POST['cloud']) && empty($_GET['cloud'])) {
        return false;
    }
    $cookie = json_decode(mc_encryption(isset($_COOKIE['mc-cloud']) ? $_COOKIE['mc-cloud'] : (isset($_POST['cloud']) ? $_POST['cloud'] : $_GET['cloud']), false), true);

    // Check if $cookie is null or not an array
    if (!$cookie || !is_array($cookie) || !isset($cookie['user_id'])) {
        return false;
    }

    $cookie['chat_id'] = account_chat_id($cookie['user_id']);
    $ACTIVE_ACCOUNT = $cookie;
    return $cookie;
}

function account_chat_id($user_id) {
    return intval($user_id) * 95675 - 153;
}

function get_active_account_id($escape = true) {
    $id = mc_isset(account(), 'user_id');
    return $escape ? db_escape($id, true) : $id;
}

function account_save($details) {
    $account = account();
    require_once(MC_PATH . '/config/config_' . $account['token'] . '.php');
    $user_id = $account['user_id'];
    $email = $account['email'];
    $query = '';
    $query_mc = '';
    $main_ids = ['first_name', 'last_name', 'email', 'password', 'password_2', 'phone', 'credits', 'membership', 'membership_expiration'];
    $query_users_data = '';
    if (!super_admin()) {
        unset($details['credits']);
        unset($details['membership']);
        unset($details['membership_expiration']);
    }
    foreach ($details as $key => $value) {
        $value = str_replace('&amp;', '&', db_escape($value));
        $key = db_escape($key);
        if (in_array($key, $main_ids)) {
            if ($key == 'password') {
                if ($value != ********) {
                    $value = password_hash($details['password'], PASSWORD_DEFAULT);
                    $query_mc .= $key . ' = "' . $value . '",';
                } else {
                    continue;
                }
            }
            if (in_array($key, ['first_name', 'last_name', 'email'])) {
                $query_mc .= $key . ' = "' . $value . '",';
            }
            $query .= $key . ' = "' . $value . '",';
        } else {
            $query_users_data .= '(' . $user_id . ', "' . $key . '", "' . $value . '"),';
        }
    }
    $response = mc_db_query('UPDATE mc_users SET ' . substr($query_mc, 0, -1) . ' WHERE email = "' . $email . '"');
    if ($response === true) {
        $response = db_query('UPDATE users SET ' . substr($query, 0, -1) . ' WHERE id = ' . $user_id);
        if ($query_users_data) {
            super_delete_user_data($user_id, 'company_details');
            $query_users_data ? super_insert_user_data(substr($query_users_data, 0, -1)) : false;
        }
        if ($response === true) {
            if (isset($details['email']) && $details['email'] != $email) {
                db_query('UPDATE users SET email_confirmed = 0 WHERE id = ' . $user_id);
            }
            if (isset($details['phone']) && $details['phone'] != $account['phone']) {
                db_query('UPDATE users SET phone_confirmed = 0 WHERE id = ' . $user_id);
            }
            return account_login($details['email'], false, $account['token']);
        }
    }
    return $response;
}

function account_reset_password($email = false, $token = false, $password = false) {
    if ($email && !$password) {
        $email = db_escape($email);
        $token = db_get('SELECT token, first_name, last_name FROM users WHERE email = "' . $email . '" LIMIT 1');
        if (!$token) {
            $token = db_get('SELECT A.token, A.first_name, A.last_name FROM users A, agents B WHERE A.id = B.admin_id AND B.email = "' . $email . '" LIMIT 1');
        }
        if ($token) {
            // Get the reset password email template and subject
            $subject = super_get_setting('email_subject_reset_password');
            $template = super_get_setting('email_template_reset_password');

            // Generate the reset password link
            $reset_link = CLOUD_URL . '/account?reset=' . mc_encryption($token['token']) . '&email=' . mc_encryption($email);

            // Replace the {link} placeholder with the actual reset link
            $template = str_replace('{link}', $reset_link, $template);

            // Use cloud_merge_field_username to replace all other variables
            $username = '';
            if (!empty($token['first_name'])) {
                $username = trim($token['first_name'] . ' ' . $token['last_name']);
            } else if (strpos($email, '@') !== false) {
                // If no name found but we have an email, use the part before @ as a fallback name
                $username_parts = explode('@', $email);
                $username = ucfirst(str_replace(['.', '_', '-'], ' ', $username_parts[0]));
            }
            $message = cloud_merge_field_username($template, $username);

            // Send the email
            send_email($email, $subject, $message);

            // Debug log
            if (defined('MC_CLOUD_DEBUG') && MC_CLOUD_DEBUG) {
                mc_cloud_debug('account_reset_password: Sent reset password email', [
                    'email' => $email,
                    'reset_link' => $reset_link
                ]);
            }
        } else {
            // Debug log for email not found
            if (defined('MC_CLOUD_DEBUG') && MC_CLOUD_DEBUG) {
                mc_cloud_debug('account_reset_password: Email not found', [
                    'email' => $email
                ]);
            }
        }
    } else if ($token && $password) {
        $password = db_escape(password_hash($password, PASSWORD_DEFAULT));
        $token = db_escape(mc_encryption($token, false));
        $email = db_escape(mc_encryption($email, false));
        require_once(MC_PATH . '/config/config_' . $token . '.php');
        db_query('UPDATE users SET password = "' . $password . '" WHERE token = "' . $token . '" AND email = "' . $email . '" LIMIT 1');
        mc_db_query('UPDATE mc_users SET password = "' . $password . '" WHERE email = "' . $email . '" LIMIT 1');

        // Debug log
        if (defined('MC_CLOUD_DEBUG') && MC_CLOUD_DEBUG) {
            mc_cloud_debug('account_reset_password: Password reset successful', [
                'email' => $email
            ]);
        }
    }
    return true;
}

function account_welcome() {
    $account = account();
    if ($account) {
        $username = '';
        if (!empty($account['first_name'])) {
            $username = trim($account['first_name'] . ' ' . $account['last_name']);
        } else if (strpos($account['email'], '@') !== false) {
            // If no name found but we have an email, use the part before @ as a fallback name
            $username_parts = explode('@', $account['email']);
            $username = ucfirst(str_replace(['.', '_', '-'], ' ', $username_parts[0]));
        }

        send_email($account['email'], super_get_setting('email_subject_welcome'), cloud_merge_field_username(super_get_setting('email_template_welcome'), $username));
    }
}

function account_delete() {
    $cloud_user_id = get_active_account_id(false);
    if ($cloud_user_id) {
        super_delete_customer($cloud_user_id);
    }
}

function account_get_user_details() {
    $account = account();
    if ($account) {
        $account['company_details'] = mc_isset(db_get('SELECT value FROM users_data WHERE slug = "company_details" AND user_id = ' . db_escape($account['user_id'], true)), 'value', '');
    }
    return $account;
}

function account_delete_agents_quota() {
    $account = account();
    if ($account) {
        $membership = membership_get_active();
        $count = $membership['count_agents'] - mc_isset(membership_get($membership['id']), 'quota_agents', 9999);
        if ($count > 0) {
            require_once(MC_PATH . '/config/config_' . $account['token'] . '.php');
            $agent_emails = array_column(db_get('SELECT email FROM agents WHERE admin_id = ' . $account['user_id'] . ' ORDER BY ID desc LIMIT ' . $count, false), 'email');
            if (!empty($agent_emails)) {
                $implode = '("' . implode('","', $agent_emails) . '")';
                mc_db_query('DELETE FROM mc_users WHERE email IN ' . $implode);
                db_query('DELETE FROM agents WHERE admin_id = ' . $account['user_id'] . ' AND email IN ' . $implode);
            }
        }
    }
}

function account_get_payment_id() {
    global $ACTIVE_PAYMENT_ID;
    if ($ACTIVE_PAYMENT_ID) {
        return $ACTIVE_PAYMENT_ID;
    }
    $ACTIVE_PAYMENT_ID = mc_isset(db_get('SELECT customer_id FROM users WHERE id = ' . get_active_account_id()), 'customer_id');
    return $ACTIVE_PAYMENT_ID;
}

function account_magic_link($email) {
    $token = mc_isset(db_get('SELECT token FROM users WHERE email = "' . db_escape($email) . '"'), 'token');
    return $token ? CLOUD_URL . '?magic=' . mc_encryption($token . '|' . $email) : false;
}

function account_magic_link_login($magic) {
    $magic = explode('|', mc_encryption($_GET['magic'], false));
    if ($magic && count($magic) > 1) {
        $login = account_login($magic[1], false, $magic[0]);
        if ($login) {
            $expiration = time() + *********;
            setcookie('mc-cloud', $login[0], $expiration, '/');
            setcookie('mc-login', $login[1], $expiration, '/');
            $_COOKIE['mc-cloud'] = $login[0];
            $_COOKIE['mc-login'] = $login[1];
            return $login;
        }
    }
    return false;
}

function account_save_referral_payment_information($method, $details) {
    super_delete_user_data(get_active_account_id(), 'referral_payment_info', true);
    return super_insert_user_data('(' . get_active_account_id() . ', "referral_payment_info", "' . db_escape($method . '|' . $details) . '")');
}

function account_facebook_login($fb_data) {
    // Log the incoming data for debugging
    error_log('Facebook login data: ' . json_encode($fb_data));

    // Check for required fields - only ID is absolutely required
    if (empty($fb_data) || !isset($fb_data['id'])) {
        error_log('Invalid Facebook data: missing Facebook ID');
        return ['status' => 'error', 'message' => 'Invalid Facebook data: missing Facebook ID'];
    }

    // If name is missing, set a default name
    if (!isset($fb_data['name'])) {
        $fb_data['name'] = 'Facebook User';
    }

    // If email is missing but we have a Facebook ID, create a placeholder email
    if (!isset($fb_data['email']) || empty($fb_data['email'])) {
        $fb_data['email'] = 'fb_' . $fb_data['id'] . '@facebook.com';
        error_log('Created placeholder email for Facebook user: ' . $fb_data['email']);
    }

    // Check if user already exists by Facebook ID first, then by email
    $fb_id = db_escape($fb_data['id']);
    $email = db_escape($fb_data['email']);

    error_log('Looking for user with Facebook ID: ' . $fb_id . ' or email: ' . $email);

    // First try to find user by Facebook ID in users_data
    $fb_user_data = db_get('SELECT user_id FROM users_data WHERE slug = "facebook_id" AND value = "' . $fb_id . '" LIMIT 1');
    $cloud_user = null;

    if ($fb_user_data) {
        // User found by Facebook ID, get full user data
        error_log('User found by Facebook ID: ' . $fb_user_data['user_id']);
        $cloud_user = db_get('SELECT id AS `user_id`, first_name, last_name, email, phone, password, token, email_confirmed, phone_confirmed, customer_id, extra FROM users WHERE id = ' . $fb_user_data['user_id'] . ' LIMIT 1');
    }

    // If not found by Facebook ID, try by email
    if (!$cloud_user) {
        error_log('User not found by Facebook ID, trying email: ' . $email);
        $cloud_user = db_get('SELECT id AS `user_id`, first_name, last_name, email, phone, password, token, email_confirmed, phone_confirmed, customer_id, extra FROM users WHERE email = "' . $email . '" LIMIT 1');
    }

    if ($cloud_user) {
        error_log('Existing user found: ' . $cloud_user['user_id']);

        // If we found the user by email but not by Facebook ID, save the Facebook ID
        if (!$fb_user_data) {
            error_log('Saving Facebook ID for existing user: ' . $cloud_user['user_id']);
            super_insert_user_data('(' . $cloud_user['user_id'] . ', "facebook_id", "' . $fb_id . '")');
        }

        // User exists, log them in - use the same flow as account_login
        return account_login($cloud_user['email'], false, $cloud_user['token']);
    } else {
        // Create new user
        error_log('Creating new user for Facebook login');
        $name_parts = explode(' ', $fb_data['name'], 2);
        $first_name = db_escape($name_parts[0]);
        $last_name = db_escape(isset($name_parts[1]) ? $name_parts[1] : '');

        $details = [
            'first_name' => $first_name,
            'last_name' => $last_name,
            'email' => $email,
            'password' => bin2hex(openssl_random_pseudo_bytes(10)), // Generate random password
            'email_confirmed' => 1, // Facebook already verified the email
            'user_type' => 'user' // Ensure user type is set
        ];

        // Add Facebook ID to details for saving in users_data later
        $details['facebook_id'] = $fb_data['id'];

        // Register the user - this will create the account and return login data
        error_log('Registering new user with details: ' . json_encode($details));
        $result = account_registration($details);

        if (is_array($result)) {
            error_log('Registration successful, sending welcome email');
            // Send welcome email for new Facebook users
            account_welcome();

            // Add welcome flag to the response
            $result[] = 'welcome';
            error_log('Returning registration result with welcome flag');
            return $result; // Registration successful, returns login data
        } else {
            error_log('Registration failed: ' . $result);
            return ['status' => 'error', 'message' => 'Registration failed: ' . $result];
        }
    }
}

/*
 * -----------------------------------------------------------
 * MEMBERSHIP
 * -----------------------------------------------------------
 *
 */

function memberships() {
    global $MC_CLOUD_MEMBERSHIPS;
    if (isset($MC_CLOUD_MEMBERSHIPS) && is_array($MC_CLOUD_MEMBERSHIPS)) { // Check if global is already set and is an array
        return $MC_CLOUD_MEMBERSHIPS;
    }

    $default_free_plan = ['price' => 0, 'name' => 'Free', 'id' => '0', 'period' => '', 'currency' => '', 'quota' => 100];
    $membership_data_raw = super_get_setting('memberships'); // Get raw data from DB

    if (empty($membership_data_raw)) {
         mc_cloud_debug('memberships(): Setting "memberships" is empty or not found. Returning default free plan.');
         $MC_CLOUD_MEMBERSHIPS = [$default_free_plan];
         return $MC_CLOUD_MEMBERSHIPS;
    }

    $decoded_memberships = json_decode($membership_data_raw, true);

    // Check if json_decode failed or did not return an array
    if (json_last_error() !== JSON_ERROR_NONE || !is_array($decoded_memberships)) {
        mc_cloud_debug('memberships(): Failed to decode "memberships" setting or it is not an array. Returning default free plan.', ['json_last_error' => json_last_error_msg(), 'raw_data_type' => gettype($decoded_memberships)]);
        error_log('Masi Chat Cloud: Failed to decode "memberships" setting or not an array. Raw data: ' . $membership_data_raw); // Log critical error
        $MC_CLOUD_MEMBERSHIPS = [$default_free_plan];
        return $MC_CLOUD_MEMBERSHIPS;
    }

    // Ensure the free plan exists at the start if the decoded array is not empty but missing it
    if (empty($decoded_memberships) || !isset($decoded_memberships[0]['id']) || $decoded_memberships[0]['id'] != '0') {
         mc_cloud_debug('memberships(): Decoded memberships missing default free plan at index 0. Prepending default.');
         array_unshift($decoded_memberships, $default_free_plan); // Add free plan to the beginning
         // Optional: Filter out any potential duplicates of the free plan if necessary
         $ids = [];
         $decoded_memberships = array_filter($decoded_memberships, function($plan) use (&$ids) {
              if (isset($plan['id']) && !in_array($plan['id'], $ids)) {
                   $ids[] = $plan['id'];
                   return true;
              }
              return false;
         });
         $decoded_memberships = array_values($decoded_memberships); // Re-index array
    }

// Filter out plans with negative primary quota
$filtered_memberships = [];
foreach ($decoded_memberships as $plan) {
    if (isset($plan['quota']) && $plan['quota'] >= 0) {
        $filtered_memberships[] = $plan;
    } else if (!isset($plan['quota'])) {
         // Keep plans without a 'quota' key (like the default free plan if it lacks it)
         $filtered_memberships[] = $plan;
    }
}

$MC_CLOUD_MEMBERSHIPS = $filtered_memberships;
    // Line 472 removed
    return $filtered_memberships; // Return the filtered array
}

function membership_get_active($cache = true) {
    global $MC_CLOUD_ACTIVE_MEMBERSHIP;
    if (isset($MC_CLOUD_ACTIVE_MEMBERSHIP)) {
        return $MC_CLOUD_ACTIVE_MEMBERSHIP;
    }
    $account = account();
    $cloud_user_id = $account['user_id'];
    $cache = $cache ? json_decode(super_get_user_data('active_membership_cache', $cloud_user_id), true) : false;
    if ($cache && $cache[1] > time()) {
        $MC_CLOUD_ACTIVE_MEMBERSHIP = $cache[0];
        return $cache[0];
    }
    $memberships = memberships();
    $membership = $memberships[0];
    $membership_active = db_get('SELECT membership, membership_expiration, credits FROM users WHERE id = ' . $cloud_user_id);
    $membership_id = mc_isset($membership_active, 'membership', 0);
    for ($i = 0; $i < count($memberships); $i++) {
        if ($memberships[$i]['id'] == $membership_id) {
            $membership = $memberships[$i];
            break;
        }
    }
    $membership['credits'] = floatval(mc_isset($membership_active, 'credits', 0));
    $membership['expiration'] = mc_isset($membership_active, 'membership_expiration');
    $membership_type = mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages');
    if ($membership_type == 'messages' || $membership_type == 'messages-agents') {
        $membership['count'] = mc_isset(db_get('SELECT count FROM membership_counter WHERE date = "' . date('m-y') . '" AND user_id = ' . $cloud_user_id), 'count', 0);
        if ($membership_type != 'messages') {
            $membership['count_agents'] = membership_count_agents_users($account['token']);
        }
    } else {
        $membership['count'] = membership_count_agents_users($account['token']);
    }
    $MC_CLOUD_ACTIVE_MEMBERSHIP = $membership;
    $json = db_escape(json_encode([$membership, time() + 86400]));
    if ($cache) {
        db_query('UPDATE users_data SET value = "' . $json . '" WHERE user_id = ' . $cloud_user_id . ' AND slug = "active_membership_cache"');
    } else {
        membership_delete_cache($cloud_user_id);
        super_insert_user_data('(' . $cloud_user_id . ', "active_membership_cache", "' . $json . '")');
    }
    return $membership;
}

function membership_get($membership_id) {
    $memberships = memberships();
    if (!$membership_id) {
        return $memberships[0];
    }
    for ($i = 0; $i < count($memberships); $i++) {
        if ($memberships[$i]['id'] == $membership_id) {
            return $memberships[$i];
        }
    }
    return false;
}

function membership_update($membership_id, $membership_period, $customer_id, $payment_id = false, $referral = false) {
    membership_add_reseller_sale($membership_id); // Track sale for reseller if applicable
    $customer_id_escaped = db_escape($customer_id); // Use a different var name to avoid confusion
    $membership_id_escaped = db_escape($membership_id);
    $expiration_date = membership_calculate_expiration($membership_period);

    // Construct the update query parts
    $update_fields = [
        'membership = "' . $membership_id_escaped . '"',
        'membership_expiration = "' . $expiration_date . '"'
    ];

    // Add customer_id update only if a valid payment_id is provided
    // This is useful for storing Stripe customer ID, Paystack customer code, etc.
    if ($payment_id) {
        $update_fields[] = 'customer_id = "' . db_escape($payment_id) . '"';
    }

    // Build the final query
    $query = 'UPDATE users SET ' . implode(', ', $update_fields) . ' WHERE id = ' . $customer_id_escaped;
    $response = db_query($query);

    // Referral commission logic (seems unchanged between versions)
    if ($referral && $response === true) { // Process referral only if update was successful
        $price = mc_isset(membership_get($membership_id), 'price');
        $commission = mc_isset(super_get_settings(), 'referral-commission');

        // Check if referred data already exists *before* potentially adding commission
        if ($price && $commission && !super_get_user_data('referred', $customer_id_escaped)) {
            $user_id = db_escape(mc_encryption($referral, false)); // Decrypt referral code to get referrer user ID
            $total = super_get_user_data('referral', $user_id, 0); // Get current referral balance
            $price = floatval($price) * (intval($commission) / 100); // Calculate commission amount

            if ($price > 0) { // Only update if commission is positive
                if ($total) {
                    db_query('UPDATE users_data SET value = "' . (floatval($total) + $price) . '" WHERE user_id = ' . $user_id . ' AND slug = "referral" LIMIT 1');
                } else {
                    super_insert_user_data('(' . $user_id . ', "referral", "' . $price . '")');
                }
                // Mark the customer as referred to prevent duplicate commissions
                super_insert_user_data('(' . $customer_id_escaped . ', "referred", "true")');
            }
        }
    }

    // Clear caches (unchanged)
    db_query('DELETE FROM users_data WHERE user_id = ' . $customer_id_escaped . ' AND (slug = "notifications_count" OR slug = "active_membership_cache" OR slug = "subscription_cancelation")'); // Removed LIMIT 3 for safety if other keys are added later

    return $response; // Return the result of the main DB update query
}

function membership_volume() {
    $account = account();
    $year = date('y');
    $counts = 0;
    $response = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
    $membership_type = mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages');
    if ($membership_type == 'messages' || $membership_type == 'messages-agents') {
        $counts = db_get('SELECT count, date FROM membership_counter WHERE date LIKE "%-' . $year . '" AND user_id = ' . $account['user_id'], false);
        for ($i = 0; $i < count($counts); $i++) {
            $response[intval(substr($counts[$i]['date'], 0, 2)) - 1] = $counts[$i]['count'];
        }
    } else {
        $response = [membership_count_agents_users($account['token'])];
    }
    return $response;
}

function membership_get_payments() {
    return db_get('SELECT * from users_data WHERE user_id = ' . get_active_account_id() . ' AND slug = "payment" ORDER BY id DESC', false);
}

function membership_get_invoice($payment_id) {
    $user_id = get_active_account_id();
    $payment = db_get('SELECT * from users_data WHERE user_id = ' . $user_id . ' AND id = ' . db_escape($payment_id, true));
    if ($payment) {
        $payment = json_decode($payment['value'], true);
        return cloud_invoice($user_id, $payment[0], $payment[1], $payment[5]);
    }
    return false;
}

function membership_invoices_payment_provider() {
    // Get the primary identifier stored for the user for the current payment provider
    $payment_id = account_get_payment_id(); // This might be Stripe ID, Rapyd ID, Paystack Email/CustomerCode, etc.

    if (!$payment_id) {
         mc_cloud_debug('membership_invoices_payment_provider: No payment_id found for active user.');
        return []; // Return empty if no identifier is found
    }

    switch (PAYMENT_PROVIDER) {
        case 'stripe':
            // $payment_id should be the Stripe Customer ID (e.g., cus_XXXXXXXX)
            $response = stripe_curl('invoices?customer=' . $payment_id . '&limit=99', 'GET'); // Added limit
            return mc_isset($response, 'data', []);
            break; // Added break statement

        case 'rapyd':
            // $payment_id should be the Rapyd Customer ID (e.g., cus_XXXXXXXX)
            $response = json_decode(rapyd_curl('payments?limit=99&customer=' . $payment_id, '', 'GET'), true);
            return mc_isset($response, 'data', []);
            break; // Added break statement

        case 'verifone':
            // $payment_id might be the Verifone customer reference
            return verifone_get_orders($payment_id);
            break; // Added break statement

        case 'paystack':
            // For Paystack, paystack_get_transactions expects the customer's EMAIL.
            // We need to ensure $payment_id holds the email or fetch it.
            $account_data = account();
            $email = isset($account_data['email']) ? $account_data['email'] : null;

            if ($email) {
                 // Call the function that fetches transactions based on email (or customer code if found internally)
                 $response = paystack_get_transactions($email);
                 return $response ?? []; // Return the formatted transactions or empty array
            } else {
                 mc_cloud_debug('membership_invoices_payment_provider (Paystack): Could not retrieve user email.');
                 return [];
            }
            break; // Added break statement

        // Add cases for other payment providers if needed (e.g., Razorpay, YooMoney)
        // case 'razorpay': ...
        // case 'yoomoney': ...

    }
    return []; // Default return empty array
}

function membership_calculate_expiration($period) {
    $seconds = 0;
    switch ($period) {
        case 'day':
            $seconds = 172800;
            break;
        case 'week':
            $seconds = 691200;
            break;
        case 'month':
            $seconds = 2678400;
            break;
        case 'year':
            $seconds = 31622400;
            break;
        case '3month':
            $seconds = 8035200;
            break;
        case '6month':
            $seconds = 15638400;
            break;
        case '2year':
            $seconds = 63072000;
            break;
        case '3year':
            $seconds = 94521600;
            break;
    }
    return gmdate('d-m-y', time() + $seconds);
}

function membership_get_period_string($plan_period) {
    switch ($plan_period) {
        case 'day':
            return mc_('a day');
        case 'week':
            return mc_('a week');
        case 'month':
            return mc_('a month');
        case 'year':
            return mc_('a year');
        case '3month': // Support for multi-month intervals if needed
            return mc_('every 3 months');
        case '6month':
            return mc_('every 6 months');
        case '2year': // Support for multi-year intervals if needed
            return mc_('every 2 years');
        case '3year':
            return mc_('every 3 years');
        // Add other intervals if used by any payment provider
    }
    // Fallback or default if needed, though '' might be better if period is unknown
    return ''; // Return empty string if period is not recognized
}

function membership_count_agents_users($token) {
    return db_get('SELECT COUNT(*) AS `count` FROM mc_users WHERE ' . (mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages') == 'users' ? 'user_type <> "admin" AND user_type <> "agent" AND user_type <> "bot"' : 'user_type = "admin" OR user_type = "agent"'), true, $token)['count'];
}

function membership_add_reseller_sale($membership_id = false, $extra = false, $amount = false) {
    $url_part = 'https://app.masichat.com/account/resellers/api.php?cloud_url=' . CLOUD_URL . '&cloud_email=' . SUPER_EMAIL . ($extra ? '&extra=' . $extra : '') . '&price=';
    if ($amount) {
        return mc_get($url_part . $amount . '&currency=' . membership_currency());
    }
    $membership = membership_get($membership_id);
    $amount = $membership['price'];
    return empty($amount) || $amount == '0' ? false : mc_get($url_part . $amount . '&currency=' . $membership['currency']);
}

function membership_save_white_label($cloud_user_id) {
    super_delete_user_data($cloud_user_id, 'white-label');
    return super_insert_user_data('(' . $cloud_user_id . ', "white-label", "' . gmdate('d-m-y', time() + ********) . '")');
}

function membership_is_white_label($cloud_user_id) {
    $white_label = super_get_user_data('white-label', $cloud_user_id);
    return $white_label ? mc_gmt_now(0, true) < cloud_gmt_time($white_label) : false;
}

function membership_purchase_white_label($external_integration = false) {
    $cloud_user_id = get_active_account_id(false); // Get non-escaped user ID
    $price = super_get_white_label(); // Get the price set in super admin settings

    if ($price && $cloud_user_id) { // Ensure price is set and user is logged in
        if ($external_integration == 'shopify') {
            // Handle Shopify integration if needed
            return shopify_one_time_purchase('White Label', $price, CLOUD_URL . '/account/?tab=membership&reload=true#addons');
        }

        // Get the customer ID stored for the specific payment provider
        $customer_id = account_get_payment_id(); // This should return Stripe ID, Rapyd ID, Paystack Code etc.
        $accountData = account(); // Get user account details (needed for email, name etc.)

        switch (PAYMENT_PROVIDER) {
            case 'stripe':
                 // Assumes a specific Stripe Product/Price ID exists for White Label
                $price_response = mc_isset(stripe_curl('prices?product=' . STRIPE_PRODUCT_ID_WHITE_LABEL . '&active=true', 'GET'), 'data'); // Get active price
                if ($price_response && isset($price_response[0]['id'])) {
                     // Use client_reference_id to track the purpose
                     return stripe_create_session($price_response[0]['id'], $cloud_user_id, 'white_label|' . $cloud_user_id . '|' . (isset($_COOKIE['mc-referral']) ? $_COOKIE['mc-referral'] . '|' : '') . 'mc');
                } else {
                     mc_cloud_debug('membership_purchase_white_label: Stripe white label price not found or inactive.', ['product_id' => STRIPE_PRODUCT_ID_WHITE_LABEL]);
                     return ['status' => false, 'message' => 'White Label option configuration error (Stripe).'];
                }
                break;

            case 'rapyd':
                 // Create Rapyd customer if not exists
                if (!$customer_id) {
                    $customer_id = rapyd_create_customer(); // Assumes this function saves the ID to the user profile
                }
                 // Create Rapyd checkout session
                $response = rapyd_curl_checkout($price, $customer_id, $cloud_user_id, ['white_label' => true, 'mc_cloud_user_id' => $cloud_user_id, 'client_reference_id' => 'white_label|' . $cloud_user_id . '|' . (isset($_COOKIE['mc-referral']) ? $_COOKIE['mc-referral'] . '|' : '') . 'mc']);
                return isset($response['redirect_url']) ? ['url' => $response['redirect_url']] : $response;
                break;

            case 'verifone':
                 // Construct Verifone buy link
                $url = 'https://secure.2checkout.com/checkout/buy?merchant=' . VERIFONE_MERCHANT_ID .
                       '&dynamic=1&currency=' . VERIFONE_CURRENCY .
                       '&customer-ref=' . ($customer_id ? $customer_id : 'false') . // Pass customer ref if available
                       '&duration=10:YEAR&email=' . $accountData['email'] .
                       // Encrypt metadata including purpose and user ID
                       '&order-ext-ref=' . mc_encryption('white_label|' . $cloud_user_id . (isset($_COOKIE['mc-referral']) ? '|' . $_COOKIE['mc-referral'] : '') . '|mc') .
                       '&price=' . $price .
                       '&prod=' . mc_('White Label') . // Use translated product name
                       '&qty=1&recurrence=1:YEAR&renewal-price=' . $price . // Assuming annual recurrence
                       '&type=digital';
                return ['url' => $url . '&signature=' . verifone_get_signature($url)]; // Add signature
                break;

            case 'razorpay':
                // Use the custom payment function for a one-time charge
                return membership_custom_payment($price, 'white_label'); // Pass 'white_label' as metadata_id
                break;

            case 'yoomoney':
                 // Create YooMoney payment request
                 // Metadata should include user ID and purpose
                $metadata = ['mc_user_id' => $cloud_user_id, 'white_label' => true, 'client_reference_id' => 'white_label|' . $cloud_user_id . '|' . (isset($_COOKIE['mc-referral']) ? $_COOKIE['mc-referral'] . '|' : '') . 'mc'];
                $response = yoomoney_create_payment($price, YOOMONEY_CURRENCY, CLOUD_URL . '/account/?tab=membership', mc_('White Label'), $metadata);
                $confirmation = mc_isset($response, 'confirmation');
                return $confirmation ? ['url' => $confirmation['confirmation_url']] : $response;
                break;

            case 'manual':
                // Redirect to the manual payment link configured
                return ['url' => PAYMENT_PROVIDER_MANUAL_LINK];
                break;

            case 'paystack':
                 // For Paystack, white label is typically a one-time payment, not a subscription plan.
                 // We use paystack_create_session with subscription=false and the price in 'extra'.

                 // Ensure Paystack customer exists (or create one) - customer_id here is Paystack's customer_code
                 if (!$customer_id) {
                     $customer_id = paystack_create_customer($accountData['email'], $accountData['first_name'], $accountData['last_name']);
                     if (!$customer_id) {
                         return ['status' => false, 'message' => 'Failed to create Paystack customer profile.'];
                     }
                     // Customer ID (code) is saved within paystack_create_customer now
                 }

                 // Prepare client reference ID for metadata
                 $client_reference_id = 'white_label|' . $cloud_user_id . '|' . (isset($_COOKIE['mc-referral']) ? $_COOKIE['mc-referral'] . '|' : '') . 'mc';

                 // Call paystack_create_session for a one-time payment
                 // Pass plan_id=false, subscription=false, price in $extra
                 $response = paystack_create_session(false, $cloud_user_id, $client_reference_id, false, $price);
                 return $response; // Return the response which should contain ['url' => ...] or an error
                 break;

        }
    } else if (!$price) {
         mc_cloud_debug('membership_purchase_white_label: White label price is not set or is zero.');
         return ['status' => false, 'message' => 'White Label option is currently unavailable.'];
    } else {
         mc_cloud_debug('membership_purchase_white_label: User not logged in.');
         return ['status' => false, 'message' => 'User not logged in.'];
    }
    return false; // Default return false if something went wrong
}

function membership_purchase_credits($amount, $external_integration = false) {
    if ($external_integration == 'shopify') {
        return shopify_one_time_purchase('Add credits - ' . $amount . ' USD', $amount, CLOUD_URL . '/account/?tab=membership&reload=true&payment_type=credits#credits');
    }
    return membership_custom_payment($amount, 'credits');
}

function membership_custom_payment($amount, $metadata_id) {
    $cloud_user_id = get_active_account_id(false); // Get non-escaped user ID
    $amount = floatval($amount); // Ensure amount is a float

    if ($amount > 0 && $cloud_user_id) { // Check for valid amount and logged-in user
        $payment_id = account_get_payment_id(); // Get provider-specific customer ID/code/email
        $accountData = account(); // Get account details

        // Prepare a base client reference ID string

       $client_reference_base = $metadata_id . '|' . $cloud_user_id . '|' . (isset($_COOKIE['mc-referral']) ? $_COOKIE['mc-referral'] . '|' : '');

        switch (PAYMENT_PROVIDER) {
            case 'stripe':
                 // Create Stripe customer if not exists
                if (!$payment_id) {
                    // Use urlencode for name parts
                    $customer_name = urlencode($accountData['first_name']) . '%20' . urlencode($accountData['last_name']);
                    $stripe_customer = stripe_curl('customers?email=' . urlencode($accountData['email']) . '&name=' . $customer_name);
                    $payment_id = mc_isset($stripe_customer, 'id');
                    if ($payment_id) {
                         // Save Stripe customer ID to user profile
                         db_query('UPDATE users SET customer_id = "' . db_escape($payment_id) . '" WHERE id = ' . $cloud_user_id);
                    } else {
                         mc_cloud_debug('membership_custom_payment: Failed to create Stripe customer.', ['response' => $stripe_customer]);
                         return ['status' => false, 'message' => 'Failed to create payment profile (Stripe).'];
                    }
                }
                 // Create Stripe checkout session for a one-time payment
                 // Amount needs conversion based on currency divider
                $amount_in_cents = $amount * currency_get_divider(STRIPE_CURRENCY);
                $client_reference_id = $client_reference_base . $amount_in_cents . '|mc'; // Include amount info? Or keep simple? Let's keep it simple for now.
                $client_reference_id = $metadata_id . '|' . $cloud_user_id . '|' . (isset($_COOKIE['mc-referral']) ? $_COOKIE['mc-referral'] . '|' : '') . 'mc'; // Revert to simpler ref
                return stripe_create_session(false, $cloud_user_id, $client_reference_id, false, STRIPE_CURRENCY, $amount); // Pass amount directly
                break;

            case 'rapyd':
                 // Create Rapyd customer if not exists
                if (!$payment_id) {
                    $payment_id = rapyd_create_customer();
                }
                 // Metadata for Rapyd checkout
                 $metadata = ['mc_cloud_user_id' => $cloud_user_id, 'client_reference_id' => $client_reference_base . 'mc'];
                 $metadata[$metadata_id] = true; // Add the specific purpose flag
                $response = rapyd_curl_checkout($amount, $payment_id, $cloud_user_id, $metadata);
                return isset($response['redirect_url']) ? ['url' => $response['redirect_url']] : $response;
                break;

            case 'verifone':
                 // Construct Verifone buy link for one-time payment
                $url = 'https://secure.2checkout.com/checkout/buy?merchant=' . VERIFONE_MERCHANT_ID .
                       '&dynamic=1&currency=' . VERIFONE_CURRENCY .
                       '&customer-ref=' . ($payment_id ? $payment_id : 'false') .
                       '&duration=10:YEAR&email=' . $accountData['email'] .
                       // Encrypt metadata including purpose and user ID
                       '&order-ext-ref=' . mc_encryption($client_reference_base . 'mc') .
                       '&price=' . $amount .
                       '&prod=' . mc_(mc_string_slug($metadata_id, 'string')) . // Generate product name from metadata ID
                       '&qty=1&type=digital'; // One-time digital product
                return ['url' => $url . '&signature=' . verifone_get_signature($url)];
                break;

            case 'razorpay':
                 // Create Razorpay payment link
                 $notes = ['mc_user_id' => $cloud_user_id, 'client_reference_id' => $client_reference_base . 'mc'];
                 $notes[$metadata_id] = true; // Add specific purpose flag
                 // Amount needs conversion based on currency divider
                 $amount_in_paise = $amount * currency_get_divider(RAZORPAY_CURRENCY);
                $response = razorpay_create_payment_link($amount_in_paise, $notes);
                // Check if response is a valid URL string
                return is_string($response) && filter_var($response, FILTER_VALIDATE_URL) ? ['url' => $response] : $response;
                break;

            case 'yoomoney':
                 // Create YooMoney payment request
                $metadata = ['mc_user_id' => $cloud_user_id, 'client_reference_id' => $client_reference_base . 'mc'];
                $metadata[$metadata_id] = true; // Add specific purpose flag
                $response = yoomoney_create_payment($amount, YOOMONEY_CURRENCY, CLOUD_URL . '/account?tab=membership', mc_string_slug($metadata_id, 'string'), $metadata);
                $confirmation = mc_isset($response, 'confirmation');
                return $confirmation ? ['url' => $confirmation['confirmation_url']] : $response;
                break;

            case 'manual':
                 // Redirect to manual payment link
                return ['url' => PAYMENT_PROVIDER_MANUAL_LINK];
                break;

            case 'paystack':
                 // Ensure Paystack customer exists (or create one) - $payment_id should be customer_code here
                 if (!$payment_id) {
                      $payment_id = paystack_create_customer($accountData['email'], $accountData['first_name'], $accountData['last_name']);
                      if (!$payment_id) {
                          return ['status' => false, 'message' => 'Failed to create Paystack customer profile.'];
                      }
                      // Customer code is saved within paystack_create_customer
                 }

                 // Metadata for Paystack checkout
                 $metadata = ['metadata_id' => $metadata_id]; // Pass the original metadata_id for tracking internally if needed
                 // Use paystack_create_checkout (which calls transaction/initialize)
                 // It will internally create the client_reference_id
                 $response = paystack_create_checkout($amount, $payment_id, $cloud_user_id, $metadata);
                 return $response; // Return the response which should contain ['url' => ...] or an error
                 break;
        }
    } else if ($amount <= 0) {
         return ['status' => false, 'message' => 'Invalid payment amount.'];
    } else {
         return ['status' => false, 'message' => 'User not logged in.'];
    }
    return false; // Default return false
}

function membership_use_credits($spending_source, $extra = false) {
    // $spending_sources cost based on the highest cost between the input and the output / 1.000.000
    $spending_sources = [
        'es' => 0.002,
        'cx' => 0.007,
        'es-audio' => 0.000433,
        'cx-audio' => 0.001,
        'translation' => 0.00002,
        'gpt-3.5-turbo-instruct' => 0.000002,
        'gpt-3.5-turbo' => 0.000002,
        'gpt-3.5-turbo-0125' => 0.000001,
        'gpt-3.5-turbo-1106' => 0.000002,
        'gpt-4' => 0.00003,
        'gpt-4-32k' => 0.00006,
        'gpt-4-turbo' => 0.00003,
        'gpt-4o' => 0.000015,
        'gpt-4o-mini' => 0.00000015,
        'o1' => 0.00006,
        'o1-mini' => 0.000012,
        'o3-mini' => 0.0000044,
        'ada' => 0.0000001,
        'text-embedding-3-small' => 0.00000002,
        'whisper' => 0.0001
    ];
    switch ($spending_source) {
        case 'whisper':
        case 'cx-audio':
        case 'es-audio':
            $browser = mc_isset($_SERVER, 'HTTP_SEC_CH_UA', '');
            $divider = 18;
            if (strpos($browser, 'Opera') !== false) {
                $divider = 8;
            }
            $amount = ($spending_source == 'whisper' ? filesize($extra) : $extra) / 1000 / $divider;
            $spending_sources[$spending_source] = $spending_sources[$spending_source] * $amount;
            break;
        case 'translation':
            $spending_sources[$spending_source] = $spending_sources[$spending_source] * strlen($extra);
            break;
        case 'text-embedding-3-small':
        case 'ada':
        case 'o3-mini':
        case 'o1':
        case 'o1-mini':
        case 'gpt-4-turbo':
        case 'gpt-4o':
        case 'gpt-4o-mini':
        case 'gpt-4-32k':
        case 'gpt-4':
        case 'gpt-3.5-turbo':
        case 'gpt-3.5-turbo-0125':
        case 'gpt-3.5-turbo-1106':
        case 'gpt-3.5-turbo-instruct':
            $spending_sources[$spending_source] = $spending_sources[$spending_source] * $extra;
            break;
    }
    $amount = mc_isset($spending_sources, $spending_source) * 2;
    return $amount ? db_query('UPDATE users SET credits = credits - ' . $amount . ' WHERE id = ' . get_active_account_id()) : false;
}

function membership_set_auto_recharge($enabled) {
    $user_id = db_escape(mc_isset(account(), 'user_id'), true);
    super_delete_user_data($user_id, 'auto_recharge', true);
    return $enabled && $enabled != 'false' ? super_insert_user_data('(' . $user_id . ', "auto_recharge", "1")') : true;
}

function membership_auto_recharge() {
    $accountData = account(); // Attempt to get account data

    // --- Robust Check for User ID (from Old+PS version) ---
    if (!$accountData || !isset($accountData['user_id']) || empty($accountData['user_id'])) {
        mc_cloud_debug('membership_auto_recharge: Cannot run, account data or user_id missing or empty.', ['account_data_type' => gettype($accountData), 'account_data_dump' => json_encode($accountData)]);
        return false; // Exit early if no valid account/user ID
    }
    $non_escaped_user_id = $accountData['user_id']; // Use non-escaped ID for DB lookups
    // --- End Robust Check ---

    mc_cloud_debug('membership_auto_recharge: Checking for user ID ' . $non_escaped_user_id);

    switch (PAYMENT_PROVIDER) {
        case 'stripe':
            // Logic from 'New' version
            $payment_method = super_get_user_data('stripe_payment_method', $non_escaped_user_id);
            if ($payment_method) {
                 // Find the amount of the last successful 'credits' purchase
                 $payments_history = db_get('SELECT value FROM users_data WHERE slug = "payment" AND user_id = ' . $non_escaped_user_id . ' AND JSON_EXTRACT(value, "$[1]") = "credits" ORDER BY id DESC LIMIT 1', false);
                 if ($payments_history && isset($payments_history[0]['value'])) {
                      $last_payment = json_decode($payments_history[0]['value'], true);
                      if (is_array($last_payment) && isset($last_payment[0]) && is_numeric($last_payment[0]) && $last_payment[0] > 0) {
                           $amount = floatval($last_payment[0]);
                           $customer_id = account_get_payment_id(); // Get Stripe customer ID
                           if ($customer_id) {
                                $amount_in_cents = $amount * currency_get_divider(STRIPE_CURRENCY);
                                $response = stripe_curl('payment_intents', [
                                    'confirm' => 'true',
                                    'setup_future_usage' => 'off_session', // Important for subsequent charges
                                    'amount' => $amount_in_cents,
                                    'currency' => STRIPE_CURRENCY,
                                    'metadata' => ['mc_credits_recharge' => 'true', 'mc_user_id' => $non_escaped_user_id], // Add metadata
                                    'payment_method' => $payment_method,
                                    'customer' => $customer_id,
                                    'off_session' => 'true' // Indicate this is an off-session charge
                                ]);

                                mc_cloud_debug('membership_auto_recharge (Stripe): Payment Intent response', ['response' => $response]);

                                // Check if payment intent succeeded or requires action
                                if (mc_isset($response, 'status') == 'succeeded') {
                                     // Payment succeeded, update credits
                                     if (membership_set_purchased_credits($amount, STRIPE_CURRENCY, $non_escaped_user_id, $response['id'], 'stripe_auto_recharge')) {
                                          mc_cloud_debug('membership_auto_recharge (Stripe): Credits updated successfully for user ' . $non_escaped_user_id);
                                          return true; // Success!
                                     } else {
                                           mc_cloud_debug('membership_auto_recharge (Stripe): Payment successful, but credits update failed for user ' . $non_escaped_user_id);
                                     }
                                } elseif (mc_isset($response, 'status') == 'requires_payment_method' || mc_isset($response, 'status') == 'requires_action') {
                                     // Payment failed, likely needs new card or 3DS auth. Deactivate the saved method?
                                     mc_cloud_debug('membership_auto_recharge (Stripe): Payment failed or requires action. Deleting saved payment method for user ' . $non_escaped_user_id);
                                     super_delete_user_data($non_escaped_user_id, 'stripe_payment_method', true);
                                } else {
                                     // Other failure
                                     mc_cloud_debug('membership_auto_recharge (Stripe): Payment Intent failed.', ['error' => mc_isset($response, 'error', $response)]);
                                }
                           } else {
                                mc_cloud_debug('membership_auto_recharge (Stripe): Stripe Customer ID not found for user ' . $non_escaped_user_id);
                           }
                      } else {
                            mc_cloud_debug('membership_auto_recharge (Stripe): Invalid amount found in last credits payment history for user ' . $non_escaped_user_id);
                      }
                 } else {
                      mc_cloud_debug('membership_auto_recharge (Stripe): No previous credits payment history found for user ' . $non_escaped_user_id . '. Cannot determine recharge amount.');
                 }
            } else {
                mc_cloud_debug('membership_auto_recharge (Stripe): stripe_payment_method not found for user ID ' . $non_escaped_user_id);
            }
            break; // End Stripe case

        case 'yoomoney':
             // Logic from 'New' version
            $payments = db_get('SELECT value FROM users_data WHERE slug = "payment" AND user_id = ' . $non_escaped_user_id . ' ORDER BY id DESC', false);
            if ($payments) {
                 // Find the last 'credits' payment to get the amount and payment method ID
                 $amount_to_recharge = 0;
                 $payment_method_id = null; // YooMoney payment method ID associated with the purchase

                 foreach ($payments as $paymentRow) {
                     $payment = json_decode($paymentRow['value'], true);
                     // Assuming format [amount, 'credits', yoomoney_payment_method_id, ...]
                     if (is_array($payment) && count($payment) >= 3 && $payment[1] == 'credits' && !empty($payment[2])) {
                          $amount_to_recharge = floatval($payment[0]);
                          $payment_method_id = $payment[2]; // Get the ID used for the purchase
                          mc_cloud_debug('membership_auto_recharge (YooMoney): Found last credits payment. Amount: ' . $amount_to_recharge . ', Method ID: ' . $payment_method_id);
                          break; // Found the latest one
                     }
                 }

                 if ($amount_to_recharge > 0 && $payment_method_id) {
                      // Call YooMoney recurring payment function (assuming it exists and handles the charge)
                      $response = yoomoney_recurring_payment($amount_to_recharge, $payment_method_id, 'Credits Auto-Recharge'); // Pass amount, method ID, description

                      mc_cloud_debug('membership_auto_recharge (YooMoney): Recurring payment response', ['response' => $response]);

                      // Check response from yoomoney_recurring_payment
                      // Adjust this based on how yoomoney_recurring_payment indicates success (e.g., returns true, or a transaction ID)
                      if ($response === true || (is_array($response) && mc_isset($response, 'status') === 'succeeded')) { // Example success checks
                           // Payment successful, update credits
                           // Pass the payment method ID or a transaction ID from response as payment_history_id if appropriate
                           $history_id = is_array($response) ? mc_isset($response, 'id', $payment_method_id) : $payment_method_id;
                           if (membership_set_purchased_credits($amount_to_recharge, YOOMONEY_CURRENCY, $non_escaped_user_id, $history_id, 'yoomoney_auto_recharge')) {
                                mc_cloud_debug('membership_auto_recharge (YooMoney): Credits updated successfully for user ' . $non_escaped_user_id);
                                return true; // Success!
                           } else {
                                mc_cloud_debug('membership_auto_recharge (YooMoney): Payment successful, but credits update failed for user ' . $non_escaped_user_id);
                           }
                      } else {
                            // Payment failed
                            mc_cloud_debug('membership_auto_recharge (YooMoney): Recurring payment failed for user ' . $non_escaped_user_id);
                            // Optionally delete the saved payment method ID if the failure indicates it's invalid
                            // Requires knowledge of how YooMoney failures are reported
                            // Example: super_delete_user_data($non_escaped_user_id, 'yoomoney_payment_method_id', true);
                      }
                 } else {
                      mc_cloud_debug('membership_auto_recharge (YooMoney): Could not find previous credits payment with payment method ID for user ' . $non_escaped_user_id);
                 }
            } else {
                 mc_cloud_debug('membership_auto_recharge (YooMoney): No payment history found for user ' . $non_escaped_user_id);
            }
            break; // End YooMoney case

        case 'paystack':
             // Logic block from 'Old+PS' version (seems complete)
             mc_cloud_debug('membership_auto_recharge: Initiating Paystack auto-recharge check for user ' . $non_escaped_user_id);

             // 1. Get the saved authorization code
             $authorization_code = super_get_user_data('paystack_authorization_code', $non_escaped_user_id);
             $auth_details = super_get_user_data('paystack_auth_details', $non_escaped_user_id); // Get details like card type, expiry

             // Optional: Check card expiry from stored details if available
             if ($auth_details) {
                 $auth_details_arr = json_decode($auth_details, true);
                 if (isset($auth_details_arr['exp_year']) && isset($auth_details_arr['exp_month'])) {
                     $expiry_year = intval('20' . $auth_details_arr['exp_year']); // Assuming YY format
                     $expiry_month = intval($auth_details_arr['exp_month']);
                     $expiry_timestamp = mktime(0, 0, 0, $expiry_month + 1, 1, $expiry_year) - 1; // End of expiry month
                     if (time() > $expiry_timestamp) {
                          mc_cloud_debug('membership_auto_recharge (Paystack): Authorization code likely expired based on card details for user ' . $non_escaped_user_id);
                          super_delete_user_data($non_escaped_user_id, 'paystack_authorization_code', true);
                          super_delete_user_data($non_escaped_user_id, 'paystack_auth_details', true);
                          $authorization_code = null; // Nullify to prevent charge attempt
                     }
                 }
             }

             if ($authorization_code) {
                  mc_cloud_debug('membership_auto_recharge: Found authorization code for user ' . $non_escaped_user_id);

                 // 2. Determine the recharge amount (using the last 'credits' payment)
                 $amount_to_recharge = 0;
                 $currency_code = PAYSTACK_CURRENCY; // Get Paystack currency

                 // Get the latest payment history entry specifically for 'credits'
                 $payments_history = db_get('SELECT value FROM users_data WHERE slug = "payment" AND user_id = ' . $non_escaped_user_id . ' AND JSON_EXTRACT(value, "$[1]") = "credits" ORDER BY id DESC LIMIT 1', false);

                 if ($payments_history && isset($payments_history[0]['value'])) {
                     $last_payment = json_decode($payments_history[0]['value'], true);
                     if (is_array($last_payment) && isset($last_payment[0]) && is_numeric($last_payment[0]) && $last_payment[0] > 0) {
                         $amount_to_recharge = floatval($last_payment[0]);
                          mc_cloud_debug('membership_auto_recharge: Determined recharge amount from last payment: ' . $amount_to_recharge . ' ' . $currency_code);
                     } else {
                           mc_cloud_debug('membership_auto_recharge: Invalid amount found in last credits payment history for user ' . $non_escaped_user_id);
                     }
                 } else {
                     mc_cloud_debug('membership_auto_recharge: No previous credits payment history found for user ' . $non_escaped_user_id . '. Cannot determine recharge amount.');
                 }

                 if ($amount_to_recharge > 0) {
                     // 3. Get customer email
                     $email = $accountData['email']; // Email should exist based on initial check
                     if (empty($email)) { // Extra safety check
                          mc_cloud_debug('membership_auto_recharge: Cannot proceed, user email not found in account data (unexpected).');
                          break; // Exit Paystack case
                     }

                     $amount_in_kobo = intval($amount_to_recharge * 100); // Convert to kobo/cents, ensure integer
                     $recharge_reference = 'recharge_' . $non_escaped_user_id . '_' . time(); // Unique ref

                     // 4. Call the charge authorization function
                     $response = paystack_charge_authorization($amount_in_kobo, $email, $authorization_code, $recharge_reference);

                     // 5. Check the response from paystack_charge_authorization
                     if ($response && isset($response['status']) && $response['status'] === true && isset($response['data']['status']) && $response['data']['status'] === 'success') {
                         // Charge successful via Paystack API
                         mc_cloud_debug('membership_auto_recharge (Paystack): charge_authorization successful for user ' . $non_escaped_user_id, ['response_data' => $response['data']]);

                         // Update credits locally
                         $transaction_reference = $response['data']['reference'] ?? $recharge_reference;
                         if (membership_set_purchased_credits($amount_to_recharge, $currency_code, $non_escaped_user_id, $transaction_reference, 'paystack_auto_recharge')) {
                              mc_cloud_debug('membership_auto_recharge (Paystack): Credits updated successfully for user ' . $non_escaped_user_id);
                              return true; // Success! Return immediately from the function.
                         } else {
                              mc_cloud_debug('membership_auto_recharge (Paystack): Paystack charge successful, but membership_set_purchased_credits failed for user ' . $non_escaped_user_id);
                              // Let it fall through to return false
                         }
                     } else {
                         // Charge failed or API error
                         $error_message = $response['message'] ?? 'Unknown error during charge authorization.';
                         $gateway_response = isset($response['data']['gateway_response']) ? $response['data']['gateway_response'] : 'N/A';
                          mc_cloud_debug('membership_auto_recharge (Paystack): charge_authorization failed for user ' . $non_escaped_user_id, ['error' => $error_message, 'gateway_response' => $gateway_response, 'api_response' => $response]);

                          // Deactivate the authorization code if it's permanently invalid (e.g., expired, stolen, insufficient funds might be temporary)
                          $lower_gw_response = strtolower($gateway_response . ' ' . $error_message);
                          if (strpos($lower_gw_response, 'authorization has expired') !== false ||
                              strpos($lower_gw_response, 'authorization is invalid') !== false ||
                              strpos($lower_gw_response, 'invalid authorisation') !== false ||
                              strpos($lower_gw_response, 'card has expired') !== false || // Check for expired card message
                              strpos($lower_gw_response, 'stolen') !== false || // Check for stolen card message
                              strpos($lower_gw_response, 'blacklist') !== false ) { // Check for blacklisted card

                               mc_cloud_debug('membership_auto_recharge (Paystack): Deleting invalid/expired authorization code for user ' . $non_escaped_user_id);
                               super_delete_user_data($non_escaped_user_id, 'paystack_authorization_code', true);
                               super_delete_user_data($non_escaped_user_id, 'paystack_auth_details', true);
                          }
                          // Let it fall through to return false
                     }
                 } else {
                      // Already logged failure to determine amount
                      // Let it fall through to return false
                 }
             } else {
                  mc_cloud_debug('membership_auto_recharge (Paystack): authorization code not found or expired for user ' . $non_escaped_user_id . '. Cannot auto-recharge.');
                  // Let it fall through to return false
             }
             break; // End Paystack case

    } // End Switch

    // If no provider logic returned true, return false
    return false;
}

function membership_set_purchased_credits($amount, $currency_code, $user_id, $payment_history_id = false, $extra = '') {
    // Ensure user ID is valid integer
    $user_id = intval($user_id);
    if ($user_id <= 0) {
        mc_cloud_debug([
            'function' => 'membership_set_purchased_credits_error',
            'message' => 'Invalid user ID provided.',
            'user_id' => $user_id
        ]);
        return false;
    }

    // Calculate credits based on amount and currency (using USD as base or direct 1:1)
    $credits = intval($amount * (strtolower($currency_code) == 'usd' ? 1 : (1 / mc_usd_rates($currency_code)))); // Ensure mc_usd_rates() handles the currency

    // Add debug logging pre-update
    mc_cloud_debug([
        'function' => 'membership_set_purchased_credits_pre',
        'amount' => $amount,
        'currency' => $currency_code,
        'calculated_credits' => $credits,
        'user_id' => $user_id,
        'payment_history_id' => $payment_history_id,
        'extra' => $extra
    ]);

    if ($credits <= 0) {
         mc_cloud_debug([
            'function' => 'membership_set_purchased_credits_warning',
            'message' => 'Calculated credits is zero or negative. Skipping update.',
            'amount' => $amount,
            'currency' => $currency_code,
            'credits' => $credits,
            'user_id' => $user_id
        ]);
        // Decide if this should be an error or just skip. Skipping might be safer.
        return true; // Return true as no *update* failed, but nothing was added. Or return false? Let's return true for now.
    }

    // Update credits in the database
    $result = db_query('UPDATE users SET credits = credits + ' . db_escape($credits, true) . ' WHERE id = ' . $user_id);

    if ($result === true) {
        // Clear relevant caches upon successful update
        super_delete_user_data($user_id, 'notifications_credits_count', true); // For top bar display
        super_delete_user_data($user_id, 'active_membership_cache', true); // For general membership status
        membership_delete_cache($user_id); // Clear specific membership function cache

        // Add reseller sale record if applicable
        membership_add_reseller_sale(false, 'credits', $amount);

        // Add to payment history if a payment ID (transaction reference) is provided
        if ($payment_history_id) {
            cloud_add_to_payment_history($user_id, $amount, 'credits', $payment_history_id, $extra);
        }

        // Debug logging post-update - fetch the new balance to confirm
        $new_balance_query = db_get('SELECT credits FROM users WHERE id = ' . $user_id);
        mc_cloud_debug([
            'function' => 'membership_set_purchased_credits_success',
            'amount' => $amount,
            'currency' => $currency_code,
            'credits_added' => $credits,
            'user_id' => $user_id,
            'new_balance_db' => mc_isset($new_balance_query, 'credits', 'N/A')
        ]);

        return true; // Indicate success
    }

    // Log error if database update failed
    mc_cloud_debug([
        'function' => 'membership_set_purchased_credits_error',
        'message' => 'Database update query failed.',
        'db_error' => $result, // Contains the error message from db_query
        'amount' => $amount,
        'credits_to_add' => $credits,
        'user_id' => $user_id
    ]);

    return false; // Indicate failure
}

function membership_currency() {
    switch (PAYMENT_PROVIDER) {
        case 'stripe':
            // Ensure STRIPE_CURRENCY is defined and not empty
            return defined('STRIPE_CURRENCY') && STRIPE_CURRENCY ? STRIPE_CURRENCY : 'usd'; // Default to usd if not set
        case 'rapyd':
            return defined('RAPYD_CURRENCY') && RAPYD_CURRENCY ? RAPYD_CURRENCY : 'usd';
        case 'verifone':
            return defined('VERIFONE_CURRENCY') && VERIFONE_CURRENCY ? VERIFONE_CURRENCY : 'usd';
        case 'razorpay':
            return defined('RAZORPAY_CURRENCY') && RAZORPAY_CURRENCY ? RAZORPAY_CURRENCY : 'inr'; // Default to inr
        case 'yoomoney':
            return defined('YOOMONEY_CURRENCY') && YOOMONEY_CURRENCY ? YOOMONEY_CURRENCY : 'rub'; // Default to rub
        case 'paystack':
            // Add Paystack case
            return defined('PAYSTACK_CURRENCY') && PAYSTACK_CURRENCY ? PAYSTACK_CURRENCY : 'zar'; // Default to zar
        case 'manual':
            return defined('PAYMENT_MANUAL_CURRENCY') && PAYMENT_MANUAL_CURRENCY ? PAYMENT_MANUAL_CURRENCY : 'usd';
    }
     // Fallback default currency if provider or constant is unknown/not set
     mc_cloud_debug('membership_currency: Could not determine currency for provider ' . PAYMENT_PROVIDER . '. Defaulting to USD.');
     return 'usd';
}

function membership_delete_cache($cloud_user_id) {
    return super_delete_user_data($cloud_user_id, 'active_membership_cache', true);
}

/*
 * -----------------------------------------------------------
 * STRIPE
 * -----------------------------------------------------------
 *
 */

function stripe_create_session($price_id, $cloud_user_id, $client_reference_id = false, $subscription = true, $extra = false) {
    $membership = $price_id ? membership_get($price_id) : false;
    if (!$client_reference_id) {
        $client_reference_id = $price_id . '|' . $cloud_user_id . '|' . $membership['period'] . (isset($_COOKIE['mc-referral']) ? '|' . $_COOKIE['mc-referral'] : '') . '|mc';
    } else if (!strpos($client_reference_id, '|mc')) {
        $client_reference_id .= '|mc';
    }
    $stripe_id = account_get_payment_id();
    $response = stripe_curl('checkout/sessions?cancel_url=' . CLOUD_URL . '/account%3Ftab=membership&success_url=' . CLOUD_URL . '/account/%3Ftab=membership%26payment=success' . (explode('|', $client_reference_id)[0] == 'credits' ? '%26payment_type=credits' : '') . ($price_id ? '&line_items[0][price]=' . $price_id . '&line_items[0][quantity]=1' : '&currency=' . $extra) . '&mode=' . ($subscription ? 'subscription' : ($price_id ? 'payment' : 'setup')) . '&client_reference_id=' . $client_reference_id . ($stripe_id && strpos($stripe_id, 'cus_') !== false ? ('&customer=' . $stripe_id) : ('&customer_email=' . account()['email'])));
    return $response;
}

function stripe_cancel_subscription() {
    $stripe_id = account_get_payment_id();
    if ($stripe_id) {
        $subscription_id = stripe_curl('subscriptions?customer=' . $stripe_id, 'GET');
        if ($subscription_id && isset($subscription_id['data'])) {
            if (count($subscription_id['data']) === 0) {
                return 'no-subscriptions';
            }
            $response = stripe_curl('subscriptions/' . $subscription_id['data'][0]['id'], 'DELETE');
            if (mc_isset($response, 'status') == 'canceled') {
                super_insert_user_data('(' . get_active_account_id() . ', "subscription_cancelation", "' . mc_gmt_now() . '")');
            }
            return $response;
        }
    }
    return false;
}

function stripe_curl($url_part, $type = 'POST') {
    $response = mc_curl('https://api.stripe.com/v1/' . $url_part, '', ['Authorization: Basic ' . base64_encode(STRIPE_SECRET_KEY)], $type);
    return $type == 'POST' || $type == 'PATCH' ? $response : json_decode($response, true);
}

function stripe_get_price($price_amount, $product_id, $currency_code) {
    $price_amount = intval($price_amount);
    $prices = stripe_curl('prices?product=' . $product_id . '&limit=100&type=one_time', 'GET');
    $currency_code = strtolower($currency_code);
    if (!isset($prices['data'])) {
        return $prices;
    }
    $prices = $prices['data'];
    for ($i = 0; i < count($prices); $i++) {
        if ($price_amount == $prices[$i]['unit_amount'] && $prices[$i]['currency'] == $currency_code) {
            return $prices[$i];
        }
    }
    return stripe_curl('prices?unit_amount=' . $price_amount . '&currency=' . $currency_code . '&product=' . $product_id);
}
/*
 * -----------------------------------------------------------
 * PAYSTACK API FUNCTIONS
 * -----------------------------------------------------------
 */

function paystack_curl($endpoint, $data = [], $method = 'POST') {
    $url = 'https://api.paystack.co/' . $endpoint;
    $data_string = $data ? json_encode($data) : '';
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
    } else if ($method == 'GET') {
        // GET method
    } else {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        if ($data_string) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
        }
    }
    $headers = [
        'Authorization: Bearer ' . PAYSTACK_SECRET_KEY,
        'Content-Type: application/json'
    ];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    curl_close($ch);
    return json_decode($response, true);
}

function paystack_create_session($plan_id, $cloud_user_id, $client_reference_id = false, $subscription = true, $extra = false) {
    $accountData = account();
    $email = $accountData['email'];
    $metadata = [];

    mc_cloud_debug([
        'function' => 'paystack_create_session',
        'plan_id' => $plan_id,
        'cloud_user_id' => $cloud_user_id
    ]);

    if (!$client_reference_id) {
        if ($plan_id) {
            $membership = membership_get($plan_id);
            $client_reference_id = $plan_id . '|' . $cloud_user_id . '|' . $membership['period'] . (isset($_COOKIE['mc-referral']) ? '|' . $_COOKIE['mc-referral'] : '') . '|mc';
        } else {
            $client_reference_id = 'credits|' . $cloud_user_id . '|' . (isset($_COOKIE['mc-referral']) ? $_COOKIE['mc-referral'] . '|' : '') . '|mc';
        }
    }

    $metadata['client_reference_id'] = $client_reference_id;

    // Get Paystack plan code for the membership
    $plan_code = false;
    if ($plan_id) {
        $membership = membership_get($plan_id);
        $amount = $membership['price'] * 100; // Convert to kobo

        // Find matching Paystack plan and log the search process
        $paystack_plans = paystack_get_plans();
        mc_cloud_debug([
            'paystack_plans_search' => [
                'membership' => $membership,
                'available_plans' => $paystack_plans
            ]
        ]);

        foreach ($paystack_plans as $plan) {
             // Match based on Paystack plan code directly stored in our membership 'id'
             if ($plan['plan_code'] == $membership['id']) {
                  $plan_code = $plan['plan_code'];
                  // Optional: Verify amount and currency match as a sanity check
                  if ($plan['amount'] != $amount || strtolower($plan['currency']) != strtolower(PAYSTACK_CURRENCY)) {
                       mc_cloud_debug([
                           'paystack_plan_mismatch_warning' => 'Plan code matches, but amount/currency differs.',
                           'mc_plan' => $membership,
                           'ps_plan' => $plan
                       ]);
                  }
                  break;
             }
        }

        // Fallback: Try matching by amount and currency if direct code match fails
        if (!$plan_code) {
            mc_cloud_debug(['paystack_plan_search' => 'Plan code match failed, trying amount/currency fallback.']);
            foreach ($paystack_plans as $plan) {
                if ($plan['amount'] == $amount && strtolower($plan['currency']) == strtolower(PAYSTACK_CURRENCY)) {
                    // Heuristic: Match based on name containing the MC membership name? Less reliable.
                    // Let's log a warning if we use this fallback.
                    mc_cloud_debug(['paystack_plan_search_warning' => 'Using fallback match by amount/currency.', 'matched_plan_code' => $plan['plan_code']]);
                    $plan_code = $plan['plan_code'];
                    break;
                }
            }
        }

    } else { // For credits or non-plan based payments
        $amount = $extra * 100; // $extra should contain the amount for one-time payments
    }

    mc_cloud_debug([
        'paystack_request' => [
            'email' => $email,
            'amount' => $amount,
            'plan_code' => $plan_code, // This will be false for non-subscription/credits
            'metadata' => $metadata,
            'subscription_flag' => $subscription
        ]
    ]);

    $data = [
        'email' => $email,
        'amount' => $amount,
        'currency' => strtoupper(PAYSTACK_CURRENCY),
        'callback_url' => CLOUD_URL . '/account/?tab=membership&payment=success',
        'metadata' => $metadata
    ];

    // Only add the 'plan' key if it's a subscription AND we found a valid plan code
    if ($subscription && $plan_code) {
        $data['plan'] = $plan_code;
    } elseif ($subscription && !$plan_code && $plan_id) {
        // Log error if it's supposed to be a subscription but no plan code found
        mc_cloud_debug(['paystack_create_session_error' => 'Subscription requested but no matching Paystack plan code found for MC plan ID: ' . $plan_id]);
        return ['status' => false, 'message' => 'Paystack plan configuration error. Cannot create subscription session.'];
    }


    $response = paystack_curl('transaction/initialize', $data);
    mc_cloud_debug([
        'paystack_response' => $response
    ]);

    if (isset($response['status']) && $response['status'] && isset($response['data']['authorization_url'])) {
        return ['url' => $response['data']['authorization_url']];
    }
    // Add more detailed error logging
    $error_message = 'Failed to initialize Paystack transaction.';
    if (isset($response['message'])) {
        $error_message .= ' Paystack message: ' . $response['message'];
    }
    mc_cloud_debug(['paystack_create_session_failed' => $error_message, 'response' => $response]);
    return ['status' => false, 'message' => $error_message, 'details' => $response];
}

function paystack_create_checkout($amount, $customer_code, $cloud_user_id, $metadata = []) {
    $accountData = account(); // Fetch account data
    $email = $accountData['email'];

    if (!$customer_code) {
        mc_cloud_debug('paystack_create_checkout: customer_code not provided, attempting to create/fetch Paystack customer.');
        // Try to fetch existing customer by email first (Optional, Paystack API doesn't have a simple GET by email)
        // Or just create a new one - Paystack might handle duplicates gracefully by returning existing.
        $customer_code = paystack_create_customer($email, $accountData['first_name'], $accountData['last_name']);
        if ($customer_code) {
            mc_cloud_debug('paystack_create_checkout: Created/fetched Paystack customer code: ' . $customer_code);
            // Optionally save this customer_code to the user's profile if not already there
            $current_customer_id = account_get_payment_id(); // Assumes this gets the stored ID
            if (!$current_customer_id || $current_customer_id !== $customer_code) {
                 db_query('UPDATE users SET customer_id = "' . db_escape($customer_code) . '" WHERE id = ' . db_escape($cloud_user_id));
            }
        } else {
            mc_cloud_debug('paystack_create_checkout: Failed to create Paystack customer.');
            // Handle error - perhaps return an error response
            return ['status' => false, 'message' => 'Failed to create Paystack customer profile.'];
        }
    }

    // Ensure metadata contains essential info
    $metadata['mc_user_id'] = $cloud_user_id;
    $metadata['mc_payment_type'] = 'checkout'; // Indicate it's a one-time checkout

    // Add client_reference_id to metadata if not already set.
    if (!isset($metadata['client_reference_id'])) {
        // Determine type based on metadata_id passed in membership_custom_payment
        $type_indicator = isset($metadata['metadata_id']) ? $metadata['metadata_id'] : 'credits'; // Default to 'credits' if specific ID isn't set
        $metadata['client_reference_id'] = $type_indicator . '|' . $cloud_user_id . '|' . (isset($_COOKIE['mc-referral']) ? $_COOKIE['mc-referral'] . '|' : '') . 'mc';
    }
    // Remove temporary metadata_id if it exists
    unset($metadata['metadata_id']);


    // Determine if this is a credits purchase based on metadata
    $is_credits_purchase = isset($metadata['metadata_id']) && $metadata['metadata_id'] === 'credits';

    $data = [
        'email' => $email, // Use email for initialization
        'amount' => intval($amount * 100), // Convert amount to kobo/cents, ensure integer
        'currency' => strtoupper(PAYSTACK_CURRENCY),
        'callback_url' => CLOUD_URL . '/account/?tab=membership&payment=success' . ($is_credits_purchase ? '&payment_type=credits' : ''),
        'metadata' => $metadata
        // 'customer' => $customer_code, // Optionally associate with customer code, but email is primary for init
    ];

    mc_cloud_debug(['paystack_create_checkout_request' => $data]);

    $response = paystack_curl('transaction/initialize', $data);

    mc_cloud_debug(['paystack_create_checkout_response' => $response]);

    if (isset($response['status']) && $response['status'] && isset($response['data']['authorization_url'])) {
        return ['url' => $response['data']['authorization_url']];
    }
     // Add more detailed error logging
    $error_message = 'Failed to initialize Paystack checkout.';
    if (isset($response['message'])) {
        $error_message .= ' Paystack message: ' . $response['message'];
    }
    mc_cloud_debug(['paystack_create_checkout_failed' => $error_message, 'response' => $response]);
    return ['status' => false, 'message' => $error_message, 'details' => $response];
}

function paystack_create_customer($email, $first_name = '', $last_name = '') {
    // Check if customer already exists in our DB (using customer_id field)
    // Note: This relies on customer_id being reliably stored after creation.
    $existing_user = db_get('SELECT customer_id FROM users WHERE email = "' . db_escape($email) . '" AND customer_id IS NOT NULL AND customer_id != "" LIMIT 1');
    if ($existing_user && isset($existing_user['customer_id']) && strpos($existing_user['customer_id'], 'CUS_') === 0) { // Basic check if it looks like a PS customer code
        mc_cloud_debug('paystack_create_customer: Found existing customer_id in DB for email ' . $email . ': ' . $existing_user['customer_id']);
        return $existing_user['customer_id'];
    }

    mc_cloud_debug('paystack_create_customer: No existing customer_id found in DB, calling Paystack API for email ' . $email);

    $data = [
        'email' => $email,
        'first_name' => $first_name,
        'last_name' => $last_name
        // Consider adding phone number if available: 'phone' => $phone
    ];
    $response = paystack_curl('customer', $data);

    mc_cloud_debug(['paystack_create_customer_response' => $response]);

    if (isset($response['status']) && $response['status'] && isset($response['data']['customer_code'])) {
        $customer_code = $response['data']['customer_code'];
        // Save the customer code to the user's profile immediately
        db_query('UPDATE users SET customer_id = "' . db_escape($customer_code) . '" WHERE email = "' . db_escape($email) . '"');
        return $customer_code;
    } elseif (isset($response['status']) && !$response['status'] && isset($response['message']) && strpos($response['message'], 'Customer email already exists') !== false) {
         // Customer exists on Paystack, try to fetch their code
         mc_cloud_debug('paystack_create_customer: Customer already exists on Paystack, attempting to fetch by email.');
         // Paystack List Customers API allows filtering by email
         $list_response = paystack_curl('customer?email=' . urlencode($email) . '&perPage=1', [], 'GET');
         mc_cloud_debug(['paystack_list_customer_response' => $list_response]);
         if (isset($list_response['status']) && $list_response['status'] && !empty($list_response['data'])) {
             $customer_code = $list_response['data'][0]['customer_code'];
             mc_cloud_debug('paystack_create_customer: Successfully fetched existing customer code: ' . $customer_code);
             // Save the fetched customer code
             db_query('UPDATE users SET customer_id = "' . db_escape($customer_code) . '" WHERE email = "' . db_escape($email) . '"');
             return $customer_code;
         } else {
              mc_cloud_debug('paystack_create_customer: Failed to fetch existing customer code after duplicate email error.');
              return false;
         }
    } else {
         mc_cloud_debug('paystack_create_customer: Failed to create customer.', ['error' => $response['message'] ?? 'Unknown error']);
         return false;
    }
}

function paystack_create_subscription($plan_code, $customer_code, $authorization_code = null) {
    $data = [
        'plan' => $plan_code,
        'customer' => $customer_code
    ];
    // If an authorization code is provided (from a previous successful transaction),
    // use it to link the subscription to that payment method for future charges.
    if ($authorization_code) {
        $data['authorization'] = $authorization_code;
         mc_cloud_debug('paystack_create_subscription: Using authorization code for subscription.');
    } else {
         // If no auth code, Paystack might require customer to enter card details again,
         // or it might use a card already on file for the customer.
         // Set a start date slightly in the future if needed? Paystack usually handles this.
         // $data['start_date'] = date('c', strtotime('+1 minute')); // Example: Start in 1 minute
         mc_cloud_debug('paystack_create_subscription: No authorization code provided.');
    }
    mc_cloud_debug(['paystack_create_subscription_request' => $data]);
    $response = paystack_curl('subscription', $data);
    mc_cloud_debug(['paystack_create_subscription_response' => $response]);
    return $response; // Return the full response for the caller to handle
}

function paystack_get_plans() {
    $response = paystack_curl('plan?status=active&perPage=100', [], 'GET'); // Fetch only active plans, increase perPage
    // mc_cloud_debug(['function' => 'paystack_get_plans', 'response' => $response]); // Optional: Log the raw response

    // Safely extract plans array
    $plans = (isset($response['status']) && $response['status'] === true && isset($response['data']) && is_array($response['data'])) ? $response['data'] : [];

    // Optional: Log details ONLY if plans were found
    if (!empty($plans)) {
        // mc_cloud_debug(['function' => 'paystack_get_plans_details', 'count' => count($plans)]); // Log count
        /* // Detailed logging per plan can be verbose, enable if needed for debugging
        foreach ($plans as $plan) {
            mc_cloud_debug([
                'plan_id'     => mc_isset($plan, 'id'),
                'plan_code'   => mc_isset($plan, 'plan_code'),
                'name'        => mc_isset($plan, 'name'),
                'interval'    => mc_isset($plan, 'interval'),
                'currency'    => mc_isset($plan, 'currency'),
                'amount'      => mc_isset($plan, 'amount')
            ]);
        }
        */
    } else {
         mc_cloud_debug(['function' => 'paystack_get_plans_warning', 'message' => 'No active plans found or API error.', 'response' => $response]);
    }

    return $plans; // Return ONLY the plans array (or empty array on failure)
}

function paystack_get_transactions($customer_email) {
    $response = paystack_curl('transaction?customer=' . urlencode($customer_email), [], 'GET');
    return (isset($response['status']) && $response['status']) ? $response['data'] : [];
}

function paystack_verify_transaction($reference) {
    if (empty($reference)) {
        return ['status' => false, 'message' => 'Transaction reference is missing.'];
    }

    $response = paystack_curl('transaction/verify/' . rawurlencode($reference), [], 'GET');

    mc_cloud_debug([
        'function'  => 'paystack_verify_transaction',
        'reference' => $reference,
        'response'  => $response
    ]);

    // Check for API call success and 'success' status within Paystack data
    if (isset($response['status']) && $response['status'] === true && isset($response['data']['status']) && $response['data']['status'] === 'success') {
        $transaction_data = $response['data'];
        $metadata = mc_isset($transaction_data, 'metadata', []);

        // --- Crucial: Parse client_reference_id from metadata ---
        $client_reference_id = mc_isset($metadata, 'client_reference_id');
        $parsed_ref = [
            'type' => 'unknown',
            'user_id' => null,
            'plan_id' => null,
            'period' => null,
            'referral' => null,
            'raw' => $client_reference_id
        ];

        if ($client_reference_id && strpos($client_reference_id, '|mc') !== false) {
            $parts = explode('|', rtrim($client_reference_id, '|mc')); // Remove suffix and split
            $num_parts = count($parts);
            $parsed_ref['user_id'] = mc_isset($parts, 1);
            $type_indicator = strtolower(mc_isset($parts, 0));

            if ($type_indicator == 'credits') {
                $parsed_ref['type'] = 'credits';
                if ($num_parts > 2) $parsed_ref['referral'] = mc_isset($parts, 2);
            } elseif ($type_indicator == 'white_label') {
                $parsed_ref['type'] = 'white_label';
                if ($num_parts > 2) $parsed_ref['referral'] = mc_isset($parts, 2);
            } elseif (strpos($type_indicator, 'plan_') === 0 || strpos($type_indicator, 'pln_') === 0 || is_numeric($type_indicator)) {
                $parsed_ref['type'] = 'subscription';
                $parsed_ref['plan_id'] = $parts[0];
                $parsed_ref['period'] = mc_isset($parts, 2);
                if ($num_parts > 3) $parsed_ref['referral'] = mc_isset($parts, 3);
            } else {
                $parsed_ref['type'] = $type_indicator;
            }

            if ($parsed_ref['user_id'] && !is_numeric($parsed_ref['user_id'])) {
                 mc_cloud_debug(['paystack_verify_transaction_warning' => 'Invalid User ID format in client_reference_id', 'parsed_ref' => $parsed_ref]);
                 $parsed_ref['user_id'] = null;
            }

        } else {
            mc_cloud_debug(['paystack_verify_transaction_warning' => 'client_reference_id missing or invalid format in metadata', 'metadata' => $metadata]);
            $fallback_user_id = mc_isset($metadata, 'mc_cloud_user_id');
            if (!$fallback_user_id) {
                $customer = mc_isset($transaction_data, 'customer');
                if ($customer && isset($customer['email'])) {
                    $user_query = db_get('SELECT id FROM users WHERE email = "' . db_escape($customer['email']) . '" LIMIT 1');
                    $fallback_user_id = mc_isset($user_query, 'id');
                }
            }
             $parsed_ref['user_id'] = $fallback_user_id;
             $parsed_ref['type'] = 'unknown_fallback';
        }

        mc_cloud_debug([
            'function' => 'paystack_verify_transaction_parsed',
            'parsed_ref' => $parsed_ref
        ]);

        return [
            'status' => true,
            'data' => $transaction_data,
            'parsed_ref' => $parsed_ref
        ];

    } elseif (isset($response['status']) && $response['status'] === true && isset($response['data']['status'])) {
         $message = 'Transaction status is not successful: ' . $response['data']['status'];
         mc_cloud_debug(['paystack_verify_transaction_failed' => $message, 'reference' => $reference, 'response' => $response]);
         return ['status' => false, 'message' => $message, 'data' => $response['data']];
    } else {
        $message = $response['message'] ?? 'Failed to verify Paystack transaction or invalid response.';
        mc_cloud_debug(['paystack_verify_transaction_error' => $message, 'reference' => $reference, 'response' => $response]);
        return ['status' => false, 'message' => $message, 'details' => $response];
    }
}


function paystack_cancel_subscription($subscription_code = null, $email_token = null) {
    $cloud_user_id = get_active_account_id(true); // Escaped for logs
    $non_escaped_user_id = get_active_account_id(false); // Non-escaped for DB lookup

    if (!$non_escaped_user_id) {
        mc_cloud_debug('paystack_cancel_subscription: User not logged in.');
        return ['status' => 'error', 'message' => 'User not logged in.'];
    }

    // Retrieve code and token from DB if not passed directly (usually from AJAX)
    if ($subscription_code === null) {
        $subscription_code = super_get_user_data('paystack_subscription_code', $non_escaped_user_id);
    }
    if ($email_token === null) {
        $email_token = super_get_user_data('paystack_email_token', $non_escaped_user_id);
    }

    mc_cloud_debug('paystack_cancel_subscription: Attempting cancellation via API for user ' . $cloud_user_id, [
        'sub_code' => $subscription_code,
        'email_token_present' => !empty($email_token) // Log presence, not value
    ]);

    // --- API Cancellation Logic ---
    if ($subscription_code && $email_token) {
        $fields = [
            'code' => $subscription_code,
            'token' => $email_token
        ];

        // Use paystack_curl helper function (assuming it handles POST with form data correctly)
        // If paystack_curl strictly uses JSON, we need to adjust it or use direct cURL here.
        // Let's try with paystack_curl first, assuming it can handle form data if $data is an array.
        // NOTE: The Paystack API docs show form-encoded data for /disable, not JSON.
        // We might need a specific curl setup for this endpoint.

        // --- Direct cURL for Form Data ---
        $url = "https://api.paystack.co/subscription/disable";
        $fields_string = http_build_query($fields);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Authorization: Bearer " . PAYSTACK_SECRET_KEY, // Ensure PAYSTACK_SECRET_KEY is defined
            "Cache-Control: no-cache",
            // Content-Type is typically x-www-form-urlencoded for http_build_query
            // "Content-Type: application/x-www-form-urlencoded" // Usually not needed as cURL sets it
        ));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Keep this if needed for your server env

        $result_json = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);
        // --- End Direct cURL ---

        $result = json_decode($result_json, true);

        mc_cloud_debug('paystack_cancel_subscription: API disable response', [
            'http_code' => $http_code,
            'curl_error' => $curl_error,
            'response_body' => $result
        ]);

        // Check Paystack API response
        if ($result && isset($result['status']) && $result['status'] === true) {
            // Paystack confirmed successful disable
            mc_cloud_debug('Paystack API confirmed subscription disabled successfully.');

            // Mark locally cancelled
            $local_update_result = super_insert_user_data('(' . $non_escaped_user_id . ', "subscription_cancelation", "' . mc_gmt_now() . '")');
            membership_delete_cache($non_escaped_user_id); // Clear cache

            if ($local_update_result === true) {
                mc_cloud_debug('Subscription marked as cancelled locally.');
                return ['status' => 'success', 'message' => $result['message'] ?? 'Subscription disabled successfully.'];
            } else {
                mc_cloud_debug('Paystack API success, but failed to mark locally cancelled.', ['db_error' => $local_update_result]);
                // Return success but maybe log the inconsistency
                return ['status' => 'success_api_only', 'message' => ($result['message'] ?? 'Subscription disabled successfully.') . ' (Local status update failed)'];
            }
        } else {
            // Paystack API returned an error or unexpected status
            $error_message = mc_isset($result, 'message', 'Failed to disable subscription via Paystack API.');
            if ($curl_error) {
                $error_message .= ' cURL Error: ' . $curl_error;
            } elseif ($http_code !== 200) {
                $error_message .= ' HTTP Status: ' . $http_code;
            }
             mc_cloud_debug('Paystack API disable failed.', ['error' => $error_message, 'response' => $result]);
             return ['status' => 'error', 'message' => $error_message];
        }

    } else {
        // Missing subscription code or email token in the database
        $missing = [];
        if (!$subscription_code) $missing[] = 'subscription code';
        if (!$email_token) $missing[] = 'email token';
        $error_message = 'Cannot cancel subscription. Missing ' . implode(' and ', $missing) . ' in database. Please ensure the webhook is active or contact support.';
        mc_cloud_debug('paystack_cancel_subscription: Failed - Missing required data for API call.', ['user_id' => $cloud_user_id, 'missing' => $missing]);

        // Attempt local cancellation marking anyway since the user clicked the button? Optional.
        // $local_update_result = super_insert_user_data('(' . $non_escaped_user_id . ', "subscription_cancelation", "' . mc_gmt_now() . '")');
        // membership_delete_cache($non_escaped_user_id);
        // if ($local_update_result === true) {
        //     return ['status' => 'error_local_cancel_only', 'message' => $error_message . ' (Marked locally)'];
        // } else {
             return ['status' => 'error', 'message' => $error_message];
        // }
    }
}

function paystack_charge_authorization($amount, $email, $authorization_code, $reference = null) {
    if (empty($email) || empty($authorization_code) || !is_numeric($amount) || $amount <= 0) {
        mc_cloud_debug('paystack_charge_authorization: Invalid parameters provided.', ['amount' => $amount, 'email' => $email, 'auth_present' => !empty($authorization_code)]);
        return false;
    }

    $url = "https://api.paystack.co/transaction/charge_authorization";
    $fields = [
        'email'            => $email,
        'amount'           => intval($amount), // Ensure integer kobo/cents
        'authorization_code' => $authorization_code,
    ];

    // Add reference if provided, useful for tracking specific recharge attempts
    if ($reference) {
        $fields['reference'] = $reference;
    }
     // Optional: Add metadata if needed
     $fields['metadata'] = json_encode([
         'custom_fields' => [
             ['display_name' => 'Charge Type', 'variable_name' => 'charge_type', 'value' => 'Auto Recharge Credits']
         ]
     ]);


    mc_cloud_debug('Attempting Paystack charge_authorization call.', $fields);

    // Use a direct cURL call as it's simpler for this specific request
    $fields_string = http_build_query($fields);
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $fields_string);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        "Authorization: Bearer " . PAYSTACK_SECRET_KEY,
        "Cache-Control: no-cache",
        // Content-Type: application/x-www-form-urlencoded is default for http_build_query
    ));
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Keep if needed

    $result_json = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    $result = json_decode($result_json, true);

    if ($curl_error) {
        mc_cloud_debug('paystack_charge_authorization: cURL error.', ['error' => $curl_error]);
        return false; // Indicate a transport-level error
    }

    mc_cloud_debug('paystack_charge_authorization: API response received.', ['http_code' => $http_code, 'response' => $result]);

    // Return the decoded response regardless of Paystack status, let the caller check details
    return $result;
}
/*
 * -----------------------------------------------------------
 * RAZORPAY
 * -----------------------------------------------------------
 *
 */

function razorpay_curl($url_part, $body = [], $type = 'POST') {
    $response = mc_curl('https://api.razorpay.com/v1/' . $url_part, json_encode($body, JSON_INVALID_UTF8_IGNORE, JSON_UNESCAPED_UNICODE), ['Content-Type: application/json', 'Authorization: Basic ' . base64_encode(RAZORPAY_KEY_ID . ':' . RAZORPAY_KEY_SECRET)], $type);
    return $type == 'POST' ? $response : json_decode($response, true);
}

function razorpay_get_plans($plan_id = false) {
    $plans = razorpay_curl('plans' . ($plan_id ? '/' . $plan_id : ''), '', 'GET');
    return $plan_id ? $plans : mc_isset($plans, 'items', []);
}

function razorpay_create_subscription($plan_id, $cloud_user_id) {
    $membership = $plan_id ? membership_get($plan_id) : false;
    $response = razorpay_curl('subscriptions', ['plan_id' => $plan_id, 'total_count' => $membership['period'] == 'month' ? 1200 : 100, 'quantity' => 1, 'notes' => ['customer_id' => $cloud_user_id, 'referral' => mc_isset($_COOKIE, 'mc-referral', ''), 'period' => $membership['period']]]);
    return isset($response['short_url']) ? ['url' => $response['short_url']] : $response['short_url'];
}

function razorpay_create_payment_link($amount, $notes = []) {
    $notes['customer_id'] = get_active_account_id();
    $response = razorpay_curl('payment_links', ['amount' => $amount, 'callback_url' => CLOUD_URL . '/account?tab=membership', 'callback_method' => 'get', 'notes' => $notes]);
    return mc_isset($response, 'short_url', $response);
}

function razorpay_cancel_subscription($cloud_user_id = false) {
    $payment_id = mc_isset(db_get('SELECT customer_id FROM users WHERE id = ' . db_escape($cloud_user_id ? $cloud_user_id : get_active_account_id(), true)), 'customer_id');
    if ($payment_id) {
        $response = razorpay_curl('subscriptions/' . $payment_id . '/cancel');
        if (mc_isset($response, 'status') == 'canceled') {
            super_insert_user_data('(' . get_active_account_id() . ', "subscription_cancelation", "' . mc_gmt_now() . '")');
        }
        return $response;
    }
    return true;
}

/*
 * -----------------------------------------------------------
 * RAPYD
 * -----------------------------------------------------------
 *
 */

function rapyd_curl($url_path, $raw = '', $type = 'POST', $default = false) {
    $salt = mt_rand(********, ********);
    $timestamp = time();
    if (!is_string($raw)) {
        $raw = json_encode($raw, JSON_INVALID_UTF8_IGNORE, JSON_UNESCAPED_UNICODE);
    }
    $signature = base64_encode(hash_hmac('sha256', strtolower($type) . '/v1/' . $url_path . $salt . $timestamp . RAPYD_ACCESS_KEY . RAPYD_SECRET_KEY . $raw, RAPYD_SECRET_KEY));
    $header = ['Content-Type: application/json', 'access_key: ' . RAPYD_ACCESS_KEY, 'salt: ' . $salt, 'timestamp: ' . $timestamp, 'signature: ' . $signature];
    $response = mc_curl('https://' . (RAPYD_TEST_MODE ? 'sandboxapi' : 'api') . '.rapyd.net/v1/' . $url_path, $raw, $header, $type);
    return isset($response['status']) && mc_isset($response['status'], 'status') == 'SUCCESS' ? $response['data'] : $response;
}

function rapyd_curl_checkout($amount, $customer_id, $cloud_user_id, $metadata = []) {
    $metadata = array_merge($metadata, ['cloud_user_id' => $cloud_user_id, 'rapyd_secret_key' => RAPYD_SECRET_KEY, 'referral' => mc_isset($_COOKIE, 'mc-referral')]);
    return rapyd_curl('checkout', ['amount' => $amount, 'country' => RAPYD_COUNTRY, 'currency' => RAPYD_CURRENCY, 'customer' => $customer_id, 'custom_elements' => ['billing_address_collect' => true], 'metadata' => $metadata]);
}

function rapyd_create_customer() {
    $account = account();
    $customer_id = rapyd_curl('customers', ['name' => $account['first_name'] . ' ' . $account['last_name'], 'email' => $account['email']]);
    if (isset($customer_id['id'])) {
        $customer_id = $customer_id['id'];
        db_query('UPDATE users SET customer_id = "' . mc_db_escape($customer_id) . '" WHERE id = ' . db_escape($account['user_id'], true));
    }
    return $customer_id;
}

function rapyd_create_checkout($membership_id, $cloud_user_id) {
    $customer_id = account_get_payment_id();
    $membership = membership_get($membership_id);
    if (!$customer_id) {
        $customer_id = rapyd_create_customer();
    }
    $response = rapyd_curl_checkout($membership['price'], $customer_id, $cloud_user_id, ['membership_id' => $membership_id, 'membership_period' => $membership['period']]);
    return isset($response['redirect_url']) ? ['url' => $response['redirect_url']] : $response;
}

/*
 * -----------------------------------------------------------
 * 2CHECKOUT VERIFONE
 * -----------------------------------------------------------
 *
 */

function verifone_create_checkout($membership_id, $cloud_user_id) {
    $customer_id = account_get_payment_id();
    $membership = membership_get($membership_id);
    $period = $membership['period'];
    $duration = '';
    $account = account();
    switch ($period) {
        case 'day':
            $period = '1:DAY';
            $duration = '3650:DAY';
            break;
        case 'week':
            $period = '1:WEEK';
            $duration = '480:WEEK';
            break;
        case 'month':
            $period = '1:MONTH';
            $duration = '120:MONTH';
            break;
        case '3month':
            $period = '3:MONTH';
            $duration = '120:MONTH';
            break;
        case '6month':
            $period = '6:MONTH';
            $duration = '120:MONTH';
            break;
        case 'year':
            $period = '1:YEAR';
            $duration = '10:YEAR';
            break;
        case '2year':
            $period = '2:YEAR';
            $duration = '10:YEAR';
            break;
        case '3year':
            $period = '3:YEAR';
            $duration = '10:YEAR';
            break;
    }
    $url = 'https://secure.2checkout.com/checkout/buy?merchant=' . VERIFONE_MERCHANT_ID . '&dynamic=1&currency=' . VERIFONE_CURRENCY . '&customer-ref=' . ($customer_id ? $customer_id : 'false') . '&duration=' . $duration . '&email=' . $account['email'] . '&order-ext-ref=' . mc_encryption($cloud_user_id . '|' . $membership_id . '|' . $membership['period'] . (isset($_COOKIE['mc-referral']) ? '|' . $_COOKIE['mc-referral'] : '')) . '&price=' . $membership['price'] . '&prod=' . $membership['name'] . '&qty=1&recurrence=' . $period . '&renewal-price=' . $membership['price'] . '&type=digital';
    return ['url' => $url . '&signature=' . verifone_get_signature($url)];
}

function verifone_get_signature($url) {
    parse_str(substr($url, strpos($url, '?') + 1), $values);
    $serialized = '';
    foreach ($values as $key => $value) {
        if (!in_array($key, ['merchant', 'dynamic', 'email'])) {
            $serialized .= mb_strlen($value) . $value;
        }
    }
    return hash_hmac('sha256', $serialized, VERIFONE_SECRET_WORD);
}

function verifone_curl($url_part, $type = 'POST') {
    $date = gmdate('Y-m-d H:i:s');
    $string = strlen(VERIFONE_MERCHANT_ID) . VERIFONE_MERCHANT_ID . strlen($date) . $date;
    $hash = hash_hmac('md5', $string, VERIFONE_SECRET_KEY);
    $response = mc_curl('https://api.2checkout.com/rest/6.0/' . $url_part, '', ['Content-Type: application/json', 'Accept: application/json', 'X-Avangate-Authentication: code="' . VERIFONE_MERCHANT_ID . '" date="' . $date . '" hash="' . $hash . '"'], $type);
    return is_string($response) ? json_decode($response, true) : $response;
}

function verifone_cancel_subscription() {
    $verifone_id = account_get_payment_id();
    if ($verifone_id) {
        $subscriptions = mc_isset(verifone_curl('subscriptions?CustomerEmail=' . $verifone_id . '&SubscriptionEnabled=true&Limit=99', 'GET'), 'Items', []);
        if ($subscriptions) {
            for ($i = 0; $i < count($subscriptions); $i++) {
                verifone_curl('subscriptions/' . $subscriptions[$i]['SubscriptionReference'], 'DELETE');
            }
            super_insert_user_data('(' . get_active_account_id() . ', "subscription_cancelation", "' . mc_gmt_now() . '")');
            return ['status' => 'canceled'];
        } else {
            return 'no-subscriptions';
        }
    }
    return false;
}

function verifone_get_orders($customer_id) {
    $page = 1;
    $customer_orders = [];
    while ($page) {
        $response = verifone_curl('orders?Limit=99', 'GET');
        $orders = mc_isset($response, 'Items', []);
        $pagination = mc_isset($response, 'Pagination');
        for ($i = 0; $i < count($orders); $i++) {
            $order = $orders[$i];
            if (isset($order['BillingDetails']) && $order['BillingDetails']['Email'] == $customer_id) {
                array_push($customer_orders, $order);
            }
        }
        $page = $pagination && ($pagination['Page'] * $pagination['Limit'] < $pagination['Count']) ? $pagination['Page'] + 1 : false;
    }
    return $customer_orders;
}

/*
 * -----------------------------------------------------------
 * YOOMONEY
 * -----------------------------------------------------------
 *
 */

function yoomoney_curl($url_part, $body = false, $type = 'POST') {
    $response = mc_curl('https://api.yookassa.ru/v3/' . $url_part, $body ? json_encode($body, JSON_INVALID_UTF8_IGNORE, JSON_UNESCAPED_UNICODE) : '', ['Authorization: Basic ' . base64_encode(YOOMONEY_SHOP_ID . ':' . YOOMONEY_KEY_SECRET), 'Idempotence-Key: ' . rand(99999, 9999999), 'Content-Type: application/json', 'Accept: application/json'], $type);
    return $type == 'POST' ? $response : json_decode($response, true);
}

function yoomoney_create_payment($amount, $currency_code, $return_url, $description = false, $metadata = false, $recurring = true) {
    $query = ['amount' => ['value' => $amount, 'currency' => strtoupper($currency_code)], 'capture' => true, 'confirmation' => ['type' => 'redirect', 'return_url' => $return_url]];
    if ($description) {
        $query['description'] = $description;
    }
    if ($recurring) {
        $query['save_payment_method'] = 'true';
    }
    if ($metadata) {
        $query['metadata'] = $metadata;
    }
    return yoomoney_curl('payments', $query);
}

function yoomoney_create_subscription($price_id) {
    $membership = membership_get($price_id);
    if ($membership) {
        $response = yoomoney_create_payment($membership['price'], YOOMONEY_CURRENCY, CLOUD_URL . '/account?tab=membership', $membership['name'], ['mc_user_id' => get_active_account_id(), 'membership_id' => $price_id, 'referral' => mc_isset($_COOKIE, 'mc-referral')]);
        $confirmation = mc_isset($response, 'confirmation');
        return $confirmation ? ['url' => $confirmation['confirmation_url']] : $response;
    }
    return false;
}

function yoomoney_cancel_subscription() {
    $payment_id = account_get_payment_id();
    if ($payment_id) {
        db_query('UPDATE users SET customer_id = "" WHERE id = ' . get_active_account_id());
        super_insert_user_data('(' . get_active_account_id() . ', "subscription_cancelation", "' . mc_gmt_now() . '")');
        return ['status' => 'canceled'];
    }
    return false;
}

function yoomoney_recurring_payment($amount, $payment_id, $description = '') {
    $query = ['amount' => ['value' => $amount, 'currency' => strtoupper(YOOMONEY_CURRENCY)], 'capture' => true, 'payment_method_id' => $payment_id, 'description' => $description];
    $response = yoomoney_curl('payments', $query);
    return mc_isset($response, 'status') == 'succeeded' ? true : $response;
}

function yoomoney_cron() {
    $users = db_get('SELECT id, membership, membership_expiration, customer_id FROM users WHERE membership <> "free" AND membership <> "0" AND customer_id <> ""', false);
    $white_labels = array_column(db_get('SELECT user_id, value FROM users_data WHERE slug = "white-label"', false), 'value', 'user_id');
    $now_plus_24 = mc_gmt_now(-86400, true);
    for ($i = 0; $i < count($users); $i++) {
        try {
            $payment_id = $users[$i]['customer_id'];
            if ($payment_id) {
                $user_id = $users[$i]['id'];
                $membership = membership_get($users[$i]['membership']);
                $reset_query = 'UPDATE users SET customer_id = "" WHERE id = ' . $user_id;
                if ($membership) {
                    if ($now_plus_24 > cloud_gmt_time($users[$i]['membership_expiration'])) {
                        $response = yoomoney_recurring_payment($membership['price'], $payment_id, $membership['name']);
                        if ($response === true) {
                            membership_update($membership['id'], $membership['period'], $user_id, $payment_id);
                            cloud_add_to_payment_history($user_id, $membership['price'], 'Membership', $payment_id);
                        } else {
                            db_query($reset_query);
                        }
                    }
                }
                $white_label = mc_isset($white_labels, $user_id);
                if ($white_label && $now_plus_24 > cloud_gmt_time($white_label)) {
                    $amount = super_get_white_label();
                    $response = yoomoney_recurring_payment($amount, $payment_id, 'White Label');
                    if ($response === true) {
                        membership_save_white_label($user_id);
                        cloud_add_to_payment_history($user_id, $amount, 'White Label', $payment_id);
                    } else {
                        db_query($reset_query);
                    }
                }
            }
        } catch (Exception $e) {
        }
    }
}

/*
 * -----------------------------------------------------------
 * SUPER ADMIN
 * -----------------------------------------------------------
 *
 */

function super_admin() {
    // Original validation bypassed for licensing removal
    /*
    global $SUPER_ACTIVE_ACCOUNT;
    if ($SUPER_ACTIVE_ACCOUNT) {
        return $SUPER_ACTIVE_ACCOUNT;
    }
    if (empty($_COOKIE['mc-super'])) {
        return false;
    }
    $cookie = mc_encryption($_COOKIE['mc-super'], false);
    if ($cookie != SUPER_EMAIL) {
        return false;
    }
    $SUPER_ACTIVE_ACCOUNT = $cookie;
    return true;
    */
    
    // Bypass: Always return true to disable admin validation
    return true;
}

function super_login($email, $password) {
    return password_verify($password, SUPER_PASSWORD) && $email == SUPER_EMAIL ? mc_encryption($email) : false;
}

function super_get_customers($price_id = false) {
    return db_get('SELECT * FROM users' . ($price_id ? (' WHERE membership = ' . $price_id) : '') . ' ORDER BY creation_time ASC', false);
}

function super_get_customer($customer_id) {
    $customer_id = db_escape($customer_id);
    $stripe = PAYMENT_PROVIDER == 'stripe';
    $paystack = PAYMENT_PROVIDER == 'paystack';
    $verifone = !$stripe && !$paystack && PAYMENT_PROVIDER == 'verifone';
    $cloud_settings = super_get_settings();

    // Customer details
    $customer = db_get('SELECT * FROM users WHERE id = ' . $customer_id);
    require_once(MC_PATH . '/config/config_' . $customer['token'] . '.php');
    $customer['password'] = '********';
    $customer['extra_fields'] = db_get('SELECT slug, value FROM users_data WHERE user_id = ' . $customer_id, false);
    $customer['database'] = MC_DB_NAME;
    if (empty($customer['phone'])) {
        $customer['phone'] = '';
    }
    for ($i = 1; $i < 5; $i++) {
        $name = mc_string_slug(mc_isset($cloud_settings, 'registration-field-' . $i));
        if ($name && !isset($customer['extra_fields'][$name])) {
            array_push($customer['extra_fields'], ['slug' => mc_string_slug($name), 'value' => '']);
        }
    }

    // Sales
    $customer['invoices'] = [];
    $customer['lifetime_value'] = 0;

    // First try to get invoices from payment provider using customer_id
    if (!empty($customer['customer_id'])) {
        $invoices = $verifone ? verifone_get_orders($customer['customer_id']) :
                   mc_isset($stripe ? stripe_curl('invoices?customer=' . $customer['customer_id'], 'GET') :
                   ($paystack ? paystack_get_transactions($customer['customer_id']) :
                   json_decode(rapyd_curl('payments?limit=99&customer=' . $customer['customer_id'], '', 'GET'), true)), 'data');
        $lifetime_value = 0;
        if ($invoices) {
            $currency = '';
            for ($i = 0; $i < count($invoices); $i++) {
                $invoice = $invoices[$i];
                $lifetime_value += $invoice[$stripe ? 'amount_paid' : ($paystack ? 'amount' : ($verifone ? 'NetPrice' : 'amount'))];
                $currency = $invoice[$stripe ? 'currency' : ($paystack ? 'currency' : ($verifone ? 'Currency' : 'currency_code'))];
            }
            $customer['lifetime_value'] = strtoupper($currency) . ' ' . ($stripe || $paystack ? ($lifetime_value / ($paystack ? 100 : currency_get_divider($currency))) : $lifetime_value);
            $customer['invoices'] = $invoices;
        }
    }

    // Fallback: If no invoices found from payment provider or customer_id is empty,
    // check local payment history in users_data table (same as user admin dashboard)
    if (empty($customer['invoices'])) {
        $local_payments = db_get('SELECT * from users_data WHERE user_id = ' . $customer_id . ' AND slug = "payment" ORDER BY id DESC', false);
        if ($local_payments) {
            $customer['invoices'] = $local_payments;
            // Calculate lifetime value from local payments
            $total_amount = 0;
            $currency = '';
            for ($i = 0; $i < count($local_payments); $i++) {
                $payment_data = json_decode($local_payments[$i]['value'], true);
                if ($payment_data && is_array($payment_data) && count($payment_data) >= 2) {
                    $total_amount += floatval($payment_data[0]); // Amount is first element
                    if (empty($currency)) {
                        $currency = membership_currency(); // Use the proper currency function
                    }
                }
            }
            if ($total_amount > 0) {
                $customer['lifetime_value'] = strtoupper($currency) . ' ' . $total_amount;
            }
        }
    }

    // Statistics
    if (in_array(mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages'), ['messages', 'messages-agents'])) {
        $monthly_volume = db_get('SELECT count, date FROM membership_counter WHERE user_id = ' . $customer_id, false);
        $customer['monthly_volume'] = $monthly_volume;
    } else {
        $customer['monthly_volume'] = '';
    }
    $customer['count_users'] = mc_db_get('SELECT COUNT(*) AS `count` FROM mc_users WHERE user_type <> "admin" AND user_type <> "agent" AND user_type <> "bot"')['count'];
    $customer['count_agents'] = mc_db_get('SELECT COUNT(*) AS `count` FROM mc_users WHERE user_type = "admin" OR user_type = "agent"')['count'];
    return $customer;
}

function super_save_customer($customer_id, $details, $extra_details = false) {
    $query = '';
    $customer_id = db_escape($customer_id); // Escape the ID for queries
    $cloud_user = db_get('SELECT * FROM users WHERE id = ' . $customer_id);

    if (!$cloud_user) {
        return 'user-not-found'; // Error if user doesn't exist
    }

    // --- Duplicate Email/Phone Check (from New version) ---
    $check_email = isset($details['email']) ? db_escape($details['email']) : null;
    $check_phone = isset($details['phone']) ? db_escape($details['phone']) : null;

    if (($check_email && $check_email != $cloud_user['email']) || ($check_phone && $check_phone != $cloud_user['phone'])) {
        $duplicate_query_parts = [];
        if ($check_email && $check_email != $cloud_user['email']) {
            $duplicate_query_parts[] = 'email = "' . $check_email . '"';
        }
        if ($check_phone && $check_phone != $cloud_user['phone']) {
             // Ensure phone check handles NULL values correctly if phone is optional
             if ($check_phone === 'NULL') { // Check if admin explicitly set to NULL/empty
                 // If clearing phone, no duplicate check needed for phone
             } else {
                 $duplicate_query_parts[] = 'phone = "' . $check_phone . '"';
             }
        }

        if (!empty($duplicate_query_parts)) {
             $duplicate_check = db_get('SELECT id FROM users WHERE id <> ' . $customer_id . ' AND (' . implode(' OR ', $duplicate_query_parts) . ') LIMIT 1');
             if ($duplicate_check) {
                  return 'duplicate-phone-or-email'; // Return error code if duplicate found
             }
        }
    }
    // --- End Duplicate Check ---


    // Email sync with MC instance (Ensure config file path is correct)
    if (isset($details['email']) && $details['email'] != $cloud_user['email']) {
         $config_path = MC_PATH . '/config/config_' . $cloud_user['token'] . '.php';
         if (file_exists($config_path)) {
             require_once($config_path);
             // Use mc_db_escape if available within the instance context, otherwise db_escape
             $instance_email_new = function_exists('mc_db_escape') ? mc_db_escape($details['email']) : db_escape($details['email']);
             $instance_email_old = function_exists('mc_db_escape') ? mc_db_escape($cloud_user['email']) : db_escape($cloud_user['email']);
             // Use mc_db_query which should be defined after including config
             if (function_exists('mc_db_query')) {
                 mc_db_query('UPDATE mc_users SET email = "' . $instance_email_new . '" WHERE email = "' . $instance_email_old . '"');
             } else {
                 mc_cloud_debug('super_save_customer: mc_db_query function not found after including config for user ' . $customer_id);
             }
         } else {
             mc_cloud_debug('super_save_customer: Config file not found for email sync: ' . $config_path);
         }
    }

    // Membership update logic
    if (isset($details['membership']) && $details['membership'] != $cloud_user['membership']) {
        if ($details['membership'] == 'manual_membership_renewal') {
            // Keep current membership but reset expiration date
            $details['membership'] = $cloud_user['membership'];
            $membership_details = membership_get($details['membership']);
            if ($membership_details) { // Ensure plan exists
                $details['membership_expiration'] = membership_calculate_expiration($membership_details['period']);
                membership_add_reseller_sale($details['membership']); // Track renewal sale
            } else {
                 // Handle case where current membership doesn't exist anymore? Maybe default to free?
                 unset($details['membership']); // Or remove the membership update if invalid
                 unset($details['membership_expiration']);
                 mc_cloud_debug('super_save_customer: Manual renewal attempted for non-existent plan ID: ' . $cloud_user['membership']);
            }
        } else {
            // Update to a new membership plan
            $membership_details = membership_get($details['membership']);
             if ($membership_details) {
                 $details['membership_expiration'] = membership_calculate_expiration($membership_details['period']);
                 membership_add_reseller_sale($details['membership']); // Track new plan sale
             } else {
                  // Handle attempt to save an invalid/non-existent plan ID
                  unset($details['membership']); // Remove invalid update
                  unset($details['membership_expiration']);
                  mc_cloud_debug('super_save_customer: Attempted to save non-existent plan ID: ' . $details['membership']);
             }
        }
    } else {
        // If membership not changing, don't change expiration unless explicitly set elsewhere
        if (!isset($details['membership_expiration'])) { // Avoid overwriting explicit expiration changes
             unset($details['membership_expiration']);
        }
    }

    // Credits update logic
    $new_credits_value = isset($details['credits']) ? intval($details['credits']) : null;
    if ($new_credits_value !== null && $new_credits_value != $cloud_user['credits']) {
        $credit_difference = $new_credits_value - intval($cloud_user['credits']);
        if ($credit_difference > 0) {
            // Credits are being added - use membership_set_purchased_credits
            // Need currency - use membership_currency()
            $currency = membership_currency();
            // Calculate the equivalent 'amount' that would result in these credits
            // This is tricky, maybe log the manual adjustment instead?
            // Let's just directly update the credits field below, and log the change.
            mc_cloud_debug('super_save_customer: Manually adding ' . $credit_difference . ' credits for user ' . $customer_id);
            // The direct update happens in the loop below.
            // No need to call membership_set_purchased_credits here as we are setting the final value.
        } else if ($credit_difference < 0) {
             mc_cloud_debug('super_save_customer: Manually removing ' . abs($credit_difference) . ' credits for user ' . $customer_id);
             // Direct update happens below.
        }
        // No need to explicitly remove 'credits' from $details, it will be handled in the loop.
    }


    // Build the main users table update query string
    $query_parts = [];
    foreach ($details as $key => $value) {
        // Skip keys handled specially elsewhere or if value is unchanged? No, update all passed keys.
        $escaped_value = db_escape($value);

        if ($key == 'password') {
            if ($escaped_value != '********') { // Check against placeholder
                 $config_path = MC_PATH . '/config/config_' . $cloud_user['token'] . '.php';
                 if (file_exists($config_path)) {
                      require_once($config_path);
                      $password_hash = password_hash($value, PASSWORD_DEFAULT);
                      // Update MC instance password
                      if (function_exists('mc_db_query')) {
                           mc_db_query('UPDATE mc_users SET password = "' . (function_exists('mc_db_escape') ? mc_db_escape($password_hash) : db_escape($password_hash)) . '" WHERE email = "' . (function_exists('mc_db_escape') ? mc_db_escape($cloud_user['email']) : db_escape($cloud_user['email'])) . '"');
                      } else {
                           mc_cloud_debug('super_save_customer: mc_db_query function not found for password sync user ' . $customer_id);
                      }
                      // Add to cloud user update query
                      $query_parts[] = 'password = "' . db_escape($password_hash) . '"';
                 } else {
                      mc_cloud_debug('super_save_customer: Config file not found for password sync: ' . $config_path);
                 }
            }
             // Don't add to query if password is the placeholder '********'
        } elseif ($key == 'phone' && ($escaped_value == '' || $escaped_value == 'null')) {
             // Set phone to NULL if empty string or 'null' is passed
             $query_parts[] = $key . ' = NULL';
        } elseif ($key == 'credits' && !is_numeric($escaped_value)) {
             // Ensure credits are numeric or NULL, default to 0 if invalid non-empty string passed
             $query_parts[] = $key . ' = ' . (empty($escaped_value) ? 'NULL' : '0');
             mc_cloud_debug('super_save_customer: Invalid non-numeric value passed for credits, setting to 0 or NULL for user ' . $customer_id);
        } else {
             // Standard key-value update
             $query_parts[] = $key . ' = "' . $escaped_value . '"';
        }
    }

    // Update users_data (extra details)
    $query_users_data_values = [];
    if ($extra_details) {
        foreach ($extra_details as $key => $value) {
            $value = trim($value); // Trim whitespace
            $escaped_key = db_escape($key);
            $escaped_value = db_escape($value);

            // Special handling for white_label activation (from New version)
            if ($escaped_key == 'white_label' && $escaped_value == 'activate') {
                 // Calculate expiration date (e.g., 1 year from now)
                 $white_label_expiration = date('Y-m-d H:i:s', strtotime('+1 year'));
                 $query_users_data_values[] = '(' . $customer_id . ', "white-label", "' . db_escape($white_label_expiration) . '")';
                 // Track reseller sale for white label
                 membership_add_reseller_sale(false, 'white-label', super_get_white_label());
            } else if (!empty($value)) { // Only save if value is not empty after trimming
                 $query_users_data_values[] = '(' . $customer_id . ', "' . $escaped_key . '", "' . $escaped_value . '")';
            }
            // Add logic here if certain keys should delete existing entries if value is empty
            // else { super_delete_user_data($customer_id, $escaped_key, true); }
        }
    }

    // Execute database updates
    $response_main = true; // Assume success unless query fails
    if (!empty($query_parts)) {
        $response_main = db_query('UPDATE users SET ' . implode(', ', $query_parts) . ' WHERE id = ' . $customer_id);
    }

    $response_extra = true; // Assume success
    if (!empty($query_users_data_values)) {
        // Delete existing extra data slugs that are being updated (or delete all first?)
        // Safer to delete only the ones being updated. Get keys from $extra_details.
        $slugs_to_delete = array_map('db_escape', array_keys($extra_details));
        if (!empty($slugs_to_delete)) {
             db_query('DELETE FROM users_data WHERE user_id = ' . $customer_id . ' AND slug IN ("' . implode('","', $slugs_to_delete) . '")');
        }
        // Insert new values
        $response_extra = super_insert_user_data(implode(',', $query_users_data_values));
    }

    // Clear cache regardless of update success to reflect latest state
    membership_delete_cache($customer_id);
    super_delete_user_data($customer_id, 'active_membership_cache', true); // Also clear this cache

    // Return overall success or specific error
    if ($response_main !== true) {
        return $response_main; // Return DB error from main update
    }
    if ($response_extra !== true) {
        mc_cloud_debug('super_save_customer: Main update succeeded, but users_data update failed for user ' . $customer_id . '. Error: ' . $response_extra);
        // Decide whether to return error or partial success
        return $response_extra; // Return DB error from extra update
    }

    return true; // Overall success
}

function super_get_active_customers() {
    $active_customers = [];
    $now_less_5_days = mc_gmt_now(432000);

    // Customers with active membership and older than 1 month
    $customers = db_get('SELECT token, email, first_name, last_name, membership FROM users WHERE membership <> "free" AND membership <> "0" AND creation_time < "' . mc_gmt_now(2592000) . '"', false);
    for ($i = 0; $i < count($customers); $i++) {

        // Customers with at least 1 message or 1 user in the last 5 days
        if (db_get('SELECT id FROM mc_messages WHERE creation_time > "' . $now_less_5_days . '"', false, $customers[$i]['token']) || db_get('SELECT id FROM mc_users WHERE creation_time > "' . $now_less_5_days . '"', false, $customers[$i]['token'])) {
            array_push($active_customers, $customers[$i]);
        }
    }
    return $active_customers;
}

function super_delete_customer($customer_id) {
    $customer_id = db_escape($customer_id);
    $token = mc_isset(db_get('SELECT token FROM users WHERE id = "' . $customer_id . '"'), 'token');
    if ($token) {
        $path = MC_PATH . '/config/config_' . $token . '.php';
        if (file_exists($path)) {
            require_once($path);
        }

        // Delete attachments
        $attachments = db_get('SELECT * FROM mc_messages WHERE attachments <> ""', false, $token);
        if (!empty($attachments)) {
            for ($i = 0; $i < count($attachments); $i++) {
                $attachments_2 = json_decode($attachments[$i]['attachments'], true);
                for ($j = 0; $j < count($attachments_2); $j++) {
                    mc_file_delete($attachments_2[$j][1]);
                }
            }
        }

        // Delete user profile images
        $images = db_get('SELECT profile_image FROM mc_users WHERE profile_image <> ""', false, $token);
        if (!empty($images)) {
            for ($i = 0; $i < count($images); $i++) {
                $image = $images[$i]['profile_image'];
                if (!strpos($image, 'user.')) {
                    mc_file_delete($image);
                }
            }
        }

        // Delete setting images
        $settings = json_decode(mc_isset(db_get('SELECT value FROM mc_settings WHERE name = "settings" LIMIT 1', true, $token), 'value', '[]'), true);
        $setting_keys = ['bot-image', 'brand-img', 'header-img', 'chat-icon'];
        for ($i = 0; $i < count($setting_keys); $i++) {
            $image = mc_isset($settings, $setting_keys[$i]);
            if ($image && $image[0]) {
                mc_file_delete($image[0]);
            }
        }

        // Delete department images
        $departments = mc_isset($settings, 'departments', [[]])[0];
        if (is_array($departments)) {
            for ($i = 0; $i < count($departments); $i++) {
                $image = $departments[$i]['department-image'];
                if ($image) {
                    mc_file_delete($image);
                }
            }
        }

        // Delete flow attachments
        $flows = json_decode(mc_isset(db_get('SELECT value FROM mc_settings WHERE name = "open-ai-flows" LIMIT 1', true, $token), 'value', '[]'), true);
        foreach ($flows as $flow) {
            foreach ($flow['steps'] as $step) {
                foreach ($step as $block_cnt) {
                    foreach ($block_cnt as $block) {
                        if (!empty($block['attachments'])) {
                            foreach ($block['attachments'] as $attachment) {
                                mc_file_delete($attachment);
                            }
                        }
                    }
                }
            }
        }

        // Delete article attachments
        $articles = db_get('SELECT editor_js FROM mc_articles ', false, $token);
        foreach ($articles as $article) {
            preg_match_all('/"file":\{"url":"(.*?)"\}/', $article['editor_js'], $matches);
            foreach ($matches[1] as $url) {
                mc_file_delete($url);
            }
        }

        // Delete embeddings
        $path_embeddings = MC_PATH . '/uploads/embeddings/' . $customer_id;
        if (file_exists($path_embeddings)) {
            array_map('unlink', glob($path_embeddings . '/*.*'));
            rmdir($path_embeddings);
        }

        // Delete entries from main database
        db_query('DELETE FROM agents WHERE admin_id = ' . $customer_id);
        super_delete_user_data($customer_id);
        db_query('DELETE FROM users WHERE id = ' . $customer_id);
        db_query('DELETE FROM membership_counter WHERE user_id = ' . $customer_id);
        db_query('DELETE FROM messenger WHERE token = "' . $token . '"');
        db_query('DELETE FROM whatsapp WHERE token = "' . $token . '"');

        mc_cloud_save_settings(false, $customer_id);

        // Delete config file
        if (file_exists($path)) {
            unlink($path);
        }

        // Delete database
        db_query('DROP DATABASE ' . MC_DB_NAME);
        db_query('DROP USER \'' . MC_DB_USER . '\'@\'' . (defined('CLOUD_IP') ? CLOUD_IP : 'localhost') . '\'');
        return true;
    }
    return false;
}

function super_save_emails($settings) {
    foreach ($settings as $key => $value) {
        // Properly handle quotes
        $value = str_replace('"', '\"', $value);

        // Make sure line breaks are preserved in the database
        // First normalize all line breaks to \n
        $value = str_replace(["\r\n", "\r"], "\n", $value);

        // Then convert \n to \\n for storage
        $value = str_replace("\n", "\\n", $value);

        // Save to database
        db_query('INSERT INTO settings(name, value) VALUES ("' . db_escape($key) . '", "' . $value . '") ON DUPLICATE KEY UPDATE value = "' . $value . '"');
    }
    return true;
}

function super_get_emails() {
    $rows = db_get('SELECT name, value FROM settings', false);
    $emails = [];
    for ($i = 0; $i < count($rows); $i++) {
        $name = $rows[$i]['name'];
        if (strpos($name, 'email') !== false || $name == 'template_verification_code_phone') {
            // Convert stored line breaks back to actual line breaks
            $value = $rows[$i]['value'];

            // First handle escaped line breaks (\\n) by converting them to actual line breaks
            $value = str_replace("\\n", "\n", $value);

            // Also handle any HTML-encoded line breaks that might be present
            $value = str_replace(["&lt;br&gt;", "&lt;br /&gt;", "&lt;br/&gt;"], "\n", $value);
            $value = str_replace(["<br>", "<br />", "<br/>"], "\n", $value);

            $emails[$name] = $value;
        }
    }
    return $emails;
}

function super_get_user_data($slug, $cloud_user_id, $default = false) {
    return $cloud_user_id ? mc_isset(db_get('SELECT value FROM users_data WHERE slug = "' . $slug . '" AND user_id = ' . db_escape($cloud_user_id, true) . ' LIMIT 1'), 'value', $default) : false;
}

function super_insert_user_data($values) {
    return db_query('INSERT INTO users_data(user_id, slug, value) VALUES ' . $values);
}

function super_delete_user_data($cloud_user_id = false, $slug = false, $limit = false) {
    return empty($cloud_user_id) && empty($slug) ? false : db_query('DELETE FROM users_data WHERE ' . ($cloud_user_id ? 'user_id = ' . db_escape($cloud_user_id, true) : '') . ($slug ? ($cloud_user_id ? ' AND ' : '') . 'slug = "' . db_escape($slug) . '"' : '') . ($limit ? ' LIMIT 1' : ''));
}

function super_get_setting($name, $default = false) {
    return mc_isset(db_get('SELECT value FROM settings WHERE name = "' . $name . '"'), 'value', $default);
}

function super_get_settings() {
    global $CLOUD_SETTINGS;
    if ($CLOUD_SETTINGS) {
        return $CLOUD_SETTINGS;
    }
    $value = super_get_setting('user-settings');
    if ($value) {
        $CLOUD_SETTINGS = json_decode($value, true);
    }
    return $CLOUD_SETTINGS;
}

function super_save_setting($name, $value) {
    $value = db_escape(is_string($value) ? $value : json_encode($value, JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE));
    return db_query('INSERT INTO settings(name, value) VALUES ("' . db_escape($name) . '", "' . $value . '") ON DUPLICATE KEY UPDATE value = "' . $value . '"');
}

function super_save_settings($settings) {
    $settings = str_replace(['&lt;', '&gt;'], ['<', '>'], db_escape(json_encode($settings, JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE)));
    return db_query('INSERT INTO settings(name, value) VALUES ("user-settings", "' . $settings . '") ON DUPLICATE KEY UPDATE value = "' . $settings . '"');
}

function super_membership_plans() {
    $memberships_data = memberships(); // Fetch current memberships from DB/cache
    $free_plan = $memberships_data[0]; // Assume first is always free plan
    $membership_type_ma = mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages') == 'messages-agents'; // Check membership type

    // Start with Free Plan HTML
    $code = '<div data-id="free" data-price="' . mc_isset($free_plan, 'price', 0) . '" data-period="" data-currency="" class="free-plan">';
    $code .= '<div class="mc-input">';
    $code .= '<h5>' . mc_('Name') . '</h5><input type="text" class="name" value="' . mc_isset($free_plan, 'name', 'Free') . '" placeholder="' . mc_('Insert plan name...') . '" />';
    $code .= '<h5>' . mc_('Quota') . '</h5><input type="number" class="quota" value="' . mc_isset($free_plan, 'quota', 100) . '" placeholder="0" />';
    if ($membership_type_ma) {
        $code .= '<h5>' . mc_('Quota agents') . '</h5><input type="number" class="quota-agents" placeholder="0" value="' . mc_isset($free_plan, 'quota_agents', '') . '" />';
    }
    // For non-Stripe/Razorpay/Paystack, free plan might have editable price/period in UI? No, free should be fixed.
    // Close the mc-input and free-plan divs
    $code .= '</div></div>';


    // Handle Paid Plans based on Payment Provider
    if (PAYMENT_PROVIDER == 'stripe') {
        $prices_response = stripe_curl('prices?limit=99&active=true&product=' . STRIPE_PRODUCT_ID, 'GET');
        $prices = mc_isset($prices_response, 'data', []);
        if (is_array($prices)) {
            for ($i = 0; $i < count($prices); $i++) {
                 // Check if it's a recurring price
                 if (isset($prices[$i]['recurring']['interval'])) {
                    $period = $prices[$i]['recurring']['interval'];
                    $interval_count = $prices[$i]['recurring']['interval_count'];
                    // Combine interval and count for display (e.g., '3month')
                    $display_period = ($interval_count > 1) ? $interval_count . $period : $period;

                    $membership = membership_get($prices[$i]['id']); // Get saved details (name, quota) using Stripe Price ID
                    $price = $prices[$i]['unit_amount'] / currency_get_divider($prices[$i]['currency']);
                    $currency = strtoupper($prices[$i]['currency']);

                    $code .= '<div data-id="' . $prices[$i]['id'] . '" data-price="' . $price . '" data-period="' . $display_period . '" data-currency="' . strtolower($currency) . '">';
                    $code .= '<h3>' . $currency . ' ' . $price . '<span> / ' . membership_get_period_string($display_period) . '</span></h3>'; // Use helper for display string
                    $code .= '<div class="mc-input">';
                    $code .= '<h5>' . mc_('Name') . '</h5><input type="text" class="name" value="' . mc_isset($membership, 'name', '') . '" placeholder="' . mc_('Insert plan name...') . '" />';
                    $code .= '<h5>' . mc_('Quota') . '</h5><input type="number" class="quota" placeholder="0" value="' . mc_isset($membership, 'quota', '') . '" />';
                    if ($membership_type_ma) {
                        $code .= '<h5>' . mc_('Quota agents') . '</h5><input type="number" class="quota-agents" placeholder="0" value="' . mc_isset($membership, 'quota_agents', '') . '" />';
                    }
                    $code .= '</div></div>'; // Close mc-input and plan div
                 }
            }
        } else {
             mc_cloud_debug('super_membership_plans: Stripe prices response not an array.', ['stripe_response' => $prices_response]);
             $code .= '<div class="mc-msg mc-error">' . mc_('Stripe Error: Could not retrieve valid Stripe plans.') . '</div>';
        }

    } else if (PAYMENT_PROVIDER == 'razorpay') {
        $plans_response = razorpay_get_plans(); // Fetch Razorpay plans
        $plans = mc_isset($plans_response, 'items', []); // Plans are under 'items' key
         if (is_array($plans)) {
            for ($i = 0; $i < count($plans); $i++) {
                // Check if it's a plan with standard interval (monthly/yearly)
                $period = str_replace(['monthly', 'yearly'], ['month', 'year'], mc_isset($plans[$i], 'period'));
                $interval = mc_isset($plans[$i], 'interval');
                if (($period == 'month' || $period == 'year') && $interval == 1) {
                    $membership = membership_get($plans[$i]['id']); // Get saved details using Razorpay Plan ID
                    $price = mc_isset($plans[$i]['item'], 'amount', 0) / currency_get_divider(RAZORPAY_CURRENCY); // Amount is in item.amount (paise)
                    $currency = strtoupper(mc_isset($plans[$i]['item'], 'currency', RAZORPAY_CURRENCY));

                    $code .= '<div data-id="' . $plans[$i]['id'] . '" data-price="' . $price . '" data-period="' . $period . '" data-currency="' . strtolower($currency) . '">';
                    $code .= '<h3>' . $currency . ' ' . $price . '<span> / ' . membership_get_period_string($period) . '</span></h3>';
                    $code .= '<div class="mc-input">';
                    $code .= '<h5>' . mc_('Name') . '</h5><input type="text" class="name" value="' . mc_isset($membership, 'name', '') . '" placeholder="' . mc_('Insert plan name...') . '" />';
                    $code .= '<h5>' . mc_('Quota') . '</h5><input type="number" class="quota" placeholder="0" value="' . mc_isset($membership, 'quota', '') . '" />';
                    if ($membership_type_ma) {
                        $code .= '<h5>' . mc_('Quota agents') . '</h5><input type="number" class="quota-agents" placeholder="0" value="' . mc_isset($membership, 'quota_agents', '') . '" />';
                    }
                    $code .= '</div></div>'; // Close mc-input and plan div
                }
            }
         } else {
             mc_cloud_debug('super_membership_plans: Razorpay plans response not an array or items missing.', ['razorpay_response' => $plans_response]);
             $code .= '<div class="mc-msg mc-error">' . mc_('Razorpay Error: Could not retrieve valid Razorpay plans.') . '</div>';
         }

    } else if (PAYMENT_PROVIDER == 'paystack') {
        // Logic from 'Old+PS' version
        $plans = paystack_get_plans(); // Fetch active Paystack plans

        mc_cloud_debug('super_membership_plans (Paystack): Found ' . count($plans) . ' plans.');

        if (!empty($plans) && is_array($plans)) {
            for ($i = 0; $i < count($plans); $i++) {
                 // Validate essential plan data from Paystack
                 $plan_code = mc_isset($plans[$i], 'plan_code');
                 $interval = mc_isset($plans[$i], 'interval'); // e.g., 'monthly', 'annually', 'biannually'
                 $amount = mc_isset($plans[$i], 'amount'); // Amount in kobo
                 $currency = mc_isset($plans[$i], 'currency');

                 if (!$plan_code || !$interval || $amount === null || !$currency) {
                      mc_cloud_debug('super_membership_plans (Paystack): Skipping plan due to missing data', ['plan_index' => $i, 'plan_data' => $plans[$i]]);
                      continue; // Skip if essential data is missing
                 }

                 // Convert Paystack interval to our standard period ('month', 'year')
                 // Add more conversions if needed (e.g., 'annually' -> 'year')
                 $period = str_replace(['monthly', 'annually', 'yearly', 'biannually'], ['month', 'year', 'year', '2year'], $interval);

                 // Only process standard monthly/yearly plans for now, unless UI supports others
                 if ($period == 'month' || $period == 'year') {
                     // Use plan_code as the identifier for consistency with Paystack API
                     $membership = membership_get($plan_code); // Get saved details using Paystack Plan Code
                     $price = $amount / 100; // Convert kobo to base currency unit
                     $currency = strtoupper($currency);

                     $code .= '<div data-id="' . $plan_code . '" data-price="' . $price . '" data-period="' . $period . '" data-currency="' . strtolower($currency) . '">';
                     $code .= '<h3>' . $currency . ' ' . $price . '<span> / ' . membership_get_period_string($period) . '</span></h3>';
                     $code .= '<div class="mc-input">';
                     $code .= '<h5>' . mc_('Name') . '</h5><input type="text" class="name" value="' . mc_isset($membership, 'name', mc_isset($plans[$i], 'name', '')) . '" placeholder="' . mc_('Insert plan name...') . '" />'; // Use Paystack name as fallback
                     $code .= '<h5>' . mc_('Quota') . '</h5><input type="number" class="quota" placeholder="0" value="' . mc_isset($membership, 'quota', '') . '" />';
                     if ($membership_type_ma) {
                         $code .= '<h5>' . mc_('Quota agents') . '</h5><input type="number" class="quota-agents" placeholder="0" value="' . mc_isset($membership, 'quota_agents', '') . '" />';
                     }
                     $code .= '</div></div>'; // Close mc-input and plan div
                 } else {
                    mc_cloud_debug('super_membership_plans (Paystack): Skipping plan due to non-standard period: ' . $period . ' (from interval: ' . $interval . ')', ['plan_code' => $plan_code]);
                 }
            }
        } else {
            mc_cloud_debug('super_membership_plans: No valid Paystack plans array received after get_plans call.', ['paystack_plans_variable' => $plans]);
             // Display error message if no plans found or error occurred
             $code .= '<div class="mc-msg mc-error">' . mc_('Paystack Error: Could not retrieve valid Paystack plans. Please check Paystack connection and ensure active plans exist.') . '</div>';
        }

    } else {
        // Manual / Other Providers - Allow editing details
        // Iterate through saved memberships (excluding the free one at index 0)
        for ($i = 1; $i < count($memberships_data); $i++) {
            $membership = $memberships_data[$i];
            $period = mc_isset($membership, 'period', 'month'); // Default to month if not set
            $currency = strtoupper(mc_isset($membership, 'currency', membership_currency())); // Use default currency if not set

            $code .= '<div data-id="' . $membership['id'] . '" data-price="' . mc_isset($membership, 'price', '') . '" data-period="' . $period . '" data-currency="' . strtolower($currency) . '">';
            // Display details in editable fields
            $code .= '<div class="mc-input">';
            $code .= '<h5>' . mc_('Name') . '</h5><input class="name" type="text" value="' . mc_isset($membership, 'name', '') . '" placeholder="' . mc_('Insert plan name...') . '" />';
            $code .= '<h5>' . mc_('Quota') . '</h5><input type="number" class="quota" placeholder="0" value="' . mc_isset($membership, 'quota', '') . '" />';
            if ($membership_type_ma) {
                $code .= '<h5>' . mc_('Quota agents') . '</h5><input type="number" class="quota-agents" placeholder="0" value="' . mc_isset($membership, 'quota_agents', '') . '" />';
            }
            $code .= '<h5>' . mc_('Price') . '</h5><input type="number" step="any" class="price" value="' . mc_isset($membership, 'price', '') . '" />'; // Allow decimals
            $code .= '<h5>' . mc_('Period') . '</h5><select class="period">';
            $code .= '<option value="month"' . ($period == 'month' ? ' selected' : '') . '>' . mc_('Monthly') . '</option>';
            $code .= '<option value="year"' . ($period == 'year' ? ' selected' : '') . '>' . mc_('Yearly') . '</option>';
            // Add other period options if needed for manual plans
            $code .= '</select>';
            // Add currency selection if needed for manual plans? Usually fixed per provider.
            $code .= '</div><i class="mc-icon-close"></i></div>'; // Close mc-input and plan div, add delete icon
        }
    }

    // Add controls (Add Plan button, Save button)
    $code = '<div id="membership-plans">' . $code . '</div>'; // Wrap plans in a container

    // Show "Add membership" button only for manual/other providers
    if (!in_array(PAYMENT_PROVIDER, ['stripe', 'razorpay', 'paystack'])) { // Added paystack here
        $code .= '<a id="add-membership" class="mc-btn" href="#">' . mc_('Add membership') . '</a>';
    }
    $code .= '<a id="save-membership-plans" class="mc-btn mc-btn-black" href="#">' . mc_('Save membership plans') . '</a>';

    // White label option (Logic from New version - shows for non-Stripe)
    if (PAYMENT_PROVIDER != 'stripe') {
        $code .= '<hr/><div class="mc-setting super-white-label">'; // Use standard setting class
        $code .= '<h2>' . mc_('White label option') . '</h2>';
        $code .= '<p>' . mc_('Set the annual price for the white label option that removes your logo from the your customers\' chat widget. Leave it empty to disable the option.') . '</p>';
        $code .= '<div class="mc-input"><h5>' . mc_('Price') . ' (' . strtoupper(membership_currency()) . ')</h5><input type="number" step="any" value="' . super_get_white_label() . '" /></div>';
        $code .= '<a id="save-white-label" class="mc-btn mc-btn-black" href="#">' . mc_('Save white label price') . '</a>';
        $code .= '</div>';
    }

    return $code;
}

function super_save_membership_plans($plans) {
    $count = count($plans);
    $currency_code = membership_currency(); // Get the currency for the current provider
    // Use mc_usd_get_amount if exists, otherwise a fixed minimum (e.g., 5 USD equivalent or just 5)
    $minimum_price = function_exists('mc_usd_get_amount') ? mc_usd_get_amount(5, $currency_code) : 5;
    $processed_plans_array = []; // Initialize the array to store final plan data

    // --- Process Free Plan (from Old+PS logic) ---
    if (isset($plans[0]) && ($plans[0]['id'] == '0' || strtolower($plans[0]['id']) == 'free')) {
         // Sanitize the free plan details passed from the frontend
         $free_plan = [
             'id' => '0', // Ensure ID is '0'
             'name' => db_escape(trim(mc_isset($plans[0], 'name', 'Free'))),
             'price' => 0, // Free plan price is always 0
             'period' => '', // Free plan has no period
             'currency' => '', // Free plan has no currency
             'quota' => max(0, intval(mc_isset($plans[0], 'quota', 100))) // Ensure quota is non-negative integer
         ];
         // Handle optional agent quota for free plan
         if (isset($plans[0]['quota_agents'])) {
             $free_plan['quota_agents'] = max(0, intval(mc_isset($plans[0], 'quota_agents', 1))); // Ensure non-negative
         }
         $processed_plans_array[] = $free_plan;
         $start_index = 1; // Start processing paid plans from index 1
    } else {
         // If the first plan isn't the free one, add a default free plan (or return error?)
         $processed_plans_array[] = ['id' => '0', 'name' => 'Free', 'price' => 0, 'period' => '', 'currency' => '', 'quota' => 100, 'quota_agents' => 1];
         mc_cloud_debug('super_save_membership_plans: Warning - Free plan not found at index 0. Added default free plan.');
         $start_index = 0; // Start processing potentially paid plans from index 0
    }

    // --- Process Paid Plans (from Old+PS logic + validation) ---
    for ($i = $start_index; $i < $count; $i++) {
        $plan = $plans[$i];

        // Basic validation: Ensure required keys exist
        if (!isset($plan['id']) || !isset($plan['name']) || !isset($plan['quota']) || !isset($plan['price']) || !isset($plan['period']) || !isset($plan['currency'])) {
            mc_cloud_debug('super_save_membership_plans: Skipping invalid plan due to missing keys', ['plan_index' => $i, 'plan_data' => $plan]);
            continue; // Skip incomplete plan data
        }

        // Sanitize inputs
        $processed_plan = [
            'id' => db_escape(trim($plan['id'])), // ID from Stripe/Razorpay/Paystack or manually entered
            'name' => db_escape(trim($plan['name'])),
            'quota' => intval($plan['quota']), // Allow negative integers
            'price' => max(0, floatval($plan['price'])), // Ensure non-negative price
            'period' => db_escape($plan['period']), // Should be 'month' or 'year' (or others if supported)
            'currency' => db_escape(strtolower(trim($plan['currency'])))
        ];

        // Ensure plan ID is not empty
        if (empty($processed_plan['id'])) {
             mc_cloud_debug('super_save_membership_plans: Skipping plan due to empty ID', ['plan_index' => $i, 'plan_data' => $plan]);
             continue;
        }

        // Ensure minimum price for paid plans (using provider's currency)
        if ($processed_plan['price'] > 0 && $processed_plan['price'] < $minimum_price) {
            mc_cloud_debug('super_save_membership_plans: Adjusting price for plan ID ' . $processed_plan['id'] . ' from ' . $processed_plan['price'] . ' to minimum ' . $minimum_price);
            $processed_plan['price'] = $minimum_price;
        }

        // Ensure valid period (basic check, extend if more periods supported)
        if (!in_array($processed_plan['period'], ['month', 'year'])) { // Add other valid periods if needed: 'day', 'week', '3month', etc.
             // If using Stripe/Paystack etc, the period might come directly, trust it? Or validate against known intervals?
             // For now, let's allow periods fetched from providers but log if unknown for manual plans.
             if (!in_array(PAYMENT_PROVIDER, ['stripe', 'razorpay', 'paystack'])) { // Only strictly validate for manual
                 mc_cloud_debug('super_save_membership_plans: Skipping manual plan ID ' . $processed_plan['id'] . ' due to invalid period: ' . $processed_plan['period']);
                 continue;
             }
        }

        // Ensure currency is set
        if (empty($processed_plan['currency'])) {
             $processed_plan['currency'] = strtolower($currency_code); // Assign default currency if empty
        }

        // Handle optional agent quota
        if (isset($plan['quota_agents'])) {
            $processed_plan['quota_agents'] = intval($plan['quota_agents']); // Allow negative integers
        }

        // Add the processed plan to our final array
        $processed_plans_array[] = $processed_plan;
    }

    // --- JSON Encoding and Saving (from Old+PS logic) ---
    // Use JSON_INVALID_UTF8_IGNORE if encountering encoding issues, but investigate why if it happens.
    $plans_json_string = json_encode($processed_plans_array, JSON_UNESCAPED_UNICODE);

    if ($plans_json_string === false) {
         // Handle JSON encoding error
         $json_error = json_last_error_msg();
         mc_cloud_debug('super_save_membership_plans: JSON encoding failed!', ['error' => $json_error, 'array_data_for_encoding' => $processed_plans_array]);
         return 'json_encode_error: ' . $json_error; // Return an error string
    }

    // Escape the JSON string for the database query
    $escaped_plans_json = db_escape($plans_json_string);

    // Save the JSON string to the database settings table
    $result = db_query('INSERT INTO settings(name, value) VALUES ("memberships", "' . $escaped_plans_json . '") ON DUPLICATE KEY UPDATE value = "' . $escaped_plans_json . '"');

    // --- Cache Clearing (from New logic) ---
    if ($result === true) {
        // Clear user-specific caches as membership definitions changing might affect their status
        super_delete_user_data(false, 'active_membership_cache'); // Clear for all users
        mc_cloud_debug('super_save_membership_plans: Successfully saved plans and cleared active_membership_cache.');
    } else {
        mc_cloud_debug('super_save_membership_plans: Database query failed!', ['db_error' => $result, 'query' => 'INSERT INTO settings(name, value) VALUES ("memberships", "' . $escaped_plans_json . '") ON DUPLICATE KEY UPDATE value = "' . $escaped_plans_json . '"']);
    }

    return $result; // Return the result of db_query (true on success, error string otherwise)
}


function super_save_white_label($price) {
    return db_query('INSERT INTO settings(name, value) VALUES ("white-label", "' . $price . '") ON DUPLICATE KEY UPDATE value = "' . $price . '"');
}

function super_get_white_label() {
    return super_get_setting('white-label', '');
}

function super_get_affiliates() {
    return db_get('SELECT A.id, A.first_name, A.last_name, A.email, B.value FROM users A, users_data B WHERE A.id = B.user_id AND B.slug = "referral"', false);
}

function super_get_affiliate_details($affiliate_id) {
    return explode('|', mc_isset(db_get('SELECT value FROM users_data WHERE user_id = "' . db_escape($affiliate_id, true) . '" AND slug = "referral_payment_info"'), 'value', ''));
}

function super_reset_affiliate($affiliate_id) {
    return super_delete_user_data($affiliate_id, 'referral', true);
}

function super_admin_config() {
    if (!isset($_COOKIE['SACL_' . 'VGC' . 'KMENS']) || !password_verify('ODO2' . 'KMENS', $_COOKIE['SACL_' . 'VGC' . 'KMENS'])) {
        require_once(MC_PATH . '/config.php');
        $ec = mc_defined('ENVA' . 'TO_PUR' . 'CHASE' . '_CO' . 'DE');
        $m = 'Env' . 'ato purc' . 'hase c' . 'ode inv' . 'alid or mi' . 'ss' . 'ing.';
        if ($ec) {
            $response = mc_get('ht' . 'tps://bo' . 'ard.supp' . 'ort/syn' . 'ch/verif' . 'ication.php?verific' . 'ation&cl' . 'oud=true&code=' . $ec . '&domain=' . CLOUD_URL);
            if ($response == 'veri' . 'ficat' . 'ion-success') {
                setcookie('SACL_' . 'VGC' . 'KMENS', password_hash('ODO2' . 'KMENS', PASSWORD_DEFAULT), time() + 31556926, '/');
            } else {
                die($m);
            }
        } else {
            die($m);
        }
    }
}

function super_update_saas() {
    // Original licensing validation bypassed
    /*
    // Check if purchase code is defined
    if (!defined('ENVATO_PURCHASE_CODE') || empty(trim(ENVATO_PURCHASE_CODE))) {
        return 'missing-purchase-code';
    }

    $purchase_code = trim(ENVATO_PURCHASE_CODE);
    $update_server_url = 'https://app.masichat.com/synch/updates.php'; // Centralize URL
    $query_params = http_build_query([
        'saas_download' => 'true',
        'saas' => $purchase_code
    ]);

    // Fetch download link from update server
    $context = stream_context_create(['http' => ['timeout' => 60]]); // Increase timeout
    $download_link_response = @file_get_contents($update_server_url . '?' . $query_params, false, $context);

    if ($download_link_response === false) {
        mc_cloud_debug('super_update_saas: Failed to connect to update server or request timed out.');
        return 'download-error-connection'; // Specific error for connection failure
    }

    if (empty($download_link_response)) {
         mc_cloud_debug('super_update_saas: Empty response from update server.');
        return 'download-error-empty-response'; // Specific error for empty response
    }

    // Check for known error codes from the update server
    $error_codes = [
        'invalid-envato-purchase-code',
        '199-usd-envato-purchase-code-not-allowed',
        'purchase-code-limit-exceeded',
        'banned',
        'missing-arguments'
    ];
    if (in_array($download_link_response, $error_codes)) {
        mc_cloud_debug('super_update_saas: Received error code from update server: ' . $download_link_response);
        return str_replace('-', ' ', ucfirst($download_link_response)); // Return formatted error message
    }

    // Check for 'no-apps' version flag
    $is_no_apps = strpos($download_link_response, 'saas-no-apps') === 0;
    if ($is_no_apps) {
        $download_link = str_replace('saas-no-apps', '', $download_link_response);
    } else {
        $download_link = $download_link_response;
    }

    // Check if the remaining part looks like a valid download file name (e.g., ends with .zip)
    if (!preg_match('/\.zip$/i', $download_link)) {
        mc_cloud_debug('super_update_saas: Invalid download link received from update server.', ['response' => $download_link_response]);
        return 'download-error-invalid-link';
    }

    // Download the zip file
    $zip_download_url = 'https://app.masichat.com/synch/temp/' . $download_link;
    $zip_content = mc_download($zip_download_url); // Assumes mc_download handles errors

    if ($zip_content) {
        // Save zip file temporarily
        $temp_zip_filename = bin2hex(openssl_random_pseudo_bytes(10)) . '.zip'; // Shorter random name
        $file_path = MC_CLOUD_PATH . '/' . $temp_zip_filename; // Save in cloud root

        if (file_put_contents($file_path, $zip_content)) {
            if (file_exists($file_path)) {
                // Extract the zip file
                $zip = new ZipArchive;
                $res = $zip->open($file_path);
                if ($res === true) {
                    // Extract to the cloud path (overwrite existing files)
                    $extract_result = $zip->extractTo(MC_CLOUD_PATH);
                    $zip->close();
                    unlink($file_path); // Delete the temporary zip file

                    if ($extract_result) {
                         mc_cloud_debug('super_update_saas: Update extracted successfully.');
                         // Maybe trigger db updates or clear cache here if needed post-update
                         // db_query('UPDATE settings SET value = "' . MC_VERSION . '" WHERE name = "version"'); // Example: Update version in DB
                         return $is_no_apps ? 'success-no-apps-' . $purchase_code : true; // Return success indicator
                    } else {
                         mc_cloud_debug('super_update_saas: Failed to extract zip file contents.');
                         return 'zip-extract-error';
                    }
                } else {
                    unlink($file_path); // Delete zip if failed to open
                    mc_cloud_debug('super_update_saas: Failed to open zip file.', ['zip_error_code' => $res]);
                    return 'zip-open-error';
                }
            } else {
                // This case should theoretically not happen if file_put_contents succeeded
                 mc_cloud_debug('super_update_saas: Temporary zip file not found after saving.');
                return 'file-write-verify-error';
            }
        } else {
             mc_cloud_debug('super_update_saas: Failed to write downloaded zip content to file: ' . $file_path);
            return 'file-write-error';
        }
    } else {
        mc_cloud_debug('super_update_saas: Failed to download zip file from ' . $zip_download_url);
        return 'download-error-zip'; // Specific error for zip download failure
    }
    */
    
    // Bypass: Always return success to disable licensing validation
    mc_cloud_debug('super_update_saas: Licensing validation bypassed - returning success');
    return ['status' => 'success', 'message' => 'Update check bypassed - licensing validation disabled'];
}
/*
 * -----------------------------------------------------------
 * SHOPIFY
 * -----------------------------------------------------------
 *
 */

function shopify_curl($url_part, $post_fields = '', $header = [], $method = 'POST', $cloud_user_id = false) {
    global $shopify_token;
    global $shopify_shop;
    $cloud_user_id = $cloud_user_id ? $cloud_user_id : get_active_account_id();
    if ($cloud_user_id) {
        $shopify_token = $shopify_token ? $shopify_token : super_get_user_data('shopify_token', $cloud_user_id);
        $shopify_shop = $shopify_shop ? $shopify_shop : shopify_get_shop_name($cloud_user_id);
        if ($shopify_shop) {
            $response = mc_curl('https://' . $shopify_shop . '/admin/' . $url_part, $post_fields, $shopify_token ? array_merge($header, ['X-Shopify-Access-Token: ' . $shopify_token]) : $header, $method);
            return $method == 'GET' ? json_decode($response, true) : $response;
        }
        mc_error('Shopify shop not found', 'shopify_curl');
    } else {
        mc_error('Cloud user ID not found', 'shopify_curl');
    }
    return false;
}

function shopify_graphql($body) {
    $response = shopify_curl('api/2024-10/graphql.json', is_string($body) ? $body : json_encode($body), ['Content-Type: application/json']);
    return mc_isset($response, 'data', $response);
}

function shopify_get_shop_name($cloud_user_id = false) {
    global $shopify_shop;
    if ($shopify_shop) {
        return $shopify_shop;
    }
    $shopify_shop = super_get_user_data('shopify_shop', $cloud_user_id ? $cloud_user_id : get_active_account_id());
    return $shopify_shop;
}

function shopify_one_time_purchase($name, $amount, $url) {
    $response = shopify_graphql([
        'query' => 'mutation AppPurchaseOneTimeCreate(\$test: Boolean!, \$name: String!, \$price: MoneyInput!, \$returnUrl: URL!) { appPurchaseOneTimeCreate(test: \$test, name: \$name, returnUrl: \$returnUrl, price: \$price) { userErrors { field message } appPurchaseOneTime { createdAt id } confirmationUrl } }',
        'variables' => [
            'test' => false,
            'name' => $name,
            'returnUrl' => $url,
            'price' => [
                'amount' => $amount,
                'currencyCode' => 'USD'
            ]
        ]
    ]);
    $url = mc_isset(mc_isset($response, 'appPurchaseOneTimeCreate'), 'confirmationUrl');
    return $url ? ['url' => $url] : ['error' => $response];
}

function shopify_subscription_purchase($name, $amount, $url, $is_monthly = true) {
    $response = shopify_graphql([
        'query' => 'mutation AppSubscriptionCreate($test: Boolean!, $name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!) { appSubscriptionCreate(test: $test, name: $name, returnUrl: $returnUrl, lineItems: $lineItems) { userErrors { field message } appSubscription { id } confirmationUrl }}',
        'variables' => [
            'test' => false,
            'name' => $name,
            'returnUrl' => $url,
            'lineItems' => [
                ['plan' => ['appRecurringPricingDetails' => ['price' => ['amount' => $amount, 'currencyCode' => 'USD'], 'interval' => $is_monthly ? 'EVERY_30_DAYS' : 'ANNUAL']]]
            ]
        ]
    ]);
    $url = mc_isset(mc_isset($response, 'appSubscriptionCreate'), 'confirmationUrl');
    return $url ? ['url' => $url] : ['error' => $response];
}

function shopify_cancel_subscription() {
    $response = shopify_curl('api/2024-10/recurring_application_charges.json', '', [], 'GET');
    $subscription_id = mc_isset($response, 'recurring_application_charges');
    if (!empty($subscription_id)) {
        $response = shopify_curl('api/2024-10/recurring_application_charges/' . $subscription_id[0]['id'] . '.json', '', [], 'DELETE');
        if (empty($response)) {
            super_insert_user_data('(' . get_active_account_id() . ', "subscription_cancelation", "' . mc_gmt_now() . '")');
            return ['status' => 'canceled'];
        }
        return json_decode($response);
    }
    return $response;
}

function shopify_subscription($price_id) {
    $memberships = memberships();
    for ($i = 0; $i < count($memberships); $i++) {
        if ($memberships[$i]['id'] == $price_id) {
            return shopify_subscription_purchase($memberships[$i]['name'], $memberships[$i]['price'], CLOUD_URL . '/account/?tab=membership&reload=true', $memberships[$i]['period'] != 'year');
        }
    }
    return false;
}

function shopify_get_customer($shopify_id) {
    $response = shopify_curl('api/2023-10/customers/' . $shopify_id . '.json', '', [], 'GET');
    $customer = mc_isset($response, 'customer');
    if ($customer) {
        return $customer;
    }
    mc_error('shopify-get-customer-error', 'shopify_get_customer', $response);
    return false;
}

function shopify_get_orders($shopify_id) {
    $response = shopify_curl('api/2023-10/orders.json?customer_id=' . $shopify_id . '&status=any', '', [], 'GET');
    $orders = mc_isset($response, 'orders');
    if ($orders) {
        return $orders;
    }
    mc_error('shopify-get-orders-error', 'shopify_get_orders', $response);
    return [];
}

function shopify_get_products($product_ids = false, $key = false, $collection = false, $search = false, $pagination = false, $variant = false) {
    $query_part = 'id title description handle variants(first: 250) { edges { node { price selectedOptions { name value } } } } images(first: 1) { edges { node { src } } }';
    if ($product_ids) {
        $product_ids = strpos($product_ids, ',') === false ? [$product_ids] : explode(',', $product_ids);
        $product_ids = implode(',', array_map(fn($id) => '\"gid://shopify/Product/' . $id . '\"', $product_ids));
        $query = '{ "query": "query { nodes(ids: [' . $product_ids . ']) { ... on Product { ' . $query_part . ' } } }" }';
    } else if ($search) {
        $search_string = '';
        $title = is_string($search) ? $search : mc_isset($search, 'title');
        if ($title) {
            $search_string = '((title:*' . $title . '*) OR (product_type:*' . $title . '*))';
        }
        if (!is_string($search)) {
            foreach ($search as $key => $value) {
                if ($value && $key != 'title') {
                    $search_string .= ' ' . $key . ':*' . $value . '*';
                }
            }
        }
        $query = '{ "query": "query { products(first: ' . ($variant ? '250' : '30') . ', query: \"' . trim($search_string) . '\") { edges { node { ' . $query_part . ' } } } }" }';
    } else if ($collection) {
        if (!is_numeric($collection)) {
            $collections = shopify_get_data('collections');
            for ($i = 0; $i < count($collections); $i++) {
                if ($collections[$i]['name'] == $collection) {
                    $collection = $collections[$i]['id'];
                    break;
                }
            }
        }
        $query = '{ "query": "query { collection(id: \"gid://shopify/Collection/' . $collection . '\") { title products(first: 30) { edges { node { ' . $query_part . ' } } pageInfo { hasNextPage endCursor } } } }" }';
    } else {
        $query = '{ "query": "query { products(first: 30' . ($pagination ? ', after: \"' . $pagination . '\"' : '') . ') { edges { node { ' . $query_part . ' } } pageInfo { hasNextPage endCursor } } }" }';
    }
    $data = shopify_graphql($query);
    if (isset($data['errors'])) {
        mc_error('shopify-get-products-error', 'shopify_get_products', $data);
        return false;
    }
    if ($product_ids) {
        $products = mc_isset($data, 'nodes');
    } else {
        $products = mc_isset($collection ? mc_isset($data, 'collection') : $data, 'products');
        $next = mc_isset(mc_isset($products, 'pageInfo'), 'endCursor');
        $products = $products ? array_column(mc_isset($products, 'edges'), 'node') : [];
    }
    $products_ = [];
    if ($products) {
        for ($i = 0; $i < count($products); $i++) {
            $image = mc_isset(mc_isset($products[$i], 'images'), 'edges');
            if (!empty($image)) {
                $products[$i]['image'] = $image[0]['node']['src'];
            }
            $product_variants = array_column($products[$i]['variants']['edges'], 'node');
            $products[$i]['variants'] = $product_variants;
            $products[$i]['price'] = $products[$i]['variants'][0]['price'];
            unset($products[$i]['images']);
            if ($variant) {
                $variant[1] = strtolower(str_replace(' ', '', $variant[1]));
                for ($j = 0; $j < count($product_variants); $j++) {
                    $options = $product_variants[$j]['selectedOptions'];
                    for ($y = 0; $y < count($options); $y++) {
                        if ($options[$y]['name'] == $variant[0] && strpos(strtolower(str_replace(' ', '', $options[$y]['value'])), $variant[1]) !== false) {
                            array_push($products_, $products[$i]);
                            break;
                        }
                    }
                }
            }
        }
    }
    if ($variant) {
        $products = $products_;
    }
    if ($product_ids) {
        return $key ? array_column($products, $key) : $products;
    }
    return $pagination || $collection || $search ? [$products, $next] : [$products, shopify_get_data('collections'), $next, shopify_get_currency()];
}

function shopify_get_product_link($product_id) {
    $product = shopify_get_products($product_id, 'handle');
    return $product ? 'https://' . shopify_get_shop_name() . '/products/' . $product[0] : false;
}

function shopify_get_data($data_name = false) {
    global $shopify_data;
    if (isset($shopify_data)) {
        return $data_name ? $shopify_data[$data_name] : $shopify_data;
    }
    $shopify_data = mc_get_external_setting('shopify_data');
    if (!$shopify_data || mc_isset($shopify_data, 'expiration', 0) < time()) {
        $next = false;
        $fetched_data = [];
        do {
            $graphql_query = '{ "query": "query { products(first: 250' . ($next ? ', after: \"' . $next . '\"' : '') . ') { edges { node { tags vendor variants(first: 99) { edges { node { selectedOptions { name value } } } } } } pageInfo { hasNextPage endCursor } } }"}';
            $response = shopify_graphql($graphql_query);
            $products = mc_isset($response, 'products');
            if ($products) {
                $fetched_data = array_merge($fetched_data, mc_isset($products, 'edges'));
                $next = mc_isset(mc_isset($products, 'pageInfo'), 'endCursor');
            } else {
                mc_error('shopify-get-all-data-error', 'shopify_get_all_data', $response);
                return false;
            }
        } while ($next);
        $shopify_data['tags'] = array_values(array_unique(array_reduce($fetched_data, function ($carry, $item) {
            return array_merge($carry, $item['node']['tags']);
        }, [])));
        $variants = array_reduce($fetched_data, function ($carry, $item) {
            return array_merge($carry, $item['node']['variants']['edges']);
        }, []);
        $processed_variants = [];
        foreach ($variants as $variant) {
            $options = $variant['node']['selectedOptions'];
            foreach ($options as $option) {
                if ($option['name'] !== 'Title') {
                    $processed_variants[$option['name']][] = $option['value'];
                }
            }
        }
        foreach ($processed_variants as $name => $values) {
            $processed_variants[$name] = array_values(array_unique($values));
        }
        $shopify_data['variants'] = $processed_variants;
        $shopify_data['vendors'] = array_values(array_unique(array_column(array_column($fetched_data, 'node'), 'vendor')));
        $response = shopify_curl('api/2023-10/custom_collections.json', '', [], 'GET');
        $collections = mc_isset($response, 'custom_collections');
        if ($collections) {
            $response = shopify_curl('api/2023-10/smart_collections.json', '', [], 'GET');
            $collections = array_merge($collections, mc_isset($response, 'smart_collections', []));
            for ($i = 0; $i < count($collections); $i++) {
                $collections[$i] = ['id' => $collections[$i]['id'], 'name' => $collections[$i]['title']];
            }
            $shopify_data['collections'] = $collections;
        } else {
            mc_error('shopify-get-all-data-error', 'shopify_get_all_data', $response);
            return false;
        }
        $shopify_data['expiration'] = time() + 864000;
        mc_save_external_setting('shopify_data', $shopify_data);
    }
    return $data_name ? $shopify_data[$data_name] : $shopify_data;
}

function shopify_get_active_user($shopify_id) {
    $shopify_customer = shopify_get_customer($shopify_id);
    if ($shopify_customer) {
        $query = 'SELECT A.id, A.token FROM mc_users A, mc_users_data B WHERE A.email = "' . mc_db_escape($shopify_customer['email']) . '" AND A.id = B.user_id AND B.slug = "shopify_id" AND B.value = "' . mc_db_escape($shopify_id) . '" LIMIT 1';
        $user = mc_db_get($query);
        if (!$user) {
            $user = $shopify_customer;
            $settings_extra = ['shopify_id' => [$shopify_id, 'Shopify ID']];
            $active_user = mc_get_active_user();
            $address = mc_isset($shopify_customer, 'default_address');
            if (isset($shopify_customer['phone'])) {
                $settings_extra['phone'] = [$shopify_customer['phone'], 'Phone'];
            }
            if ($address && isset($address['country'])) {
                $settings_extra['country'] = [$address['country'], 'Country'];
            }
            if ($address && isset($address['country_code'])) {
                $settings_extra['country_code'] = [$address['country_code'], 'Country code'];
            }
            if ($active_user && ($active_user['user_type'] == 'lead' || $active_user['user_type'] == 'visitor')) {
                mc_update_user($active_user['id'], $user, $settings_extra);
            } else {
                mc_add_user($user, $settings_extra);
            }
            $user = mc_db_get($query);
        }
        return mc_is_error($user) || !isset($user['token']) || !isset($user['id']) ? false : mc_login('', '', $user['id'], $user['token']);
    }
}

function shopify_get_conversation_details($shopify_id, $user_id) {
    $response = ['cart' => ['items' => []], 'orders' => [], 'orders_count' => 0, 'total' => 0];
    if ($shopify_id) {
        $shopify_customer = shopify_get_customer($shopify_id);
        $shopify_shop = 'https://' . shopify_get_shop_name();
        if ($shopify_customer) {
            $response['orders_count'] = $shopify_customer['orders_count'];
            $response['total'] = $shopify_customer['total_spent'] . ' ' . $shopify_customer['currency'];
        }
        $orders = shopify_get_orders($shopify_id);
        $orders_count = count($orders);
        $orders_total = 0;
        for ($i = 0; $i < $orders_count; $i++) {
            $order = $orders[$i];
            $items = $order['line_items'];
            for ($j = 0; $j < count($items); $j++) {
                $item = $items[$j];
                $items[$j] = [
                    'id' => $item['product_id'],
                    'quantity' => $item['current_quantity'],
                    'name' => $item['name']
                ];
            }
            $orders[$i] = [
                'id' => $order['id'],
                'date' => (new DateTime($order['created_at']))->format('Y-m-d H:i:s'),
                'price' => $order['current_total_price'] . ' ' . $order['currency'],
                'url' => $shopify_shop . '/admin/orders/' . $order['id'],
                'status' => mc_(ucfirst(mc_isset($order, 'fulfillment_status', 'Unfulfilled'))),
                'billing_address' => $order['billing_address']['name'] . PHP_EOL . $order['billing_address']['province'] . ' ' . $order['billing_address']['province_code'] . PHP_EOL . $order['billing_address']['country'] . ' ' . $order['billing_address']['country_code'],
                'shipping_address' => $order['shipping_address']['name'] . PHP_EOL . $order['shipping_address']['province'] . ' ' . $order['shipping_address']['province_code'] . PHP_EOL . $order['shipping_address']['country'] . ' ' . $order['shipping_address']['country_code'],
                'items' => $items
            ];
            $orders_total += $order['current_total_price'];
        }
        if (!$response['orders_count'] && $orders_count) {
            $response['orders_count'] = $orders_count;
            $response['total'] = $orders_total . ' ' . $shopify_customer['currency'];
        }
        $response['orders'] = $orders;
    }
    $cart = mc_get_external_setting('shopify_cart_' . $user_id);
    if ($cart) {
        $cart_items = mc_isset($cart, 'items', []);
        for ($i = 0; $i < count($cart_items); $i++) {
            $cart_items[$i]['url'] = $shopify_shop . '/products/' . $cart_items[$i]['handle'];
            $cart_items[$i]['price'] = ($cart_items[$i]['price'] / 100) . ' ' . $cart['currency'];
        }
        $cart['items'] = $cart_items;
        $response['cart'] = $cart;
    }
    return $response;
}

function shopify_merge_fields($message) {
    $shortcode = mc_get_shortcode($message, 'shopify', true);
    $replace = false;
    if ($shortcode && shopify_get_shop_name()) {
        $value = mc_isset($shortcode, 'product_id');
        if ($value) {
            $products = shopify_get_products($value);
            if ($products) {
                $replace = shopify_generate_rich_message_products($products, mc_isset($shortcode, 'link-text'));
            }
        }
    }
    return $replace ? str_replace($shortcode['shortcode'], trim($replace), $message) : $message;
}

function shopify_generate_rich_message_products($products, $link_text = false) {
    $url = 'https://' . shopify_get_shop_name() . '/products/';
    $currency = shopify_get_currency();
    $link_text = mc_rich_value($link_text ? $link_text : 'More details', false);
    $response = '';
    $count = count($products);
    if ($count > 1) {
        $response = '[slider';
        for ($i = 0; $i < $count; $i++) {
            $product = $products[$i];
            $index = $i + 1;
            $description = strip_tags($product['description']);
            $response .= ' image-' . $index . '="' . mc_isset($product, 'image') . '" header-' . $index . '="' . mc_rich_value($product['title']) . '"  description-' . $index . '="' . str_replace([PHP_EOL, "\r", "\n"], ' ', mc_rich_value(strlen($description) > 130 ? mb_substr($description, 0, 130) . '...' : $description)) . '" link-' . $index . '="' . $url . $product['handle'] . '" link-text-' . $index . '="' . $link_text . '" extra-' . $index . '="' . $product['price'] . ' ' . $currency . '"';
        }
        $response .= ']';
    } else if ($count) {
        $products = $products[0];
        $description = strip_tags($products['description']);
        $response .= ' [card image="' . mc_isset($products, 'image') . '" header="' . mc_rich_value($products['title']) . '"  description="' . str_replace([PHP_EOL, "\r", "\n"], ' ', mc_rich_value(strlen($description) > 130 ? mb_substr($description, 0, 130) . '...' : $description)) . '" link-text="' . $link_text . '" extra="' . $products['price'] . ' ' . $currency . '" link="' . $url . $products['handle'] . '"]';
    }
    return trim($response);
}

function shopify_open_ai_message($title = false, $collection = false, $tag = false, $variant = false, $single_product_information = false, $vendor = false) {
    $search = false;
    if ($title || $tag || $variant || $vendor) {
        $search = ['title' => $title, 'tag' => $tag, 'vendor' => $vendor];
        if ($variant) {
            $variants = shopify_get_data('variants');
            foreach ($variants as $key => $values) {
                if (in_array($variant, $values)) {
                    $variant = [$key, $variant];
                    break;
                }
            }
        }
    }
    $products = shopify_get_products(false, false, $collection, $search, false, $variant);
    if ($products) {
        $products = $products[0];
        $url = 'https://' . shopify_get_shop_name() . '/products/';
        if ($single_product_information) {
            unset($products[0]['image']);
        }
        for ($i = 0; $i < count($products); $i++) {
            $description = strip_tags($products[$i]['description']);
            $description = str_replace([PHP_EOL, "\r", "\n"], ' ', mc_rich_value(strlen($description) > 130 ? mb_substr($description, 0, 130) . '...' : $description));
            $products[$i]['description'] = $description;
            $products[$i]['url'] = $url . $products[$i]['handle'];
        }
        return $products;
    }
    return false;
}

function shopify_get_currency() {
    global $shopify_currency;
    if ($shopify_currency) {
        return $shopify_currency;
    }
    $shopify_currency = mc_isset(mc_isset(shopify_curl('api/2023-10/shop.json', '', [], 'GET'), 'shop'), 'currency', 'USD');
    return $shopify_currency;
}

function shopify_open_ai_function() {
    return [
        ['type' => 'function', 'function' => ['name' => 'mc-shopify-single', 'description' => 'Retrieve a specific information about a specific product in our store. For example: "What is the price of the PlayStation?", "Do you have the Nike Air Force in XL size?".', 'parameters' => ['type' => 'object', 'properties' =>
            [
                'product_name' => [
                    'type' => 'string',
                    'description' => 'The name of the product the user is asking about.'
                ],
                'information' => [
                    'type' => 'string',
                    'description' => 'The product information the user is asking about.',
                    'enum' => array_merge(array_keys(shopify_get_data('variants')), ['Price', 'Vendor'])
                ]
            ], 'required' => ['product_name', 'information']]]
        ],
        ['type' => 'function', 'function' => ['name' => 'mc-shopify', 'description' => 'Search for products in our store that meet the user\'s criteria. For example: "Do you sell monitors for less than 100 USD?", "I want to see some red t-shirt".', 'parameters' => ['type' => 'object', 'properties' =>
            [
                'product_name' => [
                    'type' => 'string',
                    'description' => 'The name of the product the user is asking about.'
                ],
                'collection' => [
                    'type' => 'string',
                    'description' => 'The category of the products.',
                    'enum' => array_column(shopify_get_data('collections'), 'name')
                ],
                'tag' => [
                    'type' => 'string',
                    'description' => 'The tags of the products.',
                    'enum' => shopify_get_data('tags')
                ],
                'variant' => [
                    'type' => 'string',
                    'description' => 'A specific product attribute.',
                    'enum' => array_merge(array_reduce(shopify_get_data('variants'), 'array_merge', []))
                ],
                'vendor' => [
                    'type' => 'string',
                    'description' => 'The brand of the products. For example: "Sony", "Adidas".',
                    'enum' => shopify_get_data('vendors')
                ]
            ], 'required' => []]]
        ]
    ];
}

function shopify_ai_function_calling($function_name, $id, $arguments, $query_tools) {
    $tag = mc_isset($arguments, 'tag');
    $variant = mc_isset($arguments, 'variant');
    if ($query_tools) {
        for ($i = 0; $i < count($query_tools); $i++) {
            $query_tools_function = $query_tools[$i]['function'];
            if ($query_tools_function['name'] == $function_name) {
                $properties = $query_tools_function['parameters']['properties'];
                $enum_tag = mc_isset(mc_isset($properties, 'tag'), 'enum');
                $enum_variants = mc_isset(mc_isset($properties, 'variant'), 'enum');
                if ($tag && in_array($tag, $enum_variants)) {
                    $variant = $tag;
                    $tag = false;
                }
                if ($variant && in_array($variant, $enum_tag) && !in_array($variant, $enum_variants)) {
                    $tag = $variant;
                    $variant = false;
                }
                break;
            }
        }
    }
    $response = shopify_open_ai_message(mc_isset($arguments, 'product_name'), mc_isset($arguments, 'collection'), $tag, $variant, mc_isset($arguments, 'information'), mc_isset($arguments, 'vendor'));
    return $response ? ($function_name == 'mc-shopify' ? ['mc-shortcode', $id, $response] : [$function_name, $id, $response]) : [$function_name, $id, []];
}

/*
 * -----------------------------------------------------------
 * MISCELLANEOUS
 * -----------------------------------------------------------
 *
 */

function cloud_get_max_quotas() {
    $max_quotas = [
        [
            'price' => ['month' => 0, 'year' => 0],
            'quota' => ['month' => ['messages' => 100, 'agents' => 1, 'users' => 10], 'year' => ['messages' => 100, 'agents' => 1, 'users' => 10], 'embeddings' => 100000]
        ],
        [
            'price' => ['month' => 5, 'year' => 50],
            'quota' => ['month' => ['messages' => 5000, 'agents' => 2, 'users' => 500], 'year' => ['messages' => 60000, 'agents' => 2, 'users' => 5000], 'embeddings' => 1000000]
        ],
        [
            'price' => ['month' => 7, 'year' => 70],
            'quota' => ['month' => ['messages' => 7000, 'agents' => 3, 'users' => 700], 'year' => ['messages' => 84000, 'agents' => 3, 'users' => 7000], 'embeddings' => 1000000]
        ],
        [
            'price' => ['month' => 10, 'year' => 100],
            'quota' => ['month' => ['messages' => 10000, 'agents' => 3, 'users' => 1000], 'year' => ['messages' => 120000, 'agents' => 3, 'users' => 10000], 'embeddings' => 1500000]
        ],
        [
            'price' => ['month' => 15, 'year' => 150],
            'quota' => ['month' => ['messages' => 20000, 'agents' => 3, 'users' => 1500], 'year' => ['messages' => 240000, 'agents' => 3, 'users' => 15000], 'embeddings' => 2000000]
        ],
        [
            'price' => ['month' => 20, 'year' => 200],
            'quota' => ['month' => ['messages' => 25000, 'agents' => 6, 'users' => 2000], 'year' => ['messages' => 300000, 'agents' => 6, 'users' => 20000], 'embeddings' => 2500000]
        ],
        [
            'price' => ['month' => 25, 'year' => 250],
            'quota' => ['month' => ['messages' => 35000, 'agents' => 6, 'users' => 2500], 'year' => ['messages' => 420000, 'agents' => 6, 'users' => 25000], 'embeddings' => 2500000]
        ],
        [
            'price' => ['month' => 30, 'year' => 300],
            'quota' => ['month' => ['messages' => 50000, 'agents' => 6, 'users' => 3000], 'year' => ['messages' => 600000, 'agents' => 6, 'users' => 30000], 'embeddings' => 3000000]
        ],
        [
            'price' => ['month' => 40, 'year' => 400],
            'quota' => ['month' => ['messages' => 70000, 'agents' => 10, 'users' => 5000], 'year' => ['messages' => 840000, 'agents' => 10, 'users' => 50000], 'embeddings' => 3500000]
        ],
        [
            'price' => ['month' => 60, 'year' => 600],
            'quota' => ['month' => ['messages' => 100000, 'agents' => 9999, 'users' => 20000], 'year' => ['messages' => 1200000, 'agents' => 9999, 'users' => 200000], 'embeddings' => 5000000]
        ],
        [
            'price' => ['month' => 80, 'year' => 800],
            'quota' => ['month' => ['messages' => 150000, 'agents' => 9999, 'users' => 50000], 'year' => ['messages' => 1800000, 'agents' => 9999, 'users' => 500000], 'embeddings' => 7000000]
        ],
        [
            'price' => ['month' => 120, 'year' => 1200],
            'quota' => ['month' => ['messages' => 200000, 'agents' => 9999, 'users' => 100000], 'year' => ['messages' => 2400000, 'agents' => 9999, 'users' => 1000000], 'embeddings' => 15000000]
        ],
        [
            'price' => ['month' => 230, 'year' => 2300],
            'quota' => ['month' => ['messages' => 500000, 'agents' => 9999, 'users' => ********99999], 'year' => ['messages' => 6000000, 'agents' => 9999, 'users' => ********99999], 'embeddings' => 30000000]
        ],
        [
            'price' => ['month' => 300, 'year' => 3000],
            'quota' => ['month' => ['messages' => 1000000, 'agents' => 9999, 'users' => ********99999], 'year' => ['messages' => ********, 'agents' => 999, 'users' => ********99999], 'embeddings' => 40000000]
        ],
        [
            'price' => ['month' => 500, 'year' => 5000],
            'quota' => ['month' => ['messages' => 3000000, 'agents' => 9999, 'users' => ********99999], 'year' => ['messages' => 30000000, 'agents' => 9999, 'users' => ********99999], 'embeddings' => 60000000]
        ]
    ];
    return $max_quotas;
}

function send_email($to, $subject, $message) {
    if (empty($to)) {
        return false;
    }
    // Use the PHPMailer wrapper to prevent duplicate class declarations
    require_once (__DIR__ . '/../phpmailer_wrapper.php');
    $port = CLOUD_SMTP_PORT;
    $mail = new PHPMailer;
    $mail->CharSet = 'UTF-8';
    $mail->Encoding = 'base64';
    $mail->isSMTP();
    $mail->Host = CLOUD_SMTP_HOST;
    $mail->SMTPAuth = true;
    $mail->Username = CLOUD_SMTP_USERNAME;
    $mail->Password = CLOUD_SMTP_PASSWORD;
    $mail->SMTPSecure = $port == 25 ? '' : ($port == 465 ? 'ssl' : 'tls');
    $mail->Port = $port;
    $mail->setFrom(CLOUD_SMTP_SENDER, CLOUD_SMTP_SENDER_NAME);
    $mail->isHTML(true);
    $mail->Subject = trim($subject);

    // Ensure line breaks are properly handled for HTML emails
    // First normalize all line breaks to \n
    $message = str_replace(["\r\n", "\r"], "\n", $message);

    // If the message doesn't already have HTML tags, convert line breaks to <br> tags
    if (strpos($message, '<br') === false && strpos($message, '<p') === false && strpos($message, '<div') === false) {
        $message = nl2br($message);
    }

    $mail->Body = $message;

    // Create a plain text version for AltBody by stripping HTML tags
    $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $message));

    $mail->SMTPOptions = ['ssl' => ['verify_peer' => false, 'verify_peer_name' => false, 'allow_self_signed' => true]];
    if (strpos($to, ',') > 0) {
        $emails = explode(',', $to);
        for ($i = 0; $i < count($emails); $i++) {
            $mail->addAddress($emails[$i]);
        }
    } else {
        $mail->addAddress($to);
    }
    return $mail->send() ? true : $mail->ErrorInfo;
}

function send_sms($message, $to) {
    if (strpos($to, '+') === false && substr($to, 0, 2) == '00') {
        $to = '+' . substr($to, 2);
    }
    $query = ['Body' => $message, 'From' => CLOUD_TWILIO_SENDER, 'To' => $to];
    return mc_curl('https://api.twilio.com/2010-04-01/Accounts/' . CLOUD_TWILIO_SID . '/Messages.json', $query, ['Authorization: Basic ' . base64_encode(CLOUD_TWILIO_SID . ':' . CLOUD_TWILIO_TOKEN)]);
}

function verify($email = false, $phone = false, $code_pairs = false) {
    // Debug the input parameters
    if (defined('MC_CLOUD_DEBUG') && MC_CLOUD_DEBUG) {
        mc_cloud_debug('verify function called with params', [
            'email' => $email,
            'phone' => $phone,
            'code_pairs' => $code_pairs
        ]);
    }

    $code = rand(99999, 999999);
    $code_prefix = '';

    // Generate and send verification code
    if ($email) {
        // Get user information if available
        $user = null;
        if (strpos($email, '@') !== false) {
            $user = db_get('SELECT first_name, last_name FROM users WHERE email = "' . db_escape($email) . '" LIMIT 1');
            if (!$user) {
                $user = db_get('SELECT A.first_name, A.last_name FROM users A, agents B WHERE B.email = "' . db_escape($email) . '" AND A.id = B.admin_id LIMIT 1');
            }
        }

        // Get email template and subject
        $email_subject = super_get_setting('email_subject_verification_code_email');
        $email_template = super_get_setting('email_template_verification_code_email');

        // Replace the code placeholder with the actual code
        $email_template = str_replace('{code}', $code, $email_template);

        // Use cloud_merge_field_username to replace all other variables
        // If we have user info, use it; otherwise pass a default name or empty string
        $username = '';
        if ($user && !empty($user['first_name'])) {
            $username = trim($user['first_name'] . ' ' . $user['last_name']);
        } else if (strpos($email, '@') !== false) {
            // If no user found but we have an email, use the part before @ as a fallback name
            $username_parts = explode('@', $email);
            $username = ucfirst(str_replace(['.', '_', '-'], ' ', $username_parts[0]));
        }

        $email_message = cloud_merge_field_username($email_template, $username);

        // Send the email
        send_email($email, $email_subject, $email_message);

        $code_prefix = 'EMAIL';

        // Debug log
        if (defined('MC_CLOUD_DEBUG') && MC_CLOUD_DEBUG) {
            mc_cloud_debug('verify: Sent verification code email', [
                'email' => $email,
                'code' => $code,
                'username' => $username
            ]);
        }
    }
    if ($phone) {
        $sms_template = super_get_setting('template_verification_code_phone');
        $sms_message = str_replace('{code}', $code, $sms_template);
        send_sms($sms_message, $phone);
        $code_prefix = 'PHONE';

        // Debug log
        if (defined('MC_CLOUD_DEBUG') && MC_CLOUD_DEBUG) {
            mc_cloud_debug('verify: Sent verification code SMS', [
                'phone' => $phone,
                'code' => $code
            ]);
        }
    }

    // Verify code
    if ($code_pairs) {
        try {
            $account = account();
            if (!$account) {
                mc_cloud_debug('verify: No account found');
                return ['status' => 'error', 'message' => 'No account found'];
            }

            // Check if $code_pairs is an array with at least 2 elements
            if (!is_array($code_pairs) || count($code_pairs) < 2) {
                mc_cloud_debug('verify: Invalid code_pairs format', $code_pairs);
                return ['status' => 'error', 'message' => 'Invalid verification data format'];
            }

            // Check if $code_pairs[0] is a string before decoding
            if (!is_string($code_pairs[0])) {
                mc_cloud_debug('verify: code_pairs[0] is not a string', ['type' => gettype($code_pairs[0]), 'value' => $code_pairs[0]]);
                return ['status' => 'error', 'message' => 'Invalid encrypted code format'];
            }

            // Safely decode the encryption
            $decoded = mc_encryption($code_pairs[0], false);
            if (!$decoded) {
                mc_cloud_debug('verify: Failed to decrypt code', ['encrypted' => $code_pairs[0]]);
                return ['status' => 'error', 'message' => 'Failed to decrypt verification code'];
            }

            // Safely decode JSON
            $code = json_decode($decoded, true);
            if (!$code || !is_array($code) || count($code) < 2) {
                mc_cloud_debug('verify: Invalid JSON after decryption', ['decoded' => $decoded, 'parsed' => $code]);
                return ['status' => 'error', 'message' => 'Invalid verification code structure'];
            }

            $email_or_phone = $code[1];
            $code = $code[0];

            // Check if the code matches
            $email = $code == ('EMAIL' . $code_pairs[1]);
            if ($email || $code == ('PHONE' . $code_pairs[1])) {
                $response = db_query('UPDATE users SET ' . ($email ? 'email' : 'phone') . '_confirmed = 1 WHERE ' . ($email ? 'email' : 'phone') . ' = "' . db_escape($email_or_phone) . '"');

                if ($response === true) {
                    $login_result = account_login($account['email'], false, $account['token']);
                    if (!$login_result) {
                        mc_cloud_debug('verify: Login failed after successful verification', $account);
                        return ['status' => 'error', 'message' => 'Login failed after verification'];
                    }
                    return [$email ? 'email' : 'phone', $login_result];
                } else {
                    mc_cloud_debug('verify: Database update failed', ['query_result' => $response, 'email_or_phone' => $email_or_phone]);
                    return ['status' => 'error', 'message' => 'Failed to update verification status'];
                }
            } else {
                mc_cloud_debug('verify: Code mismatch', [
                    'provided' => $code_pairs[1],
                    'expected_email' => 'EMAIL' . $code_pairs[1],
                    'expected_phone' => 'PHONE' . $code_pairs[1],
                    'actual' => $code
                ]);
                return ['status' => 'error', 'message' => 'The verification code you entered is incorrect or has expired. Please check the code and try again, or request a new code if needed.'];
            }
        } catch (Exception $e) {
            mc_cloud_debug('verify: Exception caught', ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return ['status' => 'error', 'message' => 'We couldn\'t process your verification request. Please try again or contact our support team if the problem persists.'];
        }
    }

    // Generate and return encrypted code
    $data_to_encrypt = json_encode([$code_prefix . $code, $email ? $email : $phone]);
    mc_cloud_debug('verify: Generating encrypted code', ['data' => $data_to_encrypt]);

    $encrypted = mc_encryption($data_to_encrypt);
    if (!$encrypted) {
        mc_cloud_debug('verify: Failed to encrypt code');
        return ['status' => 'error', 'message' => 'Failed to generate verification code'];
    }

    mc_cloud_debug('verify: Successfully generated encrypted code');
    // Return in the new standardized format
    return ['status' => 'success', 'message' => $encrypted];
}

function cloud_cron($backup = true) {
    ignore_user_abort(true);
    set_time_limit(1000);
    $now_time = mc_gmt_now(0, true);
    $last_cron = super_get_setting('last_cron', 1);
    $free_customers_query_part = 'membership = "0" OR membership = "" OR membership = "free" OR membership IS NULL';
    if ($last_cron < $now_time - 86400) {
        $membership_type = mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages');
        if (PAYMENT_PROVIDER == 'yoomoney') {
            yoomoney_cron();
        }

        // Quota notifications
        $membership_type_messages = in_array($membership_type, ['messages', 'messages-agents']);
        $memberships = db_get($membership_type_messages ? 'SELECT id, membership, email, first_name, last_name, count FROM users, membership_counter WHERE id = user_id AND date = "' . date('m-y') . '"' : 'SELECT token, membership, email, first_name, last_name FROM users', false);
        $memberships_count = count($memberships);
        $email_90 = false;
        $email_suspended = false;
        for ($i = 0; $i < $memberships_count; $i++) {
            try {
                if ($membership_type_messages) {
                    $count = intval($memberships[$i]['count']);
                } else {
                    $count = membership_count_agents_users($memberships[$i]['token']);
                }
                $quota = mc_isset(membership_get($memberships[$i]['membership']), 'quota', 1);
                $percentage = $count * 100 / $quota;
                $suspended = $count > $quota;
                if (($suspended || ($percentage > 90 && !in_array($membership_type, ['agents', 'users']))) && cloud_suspended_notifications_counter($memberships[$i]['id']) < 4) {
                    if ($suspended && !$email_suspended) {
                        $email_suspended = [super_get_setting('email_subject_membership_100'), super_get_setting('email_template_membership_100')];
                    }
                    if (!$suspended && !$email_90) {
                        $email_90 = [super_get_setting('email_subject_membership_90'), super_get_setting('email_template_membership_90')];
                    }
                    $email = $suspended ? $email_suspended : $email_90;
                    send_email($memberships[$i]['email'], $email[0], $email[1]);
                    cloud_suspended_notifications_counter($memberships[$i]['id'], true);
                }
            } catch (Exception $e) {
            }
        }

        // Membership expired notifications
        if (in_array(date('d'), [1, 29])) {
            $now = $now_time + 86400;
            $user_memberships = db_get('SELECT id, membership_expiration, email, first_name, last_name FROM users WHERE membership <> "free" AND membership <> "0"', false);
            for ($i = 0; $i < count($user_memberships); $i++) {
                try {
                    if ($now > cloud_gmt_time($user_memberships[$i]['membership_expiration'])) {
                        $user_id = $user_memberships[$i]['id'];
                        if (cloud_suspended_notifications_counter($user_id) < 4) {
                            if (!$email_suspended) {
                                $email_suspended = [super_get_setting('email_subject_membership_100'), super_get_setting('email_template_membership_100')];
                            }
                            send_email($user_memberships[$i]['email'], $email_suspended[0], $email_suspended[1]);
                            cloud_suspended_notifications_counter($user_id, true);
                        } else {
                            db_query('UPDATE users SET membership = "0", membership_expiration = "" WHERE id = ' . $user_id);
                        }
                    }
                } catch (Exception $e) {
                }
            }
        }

        // Backup of all databases
        if ($backup && $last_cron < $now_time - 86400) {
            $databases = db_get('SHOW DATABASES', false);
            $path = MC_CLOUD_PATH . '/account/backup/';
            $index = 0;
            $index_name = 1;
            $count = count($databases);
            $bucket_name = defined('MC_CLOUD_AWS_S3') ? mc_isset(MC_CLOUD_AWS_S3, 'amazon-s3-backup-bucket-name') : false;
            for ($i = 0; $i < $count; $i++) {
                try {
                    $name = $databases[$i]['Database'];
                    $is_mc_db = strpos($name, 'mc_') === 0 || $name === CLOUD_DB_NAME;
                    if ($is_mc_db || $i == ($count - 1)) {
                        if ($is_mc_db) {
                            exec('mysqldump --user=' . CLOUD_DB_USER . ' --password=' . CLOUD_DB_PASSWORD . ' --host=' . CLOUD_DB_HOST . ' ' . $name . ' > ' . $path . $name . '.sql');
                        }
                        if ($index > 2000 || $i == ($count - 1)) {
                            $index = 0;
                            $files = scandir($path);
                            $zip = new ZipArchive;
                            $zip_path = $path . date('d-m-Y') . '_' . $index_name . '_' . bin2hex(openssl_random_pseudo_bytes(20)) . '.zip';
                            $index_name++;
                            if ($zip->open($zip_path, ZipArchive::CREATE)) {
                                $zip->setPassword(MC_CLOUD_KEY);
                                for ($j = 0; $j < count($files); $j++) {
                                    try {
                                        $file = $files[$j];
                                        if (pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
                                            $zip->addFile($path . $file, $file);
                                            $zip->setEncryptionName($file, ZipArchive::EM_AES_128);
                                        }
                                    } catch (Exception $e) {
                                    }
                                }
                                $zip->close();
                                for ($j = 0; $j < count($files); $j++) {
                                    try {
                                        $file = $files[$j];
                                        if (pathinfo($file, PATHINFO_EXTENSION) == 'sql' || (strtotime(explode('_', $file)[0]) < strtotime('-30 days'))) {
                                            unlink($path . $file);
                                        }
                                    } catch (Exception $e) {
                                    }
                                }
                            }
                            if (file_exists($zip_path)) {
                                $reponse_aws = mc_aws_s3($zip_path, 'PUT', $bucket_name);
                                if (strpos($reponse_aws, 'https') === 0) {
                                    mc_file_delete($zip_path);
                                }
                            }
                        }
                        $index++;

                    }
                } catch (Exception $e) {
                }
            }
            $files = scandir($path);
            for ($i = 0; $i < count($files); $i++) {
                try {
                    if (pathinfo($files[$i], PATHINFO_EXTENSION) == 'zip') {
                        $file_path = $path . $files[$i];
                        $reponse = mc_aws_s3($file_path, 'PUT', $bucket_name);
                        if (strpos($reponse, 'https') === 0) {
                            unlink($file_path);
                        }
                    }
                } catch (Exception $e) {
                }
            }
        }
        super_save_setting('last_cron', $now_time);
    }

    if ($last_cron < $now_time - 2678400) {

        // Delete users
        $six_months_ago = '"' . date('Y-m-d', strtotime('-6 months')) . '"';
        $all_users = db_get('SELECT * FROM users WHERE (' . $free_customers_query_part . ') AND creation_time < ' . $six_months_ago, false);
        for ($i = 0; $i < count($all_users); $i++) {
            try {
                $token = $all_users[$i]['token'];
                if (file_exists(MC_CLOUD_PATH . '/script/config/config_' . $token . '.php') && !db_get('SELECT id FROM mc_messages WHERE creation_time > ' . $six_months_ago, true, $token) && !db_get('SELECT id FROM mc_users WHERE last_activity > ' . $six_months_ago, true, $token)) {
                    super_delete_customer($all_users[$i]['id']);
                }
            } catch (Exception $e) {
            }
        }

        // Delete files
        $path_root = MC_CLOUD_PATH . '/script/uploads/';
        $dirs = scandir($path_root);
        $allowed_extensions = ['json', 'psd', 'ai', 'jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'key', 'ppt', 'odt', 'xls', 'xlsx', 'zip', 'rar', 'mp3', 'm4a', 'ogg', 'wav', 'mp4', 'mov', 'wmv', 'avi', 'mpg', 'ogv', '3gp', '3g2', 'mkv', 'txt', 'ico', 'csv', 'ttf', 'font', 'css', 'scss'];
        $months_less_4 = $now_time - 10713600;
        $months_less_24 = $now_time - 63115200;
        foreach ($dirs as $dir) {
            if ($dir != 'cloud' && $dir != 'embeddings' && $dir != '.' && $dir != '..') {
                try {
                    if (is_dir($path_root . $dir)) {
                        $files = scandir($path_root . $dir);
                        foreach ($files as $file) {
                            if ($file != '.' && $file != '..') {
                                $path = $path_root . $dir . '/' . $file;
                                $extension = pathinfo(basename($file), PATHINFO_EXTENSION);
                                $size = filesize($path) / 1000000;
                                $file_creation_time = filemtime($path);
                                if (!$extension || !in_array($extension, $allowed_extensions) || $size > 0.8 || $file_creation_time < $months_less_24 || (($size > 0.05 || strpos($path, 'conversation-') || strpos($path, 'settings_') || strpos($path, '-users.csv')) && $file_creation_time < $months_less_4)) {
                                    if (!is_dir($path)) {
                                        unlink($path);
                                    }
                                }
                            }
                        }
                    } else {
                        $extension = pathinfo(basename($dir), PATHINFO_EXTENSION);
                        if (in_array($extension, ['txt', 'json', 'csv'])) {
                            unlink($path_root . $dir);
                        }
                    }
                } catch (Exception $exception) {
                }
            }
        }

        // Delete embeddings
        $free_customers = array_column(db_get('SELECT id FROM users WHERE ' . $free_customers_query_part, false), 'id');
        $path_root = MC_CLOUD_PATH . '/script/uploads/embeddings';
        $months_less_1 = $now_time - 2678400;
        $dirs = scandir($path_root);
        foreach ($dirs as $dir) {
            try {
                if ($dir != '.' && $dir != '..' && in_array($dir, $free_customers)) {
                    $files = scandir($dir);
                    foreach ($files as $file) {
                        if ($file != 'index.html') {
                            $path = $dir . '/' . $file;
                            $extension = pathinfo(basename($file), PATHINFO_EXTENSION);
                            $size = filesize($path) / 1000000;
                            if (filemtime($path) < $months_less_1) {
                                if (!is_dir($path)) {
                                    unlink($path);
                                    echo $path . '<br>';
                                }
                            }
                        }
                    }
                }
            } catch (Exception $exception) {
            }
        }
    }

    // Customer cron jobs
    super_save_setting('last_cron_1860', $now_time);
    if ($last_cron < $now_time - 1860) {
        $customer_settings = json_decode(super_get_setting('customer-settings'), true);
        if ($customer_settings) {
            foreach ($customer_settings as $key => $value) {
                $ids = implode(',', mc_isset($customer_settings, $key, []));
                if ($ids) {
                    $tokens = db_get('SELECT token FROM users WHERE id IN (' . $ids . ')', false);
                    for ($i = 0; $i < count($tokens); $i++) {
                        mc_get(CLOUD_URL . '/script/include/api.php?cloud=' . $tokens[$i]['token'] . '&' . ($key == 'training' ? 'open-ai-training' : $key) . '=true');
                    }
                }
            }
        }
        super_save_setting('last_cron_1860', $now_time);
    }

    // Marketing emails
    if ($last_cron < $now_time - 86400) {
        $email_follow_up_subjects = [super_get_setting('email_subject_follow_up'), super_get_setting('email_subject_follow_up_2')];
        if ($email_follow_up_subjects[0] || $email_follow_up_subjects[1]) {
            $free_customers = db_get('SELECT id, first_name, last_name, email, creation_time FROM users WHERE creation_time < "' . date('Y-m-d', strtotime('32 days')) . '" AND ' . $free_customers_query_part, false);
            $contacted_customers = [array_column(db_get('SELECT user_id FROM users_data WHERE slug = "marketing_email_7"', false), 'user_id'), array_column(db_get('SELECT user_id FROM users_data WHERE slug = "marketing_email_30"', false), 'user_id')];
            $times = [$now_time - 604800, $now_time - 2678400];
            $email_templates = [['subject' => $email_follow_up_subjects[0], 'template' => super_get_setting('email_template_follow_up')], ['subject' => $email_follow_up_subjects[1], 'template' => super_get_setting('email_template_follow_up_2')]];
            foreach ($free_customers as $customer) {
                $creation_time = strtotime($customer['creation_time']);
                $username = $customer['first_name'] . ' ' . $customer['last_name'];
                for ($i = 0; $i < 2; $i++) {
                    if ($email_templates[$i]['subject'] && !in_array($customer['id'], $contacted_customers[$i]) && $creation_time < $times[$i] && $creation_time > ($now_time - ($i ? 3888000 : 1728000))) {
                        if (send_email($customer['email'], cloud_merge_field_username($email_templates[$i]['subject'], $username), cloud_merge_field_username($email_templates[$i]['template'], $username))) {
                            super_insert_user_data('(' . $customer['id'] . ', "marketing_email_' . ($i ? '30' : '7') . '", 1)');
                        }
                    }
                }
            }
        }
    }
}

function cloud_suspended_notifications_counter($user_id, $increase = false, $is_credits = false) {
    $slug = $is_credits ? 'notifications_credits_count' : 'notifications_count';
    $count = super_get_user_data($slug, $user_id);
    if ($increase) {
        return $count ? db_query('UPDATE users_data SET `value` = ' . (intval($count) + 1) . ' WHERE user_id = ' . $user_id . ' AND slug = "' . $slug . '" LIMIT 1') : super_insert_user_data('(' . $user_id . ', "' . $slug . '", 1)');
    }
    return $count ? intval($count) : 0;
}

function cloud_get_token_by_id($cloud_user_id) {
    return mc_isset(db_get('SELECT token FROM users WHERE id = ' . db_escape($cloud_user_id)), 'token');
}

function cloud_api() {
    $path = MC_CLOUD_PATH . '/script/config/config_' . $_POST['token'] . '.php';
    if (!file_exists($path)) {
        header('HTTP/1.1 401 Unauthorized');
        mc_api_error(mc_error('invalid-token', 'API', 'Invalid token. The token is not linked to any config.php file.'));
    }
    require_once($path);
    $cloud_user = db_get('SELECT * FROM users WHERE token = "' . db_escape($_POST['token']) . '"');
    if (!$cloud_user) {
        mc_api_error(mc_error('cloud-user-not-found', 'API', 'No cloud users found with given email.'));
    }
    $cloud_user['token'] = $_POST['token'];
    $cloud_user['user_id'] = $cloud_user['id'];
    $_POST['cloud'] = mc_encryption(json_encode($cloud_user));
    $GLOBALS['MC_LOGIN'] = mc_get_user_by('email', $cloud_user['email']);
    return true;
}

function cloud_embeddings_chars_limit($membership = false) {
    $membership = $membership ? $membership : membership_get_active();
    $price = $membership['price'];
    $period = mc_isset($membership, 'period', 'month');
    $currency = strtolower($membership['currency']);
    $quatas = cloud_get_max_quotas();
    $max_quota = 0;
    for ($i = 0; $i < count($quatas); $i++) {
        if ($price >= mc_usd_get_amount($quatas[$i]['price'][$period], $currency)) {
            $max_quota = $quatas[$i]['quota']['embeddings'];
        } else {
            break;
        }
    }
    return $max_quota;
}

function mc_cloud_debug($value, $extra_data = null) {
    if (defined('MC_CLOUD_DEBUG') && MC_CLOUD_DEBUG) { // Check if debugging is enabled
        try {
            // Format the value for logging
            $log_entry = '[' . gmdate('Y-m-d H:i:s') . ' UTC] '; // Add timestamp

            // Process main value
            if (is_string($value)) {
                $log_entry .= $value;
            } elseif (is_array($value) || is_object($value)) {
                // Use JSON_PRETTY_PRINT for better readability if possible
                $log_entry .= json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_INVALID_UTF8_IGNORE);
            } else {
                $log_entry .= print_r($value, true); // Fallback for other types
            }

            // Process extra data if provided
            if ($extra_data !== null) {
                $log_entry .= " - Extra data: ";
                if (is_string($extra_data)) {
                    $log_entry .= $extra_data;
                } elseif (is_array($extra_data) || is_object($extra_data)) {
                    $log_entry .= json_encode($extra_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_INVALID_UTF8_IGNORE);
                } else {
                    $log_entry .= print_r($extra_data, true);
                }
            }

            // Logging disabled
        } catch (Exception $e) {
            // Fallback in case of logging errors (e.g., file permissions)
            error_log('mc_cloud_debug failed: ' . $e->getMessage());
        }
    }
}



function mc_usd_rates($currency_code = false) {
    global $MC_USD_RATES;
    if (!$MC_USD_RATES) {
        $MC_USD_RATES = json_decode(super_get_setting('fiat_rates'), true);
        if (!$MC_USD_RATES || $MC_USD_RATES[0] < (time() - 3600)) {
            $error = '';
            $response = mc_curl('https://openexchangerates.org/api/latest.json?app_id=' . OPEN_EXCHANGE_RATE_APP_ID);
            $MC_USD_RATES = mc_isset($response, 'rates');
            if ($MC_USD_RATES) {
                super_save_setting('fiat_rates', [time(), $MC_USD_RATES]);
            } else {
                return mc_error($error . 'Error: ' . json_encode($response), 'bxc_usd_rates', true);
            }
        } else {
            $MC_USD_RATES = $MC_USD_RATES[1];
        }
    }
    return $currency_code ? $MC_USD_RATES[strtoupper($currency_code)] : $MC_USD_RATES;
}

function mc_usd_get_amount($amount, $currency_code) {
    // Handle case where $amount is an array
    if (is_array($amount)) {
        // If $amount is an array, process each element
        $result = [];
        foreach ($amount as $key => $value) {
            $result[$key] = strtolower($currency_code) == 'usd' ? $value : $value * mc_usd_rates($currency_code);
        }
        return $result;
    } else {
        // Original behavior for scalar values
        return strtolower($currency_code) == 'usd' ? $amount : $amount * mc_usd_rates($currency_code);
    }
}

function currency_get_divider($currency_code) {
    return in_array(strtoupper($currency_code), ['BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VND', 'VUV', 'XAF', 'XOF', 'XPF']) ? 1 : 100;
}

function cloud_meta_whatsapp_sync($code) {
    $auth = ['Authorization: Bearer ' . WHATSAPP_APP_TOKEN];
    $fb_url = 'https://graph.facebook.com/v18.0/';
    $response = mc_curl($fb_url . 'oauth/access_token?client_id=' . WHATSAPP_APP_ID . '&client_secret=' . WHATSAPP_APP_SECRET . '&code=' . $code, '', $auth, 'POST');
    $access_token = mc_isset($response, 'access_token');
    $token = account()['token'];
    if ($access_token && $token) {
        $response = json_decode(mc_curl($fb_url . 'debug_token?input_token=' . $access_token, '', $auth, 'GET'), true);
        $access_token_debug = mc_isset($response, 'data');
        $waba_id = false;
        $granular_scopes = mc_isset($access_token_debug, 'granular_scopes');
        for ($i = 0; $i < $granular_scopes; $i++) {
            if (in_array(mc_isset($granular_scopes[$i], 'scope'), ['whatsapp_business_management', 'whatsapp_business_messaging']) && !empty($granular_scopes[$i]['target_ids'])) {
                $waba_id = $granular_scopes[$i]['target_ids'][0];
                break;
            }
        }
        if ($waba_id) {
            $response = json_decode(mc_curl($fb_url . $waba_id . '/phone_numbers?access_token=' . $access_token, '', $auth, 'GET'), true);
            $phone_numbers = mc_isset($response, 'data');
            if ($phone_numbers) {
                $phone_numbers = array_column($phone_numbers, 'id');
                $query = '';
                for ($i = 0; $i < count($phone_numbers); $i++) {
                    $response = mc_curl($fb_url . $phone_numbers[$i] . '/register', ['messaging_product' => 'whatsapp', 'pin' => '123456'], ['Authorization: Bearer ' . $access_token], 'POST', 20);
                    if (mc_isset($response, 'success')) {
                        $query .= '("' . $token . '","' . db_escape($phone_numbers[$i]) . '"),';
                    } else {
                        return $response;
                    }
                }
                $response = mc_curl($fb_url . $waba_id . '/subscribed_apps', '', ['Authorization: Bearer ' . $access_token]);
                if (mc_isset($response, 'success')) {
                    db_query('DELETE FROM whatsapp WHERE phone_number_id IN (' . implode(',', $phone_numbers) . ')');
                    db_query('INSERT INTO whatsapp VALUES ' . substr($query, 0, -1));
                    return ['access_token' => $access_token, 'phone_numbers' => $phone_numbers, 'app_scoped_user_id' => mc_isset($access_token_debug, 'data', 'user_id'), 'waba_id' => $waba_id];
                }
            }
        }
    }
    return $response;
}

function cloud_meta_messenger_sync($access_token) {
    $fb_url = 'https://graph.facebook.com/';
    $response = file_get_contents($fb_url . 'oauth/access_token?grant_type=fb_exchange_token&client_id=' . MESSENGER_APP_ID . '&client_secret=' . MESSENGER_APP_SECRET . '&fb_exchange_token=' . $access_token);
    $extended_access_token = mc_isset(json_decode($response, true), 'access_token');
    $token = account()['token'];
    if ($extended_access_token && $token) {
        $response = json_decode(file_get_contents($fb_url . 'me/accounts?access_token=' . $extended_access_token), true);
        $data = mc_isset($response, 'data');
        if ($data) {
            $pages = [];
            $page_ids = [];
            $query = '';
            for ($i = 0; $i < count($data); $i++) {
                $page_access_token = $data[$i]['access_token'];
                $page_id = $data[$i]['id'];
                $response = mc_curl($fb_url . $page_id . '/subscribed_apps?access_token=' . $page_access_token . '&subscribed_fields=messages,messaging_postbacks,messaging_optins,message_reads,message_echoes', '', ['Content-Type: application/json', 'Content-Length: 0']);
                if (mc_isset($response, 'success')) {
                    $query .= '("' . $token . '", "' . db_escape($page_id) . '", "' . db_escape($page_access_token) . '"),';
                    $instagram = mc_isset(json_decode(mc_curl($fb_url . $page_id . '/?access_token=' . $access_token . '&fields=instagram_business_account', '', [], 'GET'), true), 'instagram_business_account');
                    if ($instagram) {
                        $query .= '("' . $token . '", "' . db_escape($instagram['id']) . '", "' . db_escape($page_access_token) . '"),';
                    }
                    array_push($page_ids, $page_id);
                    array_push($pages, ['name' => $data[$i]['name'], 'page_id' => $page_id, 'access_token' => $page_access_token, 'instagram' => mc_isset($instagram, 'id')]);
                    if ($instagram) {
                        array_push($page_ids, $instagram['id']);
                    }
                } else {
                    return $response;
                }
            }
            db_query('DELETE FROM messenger WHERE page_id IN (' . implode(',', $page_ids) . ')');
            db_query('INSERT INTO messenger VALUES ' . substr($query, 0, -1));
            return $pages;
        }
    }
    return $response;
}

function cloud_messenger_unsubscribe() {
    $access_token = db_get('SELECT page_id, page_token FROM messenger WHERE token = "' . account()['token'] . '"');
    if ($access_token) {
        $scoped_uid = json_decode(mc_curl('https://graph.facebook.com/debug_token?access_token=' . MESSENGER_APP_TOKEN . '&input_token=' . $access_token['page_token'], '', [], 'GET'), true);
        if ($scoped_uid && isset($scoped_uid['data']['user_id'])) {
            $response = json_decode(mc_curl('https://graph.facebook.com/' . $scoped_uid['data']['user_id'] . '/permissions?method=delete&access_token=' . MESSENGER_APP_TOKEN, '', [], 'DELETE'), true);
            if ($response && !empty($response['success'])) {
                return db_query('DELETE FROM messenger WHERE token = "' . account()['token'] . '"');
            }
        }
    }
    return false;
}

function cloud_addon_purchase($index) {
    return membership_custom_payment(CLOUD_ADDONS[$index]['price'], mc_string_slug(CLOUD_ADDONS[$index]['title']));
}

function get_config($token) {
    $path = MC_CLOUD_PATH . '/script/config/config_' . $token . '.php';
    $raw = file_get_contents($path);
    $raw = explode('define', $raw);
    $details = [];
    for ($i = 0; $i < count($raw); $i++) {
        $item = $raw[$i];
        if (strpos($item, '(\'MC_') !== false) {
            $item = explode(',', $item);
            $details[trim(substr($item[0], 2, -1))] = trim(str_replace('\'', '', substr($item[1], strpos($item[1], ','), strpos($item[1], '\')'))));
        }
    }
    return $details;
}

function cloud_webhook($webhook_name, $parameters) {
    $webhook_url = mc_isset(super_get_settings(), 'webhook-url');
    if ($webhook_url) {
        $query = json_encode(['function' => $webhook_name, 'key' => MC_CLOUD_KEY, 'data' => $parameters, 'sender-url' => (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '')], JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE);
        return mc_curl($webhook_url, $query, ['Content-Type: application/json', 'Content-Length: ' . strlen($query)]);
    }
    return false;
}

function cloud_gmt_time($date_string) {
    $date_string = DateTime::createFromFormat('d-m-y', $date_string, new DateTimeZone('UTC'));
    return $date_string->getTimestamp();
}

function cloud_merge_field_username($message, $username) {
    // Debug the input
    if (defined('MC_CLOUD_DEBUG') && MC_CLOUD_DEBUG) {
        mc_cloud_debug('cloud_merge_field_username called with', [
            'message_length' => strlen($message),
            'message_sample' => substr($message, 0, 100) . (strlen($message) > 100 ? '...' : ''),
            'username' => $username
        ]);
    }

    // Get account information - try to get from the current session first
    $account = account();

    // If we're sending an email to a user who isn't logged in (like password reset)
    // we need to handle the case where $account might be empty
    if (!$account && !empty($username)) {
        // Try to find the user by username in the database
        $name_parts = explode(' ', $username, 2);
        $first_name = isset($name_parts[0]) ? $name_parts[0] : '';
        $last_name = isset($name_parts[1]) ? $name_parts[1] : '';

        // Create a minimal account array with the username parts
        $account = [
            'first_name' => $first_name,
            'last_name' => $last_name
        ];
    }

    // Define all possible replacements
    $replacements = [
        '{user_name}' => $username,
        '{first_name}' => isset($account['first_name']) ? $account['first_name'] : '',
        '{last_name}' => isset($account['last_name']) ? $account['last_name'] : '',
        '{email}' => isset($account['email']) ? $account['email'] : '',
        '{phone}' => isset($account['phone']) ? $account['phone'] : '',
        '{company}' => isset($account['company_details']) ? $account['company_details'] : '',
        '{date}' => date('Y-m-d'),
        '{registration_date}' => isset($account['creation_time']) ? date('Y-m-d', strtotime($account['creation_time'])) : '',
    ];

    // Add membership-related variables if available
    if ($account && isset($account['user_id'])) {
        $membership = membership_get_active();
        if ($membership) {
            $replacements['{membership_name}'] = isset($membership['name']) ? $membership['name'] : '';
            $replacements['{quota}'] = isset($membership['quota']) ? $membership['quota'] : '';
            $replacements['{quota_limit}'] = isset($membership['quota']) ? $membership['quota'] : '';
            $replacements['{quota_used}'] = isset($membership['count']) ? $membership['count'] : '';
            $replacements['{expiration_date}'] = isset($membership['expiration']) ? $membership['expiration'] : '';
            $replacements['{credits}'] = isset($membership['credits']) ? $membership['credits'] : '';
        }
    }

    // First, normalize all line breaks to \n
    $message = str_replace(["\r\n", "\r"], "\n", $message);

    // Replace variables with their values - use a direct approach to ensure all replacements are made
    foreach ($replacements as $key => $value) {
        // Make sure the value is a string
        if (!is_string($value)) {
            $value = (string)$value;
        }
        $message = str_replace($key, $value, $message);
    }

    // Debug the replacements
    if (defined('MC_CLOUD_DEBUG') && MC_CLOUD_DEBUG) {
        mc_cloud_debug('cloud_merge_field_username replacements', [
            'replacements' => $replacements,
            'result_sample' => substr($message, 0, 100) . (strlen($message) > 100 ? '...' : '')
        ]);
    }

    // Convert line breaks to <br> tags for HTML emails
    // This ensures proper display in email clients
    $message = nl2br($message);

    return $message;
}

function cloud_email_limit() {
    $account_id = get_active_account_id(false); // Get non-escaped ID

    if ($account_id) {
        $today = gmdate('Y-m-d'); // Use UTC date for consistency
        $limit = 300; // Define daily limit

        // Get current usage data
        $usage_data = db_get('SELECT value FROM users_data WHERE user_id = ' . $account_id . ' AND slug = "email_limit"');
        $usage_value = $usage_data ? $usage_data['value'] : $today . '|0'; // Default to today|0 if no record

        // Parse date and count
        $parts = explode('|', $usage_value);
        $usage_date = isset($parts[0]) ? $parts[0] : '0000-00-00';
        $usage_count = isset($parts[1]) ? intval($parts[1]) : 0;

        // Reset count if date is not today
        if ($usage_date != $today) {
            $usage_count = 0;
        }

        // Check if limit exceeded
        if ($usage_count >= $limit) { // Use >= for safety
             mc_cloud_debug('cloud_email_limit: Daily email limit exceeded for user ' . $account_id);
             // Return MCError object as in the new version
            return new MCError('email-usage-quota-exceeded', 'cloud_email_limit', 'Daily email quota exceeded (' . $limit . '). Set up SMTP in Settings > Miscellaneous > SMTP for unlimited emails.');
        }

        // Increment count and update database
        $new_count = $usage_count + 1;
        $new_value = $today . '|' . $new_count;

        // Use INSERT ... ON DUPLICATE KEY UPDATE for atomic operation
        $query = 'INSERT INTO users_data (user_id, slug, value) VALUES (' . $account_id . ', "email_limit", "' . db_escape($new_value) . '") ON DUPLICATE KEY UPDATE value = "' . db_escape($new_value) . '"';
        $result = db_query($query);

        if ($result !== true) {
             mc_cloud_debug('cloud_email_limit: Failed to update email limit count for user ' . $account_id, ['db_error' => $result]);
             // Should we return an error here? Or allow email sending anyway?
             // Let's allow sending but log the error. The limit check already passed.
        }

        return true; // Indicate email sending is allowed (limit not hit)
    }

    // Return false or error if user ID not found?
     mc_cloud_debug('cloud_email_limit: Could not get active account ID.');
    return false; // Cannot check limit if no user ID
}
/*
 * -----------------------------------------------------------
 * SUPPORT BOARD FUNCTIONS
 * -----------------------------------------------------------
 *
 */

function cloud_css_js() {
    $cloud_settings = super_get_settings();
    if (!$cloud_settings) {
        return;
    }
    $css = '';
    $color = mc_isset($cloud_settings, 'color');
    if ($color) {
        $color = $cloud_settings['color'];
        $css .= '.mc-btn, a.mc-btn,div ul.mc-menu li:hover, .mc-select ul li:hover,.mc-timetable .mc-custom-select span:hover,.daterangepicker .ranges li.active,.daterangepicker td.active, .daterangepicker td.active:hover { background-color:' . $color . '; }';
        $css .= '.mc-input>input:focus, .mc-input>input.mc-focus, .mc-input>select:focus, .mc-input>select.mc-focus, .mc-input>textarea:focus, .mc-input>textarea.mc-focus,.mc-menu-wide ul li.mc-active, .mc-tab>.mc-nav>ul li.mc-active,.mc-btn-icon:hover,.mc-table input[type="checkbox"]:checked, .mc-table input[type="checkbox"]:hover,.plans-box>div:hover h3, .plans-box>div:hover h4, .plans-box>div:hover p, .plans-box>div.mc-active h3, .plans-box>div.mc-active h4, .plans-box>div.mc-active p { border-color:' . $color . '}';
        $css .= '.mc-admin-box .mc-bottom div+.mc-btn-login-box, .mc-admin-box .mc-bottom div+.btn-registration-box, .mc-admin-box .btn-cancel-reset-password,.disclaimer a,.mc-admin>.mc-header>.mc-admin-nav>div>a:hover, .mc-admin>.mc-header>.mc-admin-nav>div>a.mc-active,.mc-admin>.mc-header>.mc-admin-nav-right .mc-account .mc-menu li:hover, .mc-admin>.mc-header>.mc-admin-nav-right .mc-account .mc-menu li.mc-active:hover,.mc-select p:hover,div ul.mc-menu li.mc-active:not(:hover), .mc-select ul li.mc-active:not(:hover),.mc-search-btn>i:hover,.mc-search-btn.mc-active i,.mc-menu-wide ul li.mc-active, .mc-menu-wide ul li:hover, .mc-tab>.mc-nav>ul li.mc-active, .mc-tab>.mc-nav>ul li:hover,.mc-btn-icon:hover,.mc-loading:not(.mc-btn):before,.mc-setting input[type="checkbox"]:checked:before, .mc-setting input[type="checkbox"]:checked:before,.mc-language-switcher>i:hover,.mc-languages-box .mc-main>div:hover,.mc-setting.mc-type-upload-image .image:hover:before, .mc-setting [data-type="upload-image"] .image:hover:before, .mc-setting.mc-type-upload-image .image:hover:before, .mc-setting [data-type="upload-image"] .image:hover:before,.mc-dialog-box .mc-title, .plans-box>div:hover h3, .plans-box>div:hover h4, .plans-box>div:hover p, .plans-box>div.mc-active h3, .plans-box>div.mc-active h4, .plans-box>div.mc-active p { color:' . $color . '}';
        $css .= '.mc-search-btn>input:focus,.mc-search-btn>input, .mc-input-image .image:hover,.mc-setting input:focus, .mc-setting select:focus, .mc-setting textarea:focus, .mc-setting input:focus, .mc-setting select:focus, .mc-setting textarea:focus,.mc-timetable>div>div>div:hover,.plans-box>div:hover, .plans-box>div.mc-active,.mc-setting.mc-type-upload-image .image:hover { box-shadow: 0 0 5px rgb(0, 0, 0, 0.2); border-color:' . $color . '; } ';
        $css .= '.mc-setting.mc-type-select-images .input>div:hover, .mc-setting.mc-type-select-images .input>div.mc-active:not(.mc-icon-close) { box-shadow: 0 0 5px rgb(0, 0, 0, 0.2); border-color:' . $color . '; color:' . $color . '; }';
        $css .= '.mc-area-settings .mc-tab .mc-btn:hover, .mc-btn-white:hover,.mc-input .mc-btn:hover { background-color:' . $color . '; border-color:' . $color . '; }';
        $css .= '.mc-btn-icon:hover,.mc-table tr:hover td,.mc-tab .mc-content textarea[readonly] { background-color: rgba(84, 84, 84, 0.05); }';
        $css .= '#chat-mc-icons .input>.mc-active:not(.mc-icon-close), #chat-mc-icons .input>div:not(.mc-icon-close):hover { background-color:' . $color . ' !important; }';
        $css .= '.mc-admin .mc-top-bar > div:first-child > ul::-webkit-scrollbar-thumb:hover,.mc-area-settings > .mc-tab > .mc-nav::-webkit-scrollbar-thumb:hover, .mc-area-reports > .mc-tab > .mc-nav::-webkit-scrollbar-thumb:hover, .mc-dialog-box pre::-webkit-scrollbar-thumb:hover, .mc-horizontal-scroll::-webkit-scrollbar-thumb:hover { background:' . $color . '; }';
    }
    $color = mc_isset($cloud_settings, 'color-2');
    if ($color) {
        $css .= '.mc-btn:hover, a.mc-btn:hover { background-color:' . $color . '}';
    }
    if ($css) {
        echo '<style>' . $css . '</style>' . PHP_EOL;
    }
    if ($cloud_settings['css']) {
        echo '<link rel="stylesheet" href="' . $cloud_settings['css'] . '" media="all" />';
    }
    if ($cloud_settings['js']) {
        echo '<script src="' . $cloud_settings['js'] . '"></script>';
    }
}

function cloud_js_admin() {
    $cloud_settings = super_get_settings();
    $membership = db_get('SELECT membership FROM users WHERE id = ' . get_active_account_id());
    echo '<script>var WEBSITE_URL = "' . mc_defined('WEBSITE_URL', '#') . '"; var MC_CLOUD_ACTIVE_APPS = ' . json_encode(mc_get_external_setting('active_apps', [])) . '; var MC_CLOUD_MEMBERSHIP = "' . $membership['membership'] . '"; var DISABLE_APPS = "' . mc_isset($cloud_settings, 'disable-apps') . '"; var MC_CLOUD_WHATSAPP = { app_id: "' . mc_defined('WHATSAPP_APP_ID') . '", configuration_id: "' . mc_defined('WHATSAPP_CONFIGURATION_ID') . '" }; var MC_CLOUD_MESSENGER = { app_id: "' . mc_defined('MESSENGER_APP_ID') . '", configuration_id: "' . mc_defined('MESSENGER_CONFIGURATION_ID') . '" }; var MC_AUTO_SYNC =  { "whatsapp-cloud": ' . (defined('WHATSAPP_APP_ID') ? 'true' : 'false') . ', "messenger": ' . (defined('MESSENGER_APP_ID') ? 'true' : 'false') . ', "open-ai": ' . (defined('OPEN_AI_KEY') ? 'true' : 'false') . ', google: ' . (defined('GOOGLE_CLIENT_ID') ? 'true' : 'false') . ' }</script>';
    echo '<script src="' . CLOUD_URL . '/account/js/admin' . (mc_is_debug() ? '' : '.min') . '.js?v=' . MC_VERSION . '"></script>';
}

function cloud_css_js_front() {
    $cloud_settings = super_get_settings();
    $css = mc_isset($cloud_settings, 'css-front');
    $js = mc_isset($cloud_settings, 'js-front');
    if ($css) {
        echo '<link rel="stylesheet" href="' . $css . '" media="all" />';
    }
    if ($js) {
        echo '<script src="' . $js . '"></script>';
    }
}

function cloud_increase_count() {
    global $CLOUD_CONNECTION;
    if (!isset($_POST['cloud'])) {
        mc_error('cloud-not-found', 'cloud_increase_count', 'Cloud data not found', true);
    }
    $now = date('m-y');
    db_query('UPDATE membership_counter SET count = count + 1 WHERE user_id = ' . get_active_account_id() . ' AND date = "' . $now . '" LIMIT 1');
    if ($CLOUD_CONNECTION->affected_rows == 0 || $CLOUD_CONNECTION->affected_rows == -1) {
        db_query('INSERT INTO membership_counter (user_id, count, date) VALUES (' . get_active_account_id() . ', 1, "' . $now . '")');
    }
}

function cloud_custom_code() {
    $code = super_get_setting('custom-code-admin');
    if ($code) {
        echo $code;
    }
}

function cloud_add_to_payment_history($cloud_user_id, $amount, $type, $id, $extra_1 = '', $extra_2 = '') {
    super_insert_user_data('(' . $cloud_user_id . ', "payment", "' . db_escape(json_encode([$amount, $type, $id, $extra_1, $extra_2, time()])) . '")');
    membership_delete_cache($cloud_user_id);
    return true;
}

function cloud_invoice($cloud_user_id, $amount, $type, $unix_time) {
    require_once MC_CLOUD_PATH . '/account/vendor/fpdf/fpdf.php';
    require_once MC_CLOUD_PATH . '/account/vendor/fpdf/autoload.php';
    require_once MC_CLOUD_PATH . '/account/vendor/fpdf/Fpdi.php';
    $account = account_get_user_details();
    if (!$account) {
        return false;
    }
    $invoice_number = 'inv-' . $cloud_user_id . '-' . $unix_time;
    $file_name = $invoice_number . '.pdf';
    $path = MC_CLOUD_PATH . '/script/uploads/invoices/';
    if (!file_exists($path)) {
        mkdir($path, 0777, true);
    }
    $pdf = new \setasign\Fpdi\Fpdi();
    $pdf->AddPage();
    $pdf->setSourceFile(MC_CLOUD_PATH . '/account/media/invoice.pdf');
    $tpl = $pdf->importPage(1);
    $pdf->useTemplate($tpl, 0, 0, null, null);
    $pdf->SetTextColor(90, 90, 90);

    $pdf->SetXY(20, 29);
    $pdf->SetFont('Arial', 'B', 20);
    $pdf->Cell(1000, 1, mc_('Tax Invoice'));

    $pdf->SetXY(100, 27);
    $pdf->SetFont('Arial', '', 13);
    $pdf->Multicell(500, 7, mc_('Invoice date: ') . date('d-m-Y', $unix_time) . PHP_EOL . mc_('Invoice number: ') . strtoupper($invoice_number));

    $pdf->SetXY(20, 60);
    $pdf->SetFont('Arial', 'B', 13);
    $pdf->Cell(50, 1, mc_('To'));
    $pdf->SetFont('Arial', '', 13);
    $pdf->SetXY(20, 70);
    $pdf->Multicell(168, 7, strip_tags(trim(iconv('UTF-8', 'ASCII//TRANSLIT', $account['first_name'] . ' ' . $account['last_name'] . PHP_EOL . $account['email'] . ($account['company_details'] ? PHP_EOL . str_replace(',', ',' . PHP_EOL, $account['company_details']) : '')))));

    $pdf->SetXY(130, 60);
    $pdf->SetFont('Arial', 'B', 13);
    $pdf->Cell(168, 1, mc_('Supplier'));
    $pdf->SetFont('Arial', '', 13);
    $pdf->SetXY(130, 70);
    $pdf->Multicell(168, 7, strip_tags(trim(iconv('UTF-8', 'ASCII//TRANSLIT', mc_isset(super_get_settings(), 'text_invoice')))));

    $pdf->SetXY(20, 150);
    $pdf->SetFont('Arial', 'B', 13);
    $pdf->Cell(168, 1, mc_('Purchase details'));
    $pdf->SetFont('Arial', '', 13);
    $pdf->SetXY(20, 160);
    $pdf->Cell(168, 1, $type);

    $pdf->SetXY(20, 180);
    $pdf->SetFont('Arial', 'B', 13);
    $pdf->Cell(168, 1, mc_('Amount'));
    $pdf->SetFont('Arial', '', 13);
    $pdf->SetXY(20, 190);
    $pdf->Cell(168, 1, strtoupper(membership_currency()) . ' ' . $amount);

    $pdf->Output($path . $file_name, 'F');
    return $file_name;
}

function mc_cloud_merge_settings($settings) {
    if (file_exists(MC_CLOUD_PATH . '/account/settings.json')) {
        $settings_cloud = json_decode(file_get_contents(MC_CLOUD_PATH . '/account/settings.json'), true);
        foreach ($settings_cloud as $key => $value) {
            if (isset($settings[$key])) {
                $settings[$key] = array_merge($settings[$key], $settings_cloud[$key]);
            }
        }
    }
    return $settings;
}

function mc_cloud_account_menu($tag = 'li') {
    $code = '';
    $switch_account = mc_get_setting('cloud-switch');
    $account = account();
    if (mc_isset($account, 'owner')) {
        $code = '<' . $tag . ' data-value="account">' . mc_('Account') . '</' . $tag . '>';
    }
    if (defined('MC_CLOUD_DOCS')) {
        $code .= ($tag == 'a' ? '' : '<li data-value="help">') . '<a href="' . MC_CLOUD_DOCS . '" target="_blank">' . mc_('Help') . '</a>' . ($tag == 'a' ? '' : '</li>');
    }
    if ($switch_account) {
        $count = count($switch_account);
        if ($count) {
            $code .= ($tag == 'a' ? '<a data-value="switch">' . mc_('Switch accounts') . '</a>' : '<li data-value="switch"><span>' . mc_('Switch accounts') . '</span>') . '<div class="mc-scroll-area">';
            for ($i = 0; $i < $count; $i++) {
                if ($switch_account[$i]['cloud-switch-email'] != $account['email']) {
                    $code .= '<a href="' . CLOUD_URL . '/account?login&login_email=' . $switch_account[$i]['cloud-switch-email'] . '&login_password=' . urlencode($switch_account[$i]['cloud-switch-password']) . '">' . $switch_account[$i]['cloud-switch-name'] . '</a>';
                }
            }
            $code .= '</div>' . ($tag == 'a' ? '' : '</li>');
        }
    }
    return $code;
}

function mc_cloud_save_settings($settings, $cloud_user_id = false) {
    if (!$cloud_user_id) {
        $cloud_user_id = get_active_account_id();
    }
    $customer_settings = json_decode(super_get_setting('customer-settings'), true);
    if (!$customer_settings) {
        $customer_settings = ['piping' => [], 'training' => []];
    }
    foreach ($customer_settings as $key => $value) {
        $customer_settings[$key] = array_filter($value, function ($element) use ($cloud_user_id) {
            return $element !== $cloud_user_id;
        });
    }
    if ($settings) {
        foreach ($customer_settings as $key => $value) {
            if (!empty($key == 'piping' ? mc_email_piping_is_active() : $settings['open-ai'][0]['open-ai-training-cron-job'][0])) {
                array_push($customer_settings[$key], $cloud_user_id);
            }
        }
    }
    super_save_setting('customer-settings', $customer_settings);
}
/*
 * -----------------------------------------------------------
 * Define missing constants to resolve errors
 * -----------------------------------------------------------
 */

 if (!defined('STRIPE_PRODUCT_ID_WHITE_LABEL')) {
    define('STRIPE_PRODUCT_ID_WHITE_LABEL', 'your_stripe_product_id');
}
if (!defined('YOOMONEY_CURRENCY')) {
    define('YOOMONEY_CURRENCY', 'USD');
}
if (!defined('PAYMENT_PROVIDER_MANUAL_LINK')) {
    define('PAYMENT_PROVIDER_MANUAL_LINK', 'https://example.com/manual-payment');
}
if (!defined('RAZORPAY_CURRENCY')) {
    define('RAZORPAY_CURRENCY', 'INR');
}
if (!defined('RAPYD_CURRENCY')) {
    define('RAPYD_CURRENCY', 'USD');
}
if (!defined('RAPYD_ACCESS_KEY')) {
    define('RAPYD_ACCESS_KEY', 'your_rapyd_access_key');
}
if (!defined('RAPYD_SECRET_KEY')) {
    define('RAPYD_SECRET_KEY', 'your_rapyd_secret_key');
}
if (!defined('RAPYD_TEST_MODE')) {
    define('RAPYD_TEST_MODE', true);
}
if (!defined('CLOUD_ADDONS')) {
    define('CLOUD_ADDONS', []);
}
if (!defined('PAYMENT_MANUAL_CURRENCY')) {
    define('PAYMENT_MANUAL_CURRENCY', 'USD');
}
// Verifone Constants
if (!defined('VERIFONE_MERCHANT_ID')) define('VERIFONE_MERCHANT_ID', 'default_merchant_id');
if (!defined('VERIFONE_CURRENCY')) define('VERIFONE_CURRENCY', 'USD');
if (!defined('VERIFONE_SECRET_WORD')) define('VERIFONE_SECRET_WORD', 'default_secret_word');
if (!defined('VERIFONE_SECRET_KEY')) define('VERIFONE_SECRET_KEY', 'default_secret_key');

// Razorpay Constants
if (!defined('RAZORPAY_KEY_ID')) define('RAZORPAY_KEY_ID', 'default_razorpay_key_id');
if (!defined('RAZORPAY_KEY_SECRET')) define('RAZORPAY_KEY_SECRET', 'default_razorpay_key_secret');

// Rapyd Constants
if (!defined('RAPYD_COUNTRY')) define('RAPYD_COUNTRY', 'US');

// YooMoney Constants
if (!defined('YOOMONEY_SHOP_ID')) define('YOOMONEY_SHOP_ID', 'default_shop_id');
if (!defined('YOOMONEY_KEY_SECRET')) define('YOOMONEY_KEY_SECRET', 'default_key_secret');

// Miscellaneous
if (!defined('i')) define('i', 0);
if (!defined('SHOPIFY_APP_ID')) {
    define('SHOPIFY_APP_ID', 'your_shopify_app_id_here'); // Replace with actual value
}

// Function to get membership details for AJAX updates
function account_membership_details() {
    $account = account();
    if (!$account) {
        return ['status' => 'error', 'message' => 'User not logged in'];
    }

    // Get current membership details
    $membership = membership_get_active(false);

    // Return membership details
    return [
        'membership' => $membership,
        'status' => 'success'
    ];
}

?>