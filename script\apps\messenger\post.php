<?php

/*
 * ==========================================================
 * POST.PHP
 * ==========================================================
 *
 * Messenger response listener. This file receive the Facebook Messenger messages of the agents forwarded by app.masichat.com. This file requires the Messenger App.
 * © 2017-2025 app.masichat.com. All rights reserved.
 *
 */

if (isset($_GET['hub_mode']) && $_GET['hub_mode'] == 'subscribe') {
    require('../../include/functions.php');
    mc_cloud_load_by_url();
    if ($_GET['hub_verify_token'] == mc_get_multi_setting('messenger', 'messenger-key')) {
        echo $_GET['hub_challenge'];
    }
    die();
}
$raw = file_get_contents('php://input');
flush();
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
}
if ($raw) {
    require('../../include/functions.php');
    $response = json_decode($raw, true);
    $message = false;
    $sender_id = false;
    $page_id = false;
    $attachments = [];
    $response_messaging = false;
    if (isset($response['messaging'])) {
        $response_messaging = $response['messaging'];
    } else if (isset($response['object'])) {
        if (isset($response['entry'][0]['messaging'])) {
            $response_messaging = $response['entry'][0]['messaging'];
        } else if (isset($response['entry'][0]['standby'])) {
            $response_messaging = $response['entry'][0]['standby'];
        }
    }
    $response_message = [];
    if ($response_messaging) {
        $response_message = isset($response_messaging[0]['message']) ? $response_messaging[0]['message'] : (isset($response_messaging[0]['postback']) ? ['text' => mc_isset($response_messaging[0]['postback'], 'title', [])] : '');
    }
    $is_echo = isset($response_message['is_echo']);
    $postback = mc_isset($response_messaging, 'postback');
    $instagram = mc_isset($response, 'object') == 'instagram';
    $platform_code = $instagram ? 'ig' : 'fb';
    $user = false;
    $is_deleted = $response_message && !empty($response_message['is_deleted']);
    if ($response_message) {
        $sender_id = $response_messaging[0]['sender']['id'];
        $message = mc_isset($response_message, 'text');
        $attachments = mc_isset($response_message, 'attachments', []);
    } else if (isset($response['sender'])) {
        $sender_id = $response['sender']['id'];
        $message = mc_isset($response['message'], 'text');
        $attachments = mc_isset($response['attachments'], 'attachments', []);
    } else if ($postback) {
        $sender_id = $response_messaging[0]['sender']['id'];
        $message = mc_isset($postback, 'title', '');
    }
    if ($sender_id && ($message || $attachments || $is_deleted)) {
        $GLOBALS['MC_FORCE_ADMIN'] = true;
        mc_cloud_load_by_url();

        // Page ID
        $page_sender = false;
        if (isset($response['object']) && isset($response['entry'])) {
            $page_id = $response['entry'][0]['id'];
        } else if (isset($response['recipient'])) {
            $page_id = $response['recipient']['id'];
        } else if ($response_messaging) {
            $page_id = $response_messaging[0]['recipient']['id'];
        }
        if ($page_id == $sender_id) {
            $page_id = $sender_id;
            $sender_id = $response_messaging[0]['recipient']['id'];
            $page_sender = mc_db_get('SELECT id FROM mc_users WHERE user_type = "agent" OR user_type = "admin" ORDER BY user_type, creation_time LIMIT 1')['id'];
        }
        $sender_id = mc_db_escape($sender_id);
        $page_id = mc_db_escape($page_id);
        $page_settings = mc_messenger_get_page($page_id);

        // User
        $user = mc_db_get('SELECT A.id, A.first_name, A.last_name, A.profile_image, A.email, A.user_type FROM mc_users A, mc_users_data B WHERE A.user_type <> "agent" AND A.user_type <> "admin" AND A.id = B.user_id AND B.slug = "facebook-id" AND B.value = "' . mc_db_escape($sender_id) . '" LIMIT 1');
        if (!$user) {
            $user_id = mc_messenger_add_user($sender_id, $page_settings['messenger-page-token'], 'lead', $instagram, $message);
            $user = mc_get_user($user_id);
        } else {
            $user_id = $user['id'];
        }
        if ($user_id) {

            // Get user and conversation information
            $GLOBALS['MC_LOGIN'] = $user;
            $conversation = mc_db_get('SELECT id, status_code FROM mc_conversations WHERE source = "' . $platform_code . '" AND user_id = ' . $user_id . ' LIMIT 1');
            $conversation_id = mc_isset($conversation, 'id');
            $department = mc_isset($page_settings, 'messenger-page-department', -1);
            $count_attachments = count($attachments);

            // Message deleted
            if ($conversation_id && $is_deleted) {
                $message_id = mc_db_get('SELECT id FROM mc_messages WHERE conversation_id = ' . $conversation_id . ' AND payload LIKE "%' . mc_db_escape($response_message['mid']) . '%"');
                return $message_id ? mc_delete_message($message_id['id']) : false;
            }

            if (!$conversation_id) {
                $conversation_id = mc_isset(mc_new_conversation($user_id, 2, '', $department, -1, $platform_code, $page_id, false, false, mc_isset($page_settings, 'messenger-page-tags')), 'details', [])['id'];
            } else if ($is_echo && $page_sender && $response_message) {
                if (empty($message) || (isset($response_message['metadata']) && !empty(mc_db_get('SELECT id FROM mc_messages WHERE id = ' . explode('|', $response_message['metadata'])[0])))) {
                    $GLOBALS['MC_FORCE_ADMIN'] = false;
                    return false;
                }
                $message_previous = mc_db_get('SELECT message, attachments, creation_time FROM mc_messages WHERE conversation_id = ' . $conversation_id . ' ORDER BY id DESC LIMIT 1');
                $message_previous_text = trim(mc_isset($message_previous, 'message'));
                $message_previous_count = count(json_decode(mc_isset($previous_message, 'attachments', '[]'), true));
                $rich_message = trim(mc_isset(mc_messenger_rich_messages($message), 0));
                $message_previous_rich_message = trim(mc_isset(mc_messenger_rich_messages($message_previous_text), 0));
                if ($message_previous && ($message_previous_text == $message || $rich_message == $message_previous_text || $message_previous_rich_message == $rich_message || ($count_attachments && ($count_attachments == $message_previous_count || $message_previous_count > 1) && strtotime($previous_message['creation_time']) > mc_gmt_now(60, true)))) {
                    $GLOBALS['MC_FORCE_ADMIN'] = false;
                    return false;
                }
            }

            // Attachments
            $attachments_2 = [];
            for ($i = 0; $i < $count_attachments; $i++) {
                $type = $attachments[$i]['type'];
                if ($type == 'image' && mc_isset($attachments[$i]['payload'], 'sticker_id') == '369239263222822' && !$message) {
                    $message = "👍";
                } else {
                    $url = mc_isset($attachments[$i]['payload'], 'url');
                    if ($url) {
                        $file_name = urldecode(basename(strpos($url, '?') ? substr($url, 0, strpos($url, '?')) : $url));
                        $mime = !strpos($file_name, '.');
                        if ($mime && $type == 'audio') {
                            $file_name .= '.mp3';
                            $mime = false;
                        }
                        $file_name = rand(99999, 999999999) . '_' . (strpos($url, 'audio_clip') || strpos($url, 'audioclip') ? 'voice_message_' : '') . strtolower($file_name);
                        $url = mc_download_file($url, $file_name, $mime, [], 0);
                        array_push($attachments_2, [basename($url), $url]);
                    } else if ($type == 'fallback') {
                        $message_id = mc_isset($response, 'id', $response['entry'][0]['id']);
                        $message .= mc_('Attachment unavailable.') . ($message_id ? ' ' . mc_('View it on Messenger.') . PHP_EOL . 'https://www.facebook.com/messages/t/' . $message_id : '');
                    }
                }
            }

            // Send message
            $response = mc_send_message($page_sender ? $page_sender : $user_id, $conversation_id, $message, $attachments_2, false, ['mid' => mc_db_escape(mc_isset($response_message, 'mid'))]);

            // Dialogflow and bot messages
            if (!$is_echo) {
                mc_messaging_platforms_functions($conversation_id, $message, $attachments_2, $user, ['source' => $platform_code, 'platform_value' => $sender_id, 'page_id' => $page_id, 'conversation_id' => $conversation_id]);
            }

            // Queue
            if (mc_get_multi_setting('queue', 'queue-active')) {
                mc_queue($conversation_id, $department);
            }

            // Online status
            if (!$page_sender) {
                mc_update_users_last_activity($user_id);
            }
        }
    }
    $GLOBALS['MC_FORCE_ADMIN'] = false;
    return $response;
}
die();

?>