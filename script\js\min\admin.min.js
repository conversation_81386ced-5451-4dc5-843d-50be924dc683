"use strict";!function(e){var t,i,s,a,n,o,r,l,c,d,u,p,h,f,g,b,v,m,S,_,y,w,k,x,A,B,T,I,C,$,N,F,E,L,D,M,R,G,U,P,j,q,O,H,V,W,z,J,X,Y,Q,Z,K,ee,te,ie,se,ae,ne,oe,re,le=[],ce=!1,de=!1,ue=!1,pe=!1,he=1,fe=1,ge={},be=1,ve=1,me=!0,Se=!1,_e=!0,ye=!1,we=[],ke=[],xe=e(window).width()<465,Ae={last:0,header:!0,always_hidden:!1},Be="localhost"===location.hostname||"127.0.0.1"===location.hostname,Te=new Date,Ie=!1,Ce=!0,$e=!1,Ne="undefined",Fe=!1,Ee=!1,Le=!1;function De(e,t=!1){return ct.infoBottom(e,t)}function Me(e,t="info",i=!1,s="",a="",n=!1,o=!1,r=!1){return ct.infoPanel(e,t,i,s,a,n,o,r)}function Re(e){return ct.activeUser(e)}function Ge(e){return ct.loading(e)}function Ue(e=!0,t=!0){return ct.loadingGlobal(e,t)}function Pe(e){return MC_TRANSLATIONS&&e in MC_TRANSLATIONS?MC_TRANSLATIONS[e]:e}function je(){typeof caches!==Ne&&caches.delete("mc-pwa-cache")}function qe(e,t){ct.collapse(e,t)}function Oe(t,i){let s=e(t).parent().find("i"),a=e(t).val();MCF.search(a,(()=>{s.mcLoading(!0),i(a,s)}))}function He(t,i=!1,s=0){if(i)return e(t).scrollTop()+e(t).innerHeight()>=e(t)[0].scrollHeight-1;e(t).scrollTop(e(t)[0].scrollHeight-s)}function Ve(i=!1){if(!Le){if(!1===Ee)return Ee=!0,void e.getScript(MC_URL+"/vendor/editorjs.js",(()=>{Ve(i)}));We(),Le=!0,Ee=new EditorJS({data:ze(i)?{time:Date.now(),blocks:[i?{id:"mc",type:"raw",data:{html:i}}:{id:"mc",type:"paragraph",data:{text:""}}]}:i,i18n:{messages:{ui:{blockTunes:{toggler:{"Click to tune":Pe("Click to tune")}},inlineToolbar:{converter:{"Convert to":Pe("Convert to")}},toolbar:{toolbox:{Add:Pe("Add")}}},toolNames:{Text:Pe("Text"),Heading:Pe("Heading"),List:Pe("List"),Image:Pe("Image"),Code:Pe("Code"),"Raw HTML":Pe("Raw HTML"),Bold:Pe("Bold"),Italic:Pe("Italic"),Link:Pe("Link")},tools:{list:{Ordered:Pe("Ordered"),Unordered:Pe("Unordered")}},blockTunes:{delete:{Delete:Pe("Delete")},moveUp:{"Move up":Pe("Move up")},moveDown:{"Move down":Pe("Move down")}}},direction:t.hasClass("mc-rtl")?"rtl":"ltr"},tools:{list:{class:List,inlineToolbar:!0},image:{class:ImageTool,config:{uploader:{uploadByFile(t){let i=new FormData;return i.append("file",t),new Promise((t=>{e.ajax({url:MC_URL+"/include/upload.php",cache:!1,contentType:!1,processData:!1,data:i,type:"POST",success:function(e){"success"==(e=JSON.parse(e))[0]?t({success:1,file:{url:e[1]}}):console.log(e)}})}))}}}},header:Header,code:CodeTool,raw:RawTool},onReady:()=>{Le=!1,_e=!0,Se=!1},onChange:()=>{_e?_e=!1:Se=!0},minHeight:50})}}function We(){typeof Ee.destroy!==Ne&&(Ee.destroy(),Ee=!1)}function ze(e){return"string"==typeof e}function Je(e){Fe||window.history.pushState("","",e)}function Xe(){return MC_ADMIN_SETTINGS.cloud?"&cloud="+MC_ADMIN_SETTINGS.cloud.token:""}function Ye(e){return e.replace("https://","").replace("http://","").replace("www.","").replace(/\/$/,"")}function Qe(e){MCF.search(e,(()=>{let t="",i=!(e.length>1),s=ce.find(".mc-replies-list > ul"),a=`<li class="mc-no-results">${Pe("No results found.")}</li>`;for(var n=0;n<de.length;n++){let s=de[n]["reply-name"];(i||s.toLowerCase().includes(e)||s.replaceAll("-"," ").toLowerCase().includes(e)||de[n]["reply-text"].toLowerCase().includes(e))&&(t+=`<li><div>${s}</div><div>${de[n]["reply-text"]}</div></li>`)}if(s.html(t),i||!MC_ADMIN_SETTINGS.dialogflow&&!MC_ADMIN_SETTINGS.chatbot_features)t||s.html(a);else{let i=s.closest(".mc-popup").find(".mc-icon-search");i.mcLoading(!0),MC_ADMIN_SETTINGS.dialogflow?it.dialogflow.getIntents((n=>{let o=it.dialogflow.searchIntents(e,!0),r="";for(var l=0;l<o.length;l++){let e=o[l].messages[0].text;e&&e.text&&(r+=`<li><div>${o[l].displayName}</div><div>${o[l].messages[0].text.text[0]}</div></li>`)}s.html(r?t+r:t||a),i.mcLoading(!1)})):MCF.ajax({function:"open-ai-message",model:MC_ADMIN_SETTINGS.open_ai_model,message:e},(e=>{s.html(!e[1]||e[5]&&e[5].unknow_answer?t||a:`${t}<li><div></div><div>${e[1]}</div></li>`),i.mcLoading(!1)}))}}))}function Ze(e){let i=t.find("#mc-whatsapp-send-template-box");Ue(),MCF.ajax({function:"whatsapp-get-templates"},(t=>{let s='<option value=""></option>',a=t[0],n="twilio"==a;if((t=t[1]).error)return De(t.error.message,"error");if(!Array.isArray(t))return De(t,"error");for(var o=0;o<t.length;o++)"official"!=a||"APPROVED"!=t[o].status||MC_ACTIVE_AGENT.department&&t[o].department&&MC_ACTIVE_AGENT.department!=t[o].department||(s+=`<option value="${t[o].name}" data-languages="${t[o].languages}" data-phone-id="${t[o].phone_number_id}">${t[o].name} (${t[o].label})</option>`),n&&(s+=`<option value="${t[o].sid}">${t[o].friendly_name}</option>`);i.attr("data-provider",a),i.find("#mc-whatsapp-send-template-list").html(s),MCForm.clear(i),i.find(".mc-direct-message-users").val(e.length?e.join(","):"all"),i.find(".mc-bottom > div").html(""),i.find(".mc-loading").mcLoading(!1),Ue(!1),i.mcShowLightbox()}))}function Ke(e,t,i){window.open(e),Me(`${Pe("For security reasons, delete the file after downloading it. Close this window to automatically delete it. File location:")}<pre>${e}</pre>`,"info",!1,t,i)}function et(){ie=!1,se=!1,setTimeout((()=>{te&&(te[1].css("transform",""),te[1].removeClass("mc-touchmove"))}),100)}function tt(e){return l.find(`[data-conversation-id="${e}"]`)}e.fn.miniTip=function(i){var s=e.extend({title:"",content:!1,delay:300,anchor:"n",event:"hover",fadeIn:200,fadeOut:200,aHide:!0,maxW:"250px",offset:5,stemOff:0,doHide:!1},i);t&&0==t.find("#miniTip").length&&t.append('<div id="miniTip" class="mc-tooltip"><div></div></div>');var a=t.find("#miniTip"),n=a.find("div");return s.doHide?(a.stop(!0,!0).fadeOut(s.fadeOut),!1):this.each((function(){var t=e(this),i=s.content?s.content:t.attr("title");if(""!=i&&void 0!==i){window.delay=!1;var o=!1,r=!0;s.content||t.removeAttr("title"),"hover"==s.event?(t.hover((function(){a.removeAttr("click"),r=!0,l.call(this)}),(function(){r=!1,c()})),s.aHide||a.hover((function(){o=!0}),(function(){o=!1,setTimeout((function(){!r&&!a.attr("click")&&c()}),20)}))):"click"==s.event&&(s.aHide=!0,t.click((function(){return a.attr("click","t"),a.data("last_target")!==t||"none"==a.css("display")?l.call(this):c(),a.data("last_target",t),e("html").unbind("click").click((function(t){"block"==a.css("display")&&!e(t.target).closest("#miniTip").length&&(e("html").unbind("click"),c())})),!1})));var l=function(){s.show&&s.show.call(this,s),s.content&&""!=s.content&&(i=s.content),n.html(i),s.render&&s.render(a),a.hide().width("").width(a.width()).css("max-width",s.maxW);var o=t.is("area");if(o){var r,l=[],c=[],d=t.attr("coords").split(",");function I(e,t){return e-t}for(r=0;r<d.length;r++)l.push(d[r++]),c.push(d[r]);var u=t.parent().attr("name"),p=e("img[usemap=\\#"+u+"]").offset(),h=parseInt(p.left,10)+parseInt((parseInt(l.sort(I)[0],10)+parseInt(l.sort(I)[l.length-1],10))/2,10),f=parseInt(p.top,10)+parseInt((parseInt(c.sort(I)[0],10)+parseInt(c.sort(I)[c.length-1],10))/2,10)}else f=parseInt(t.offset().top,10),h=parseInt(t.offset().left,10);var g=o?0:parseInt(t.outerWidth(),10),b=o?0:parseInt(t.outerHeight(),10),v=a.outerWidth(),m=a.outerHeight(),S=Math.round(h+Math.round((g-v)/2)),_=Math.round(f+b+s.offset+8),y=Math.round(v-16)/2-parseInt(a.css("borderLeftWidth"),10),w=0,k=h+g+v+s.offset+8>parseInt(e(window).width(),10),x=v+s.offset+8>h,A=m+s.offset+8>f-e(window).scrollTop(),B=f+b+m+s.offset+8>parseInt(e(window).height()+e(window).scrollTop(),10),T=s.anchor;x||"e"==s.anchor&&!k?"w"!=s.anchor&&"e"!=s.anchor||(T="e",w=Math.round(m/2-8-parseInt(a.css("borderRightWidth"),10)),y=-8-parseInt(a.css("borderRightWidth"),10),S=h+g+s.offset+8,_=Math.round(f+b/2-m/2)):(k||"w"==s.anchor&&!x)&&("w"!=s.anchor&&"e"!=s.anchor||(T="w",w=Math.round(m/2-8-parseInt(a.css("borderLeftWidth"),10)),y=v-parseInt(a.css("borderLeftWidth"),10),S=h-v-s.offset-8,_=Math.round(f+b/2-m/2))),B||"n"==s.anchor&&!A?"n"!=s.anchor&&"s"!=s.anchor||(T="n",w=m-parseInt(a.css("borderTopWidth"),10),_=f-(m+s.offset+8)):(A||"s"==s.anchor&&!B)&&("n"!=s.anchor&&"s"!=s.anchor||(T="s",w=-8-parseInt(a.css("borderBottomWidth"),10),_=f+b+s.offset+8)),"n"==s.anchor||"s"==s.anchor?v/2>h?(S=S<0?y+S:y,y=0):h+v/2>parseInt(e(window).width(),10)&&(S-=y,y*=2):A?(_+=w,w=0):B&&(_-=w,w*=2),delay&&clearTimeout(delay),delay=setTimeout((function(){a.css({"margin-left":S+"px","margin-top":_+"px"}).stop(!0,!0).fadeIn(s.fadeIn)}),s.delay),a.attr("class","mc-tooltip "+T)},c=function(){(!s.aHide&&!o||s.aHide)&&(delay&&clearTimeout(delay),delay=setTimeout((function(){d()}),s.delay))},d=function(){!s.aHide&&!o||s.aHide?(a.stop(!0,!0).fadeOut(s.fadeOut),s.hide&&s.hide.call(this)):setTimeout((function(){c()}),200)}}}))},e.fn.mcLanguageSwitcher=function(t=[],i="",s=!1){let a=`<div class="mc-language-switcher" data-source="${i}">`,n=[],o=e(this).hasClass("mc-language-switcher-cnt")?e(this):e(this).find(".mc-language-switcher-cnt");for(var r=0;r<t.length;r++){let e=ze(t[r])?t[r]:t[r][0],i=!(ze(t[r])||!t[r][1])&&t[r][1];n.includes(e)||(a+=`<span ${s==e?'class="mc-active" ':""}data-language="${e}"${i?' data-id="'+i+'"':""}><i class="mc-icon-close"></i><img src="${MC_URL}/media/flags/${e.toLowerCase()}.png" /></span>`,n.push(e))}return o.find(".mc-language-switcher").remove(),o.append(a+`<i data-mc-tooltip="${Pe("Add translation")}" class="mc-icon-plus"></i></div>`),o.mcInitTooltips(),this},e.fn.mcShowLightbox=function(i=!1,s=""){return t.find(".mc-lightbox").mcActive(!1),K.mcActive(!0),e(this).mcActive(!0),i?e(this).addClass("mc-popup-lightbox").attr("data-action",s):e(this).css({"margin-top":e(this).outerHeight()/-2+"px","margin-left":e(this).outerWidth()/-2+"px"}),e("body").addClass("mc-lightbox-active"),setTimeout((()=>{ct.open_popup=this}),500),this.preventDefault,this},e.fn.mcHideLightbox=function(){return e(this).find(".mc-lightbox,.mc-popup-lightbox").mcActive(!1).removeClass("mc-popup-lightbox").removeAttr("data-action"),K.mcActive(!1),e("body").removeClass("mc-lightbox-active"),ct.open_popup=!1,this},e.fn.mcInitTooltips=function(){return e(this).find("[data-mc-tooltip]").each((function(){e(this).miniTip({content:e(this).attr("data-mc-tooltip"),anchor:"s",delay:500})})),this};var it={dialogflow:{intents:!1,qea:[],token:MCF.storage("dialogflow-token"),dialogflow_languages:[],original_response:!1,smart_reply_busy:!1,smartReply:function(e=!1){let t=MCChat.conversation.id;this.smart_reply_busy!=t&&(this.smart_reply_busy=t,MCF.ajax({function:"dialogflow-smart-reply",message:e,token:this.token,conversation_id:t,dialogflow_languages:this.dialogflow_languages},(e=>{if(this.smart_reply_busy=!1,MCChat.conversation.id&&t===MCChat.conversation.id){let t=e.suggestions,s="",a=n[0].scrollTop===n[0].scrollHeight-n[0].offsetHeight,o=!(!MCChat.conversation||!MCChat.conversation.getLastMessage())&&MCChat.conversation.getLastMessage().message;e.token&&(this.token=e.token,MCF.storage("dialogflow-token",e.token));for(var i=0;i<t.length;i++)t[i]!=o&&(s+=`<span>${MCF.escape(t[i])}</span>`);g.html(s),a&&MCChat.scrollBottom()}e.dialogflow_languages&&(this.dialogflow_languages=e.dialogflow_languages)})))},showCreateIntentBox:function(e){let t="",i=MCChat.conversation.getMessage(e),s=i.message;if(MCF.isAgent(i.get("user_type")))t=MCChat.conversation.getLastUserMessage(i.get("index")),t=t&&t.payload("mc-human-takeover")?MCChat.conversation.getLastUserMessage(t.get("index")).message:t.message;else{t=s;let e=MCChat.conversation.getNextMessage(i.id,"agent");e&&(s=e.message)}f.hasClass("mc-dialogflow-disabled")?(MCF.ajax({function:"open-ai-get-qea-training"},(e=>{let t='<option value="">'+Pe("New Q&A")+"</option>";for(var i=0;i<e.length;i++)this.qea=e,t+=`<option value="${i}">${e[i][0][0]}</option>`;f.find("#mc-qea-select").html(t)})),it.openAI.generateQuestions(t)):this.getIntents((e=>{let i='<option value="">'+Pe("New Intent")+"</option>";for(var s=0;s<e.length;s++)i+=`<option value="${e[s].name}">${e[s].displayName}</option>`;f.find("#mc-intents-select").html(i),it.openAI.generateQuestions(t)})),f.attr("data-message-id",i.id),f.find(".mc-type-text:not(.mc-first)").remove(),f.find(".mc-type-text input").val(t),f.find("#mc-intents-select,#mc-qea-select").val(""),f.find(".mc-search-btn").mcActive(!1).find("input").val(""),this.searchIntents(""),this.original_response=s,f.find("textarea").val(s),f.mcShowLightbox()},submitIntent:function(i){if(Ge(i))return;let s=[],a=f.find("textarea").val(),n=f.find("#mc-intents-select,#mc-qea-select").val(),o=f.find("#mc-train-chatbots").val(),r="open-ai"==o||f.hasClass("mc-dialogflow-disabled");if(f.find(".mc-type-text input").each((function(){e(this).val()&&s.push(e(this).val())})),!a&&!n||0==s.length)MCForm.showErrorMessage(f,"Please insert the bot response and at least one user expression."),e(i).mcLoading(!1);else{let l;r&&(n?(this.qea[n][0]=this.qea[n][0].concat(s),l=this.qea[n]):l=[[s,a]]),MCF.ajax({function:r?"open-ai-qea-training":n?"dialogflow-update-intent":"dialogflow-create-intent",questions_answers:l,expressions:s,response:a,agent_language:f.find(".mc-dialogflow-languages select").val(),conversation_id:MCChat.conversation.id,intent_name:n,update_index:n,services:o,language:f.find(".mc-dialogflow-languages select").val()},(s=>{e(i).mcLoading(!1),!0===s?(t.mcHideLightbox(),De("Training completed")):MCForm.showErrorMessage(f,s.error&&s.error.message?s.error&&s.error.message:"Error")}))}},getIntents:function(e){!1===this.intents?MCF.ajax({function:"dialogflow-get-intents"},(t=>{this.intents=Array.isArray(t)?t:[],e(this.intents)})):e(this.intents)},searchIntents:function(e,t=!1){if(f.hasClass("mc-dialogflow-disabled")){let t=!(e.length>1),s=t?`<option value="">${Pe("New Q&A")}</option>`:"";e=e.toLowerCase();for(var i=0;i<this.qea.length;i++)(t||this.qea[i][0].join("").toLowerCase().includes(e)||this.qea[i][1].toLowerCase().includes(e))&&(s+=`<option value="${i}">${this.qea[i][0][0]}</option>`);f.find("#mc-qea-select").html(s).change(),t&&f.find("textarea").val(this.original_response)}else{let n=!(e.length>1),o=n?`<option value="">${Pe("New Intent")}</option>`:"",r=this.intents,l=[];e=e.toLowerCase();for(i=0;i<r.length;i++){let c=n||r[i].displayName.toLowerCase().includes(e);if(!c&&r[i].trainingPhrases){let t=r[i].trainingPhrases;for(var s=0;s<t.length;s++){for(var a=0;a<t[s].parts.length;a++)if(t[s].parts[a].text.toLowerCase().includes(e)){c=!0;break}if(c)break}}c&&(t?l.push(r[i]):o+=`<option value="${r[i].name}">${r[i].displayName}</option>`)}if(t)return l;f.find("#mc-intents-select").html(o).change(),e||f.find("textarea").val(this.original_response)}},previewIntentDialogflow:function(e){let t="",i=this.getIntent(e);if(i){let e=i.trainingPhrases?i.trainingPhrases:[],n=e.length;if(n>1){for(var s=0;s<n;s++)for(var a=0;a<e[s].parts.length&&(t+=`<span>${e[s].parts[a].text}</span>`,15!=a);a++);Me(t,"info",!1,"intent-preview-box","",n>10)}}},previewIntent:function(e){if(e){let i="",s=this.qea[e];if(s[0].length>1){let e=s[0].length>15?15:s[0].length;for(var t=0;t<e;t++)i+=`<span>${s[0][t]}</span>`;Me(i,"info",!1,"qea-preview-box","",e>10)}}},getIntent:function(e){for(var t=0;t<this.intents.length;t++)if(this.intents[t].name==e)return this.intents[t];return!1},translate:function(e,t,i,s,a){e.length&&MCF.ajax({function:"google-translate",strings:e,language_code:t,token:this.token,message_ids:s,conversation_id:a},(e=>{if(this.token=e[1],!Array.isArray(e[0]))return MCF.error(JSON.stringify(e[0]),"MCApps.dialogflow.translate"),!1;i(e[0])}))}},openAI:{urls_history:[],progress:1,rewriteButton:function(e){b.length&&b.mcActive(e.length>2&&e.indexOf(" "))},rewrite:function(e,t){MCF.ajax({function:"open-ai-message",model:MC_ADMIN_SETTINGS.open_ai_model,message:(MC_ADMIN_SETTINGS.open_ai_prompt_rewrite?MC_ADMIN_SETTINGS.open_ai_prompt_rewrite:"Make the following sentence more friendly and professional")+` and use ${MC_LANGUAGE_CODES[MC_ADMIN_SETTINGS.active_agent_language]} language: """${e.replace('"',"'")}"""`,extra:"rewrite"},(i=>{i[0]||console.log("OpenAI response issue detected"),rt.previous_editor_text=e,t(i)}))},troubleshoot:function(){let e=MC_ADMIN_SETTINGS.open_ai_chatbot_status;return!0!==e&&De("inactive"==e?"Enable the chatbot in Settings > Artificial Intelligence > OpenAI > Chatbot.":"key"==e?"Enter the OpenAI API key in Settings > Artificial Intelligence > OpenAI > API key.":"The training data is ignored. Change the chatbot mode in Settings > Artificial Intelligence > OpenAI > Chatbot mode.","error"),e},getCode:{set_data:function(e){let t="",i=this.select_user_details();e&&e.length||(e=[["",""]]);for(var s=0;s<e.length;s++)t+=`<div class="repeater-item"><div>${i.replace(`"${e[s][0]}"`,`"${e[s][0]}" selected`)}<div class="mc-setting"><input type="url" placeholder="${Pe("Enter the value")}" value="${e[s][1]}"></div></div><i class="mc-icon-close"></i></div>`;return this.repeater_("Data",t)},actions:function(e){let t=[["tags","Assign tags"],["department","Assign a department"],["agent","Assign an agent"],["redirect","Go to URL"],["open_article","Show an article"],["transcript","Download transcript"],["transcript_email","Email transcript"],["send_email","Send email to user"],["send_email_agents","Send email to agents"],["archive_conversation","Archive the conversation"],["human_takeover","Human takeover"]],i="",s="";e&&e.length||(e=[["tags",""]]);for(var a=0;a<t.length;a++)i+=`<option value="${t[a][0]}">${Pe(t[a][1])}</option>`;for(a=0;a<e.length;a++)s+=`<div class="repeater-item"><div><div class="mc-setting"><select>${i.replace(`"${e[a][0]}"`,`"${e[a][0]}" selected`)}</select></div>${this.action(e[a][0],e[a][1])}</div><i class="mc-icon-close"></i></div>`;return this.repeater_("Actions",s)},action:function(e,t){let i={department:"number",agent:"number",redirect:"url"};return["send_email_agents","send_email","open_article","redirect","agent","department","tags"].includes(e)?`<div class="mc-setting"><input type="${i[e]?i[e]:"text"}" value="${t}" placeholder="${Pe({tags:"Enter tag names, separated by commas",department:"Enter the department ID",agent:"Enter the agent ID",redirect:"Enter the URL",open_article:"Enter the article ID",send_email:"Enter a message",send_email_agents:"Enter a message"}[e])}" value="${t}"></div>`:""},select_user_details:function(){let e='<div class="mc-setting"><select>',t=[["full_name","Name"],["email","Email"],["password","Password"]].concat(ot.getExtraDetailsList());for(var i=0;i<t.length;i++)e+=`<option value="${t[i][0]}">${Pe(t[i][1])}</option>`;return e+"</select></div>"},repeater_:function(e,t){return`<div class="mc-title">${Pe(e)}</div><div data-type="repeater" class="mc-setting mc-type-repeater"><div class="input"><div class="mc-repeater mc-repeater-block-${MCF.stringToSlug(e)}">${t}</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${Pe("Add new item")}</div></div></div>`}},generateQuestions:function(e){if(MC_ADMIN_SETTINGS.open_ai_user_expressions&&e){let t=f.find('[data-value="add"]');t.mcLoading(!0),f.find(".mc-open-ai-intent").remove(),MCF.ajax({function:"open-ai-user-expressions",message:e},(e=>{let i="";for(var s=0;s<e.length;s++)e[s]&&(i+=`<div class="mc-setting mc-type-text mc-open-ai-intent"><input type="text" value="${e[s].replace(/"/g,"")}"></div>`);i&&f.find("> div > .mc-type-text").last().after(i),t.mcLoading(!1)}))}},flows:{flows:[],set:function(e){if("string"==typeof e)(e=e.trim().replaceAll('"',"").replaceAll("_","-"))&&(e={name:e,steps:[[[{type:"start",start:"message",message:"",conditions:[],disabled:!1}]],[[]]]});else for(var t=0;t<this.flows.length;t++)if(this.flows[t].name==e.name)return this.flows[t]=e,this.show(e.name),!0;this.flows.push(e),U.find(".mc-active").mcActive(!1),U.append(this.navCode(e.name,!0)),this.show(e.name)},get:function(e=!1){e||(e=this.getActiveName());for(var t=0;t<this.flows.length;t++)if(this.flows[t].name==e)return this.flows[t].steps||(this.flows[t].steps=[]),this.flows[t];return!1},show:function(i=!1){i||(i=this.getActiveName());let s,a=this.get(i),n="";if(a){let i="ABCDEFGHIJKLMNOPQRSTUVWXYZ",d=[],u=[];for(var o=0;o<a.steps.length;o++){let e=a.steps[o],t=0;d=u,u=[],n+="<div><div>";for(var r=0;r<e.length;r++){let a=e[r];n+='<div class="mc-flow-block-cnt">',d[r]&&(n+=`<div class="mc-flow-block-cnt-name">${d[r]}</div>`);for(var l=0;l<a.length;l++){"start"!=a[l].type||Array.isArray(a[l].message)||(a[l].message=[{message:a[l].message}]);let e=a[l].message?Array.isArray(a[l].message)?a[l].message.length?a[l].message[0].message:"":a[l].message:"";switch(e&&(e=`<div>${e.length>45?e.substring(0,45)+"...":e}</div>`),n+=`<div class="mc-flow-block" data-type="${a[l].type}"><div>${Pe(MCF.slugToString(a[l].type))}</div>${e}`,a[l].type){case"get_user_details":u.push(!1);break;case"condition":case"button_list":let e="condition"==a[l].type,o=e?[Pe("True"),Pe("False")]:a[l].options;if(e){n+="<div>",s=a[l].conditions;for(var c=0;c<s.length;c++)n+=`<div>${MCF.slugToString(s[c][0]+" "+s[c][1])}${s[c][2]?": "+s[c][2]:""}</div>`;n+="</div>"}n+='<div class="mc-flow-connectors">';for(c=0;c<o.length;c++)u.push(i[t]+(c+1)),n+=`<div>${o[c]}<span>${u[u.length-1]}</span></div>`;n+="</div>",t++;break;case"video":case"rest_api":n+=`<div>${a[l].url}</div>`;break;case"action":case"set_data":n+="<div>",s=a[l]["set_data"==a[l].type?"data":"actions"];for(c=0;c<s.length;c++)n+=`<div>${MCF.slugToString(s[c][0])}${s[c][1]?": "+s[c][1]:""}</div>`;n+="</div>"}n+="</div>"}a.length<4&&(n+='<div class="mc-flow-add-block mc-icon-plus"></div>'),n+="</div>"}n+='</div><div class="mc-flow-add-step mc-icon-plus"></div></div>'}P.html(n),e(t).find(".mc-flow-scroll").mcActive(250*a.steps.length>P.outerWidth())}},delete:function(e){for(var t=0;t<this.flows.length;t++)if(this.flows[t].name==e){let i=U.find(`[data-value="${e}"]`);return this.flows[t].steps.forEach((e=>{e.forEach((e=>{e.forEach((e=>{e.attachments&&e.attachments.forEach((e=>{MCF.ajax({function:"delete-file",path:e})}))}))}))})),this.flows.splice(t,1),i.mcActive()&&(i.prev().length?i.prev().click():i.next().length?i.next().click():P.html("")),i.remove(),!0}return!1},save:function(e=!1){MCF.ajax({function:"open-ai-flows-save",flows:JSON.stringify(this.flows)},(t=>{e(t)}))},navCode:function(e,t=!1){return`<li${t?' class="mc-active"':""} data-value="${e}">${e}<i class="mc-icon-delete"></i></li>`},getActive:function(){return U.find(".mc-active")},getActiveName:function(){return this.getActive().attr("data-value")},getActiveIndex:function(){return this.getActive().index()},steps:{get:function(e=!1,t=!1){return it.openAI.flows.get(e).steps[t||this.getActiveIndex()]},getActiveIndex:function(){return it.openAI.flows.blocks.getActive().parent().parent().parent().index()}},blocks:{set:function(e,t=!1,i=!1,s=!1,a=!1){let n=this.getIndexes(t,i,s,a),o=it.openAI.flows.get(t);if(o){let i=!1;if(o.steps.length>n.step){let t=o.steps[n.step];if(t.length>n.cnt){let s=t[n.cnt];s.length>n.block&&(s[n.block]=e,i=!0)}i||(o.steps[n.step][n.cnt].push(e),i=!0)}i||o.steps.push([[e]]);let s=o.steps[n.step],a=o.steps[it.openAI.flows.steps.getActiveIndex()+1],c=0;for(var r=0;r<s.length;r++)for(var l=0;l<s[r].length;l++)switch(s[r][l].type){case"get_user_details":c+=1;break;case"button_list":c+=s[r][l].options.length;break;case"condition":c+=2}if(a)if(a.length>c)a.splice(-1*(c-1),c);else{let e=a.length;for(;e<c;e++)a.splice(n.cnt,0,[])}else{a=[];for(r=0;r<c;r++)a.push([]);a.length&&o.steps.push(a)}return it.openAI.flows.show(t),!0}return!1},get:function(e=!1,t=!1,i=!1,s=!1){let a=it.openAI.flows.get(e);if(a){let n=this.getIndexes(e,t,i,s);return a.steps[n.step][n.cnt][n.block]}return!1},add:function(e,t=!1,i=!1,s=!1,a=!1){let n=this.getIndexes(t,i,s,a);this.set(Object.assign({button_list:{message:"",options:[]},message:{message:""},video:{message:"",url:""},get_user_details:{message:"",details:[]},set_data:{data:[]},action:{actions:[]},rest_api:{url:"",method:"",headers:[],body:"",save_response:[]},condition:{conditions:[]}}[e],{type:e}),n.name,n.step,n.cnt,n.block),it.openAI.flows.show(t),setTimeout((()=>{P.find("> div").eq(n.step).find(".mc-flow-block-cnt").eq(n.cnt).find(".mc-flow-block").eq(n.block).click()}),100)},delete:function(e=!1,t=!1,i=!1,s=!1){let a=this.getIndexes(e,t,i,s);for(var n=0;n<it.openAI.flows.flows.length;n++){let e=it.openAI.flows.flows[n];if(a.name==e.name){if(["get_user_details","button_list","condition"].includes(e.steps[a.step][a.cnt][a.block].type)){let t=this.delete_(n,a.step,a.cnt),i=Object.assign({},e);for(var o=0;o<t.length;o++)e.steps[t[o][0]][t[o][1]]=!1;e.steps[a.step][a.cnt].splice(a.block,1),i.steps=[];for(o=0;o<e.steps.length;o++){let t=[];for(var r=0;r<e.steps[o].length;r++)e.steps[o][r]&&t.push(e.steps[o][r]);t.length&&i.steps.push(t)}it.openAI.flows.flows[n]=i}else e.steps[a.step][a.cnt].splice(a.block,1);return it.openAI.flows.show(a.name),!0}}return!1},delete_:function(e,t,i,s=[]){let a=it.openAI.flows.blocks.getNextCntIndexes(e,t,i);for(var n=0;n<a.length;n++)s.push([t+1,a[n]]),this.delete_(e,t+1,a[n],s);return s},getActive:function(){return P.find(".mc-flow-add-block.mc-active, .mc-flow-block.mc-active")},getActiveIndex:function(){let e=this.getActiveCnt().find(".mc-flow-block"),t=e.index(this.getActive());return-1===t?e.length:t},getActiveCnt:function(){return this.getActive().parent()},getActiveCntIndex:function(){return this.getActive().parent().index()},getNextCnt:function(e,t,i,s=0){let a=this.getNextCntIndexes(e,t,i,s);return a.length>s&&it.openAI.flows.flows[e].steps[t+1][a[s]]},getNextCntIndexes:function(e,t,i){let s=it.openAI.flows.flows[e],a=[];if(s&&s.steps[t+1]){let e=0;for(var n=0;n<=i;n++){let l=s.steps[t][n];for(var o=0;o<l.length;o++)if("button_list"==l[o].type)for(var r=0;r<l[o].options.length;r++)n==i&&a.push(e),e++;else if("get_user_details"==l[o].type)n==i&&a.push(e),e++;else if("condition"==l[o].type)for(r=0;r<2;r++)n==i&&a.push(e),e++}}return a},getPreviousCntIndex:function(e,t,i){let s=it.openAI.flows.flows[e].steps[t-1];if(s){let e=0;for(var a=0;a<s.length;a++){let t=s[a];for(var n=0;n<t.length;n++)e+="button_list"==t[n].type?t[n].options.length:"get_user_details"==t[n].type?1:"condition"==t[n].type?2:0;if(e>i)return a}return e}},getIndexes:function(e,t,i,s){return{name:e||it.openAI.flows.getActiveName(),step:t||it.openAI.flows.steps.getActiveIndex(),cnt:i||this.getActiveCntIndex(),block:s||this.getActiveIndex()}},activateLinkedCnts:function(t){let i=e(t).parent(),s=it.openAI.flows.getActiveIndex(),a=i.parent().parent().index(),n=P.find("> div"),o=n.eq(a-1).find(".mc-flow-block-cnt").eq(it.openAI.flows.blocks.getPreviousCntIndex(s,a,i.index()));if(!ye){let e=n.eq(a+1).find(".mc-flow-block-cnt"),t=it.openAI.flows.blocks.getNextCntIndexes(s,a,i.index());for(var r=0;r<t.length;r++)e.eq(t[r]).mcActive(!0)}P.find(".mc-flow-connectors > div").mcActive(!1),o.mcActive(!0).find(".mc-flow-block-cnt-name").mcActive(!0),i.find(".mc-flow-block-cnt-name").length&&o.find(".mc-flow-connectors > div").mcActive(!1).eq(parseInt(i.find(".mc-flow-block-cnt-name").html().substring(1))-1).mcActive(!0)}}},train:{urls:[],errors:[],base_url:!1,start_urls:[],sitemap_processed_urls:[],active_source:!1,history:[],training_button:!1,extract_url:[],skip_files:[],files:function(e,i=0){let s=q.prop("files");if(i>=s.length)return e(!0);let a=s[i].name,n=s[i].size/1048576,o=MC_ADMIN_SETTINGS.max_file_size,r=a.split(".").pop().toLowerCase();if(n>o){let t=Pe("Maximum upload size is {R}MB. File size: {R2}MB.").replace("{R}",o).replace("{R2}",n.toFixed(2));return this.errors.push(t+" File: "+a),E.find("#mc-train-chatbot").mcLoading(!1),Me(t,"info"),this.training_aborted=!0,void e(!0)}if("pdf"===r&&n>10){let t=Pe("Warning: Large PDF files may cause timeouts during upload. Consider splitting the file into smaller parts or using text files instead. Please refresh the page.");return this.errors.push(t+" File: "+a),E.find("#mc-train-chatbot").mcLoading(!1),Me(t,"info"),this.training_aborted=!0,void e(!0)}this.isFile(a)&&!this.skip_files.includes(a)?(t.find("#mc-embeddings-box p").html(Pe("We are processing the source")+"<pre>"+a+"</pre><span>"+Pe("Only {R} sources left to complete.").replace("{R}",s.length-i)+"</span>"),q.mcUploadFiles((t=>{try{if("timeout"===t||"string"==typeof t&&t.includes("error")){let s="",n="";try{if("string"==typeof t&&t.includes("error")){let e=JSON.parse(t);if("error"===e.status)switch(e.message){case"file-too-large":s=Pe("The file is too large to upload. Maximum size allowed is {R}MB.").replace("{R}",MC_ADMIN_SETTINGS.max_file_size),n=e.details||"";break;case"file-too-large-pdf":s=Pe("The PDF file is too large and may cause timeouts. Please split it into smaller files."),n=e.details||"";break;case"upload-timeout":s=Pe("The upload timed out. The file may be too large or your connection too slow."),n=e.details||"";break;case"upload-error":s=Pe("An error occurred during file upload."),n=e.details||"";break;default:s=Pe("An error occurred during file upload: {R}").replace("{R}",e.message),n=e.details||""}}}catch(e){console.error("Error parsing error response:",e)}return s||(s=Pe("timeout"===t?"The upload timed out. The file may be too large or your connection too slow.":"An error occurred during file upload.")),this.errors.push(s+" File: "+a+(n?" ("+n+")":"")),E.find("#mc-train-chatbot").mcLoading(!1),Me(s,"info"),void this.files(e,i+1)}"success"==(t=JSON.parse(t))[0]?MCF.ajax({function:"open-ai-file-training",url:t[1]},(t=>{this.isError(t),this.files(e,i+1)})):(this.errors.push("Error processing file: "+a+" - "+(t[1]||"Unknown error")),E.find("#mc-train-chatbot").mcLoading(!1),Me("Error processing file: "+a,"info"),this.files(e,i+1))}catch(t){console.error("Error parsing response:",t),this.errors.push("Error parsing response for file: "+a),E.find("#mc-train-chatbot").mcLoading(!1),Me("Error processing file: "+a,"info"),this.files(e,i+1)}}),i)):this.files(e,i+1)},website:function(e,i=0){if(i>=this.urls.length)return e(!0);let s=this.urls[i];s&&s.includes("http")?(t.find("#mc-embeddings-box p").html(Pe("We are processing the source")+"<pre>"+s+"</pre><span>"+Pe("Only {R} sources left to complete.").replace("{R}",this.urls.length-i)+"</span>"),s.includes(".xml")?MCF.ajax({function:"get-sitemap-urls",sitemap_url:s},(t=>{if(Array.isArray(t))this.urls=this.urls.concat(t);else{let e="";switch(t){case"invalid-sitemap":e=Pe("The sitemap at {R} is invalid or inaccessible. Please check the URL and ensure it points to a valid XML sitemap.").replace("{R}",s);break;case"empty-sitemap":e=Pe("The sitemap at {R} does not contain any valid URLs. Please check the sitemap content.").replace("{R}",s);break;case"invalid-xml":e=Pe("The sitemap at {R} contains invalid XML. Please verify the sitemap format.").replace("{R}",s);break;default:e=t}console.log(`Sitemap error for URL ${s}: ${t}`),this.errors.push(e)}this.website(e,i+1)})):!this.sitemap_processed_urls.includes(s)&&this.extract_url[i]?(this.sitemap_processed_urls.push(s),this.sitemap(s,(t=>{this.urls=this.urls.concat(t),this.website(e,i+1)}))):MCF.ajax({function:"open-ai-url-training",url:s},(t=>{if(this.isError(t))this.website(e,i+1);else{if(!t[0])if(E.find("#mc-train-chatbot").mcLoading(!1),t[1].includes("http-error")){let e=t[1].replace("http-error-",""),i="";if("404"===e)i=Pe("The URL {R} could not be found. The page may have been moved or deleted.").replace("{R}",s),console.log(`HTTP 404 error for URL: ${s} - continuing with training`),this.errors.push(i);else if("403"===e)i=Pe("Access to the URL {R} is restricted. The website may require authentication.").replace("{R}",s),this.errors.push(i),console.log(`HTTP 403 error for URL: ${s}`);else{if("500"===e){i=Pe("The server at URL {R} encountered an internal error. Please try again later.").replace("{R}",s),this.errors.push(i),console.log(`HTTP 500 error for URL: ${s}`),this.training_aborted=!0;let e='<div class="mc-error-message">';return e+="<h3>"+Pe("Training Aborted: Server Error")+"</h3>",e+="<p>"+i+"</p>",e+="<p>"+Pe("The training process has been aborted due to a server error. Please try again later or check if the URL is accessible.")+"</p>",e+='<div style="margin-top:15px;text-align:center;">',e+='<button class="mc-btn mc-refresh-training" onclick="window.location.reload()">'+Pe("Refresh Page")+"</button>",e+="</div>",e+="</div>",e+="<style>.mc-refresh-training{background:#e67e22;color:white;border:none;padding:8px 15px;border-radius:4px;cursor:pointer;font-size:14px;}.mc-refresh-training:hover{background:#d35400;}</style>",void Me(e,"error",!1,"mc-server-error-box","Server Error")}"e"===e?(i=Pe("Connection error while accessing URL {R}. Please check your internet connection.").replace("{R}",s),this.errors.push(i),console.log(`HTTP connection error for URL: ${s}`)):(i=Pe("Error accessing URL {R}. Please verify the URL is correct and accessible.").replace("{R}",s),this.errors.push(i),console.log(`HTTP error ${e} for URL: ${s}`))}}else!0!==t[0][0]&&this.errors.push(t);this.website(e,i+1)}}))):(s&&this.errors.push(Pe("Use a valid URL starting with http. The URL {R} is not valid.").replace("{R}",s)),this.website(e,i+1))},sitemap:function(e,i,s=[]){t.find("#mc-embeddings-box p").html(Pe("We are generating the sitemap")+"<pre>"+e+"</pre>"),MCF.ajax({function:"generate-sitemap",url:e},(t=>{if("string"==typeof t){E.find("#mc-train-chatbot").mcLoading(!1);let s="";switch(t){case"invalid-url":s=Pe("The URL format is invalid. Please enter a valid URL starting with http:// or https://");break;case"url-not-accessible":s=Pe("The URL {R} cannot be accessed. Please verify the website is online and publicly accessible.").replace("{R}",e);break;case"invalid-sitemap":s=Pe("The sitemap at {R} is invalid or inaccessible. Please check the URL and ensure it points to a valid XML sitemap.").replace("{R}",e);break;case"empty-sitemap":s=Pe("The sitemap at {R} does not contain any valid URLs. Please check the sitemap content.").replace("{R}",e);break;case"invalid-xml":s=Pe("The sitemap at {R} contains invalid XML. Please verify the sitemap format.").replace("{R}",e);break;case"sitemap-generation-failed":s=Pe("Failed to generate sitemap for {R}. Please try again or use a different URL.").replace("{R}",e);break;default:s=t}console.log(`Sitemap error for URL ${e}: ${t}`),this.errors.push(s),Me(s,"info"),i([])}else i(t)}))},qea:function(e){t.find("#mc-embeddings-box p").html(Pe("We are processing the Q&A."));let i=st.repeater.get(M.find(".mc-repeater").eq(0).find("> .repeater-item")).map((e=>[e["open-ai-faq-questions"].map((e=>e.question)),e["open-ai-faq-answer"],e["open-ai-faq-function-calling-url"],e["open-ai-faq-function-calling-method"],e["open-ai-faq-function-calling-headers"],!!e["open-ai-faq-function-calling-properties"].length&&e["open-ai-faq-function-calling-properties"].map((e=>[e.name,e.description,e.allowed])),e["open-ai-faq-set-data"].map((e=>[e.id,e.value]))]));MCF.ajax({function:"open-ai-qea-training",questions_answers:i,reset:!0},(t=>{e(t)}))},articles:function(e){t.find("#mc-embeddings-box p").html(Pe("We are processing the articles.")),MCF.ajax({function:"open-ai-articles-training"},(t=>{e(t)}))},isFile:function(e){return e.includes(".pdf")||e.includes(".txt")},isError:function(e){if(E.find("#mc-train-chatbot").mcLoading(!1),e&&Array.isArray(e)&&!1===e[0]&&("empty-pdf-data"===e[1]||"unreadable-pdf"===e[1])){let t="",i=e[2]||"PDF file";return t="empty-pdf-data"===e[1]?Pe('The PDF file "{R}" contains no readable text. It may be image-only or protected.').replace("{R}",i):Pe('The PDF file "{R}" could not be read. It may be corrupted or in an unsupported format.').replace("{R}",i),this.errors.push(t),console.error(`PDF processing error: ${e[1]} for file: ${i}`),!0}if("string"==typeof e&&e.includes("error"))try{let t=JSON.parse(e);if("error"===t.status){let e="";switch(t.message){case"file-too-large":e=Pe("The file is too large to upload. Maximum size allowed is {R}MB.").replace("{R}",MC_ADMIN_SETTINGS.max_file_size);break;case"file-too-large-pdf":e=Pe("The PDF file is too large and may cause timeouts. Please split it into smaller files.");break;case"upload-timeout":e=Pe("The upload timed out. The file may be too large or your connection too slow.");break;case"upload-error":e=Pe("An error occurred during file upload.");break;default:e=Pe("An error occurred during file upload: {R}").replace("{R}",t.message)}return Me(e,"info"),console.error("Upload error details:",t.details||"No details provided"),!0}}catch(e){console.error("Error parsing error response:",e)}let t="chars-limit-exceeded"==e[1],i=t||e[1]&&e[1][0]&&e[1][0].error;if(e[1]&&"string"==typeof e[1]&&e[1].includes("http-error")){let t=e[1].replace("http-error-",""),i="";if("404"===t)return i=Pe("The URL could not be found (404). Please check that the URL is correct and publicly accessible."),console.log("HTTP 404 error encountered. URL not found but continuing with training."),this.errors.push(i),!1;switch(t){case"403":i=Pe("Access to the URL is forbidden (403). The website may require authentication or block automated access.");break;case"500":i=Pe("The website returned a server error (500). Please try again later or contact the website administrator.");break;case"429":i=Pe("Too many requests (429). The website is limiting access. Please try again later.");break;case"408":i=Pe("Request timeout (408). The website took too long to respond. Please try again later.");break;default:i=Pe("HTTP error {R} occurred while accessing the URL. Please check that the URL is correct and publicly accessible.").replace("{R}",t)}return Me(i),!0}if(i){Me(t?Pe("The chatbot cannot be trained with these sources because the limit of your plan is {R} characters. Upgrade your plan to increase the number of characters.").replace("{R}",e[2]):e[1][0].error&&e[1][0].error.message?Pe("Training error: {R}").replace("{R}",e[1][0].error.message):Pe("An error occurred during training. Please try again or contact support."))}return i}},playground:{messages:[],last_response:!1,addMessage:function(e,t="user",i=[]){G.append(`<div data-type="${t}"><div>${Pe("user"==t?"User":"Assistant")}<div><i class="mc-icon-close mc-btn-icon mc-btn-red"></i></div></div><div>${new MCMessage({id:1,message:MCF.escape(e),creation_time:"0000-00-00 00:00:00",status_code:0,user_type:"agent",attachments:JSON.stringify(i)}).getCode()}</div></div>`),G[0].scrollTop=G[0].scrollHeight,this.messages.push([t,e])}},init:function(){MCF.ajax({function:"open-ai-get-training-files"},(e=>{let t=["",""];for(var i=0;i<e.length;i++)if(!["mc-conversations","mc-articles","mc-database","mc-flows"].includes(e[i])){let s=this.train.isFile(e[i]);t[s?1:0]+=`<tr data-url="${e[i]}"><td><input type="checkbox" /></td><td>${s?MCF.beautifyAttachmentName(e[i].split("/").pop()):Ye(e[i])}</td><td></td><td><i class="mc-icon-delete"></i></td></tr>`}L.html(t[1]).mcLoading(!1),D.html(t[0]).mcLoading(!1),E.find("#mc-chatbot-delete-website").setClass("mc-hide",!t[0])})),MCF.ajax({function:"open-ai-get-qea-training"},(t=>{for(var i=0;i<t.length;i++)t[i][0]&&!Array.isArray(t[i][0])&&(t[i][0]=[t[i][0]]);let s=t.map((e=>({"open-ai-faq-questions":e[0]?e[0].map((e=>({question:e}))):[""],"open-ai-faq-answer":e[1],"open-ai-faq-function-calling-url":e[2],"open-ai-faq-function-calling-method":e[3],"open-ai-faq-function-calling-headers":e[4],"open-ai-faq-function-calling-properties":e[5]&&e[5].length?e[5].map((e=>({name:e[0],description:e[1],allowed:e[2]}))):[["","",""]],"open-ai-faq-set-data":e[6]&&e[6].length?e[6].map((e=>({id:e[0],value:e[1]}))):[["",""]]})));s.length&&(M.find("> div > .mc-repeater").html(st.repeater.set(s,M.find("> div > .mc-repeater > .repeater-item:last-child"))),M.find(".mc-enlarger").each((function(){let t=e(this).find("select"),i=["transcript","transcript_email","human_takeover","archive_conversation"];(e(this).find("input").val()||i.includes(t.val()))&&(e(this).mcActive(!0),e(this).hasClass("mc-enlarger-function-calling")&&e(this).closest(".repeater-item").find(".mc-qea-repeater-answer").addClass("mc-hide")),t.each((function(){i.includes(e(this).val())&&e(this).parent().next().find("input").addClass("mc-hide")}))})))}))}},messenger:{check:function(e){return["fb","ig"].includes(e.get("source"))},send:function(e,t,i="",s=[],a,n=!1,o=!1){MCF.ajax({function:"messenger-send-message",psid:e,facebook_page_id:t,message:i,message_id:n,attachments:s,metadata:a},(e=>{o&&o(e),it.unsupportedRichMessages(i,"Messenger")}))}},whatsapp:{check:function(e){return"wa"==e.get("source")},send:function(e,t="",i=[],s=!1,a=!1){MCF.ajax({function:"whatsapp-send-message",to:e,message:t,attachments:i,phone_id:s},(e=>{e.error&&Me(e.error.message,"info",!1,"error-wa"),a&&a(e),it.unsupportedRichMessages(t,"WhatsApp")}))},activeUserPhone:function(e=Re()){return!!e.getExtra("phone")&&e.getExtra("phone").value.replace("+","")},hasTemplates:function(e){MCF.ajax({function:"whatsapp-get-templates"},(t=>{let i=!1;if(Array.isArray(t)&&t.length>1){let e=t[1];Array.isArray(e)&&e.length>0&&(i=!0)}return e&&e(i),i}))}},telegram:{check:function(e){return"tg"==e.get("source")},send:function(e,t="",i=[],s=!1,a=!1){MCF.ajax({function:"telegram-send-message",chat_id:e,message:t,attachments:i,conversation_id:s},(e=>{a&&a(e),it.unsupportedRichMessages(t,"Telegram")}))}},viber:{check:function(e){return"vb"==e.get("source")},send:function(e,t="",i=[],s=!1){MCF.ajax({function:"viber-send-message",viber_id:e,message:t,attachments:i},(e=>{s&&s(e),it.unsupportedRichMessages(t,"Viber")}))}},zalo:{check:function(e){return"za"==e.get("source")},send:function(e,t="",i=[],s=!1){MCF.ajax({function:"zalo-send-message",zalo_id:e,message:t,attachments:i},(e=>{s&&s(e),it.unsupportedRichMessages(t,"Zalo")}))}},twitter:{check:function(e){return"tw"==e.get("source")},send:function(e,t="",i=[],s=!1){MCF.ajax({function:"twitter-send-message",twitter_id:e,message:t,attachments:i},(e=>{s&&s(e),it.unsupportedRichMessages(t,"Twitter")}))}},line:{check:function(e){return"ln"==e.get("source")},send:function(e,t="",i=[],s=!1,a=!1){MCF.ajax({function:"line-send-message",line_id:e,message:t,attachments:i,conversation_id:s},(e=>{a&&a(e),it.unsupportedRichMessages(t,"LINE")}))}},wechat:{token:!1,check:function(e){return"wc"==e.get("source")},send:function(e,t="",i=[],s=!1){MCF.ajax({function:"wechat-send-message",open_id:e,message:t,attachments:i,token:this.token},(e=>{Array.isArray(e)&&(this.token=e[1],e=e[0]),s&&s(e),it.unsupportedRichMessages(t,"WeChat")}))}},aecommerce:{conversationPanel:function(){let t="",i=Re().getExtra("aecommerce-id");this.panel||(this.panel=a.find(".mc-panel-aecommerce")),i&&!Ge(this.panel)&&MCF.ajax({function:"aecommerce-get-conversation-details",aecommerce_id:i.value},(i=>{t=`<h3>${MC_ADMIN_SETTINGS.aecommerce_panel_title}</h3><div><div class="mc-split"><div><div class="mc-title">${Pe("Number of orders")}</div><span>${i.orders_count} ${Pe("orders")}</span></div><div><div class="mc-title">${Pe("Total spend")}</div><span>${i.total} ${i.currency_symbol}</span></div></div><div class="mc-title">${Pe("Cart")}</div><div class="mc-list-items mc-list-links mc-aecommerce-cart">`;for(var s=0;s<i.cart.length;s++){let e=i.cart[s];t+=`<a href="${e.url}" target="_blank" data-id="${e.id}"><span>#${e.id}</span> <span>${e.name}</span> <span>x ${e.quantity}</span></a>`}if(t+=(i.cart.length?"":"<p>"+Pe("The cart is currently empty.")+"</p>")+"</div>",i.orders.length){t+=`<div class="mc-title">${Pe("Orders")}</div><div class="mc-list-items mc-list-links mc-aecommerce-orders">`;for(s=0;s<i.orders.length;s++){let e=i.orders[s],a=e.id;t+=`<a data-id="${a}" href="${e.url}" target="_blank"><span>#${e.id}</span> <span>${MCF.beautifyTime(e.time,!0)}</span> <span>${e.price} ${i.currency_symbol}</span></a>`}t+="</div>"}e(this.panel).html(t).mcLoading(!1),qe(this.panel,160)})),e(this.panel).html(t)}},martfury:{conversationPanel:function(){let t=Re().getExtra("martfury-id");this.panel||(this.panel=a.find(".mc-panel-martfury")),t&&!Ge(this.panel)&&MCF.ajax({function:"martfury-get-conversation-details",martfury_id:t.value},(t=>{e(this.panel).html(t).mcLoading(!1),qe(this.panel,160)})),e(this.panel).html("")}},whmcs:{conversationPanel:function(){let t="",i=Re().getExtra("whmcs-id");this.panel||(this.panel=a.find(".mc-panel-whmcs")),i&&!Ge(this.panel)&&MCF.ajax({function:"whmcs-get-conversation-details",whmcs_id:i.value},(i=>{let s=["products","addons","domains"];t=`<h3>WHMCS</h3><div><div class="mc-split"><div><div class="mc-title">${Pe("Number of services")}</div><span>${i.services_count} ${Pe("services")}</span></div><div><div class="mc-title">${Pe("Total spend")}</div><span>${i.total} ${i.currency_symbol}</span></div></div></div>`;for(var a=0;a<s.length;a++){let e=i[s[a]];if(e.length){t+=`<div class="mc-title">${Pe(MCF.slugToString(s[a]))}</div><div class="mc-list-items">`;for(var n=0;n<e.length;n++)t+=`<div>${e[n].name}</div>`;t+="</div>"}}t+=`<a href="${MC_ADMIN_SETTINGS.whmcs_url}/clientssummary.php?userid=${i["client-id"]}" target="_blank" class="mc-btn mc-whmcs-link">${Pe("View on WHMCS")}</a>`,e(this.panel).html(t).mcLoading(!1),qe(this.panel,160)})),e(this.panel).html(t)}},perfex:{conversationPanel:function(){let e=Re().getExtra("perfex-id");a.find(".mc-panel-perfex").html(e?`<a href="${MC_ADMIN_SETTINGS.perfex_url}/admin/clients/client/${e.value}" target="_blank" class="mc-btn mc-perfex-link">${Pe("View on Perfex")}</a>`:"")}},ump:{conversationPanel:function(){if(Ge(this.panel))return;this.panel||(this.panel=a.find(".mc-panel-ump"));let t,i="";MCF.ajax({function:"ump-get-conversation-details"},(s=>{if(t=s.subscriptions,t.length){i='<i class="mc-icon-refresh"></i><h3>Membership</h3><div class="mc-list-names">';for(var a=0;a<t.length;a++){let e=t[a].expired;i+=`<div${e?' class="mc-expired"':""}><span>${t[a].label}</span><span>${Pe(e?"Expired on":"Expires on")} ${MCF.beautifyTime(t[a].expire_time,!1,!e)}</span></div>`}i+=`</div><span class="mc-title">${Pe("Total spend")} ${s.total} ${s.currency_symbol}</span>`}e(this.panel).html(i).mcLoading(!1),qe(this.panel,160)}))}},armember:{conversationPanel:function(){let t=Re().getExtra("wp-id");if(this.panel||(this.panel=a.find(".mc-panel-armember")),MCF.null(t)||Ge(this.panel))e(this.panel).html("");else{let i,s="";t=t.value,MCF.ajax({function:"armember-get-conversation-details",wp_user_id:t},(t=>{if(i=t.subscriptions,i.length){s=`<i class="mc-icon-refresh"></i><h3>${Pe("Plans")}</h3><div class="mc-list-names">`;for(var a=0;a<i.length;a++){let e=i[a].expired;s+=`<div${e?' class="mc-expired"':""}><span>${i[a].arm_current_plan_detail.arm_subscription_plan_name}</span><span>${"never"==i[a].expire_time?"":Pe(e?"Expired on":"Expires on")+" "+MCF.beautifyTime(i[a].expire_time,!1,!e)}</span></div>`}s+=`</div><span class="mc-title">${Pe("Total spend")} ${t.total} ${t.currency_symbol}<a href="${window.location.href.substr(0,window.location.href.lastIndexOf("/"))+"?page=arm_manage_members&member_id="+Re().getExtra("wp-id").value}" target="_blank" class="mc-btn-text"><i class="mc-icon-user"></i> ${Pe("View member")}</a></span>`}e(this.panel).html(s).mcLoading(!1),qe(this.panel,160)}))}}},zendesk:{conversationPanel:function(){if(!MC_ADMIN_SETTINGS.zendesk_active)return;let t=Re().getExtra("zendesk-id"),i=Re().getExtra("phone"),s=Re().get("email"),n=a.find(".mc-panel-zendesk");(t||i||s)&&!Ge(n)?MCF.ajax({function:"zendesk-get-conversation-details",conversation_id:MCChat.conversation.id,zendesk_id:!!t&&t.value,phone:!!i&&i.value,email:s},(t=>{e(n).html(t).mcLoading(!1),n.find(".mc-zendesk-date").each((function(){e(this).html(MCF.beautifyTime(e(this).html()))})),qe(n,160)})):e(n).html("")}},woocommerce:{timeout:!1,conversationPanel:function(){if(Ge(this.panel))return;this.panel||(this.panel=a.find(".mc-panel-woocommerce"));let t="";MCF.ajax({function:"woocommerce-get-conversation-details"},(i=>{t=`<i class="mc-icon-refresh"></i><h3>WooCommerce</h3><div><div class="mc-split"><div><div class="mc-title">${Pe("Number of orders")}</div><span>${i.orders_count} ${Pe("orders")}</span></div><div><div class="mc-title">${Pe("Total spend")}</div><span>${i.total} ${i.currency_symbol}</span></div></div><div class="mc-title">${Pe("Cart")}<i class="mc-add-cart-btn mc-icon-plus"></i></div><div class="mc-list-items mc-list-links mc-woocommerce-cart">`;for(var s=0;s<i.cart.length;s++){let e=i.cart[s];t+=`<a href="${e.url}" target="_blank" data-id="${e.id}"><span>#${e.id}</span> <span>${e.name}</span> <span>x ${e.quantity}</span><i class="mc-icon-close"></i></a>`}if(t+=(i.cart.length?"":"<p>"+Pe("The cart is currently empty.")+"</p>")+"</div>",i.orders.length){t+=`<div class="mc-title">${Pe("Orders")}</div><div class="mc-list-items mc-woocommerce-orders mc-accordion">`;for(s=0;s<i.orders.length;s++){let e=i.orders[s],a=e.id;t+=`<div data-id="${a}"><span><span>#${a}</span> <span>${MCF.beautifyTime(e.date,!0)}</span><a href="${ee}/wp-admin/post.php?post=${a}&action=edit" target="_blank" class="mc-icon-next"></a></span><div></div></div>`}t+="</div>"}e(this.panel).html(t).mcLoading(!1),qe(this.panel,160)}))},conversationPanelOrder:function(e){let t=this.panel.find(`[data-id="${e}"] > div`);t.html(""),MCF.ajax({function:"woocommerce-get-order",order_id:e},(e=>{let i="",s=this.panel.find(".mc-collapse-btn:not(.mc-active)");if(e){let t=e.products;i+=`<div class="mc-title">${Pe("Order total")}: <span>${e.total} ${e.currency_symbol}<span></div><div class="mc-title">${Pe("Order status")}: <span>${MCF.slugToString(e.status.replace("wc-",""))}<span></div><div class="mc-title">${Pe("Date")}: <span>${MCF.beautifyTime(e.date,!0)}<span></div><div class="mc-title">${Pe("Products")}</div>`;for(var a=0;a<t.length;a++)i+=`<a href="${ee}?p=${t[a].id}" target="_blank"><span>#${t[a].id}</span> <span>${t[a].quantity} x</span> <span>${t[a].name}</span></a>`;for(a=0;a<2;a++){let t=0==a?"shipping":"billing";e[t+"_address"]&&(i+=`<div class="mc-title">${Pe((0==a?"Shipping":"Billing")+" address")}</div><div class="mc-multiline">${e[t+"_address"].replace(/\\n/g,"<br>")}</div>`)}}s.length&&s.click(),t.html(i)}))},conversationPanelUpdate:function(e,t="added"){let i=!1,s=0;this.timeout=setInterval((()=>{i||(MCF.ajax({function:"woocommerce-get-conversation-details"},(n=>{let o=!0;for(var r=0;r<n.cart.length;r++)n.cart[r].id==e&&("added"==t?s=61:o=!1);(s>60||o)&&(this.conversationPanel(),a.find(".mc-add-cart-btn,.mc-woocommerce-cart > a i").mcLoading(!1),clearInterval(this.timeout)),s++,i=!1})),i=!0)}),1e3)}},opencart:{conversationPanel:function(){let e=a.find(".mc-panel-opencart"),t=Re().getExtra("opencart_id"),i=Re().getExtra("opencart_store_url");if(!t)return e.html("");Ge(e)||MCF.ajax({function:"opencart-panel",opencart_id:t.value,store_url:!!i&&i.value},(t=>{e.html(t).mcLoading(!1),qe(this.panel,160)}))},openOrder:function(e){MCF.ajax({function:"opencart-order-details",order_id:e},(t=>{ct.infoPanel(t,"info",!1,"opencart-order-details",Pe("Order")+" #"+e,!0)}))}},wordpress:{ajax:function(t,i,s){e.ajax({method:"POST",url:MC_WP_AJAX_URL,data:e.extend({action:"mc_wp_ajax",type:t},i)}).done((e=>{!1!==s&&s(e)}))}},is:function(e){if(typeof MC_VERSIONS==Ne)return!1;switch(e){case"opencart":case"zendesk":case"twitter":case"wechat":case"line":case"viber":case"zalo":case"telegram":case"armember":case"aecommerce":case"martfury":case"whmcs":case"perfex":case"ump":case"messenger":case"whatsapp":case"woocommerce":case"dialogflow":case"slack":case"tickets":return typeof MC_VERSIONS[e]!=Ne&&MC_VERSIONS[e];case"wordpress":return typeof MC_WP!=Ne;case"mc":return!0}return!1},unsupportedRichMessages:function(e,t,i=[]){i.push("timetable","registration","table","inputs"),["Messenger","WhatsApp"].includes(t)||i.push("email");for(var s=0;s<i.length;s++)e.includes("["+i[s])&&De("The {R} rich message is not supported by {R2}. The rich message was not sent to {R2}.".replace(/{R}/g,MCF.slugToString(i[s])).replace(/{R2}/g,t),"error")},getName:function(e){let t={fb:"Facebook",wa:"WhatsApp",tm:"Text message",ig:"Instagram",tg:"Telegram",tk:"Tickets",wc:"WeChat",em:"Email",tw:"Twitter",bm:"Business Messages",vb:"Viber",ln:"LINE",za:"Zalo"};return e in t?t[e]:e},itemsPanel:{pagination_reference:1,panel_language:"",code:function(e,t,i=!0){let s="";for(var a=0;a<e.length;a++)s+=`<li data-id="${e[a].id.split("/").pop()}"><div class="mc-image" style="background-image:url('${e[a].image?"shopify"==t?e[a].image.replace(".jpg","_small.jpg"):e[a].image:MC_URL+"/media/thumb.svg"}')"></div><div><span>${e[a].name?e[a].name:e[a].title}</span><span>${e[a].price?e[a].price:e[a].variants.edges[0].node.price} ${MC_ADMIN_SETTINGS.currency}</span></div></li>`;return i?s||`<p class="mc-no-results">${Pe("No products found")}</p>`:s},getAppInfo:function(e){switch(e){case"woocommerce":return{area:ue,ul:pe,search:"woocommerce-search-products",filter:"woocommerce-get-products",populate:"woocommerce-products-popup",pagination:"woocommerce-get-products"};case"shopify":return{area:MCCloud.shopify_products_box,ul:MCCloud.shopify_products_box_ul,search:"shopify-get-products",filter:"shopify-get-products",populate:"shopify-get-products",pagination:"shopify-get-products"}}},search:function(t,i){let s=this.getAppInfo(i);Oe(t,((t,a)=>{t?(this.pagination_reference=1,MCF.ajax({function:s.search,search:t},(t=>{"shopify"==i&&(this.pagination_reference=t[1],t=t[0]),this.getAppInfo(i).ul.html(this.code(t,i)),e(a).mcLoading(!1)}))):this.populate(i,(function(){e(a).mcLoading(!1)}))}))},filter:function(t,i){let s=this.getAppInfo(i),a=e(t).data("value");Ge(s.ul)||(s.ul.html(""),this.pagination_reference=1,MCF.ajax({function:s.filter,user_language:this.panel_language,filters:{taxonomy:a},collection:a},(e=>{"shopify"==i&&(this.pagination_reference=e[1],e=e[0]),s.ul.html(this.code(e,i)).mcLoading(!1)})))},populate:function(e,t=!1){let i=this.getAppInfo(e);this.panel_language=Re()&&MC_ADMIN_SETTINGS.languages&&MC_ADMIN_SETTINGS.languages.includes(Re().language)?Re().language:"",this.pagination_reference=1,i.ul.html("").mcLoading(!0),MCF.ajax({function:i.populate,user_language:this.panel_language},(s=>{let a="",n=i.area.find(".mc-select");for(var o=0;o<s[1].length;o++)a+=`<li data-value="${s[1][o].id}">${s[1][o].name}</li>`;s[2]&&(this.pagination_reference=s[2]),s[3]&&(MC_ADMIN_SETTINGS.currency=s[3]),n.find("> p").html(Pe("All")),n.find("ul").html(`<li data-value="" class="mc-active">${Pe("All")}</li>`+a),i.ul.html(this.code(s[0],e)).mcLoading(!1),!1!==t&&t()}))},pagination:function(t,i){let s=this.getAppInfo(i),a=e(t).parent().find(".mc-select p").attr("data-value");this.pagination_reference&&(s.ul.mcLoading(t),MCF.ajax({function:s.pagination,filters:{taxonomy:a},collection:a,pagination:this.pagination_reference,user_language:this.panel_language},(e=>{"shopify"==i?(this.pagination_reference=e[1],e=e[0]):this.pagination_reference++,s.ul.append(this.code(e,i,!1)).mcLoading(!1),e.length||(this.pagination_reference=0)})))}}},st={init:!1,save:function(t=!1){if(t&&Ge(t))return;let i={},s={};switch(k.find(" > .mc-tab > .mc-nav .mc-active").attr("id")){case"tab-automations":let a=B.find(".mc-active").attr("data-id");st.automations.save((i=>{De(!0===i?"Automations saved":i),st.automations.populate(),B.find(`[data-id="${a}"]`).click(),t&&e(t).mcLoading(!1)}));break;case"tab-translations":this.translations.updateActive(),MCF.ajax({function:"save-translations",translations:JSON.stringify(this.translations.to_update)},(()=>{De("Translations saved"),t&&e(t).mcLoading(!1)}));break;default:k.find(".mc-setting").each(((t,a)=>{let n=this.get(a),o=e(a).data("setting");if(n[0])if(typeof o!=Ne){let t=!1;if(e(a).find("[data-language]").length){let i=e(a).find("[data-language].mc-active");if(t=n[0]in this.translations.originals&&this.translations.originals[n[0]],this.translations.save(a,!!i.length&&i.attr("data-language")),t&&"string"!=typeof t)for(var r in t)t[r]=[t[r],n[1][r][1]]}o in i||(i[o]={}),i[o][n[0]]=[t||n[1],n[2]]}else s[n[0]]=[n[1],n[2]]})),MCF.ajax({function:"save-settings",settings:JSON.stringify(s),external_settings:i,external_settings_translations:this.translations.translations},(()=>{t&&(De("Settings saved. Reload to apply the changes."),e(t).mcLoading(!1))}))}},get:function(t){let i=(t=e(t)).attr("id"),s=t.data("type");switch(s){case"upload":case"range":case"number":case"text":case"password":case"color":case"upload-file":return[i,t.find("input").val(),s];case"textarea":return[i,t.find("textarea").val(),s];case"select":return[i,t.find("select").val(),s];case"checkbox":return[i,t.find("input").is(":checked"),s];case"radio":let a=t.find("input:checked").val();return MCF.null(a)&&(a=""),[i,a,s];case"upload-image":let n=t.find(".image").attr("data-value");return MCF.null(n)&&(n=""),[i,n,s];case"multi-input":let o={};return t.find(".input > div").each(((e,t)=>{let i=this.get(t);i[0]&&(o[i[0]]=[i[1],i[2]])})),[i,o,s];case"select-images":return[i,t.find(".input > .mc-active").data("value"),s];case"repeater":return[i,this.repeater.get(t.find(".repeater-item")),s];case"double-select":let r={};return t.find(".input > div").each((function(){let t=e(this).find("select").val();-1!=t&&(r[e(this).attr("data-id")]=[t])})),[i,r,s];case"select-checkbox":return[i,t.find(".mc-select-checkbox input:checked").map((function(){return e(this).attr("id")})).get(),s];case"timetable":let l={};return t.find(".mc-timetable > [data-day]").each((function(){let t=e(this).attr("data-day"),i=[];e(this).find("> div > div").each((function(){let t=e(this).html(),s=e(this).attr("data-value");MCF.null(s)?i.push(["",""]):"closed"==s?i.push(["closed","Closed"]):i.push([s,t])})),l[t]=i})),[i,l,s];case"color-palette":return[i,t.attr("data-value"),s]}return["","",""]},set:function(t,i){let s=e(i)[1],a=e(i)[0];switch(t=`#${t}`,s){case"color":case"upload":case"number":case"text":case"password":case"upload-file":k.find(`${t} input`).val(a);break;case"textarea":k.find(`${t} textarea`).val(a);break;case"select":k.find(`${t} select`).val(a);break;case"checkbox":k.find(`${t} input`).prop("checked","false"!=a&&a);break;case"radio":k.find(`${t} input[value="${a}"]`).prop("checked",!0);break;case"upload-image":a&&k.find(t+" .image").attr("data-value",a).css("background-image",`url("${a}")`);break;case"multi-input":for(var n in a)this.set(n,a[n]);break;case"range":let i=a;k.find(t+" input").val(i),k.find(t+" .range-value").html(i);break;case"select-images":k.find(t+" .input > div").mcActive(!1),k.find(t+` .input > [data-value="${a}"]`).mcActive(!0);break;case"select-checkbox":for(var o=0;o<a.length;o++)k.find(`input[id="${a[o]}"]`).prop("checked",!0);k.find(t+" .mc-select-checkbox-input").val(a.join(", "));break;case"repeater":let s=this.repeater.set(a,k.find(t+" .repeater-item:last-child"));s&&k.find(t+" .mc-repeater").html(s);break;case"double-select":for(var n in a)k.find(`${t} .input > [data-id="${n}"] select`).val(a[n]);break;case"timetable":for(var n in a){let i=k.find(`${t} [data-day="${n}"] > div > div`);for(o=0;o<i.length;o++)e(i[o]).attr("data-value",a[n][o][0]).html(a[n][o][1])}break;case"color-palette":a&&k.find(t).attr("data-value",a)}},repeater:{set:function(t,i){var s="";if(this.clear(e(i)),i=e(i).html(),t.length){e(i).find("> .mc-icon-close").remove();for(var a=0;a<t.length;a++){let o=e(e.parseHTML(`<div>${i}</div>`));for(var n in t[a])st.input.set(o.find(`[data-id="${n}"]`),t[a][n]);s+=`<div class="repeater-item">${o.html().replaceAll('<i class="mc-icon-close"></i>',"")}<i class="mc-icon-close"></i></div>`}}return s},get:function(t){let i=[];return e(t).each((function(){let t={},s=!0;e(this).find("[data-id]").removeClass("mc-exclude"),e(this).find(".mc-repeater [data-id]").addClass("mc-exclude"),e(this).find("[data-id]:not(.mc-exclude)").each((function(){let i=st.input.get(this);s&&i&&"hidden"!=e(this).attr("type")&&"auto-id"!=e(this).attr("data-type")&&(s=!1),t[e(this).attr("data-id")]=i})),s||i.push(t)})),i},add:function(t){let i=e(t).parent();t=e(e.parseHTML(`<div>${i.find("> .mc-repeater > .repeater-item:last-child").html()}</div>`)),this.clear(t),t.find(".repeater-item:not(:first-child)").remove(),t.find("[data-id]").each((function(){if(st.input.reset(this),"auto-id"==e(this).data("type")){let t=1;i.find('[data-type="auto-id"]').each((function(){let i=parseInt(e(this).val());i>t&&(t=i)})),e(this).attr("value",t+1)}})),i.find("> .mc-repeater").append(`<div class="repeater-item">${t.html()}</div>`)},delete:function(t){let i=e(t).parent(),s=i.parent();s.parent().find(".mc-repeater-upload").length&&MCF.ajax({function:"delete-file",path:i.find("input").val()}),s.find("> .repeater-item").length>1?i.remove():i.find('[data-id]:not([data-type="auto-id"]').each(((e,t)=>{st.input.reset(t)}))},clear:function(e){e.find(".mc-active").mcActive(!1),e.find('input:not([data-type="auto-id"]').removeAttr("value checked"),e.find("option").removeAttr("selected"),e.find(".mc-hide").removeClass("mc-hide")}},input:{set:function(t,i){if(t=e(t),"object"!=typeof i&&(i=e.trim(i)),t.is("select"))t.find(`option[value="${i}"]`).attr("selected","");else if(t.is(":checkbox")&&i&&"false"!=i)t.attr("checked","");else if(t.is("textarea"))t.html(i);else{let e=t.is("div");t.hasClass("mc-repeater")?t.html(st.repeater.set(i,"<div>"+t.find("> .repeater-item").eq(0).html()+"</div>").replaceAll("mc-icon-close","mc-icon-close mc-sub-repeater-close")):e||t.is("i")||t.is("li")?(t.attr("data-value",i),e&&t.hasClass("image")&&t.css("background-image",i?`url("${i}")`:"")):t.attr("value",i)}},get:function(t){if((t=e(t)).is(":checkbox"))return t.is(":checked");if(t.hasClass("mc-repeater"))return st.repeater.get(t.find("> .repeater-item"));if(t.is("div")||t.is("i")||t.is("li")){let e=t.attr("data-value");return e||""}return t.val()},reset:function(t){(t=e(t)).is("select")?t.val("").find("[selected]").removeAttr("selected"):t.is(":checkbox")?t.removeAttr("checked").prop("checked",!1):t.is("textarea")?(t.val(""),t.html("")):t.hasClass("mc-repeater")?t.find(".repeater-item:not(:first-child)").remove():t.removeAttr("value style data-value").val("")}},initColorPicker:function(t=!1){e(t||k).find(".mc-type-color input").colorPicker({renderCallback:function(t,i){e(t.context).closest(".input").find("input").css("background-color",t.text)}})},getSettingObject:function(t){return e(t)[0].hasAttribute("data-setting")?e(t):e(t).closest("[data-setting]")},visibility:function(e,t){let i=[["#push-notifications-onesignal-sw-url, #push-notifications-onesignal-app-id, #push-notifications-onesignal-api-key, #push-notifications-sw-path","#push-notifications-id, #push-notifications-key"],["#messenger-key, #messenger-path-btn","#messenger-sync-btn"],["#open-ai-assistant-id","#open-ai-prompt,#open-ai-model, #open-ai-tokens, #open-ai-temperature, #open-ai-presence-penalty, #open-ai-frequency-penalty, #open-ai-logit-bias, #open-ai-custom-model, #open-ai-source-links"]];k.find(i[e][0]).mcActive(!t),k.find(i[e][1]).setClass("mc-hide",!t)},open:function(e,t=!1){i.find(".mc-admin-nav #mc-settings").click(),t&&setTimeout((()=>{k.find("#tab-"+e).click().get(0).scrollIntoView()}),300)},automations:{items:{messages:[],emails:[],sms:[],popups:[],design:[],more:[]},translations:{},conditions:function(){let e={datetime:["Date time",["Is between","Is exactly"],"dd/mm/yyy hh:mm - dd/mm/yyy hh:mm"],repeat:["Repeat",["Every day","Every week","Every month","Every year"]],browsing_time:["Browsing time",[],"seconds"],scroll_position:["Scroll position",[],"px"],url:["Current URL",["Contains","Does not contain"],"URLs parts separated by commas"],referring:["Referring URL",["Contains","Does not contain"],"URLs parts separated by commas"],user_type:["User type",["Is visitor","Is lead","Is user","Is not visitor","Is not lead","Is not user"]],returning_visitor:["Returning visitor",["First time visitor","Returning visitor"]],countries:["Country",["Is included","Is not included","Is set","Is not set"],"Country codes separated by commas"],languages:["Language",["Is included","Is not included","Is set","Is not set"],"Language codes separated by commas"],cities:["City",["Is included","Is not included","Is set","Is not set"],"Cities separated by commas"],website:["Website",["Contains","Does not contain","Is set","Is not set"],"URLs parts separated by commas"],birthdate:["Birthdate",["Is between","Is exactly","Is set","Is not set"],"dd/mm - dd/mm"],company:["Company",["Is included","Is not included","Is set","Is not set"],"Company names separated by commas"],postal_code:["Postal code",["Is included","Is not included","Is set","Is not set"],"Postal codes separated by commas"],email:["Email",["Contains","Does not contain","Is set","Is not set"],"Email addresses separated by commas"],phone:["Phone",["Contains","Does not contain","Is set","Is not set"],"Phone numbers separated by commas"],creation_time:["Creation time",["Is between","Is exactly"],"dd/mm/yyy hh:mm - dd/mm/yyy hh:mm"],custom_variable:["Custom variable",[],"variable=value"]},t=ot.getExtraDetailsList(!0);for(var i=0;i<t.length;i++)e[t[i][0]]=[t[i][1],["Contains","Does not contain","Is set","Is not set"],"Values separated by commas"];return e},get:function(e){MCF.ajax({function:"automations-get"},(t=>{this.items=t[0],this.translations=Array.isArray(t[1])&&!t[1].length?{}:t[1],e(t)}))},save:function(e=!1){this.updateActiveItem(),MCF.ajax({function:"automations-save",automations:this.items,translations:this.translations},(t=>{e&&e(t)}))},show:function(e=!1,t=!1){this.updateActiveItem();let i=t?t in this.translations?this.translations[t]:[]:this.items,s=x.find(" > .mc-tab > .mc-content");for(var a in!1===e&&(e=this.activeID()),this.hide(!1),i)for(var n=0;n<i[a].length;n++){let o=i[a][n];if(o.id==e){for(var a in o){let e=s.find(`[data-id="${a}"]`);e.hasClass("image")?(e.css("background-image",`url(${o[a]})`).attr("data-value",o[a]),o[a]||e.removeAttr("data-value")):"checkbox"==e.attr("type")?e.prop("checked",o[a]):e.val(o[a])}return this.setConditions(o.conditions,T),T.parent().setClass("mc-hide",t),s.mcLanguageSwitcher(this.getTranslations(e),"automations",t),!0}}return!1},add:function(){let e=MCF.random(),t=`${Pe("Item")} ${B.find("li:not(.mc-no-results)").length+1}`;this.updateActiveItem(),this.items[this.activeType()].push(this.itemArray(this.activeType(),e,t)),this.hide(!1),B.find(".mc-active").mcActive(!1),B.find(".mc-no-results").remove(),B.append(`<li class="mc-active" data-id="${e}">${t}<i class="mc-icon-delete"></i></li>`),x.find(".mc-automation-values").find("input, textarea").val(""),x.mcLanguageSwitcher([],"automations"),T.html("")},delete:function(t){this.items[this.activeType()].splice(e(t).parent().index(),1),e(t).parent().remove(),this.hide(),0==this.items[this.activeType()].length&&B.html(`<li class="mc-no-results">${Pe("No results found.")}</li>`)},populate:function(e=!1){!1===e&&(e=this.activeType());let t="",i=this.items[e];if(this.updateActiveItem(),i.length)for(var s=0;s<i.length;s++)t+=`<li data-id="${i[s].id}">${i[s].name}<i class="mc-icon-delete"></i></li>`;else t=`<li class="mc-no-results">${Pe("No results found.")}</li>`;switch(B.html(t),t="",e){case"emails":t=`<h2>${Pe("Subject")}</h2><div class="mc-setting mc-type-text"><div><input data-id="subject" type="text"></div></div>`;break;case"popups":t=`<h2>${Pe("Title")}</h2><div class="mc-setting mc-type-text"><div><input data-id="title" type="text"></div></div><h2>${Pe("Profile image")}</h2><div data-type="upload-image" class="mc-setting mc-type-upload-image"><div class="input"><div data-id="profile_image" class="image"><i class="mc-icon-close"></i></div></div></div><h2>${Pe("Message fallback")}</h2><div class="mc-setting mc-type-checkbox"><div><input data-id="fallback" type="checkbox"></div></div>`;break;case"design":t=`<h2>${Pe("Header title")}</h2><div class="mc-setting mc-type-text"><div><input data-id="title" type="text"></div></div>`;for(s=1;s<4;s++)t+=`<h2>${Pe((1==s?"Primary":2==s?"Secondary":"Tertiary")+" color")}</h2><div data-type="color" class="mc-setting mc-type-color"><div class="input"><input data-id="color_${s}" type="text"><i class="mc-close mc-icon-close"></i></div></div>`;for(s=1;s<4;s++)t+=`<h2>${Pe(1==s?"Header background image":2==s?"Header brand image":"Chat button icon")}</h2><div data-type="upload-image" class="mc-setting mc-type-upload-image"><div class="input"><div data-id="${1==s?"background":2==s?"brand":"icon"}" class="image"><i class="mc-icon-close"></i></div></div></div>`;break;case"more":t=`<h2>${Pe("Department ID")}</h2><div class="mc-setting mc-type-number"><div><input data-id="department" type="number"></div></div><h2>${Pe("Agent ID")}</h2><div class="mc-setting mc-type-number"><div><input data-id="agent" type="number"></div></div><h2>${Pe("Tags")}</h2><div class="mc-setting mc-type-text"><div><input data-id="tags" type="text"></div></div><h2>${Pe("Article IDs")}</h2><div class="mc-setting mc-type-number"><div><input data-id="articles" type="text"></div></div><h2>${Pe("Articles category")}</h2><div class="mc-setting mc-type-number"><div><input data-id="articles_category" type="text"></div></div>`}x.find(".mc-automation-extra").html(t),x.attr("data-automation-type",e),st.initColorPicker(x),this.hide()},updateActiveItem:function(){let t=this.activeID();if(t){let s=x.find(".mc-language-switcher [data-language].mc-active").attr("data-language"),a=this.activeType(),n=s?s in this.translations?this.translations[s][a]:[]:this.items[a];for(var i=0;i<n.length;i++)if(n[i].id==t){n[i]={id:t,conditions:[]},x.find(".mc-automation-values").find('input,textarea,[data-type="upload-image"] .image').each((function(){n[i][e(this).attr("data-id")]=e(this).hasClass("image")&&e(this)[0].hasAttribute("data-value")?e(this).attr("data-value"):"checkbox"==e(this).attr("type")?e(this).is(":checked"):e(this).val()})),n[i].conditions=this.getConditions(T),MCF.null(n[i].name)&&this.delete(B.find(`[data-id="${t}"] i`));break}}},getConditions:function(t){let i=[];return t.find(" > div").each((function(){let t=[];e(this).find("input,select").each((function(){t.push(e(this).val())})),t[0]&&t[1]&&(2==t.length||t[2]||["is-set","is-not-set"].includes(t[1]))&&i.push(t)})),i},setConditions:function(e,t){if(t.html(""),e)for(var i in e){this.addCondition(t);let s=t.find(" > div:last-child");s.find("select").val(e[i][0]),this.updateCondition(s.find("select")),s.find(" > div").eq(1).find("select,input").val(e[i][1]),e[i].length>2&&(["is-set","is-not-set"].includes(e[i][1])?s.find(" > div").eq(2).addClass("mc-hide"):s.find(" > div").eq(2).find("input").val(e[i][2]))}},addCondition:function(e){e.append(`<div><div class="mc-setting mc-type-select mc-condition-1"><select>${this.getAvailableConditions()}</select></div></div>`)},updateCondition:function(t){e(t).parent().siblings().remove();let i=e(t).parents().eq(1);if(e(t).val()){let a=this.conditions()[e(t).val()],n="";if(a[1].length){n='<div class="mc-setting mc-type-select mc-condition-2"><select>';for(var s=0;s<a[1].length;s++)n+=`<option value="${MCF.stringToSlug(a[1][s])}">${Pe(a[1][s])}</option>`;n+="</select></div>"}i.append(n+(a.length>2?`<div class="mc-setting mc-type-text"><input placeholder="${Pe(a[2])}" type="text"></div>`:"")),i.siblings().find(".mc-condition-1 select").each((function(){let t=e(this).val();e(this).html(st.automations.getAvailableConditions([t])),e(this).val(t)}))}else i.remove()},getAvailableConditions:function(t=[],i=[]){let s='<option value=""></option>',a=[],n=this.conditions();for(var o in T.find(".mc-condition-1 select").each((function(){a.push(e(this).val())})),n)i.includes(o)||a.includes(o)&&!t.includes(o)||(s+=`<option value="${o}">${Pe(n[o][0])}</option>`);return s},addTranslation:function(e=!1,t=!1,i){if(!1===e&&(e=this.activeID()),!1===t&&(t=this.activeType()),this.getTranslations(e).includes(e))return console.warn("Automation translation already in array.");i in this.translations||(this.translations[i]={messages:[],emails:[],sms:[],popups:[],design:[]}),t in this.translations[i]||(this.translations[i][t]=[]),this.translations[i][t].push(this.itemArray(t,e))},getTranslations:function(e=!1){let t=[];for(var i in!1===e&&(e=this.activeID()),this.translations){let n=this.translations[i];for(var s in n){let o=n[s];for(var a=0;a<o.length;a++)if(o[a].id==e){t.push(i);break}}}return t},deleteTranslation:function(e=!1,t){if(!1===e&&(e=this.activeID()),t in this.translations){let a=this.translations[t];for(var i in a){let n=a[i];for(var s=0;s<n.length;s++)if(n[s].id==e)return this.translations[t][i].splice(s,1),!0}}return!1},activeID:function(){let e=B.find(".mc-active");return!!e.length&&e.attr("data-id")},activeType:function(){return A.find("li.mc-active").data("value")},itemArray:function(t,i,s="",a=""){return e.extend({id:i,name:s,message:a},"emails"==t?{subject:""}:"popups"==t?{title:"",profile_image:""}:"design"==t?{title:"",color_1:"",color_2:"",color_3:"",background:"",brand:"",icon:""}:{})},hide:function(e=!0){x.find(" > .mc-tab > .mc-content").setClass("mc-hide",e)}},translations:{translations:{},originals:{},to_update:{},add:function(e){let t=st.getSettingObject(W),i=t.attr("id"),s=W.find("[data-language].mc-active");this.save(t,!!s.length&&s.attr("data-language")),t.find('textarea,input[type="text"]').val(""),this.save(t,e),W.remove(),t.mcLanguageSwitcher(this.getLanguageCodes(i),"settings",e)},delete:function(e,t){let i=(e=st.getSettingObject(e)).attr("id");delete this.translations[t][i],e.find(`.mc-language-switcher [data-language="${t}"]`).remove(),this.activate(e)},activate:function(e,t=!1){let i=(e=st.getSettingObject(e)).attr("id"),s=t?this.translations[t][i]:this.originals[i];if(ze(s))e.find("input, textarea").val(s);else for(var a in s)e.find("#"+a).find("input, textarea").val(ze(s[a])?s[a]:s[a][0])},updateActive:function(){let t=k.find(".mc-translations-list"),i={front:{},admin:{},"admin/js":{},"admin/settings":{}},s=t.attr("data-value");if(!MCF.null(s)){for(var a in i)t.find(' > [data-area="'+a+'"] .mc-setting:not(.mc-new-translation)').each((function(){i[a][e(this).find("label").html()]=e(this).find("input").val()})),t.find('> [data-area="'+a+'"] .mc-new-translation').each((function(){let t=e(this).find("input:first-child").val(),s=e(this).find("input:last-child").val();t&&s&&(i[a][t]=s)}));this.to_update[s]=i}},save:function(t,i=!1){t=st.getSettingObject(t);let s={},a=e(t).attr("id");"multi-input"==t.data("type")?t.find(".multi-input-textarea,.multi-input-text").each((function(){s[e(this).attr("id")]=e(this).find("input, textarea").val()})):s=t.find("input, textarea").val(),i?(i in this.translations||(this.translations[i]={}),this.translations[i][a]=s):this.originals[a]=s},load:function(e){let t=k.find(".mc-translations > .mc-content");t.find(" > .mc-hide").removeClass("mc-hide"),this.updateActive(),MCF.ajax({function:"get-translation",language_code:e},(i=>{e in this.to_update&&(i=this.to_update[e]);let s="",a=["front","admin","admin/js","admin/settings"];for(var n=0;n<a.length;n++){let e=i[a[n]];for(var o in s+=`<div${n?"":' class="mc-active"'} data-area="${a[n]}">`,e)s+=`<div class="mc-setting mc-type-text"><label>${o}</label><div><input type="text" value="${e[o]}"></div></div>`;s+="</div>"}t.find(".mc-translations-list").attr("data-value",e).html(s),t.find(".mc-menu-wide li").mcActive(!1).eq(0).mcActive(!0),t.mcLoading(!1)})),t.mcLoading(!0)},getLanguageCodes:function(e){let t=[];for(var i in this.translations)e in this.translations[i]&&t.push(i);return t}}},at={category_list:[],page_url:!1,get:function(e,t=!1,i=!1,s=!0,a=!1){MCF.ajax({function:"get-articles",id:t,categories:i,articles_language:a,full:s},(t=>{e(t)}))},save:function(e=!1){let t,i=this.activeID();if(t={id:i,title:I.find(".mc-article-title input").val(),content:I.find(".mc-article-content textarea").val(),link:I.find(".mc-article-link input").val(),parent_category:F.val(),category:N.val(),language:I.find(".mc-language-switcher [data-language].mc-active").attr("data-language")},!t.title&&!t.content)return e(!1);t.language&&(t.parent_id=this.activeID(!0)),Ee&&typeof Ee.save!==Ne?Ee.save().then((i=>{t.editor_js=i,t.content=function(e){let t="";return e.map((e=>{switch(e.type){case"header":t+=`<h${e.data.level}>${e.data.text}</h${e.data.level}>`;break;case"paragraph":t+=`<p>${e.data.text}</p>`;break;case"image":t+=`<img class="img-fluid" src="${e.data.file.url}" title="${e.data.caption}" /><em>${e.data.caption}</em>`;break;case"list":t+='<ul class="mc-ul-'+e.data.style+'">',e.data.items.forEach((function(e){t+=`<li>${e}</li>`})),t+="</ul>";break;case"code":t+=`<code>${e.data.code}</code>`;break;case"raw":t+=`<div class="bxc-raw-html">${e.data.html}</div>`}})),t}(i.blocks),this.save_2(t,e)})).catch((e=>{console.log(e)})):this.save_2(t,e)},save_2:function(e,t=!1){MCF.ajax({function:"save-article",article:JSON.stringify(e)},(i=>{let s=!0!==i&&!isNaN(i);if(Se=!1,s&&(C.attr("data-id",i),e.id=i,this.viewButton(i),e.language)){let t=at.translations.get(e.parent_id);C.find(`.mc-language-switcher [data-language="${e.language}"]`).attr("data-id",i);for(var a=0;a<t.length;a++)if(t[a][0]==e.language){t[a][1]=e.id,at.translations.list[e.parent_id]=t;break}}e.language||I.find(".ul-articles .mc-active").html(e.title+'<i class="mc-icon-delete"></i>').attr("data-id",e.id),t&&t(i),De(!0===i||s?"Article saved":i)}))},show:function(e){e&&(Ge(C),this.get((t=>{C.mcLoading(!1),t=t[0],C.mcLanguageSwitcher(this.translations.get(t.parent_id?t.parent_id:e),"articles",t.language),C.attr("data-id",e),I.find(".mc-article-title input").val(t.title),I.find(".mc-article-link input").val(t.link),I.find("#mc-article-id").html(`ID <span>${e}</span>`),I.find(".mc-article-categories").setClass("mc-hide",t.language),Ee||I.find("#editorjs").length?Ve(t.editor_js?ze(t.editor_js)?JSON.parse(t.editor_js):t.editor_js:t.content):I.find(".mc-article-content textarea").val(t.content),t.language||(F.val(t.parent_category),N.val(t.category)),this.viewButton(e),Se=!1}),e))},add:function(){let e=I.find(".ul-articles");e.find(".mc-active").mcActive(!1),e.append('<li class="mc-active"></li>'),C.mcLanguageSwitcher([],"articles"),this.clear()},clear:function(){C.removeAttr("data-id").removeClass("mc-hide"),C.find("input, textarea, select").val(""),C.find("input").prop("checked",!1),Ve(),this.viewButton(),Se=!1},delete:function(e,t=!1){MCF.ajax({function:"save-article",article:JSON.stringify({id:e,delete:!0})},(e=>{this.clear(),t&&t(e)}))},populate:function(t,i=!1){let s="";for(var a=0;a<t.length;a++)s+=`<li data-id="${t[a].id}">${t[a].title}<i class="mc-icon-delete"></i></li>`;I.find(i?".ul-categories":".ul-articles").html(s),I.find(i?".ul-categories > li":".ul-articles > li").eq(0).click(),t.length||e(i?$:C).mcLoading(!1).addClass("mc-hide")},activeID:function(e=!1){return e?I.find(".ul-articles .mc-active").attr("data-id"):C.attr("data-id")},viewButton:function(e=!1){if(this.page_url){I.find(".mc-view-article").attr("href",e?this.page_url+(this.is_url_rewrite?("/"==this.page_url.charAt(this.page_url.length-1)?"":"/")+(this.cloud_chat_id?this.cloud_chat_id+"/":""):"?article_id=")+e:"")}},categories:{list:[],save:function(e=!1){this.updateActive(),MCF.ajax({function:"save-articles-categories",categories:JSON.stringify(this.list)},(t=>{e&&e(t),De(!0===t?"Categories saved":t)}))},show:function(t,i=!1){let s=this.getIndex(t);if(!1!==s){let t=i?this.list[s].languages[i]:this.list[s],a=$.find("#category-image");this.updateActive(),$.find("#category-title").val(t.title),$.find("#category-description").val(t.description),$.find("#category-parent").prop("checked",!!t.parent),$.mcLanguageSwitcher(e.map(this.list[s].languages,(function(e,t){return t})),"article-categories",i),t.image?a.attr("data-value",t.image).css("background-image",`url("${t.image}")`):a.removeAttr("data-value style"),$.find(".category-parent").setClass("mc-hide",i)}$.mcLoading(!1)},add:function(){let e=MCF.random();this.list.push({id:e,title:"",description:"",image:"",languages:[]});let t=`<li data-id="${e}">${Pe("New category")}<i class="mc-icon-delete"></i></li>`;I.find(".ul-categories").append(t),I.find(".ul-categories li").eq(I.find(".ul-categories li").length-1).click(),$.removeClass("mc-hide")},delete:function(e){let t=this.getIndex(e),i=I.find(".ul-categories");return!1!==t&&(this.list.splice(t,1),i.find(`[data-id="${e}"]`).remove(),i.find("li").eq(0).click(),!0)},update:function(){let e=N.val(),t=F.val(),i=["","<option></option>"],s=this.list.map((function(e){return e.id}));for(var a=0;a<this.list.length;a++)i[this.list[a].parent?0:1]+=`<option value="${this.list[a].id}">${this.list[a].title}</option>`;F.html(i[0]),N.html(i[1]),this.list.length&&(F.val(s.includes(t)?t:F[0].selectedIndex>-1?this.list[F[0].selectedIndex].id:""),N.val(s.includes(e)?e:N[0].selectedIndex>-1?this.list[N[0].selectedIndex].id:""))},updateActive:function(){let e=this.activeID();if(e){let t=this.getIndex(e),i=$.find(".mc-language-switcher .mc-active").attr("data-language"),s={title:$.find("#category-title").val(),description:$.find("#category-description").val(),image:$.find("#category-image").attr("data-value")};i?this.list[t].languages[i]=s:(s.id=MCF.stringToSlug(s.title),s.parent=$.find("#category-parent").is(":checked"),s.languages=this.list[t].languages,this.list[t]=s,I.find(".ul-categories .mc-active").html(s.title+'<i class="mc-icon-delete"></i>').attr("data-id",s.id))}},clear:function(){$.find("input, textarea").val(""),$.find("#category-image").removeAttr("data-value style")},getIndex:function(e){for(var t=0;t<this.list.length;t++)if(this.list[t].id==e)return t;return!1},activeID:function(){return I.find(".ul-categories .mc-active").attr("data-id")},translations:{add:function(t,i=!1){at.categories.updateActive(),i||(i=at.categories.activeID());let s=at.categories.getIndex(i);MCF.null(at.categories.list[s].languages)&&(at.categories.list[s].languages={}),at.categories.list[s].languages[t]={title:"",description:"",image:""},$.mcLanguageSwitcher(e.map(at.categories.list[s].languages,(function(e,t){return t})),"article-categories",t),at.categories.clear()},delete:function(e,t=!1){let i=at.categories.activeID();t||(t=at.categories.activeID(i)),delete at.categories.list[at.categories.getIndex(t)].languages[e],at.categories.show(i)}}},translations:{list:{},add:function(e,t=!1){t||(t=at.activeID(!0));let i=this.get(t);i.push([e,!1]),this.list[t]=i,C.mcLanguageSwitcher(i,"articles",e),at.clear()},delete:function(e,t=!1){t||(t=at.activeID(!0));let i=this.get(t);for(var s=0;s<i.length;s++)if(i[s][0]==e){at.delete(i[s][1],(e=>{!0===e&&(i.splice(s,1),this.list[t]=i,at.show(t))}));break}return!1},get:function(e){return e in this.list?this.list[e]:[]}}},nt={chart:!1,active_report:!1,active_date_range:!1,initChart:function(e,t="line",i=1){let s=[],a=[],n=MC_ADMIN_SETTINGS.color?[MC_ADMIN_SETTINGS.color]:["#049CFF","#74C4F7","#B9E5FF","#0562A0","#003B62","#1F74C4","#436786"];for(var o in e)s.push(e[o][0]),a.push(o);if("line"!=t&&s.length>6)for(var r=0;r<s.length;r++)n.push("hsl(210, "+Math.floor(100*Math.random())+"%, "+Math.floor(100*Math.random())+"%)");this.chart&&this.chart.destroy(),this.chart=new Chart(ne.find("canvas"),{type:t,data:{labels:a,datasets:s&&Array.isArray(s[0])?[{data:s.map((e=>e[0])),backgroundColor:"#13ca7e"},{data:s.map((e=>e[1])),backgroundColor:"#ca3434"}]:[{data:s,backgroundColor:"line"==t?MC_ADMIN_SETTINGS.color?"#cbcbcb82":"#028be530":n,borderColor:"line"==t?MC_ADMIN_SETTINGS.color?MC_ADMIN_SETTINGS.color:"#049CFF":"#FFFFFF",borderWidth:0}]},options:{legend:{display:!1},scales:{yAxes:[{ticks:{callback:function(e,t,s){return 1==i?e:2==i?new Date(1e3*e).toISOString().substr(11,8):e},beginAtZero:!0}}],xAxes:[{ticks:{beginAtZero:!0}}]},tooltips:{callbacks:{label:function(e,t){let a=e.index,n=t.datasets[e.datasetIndex].data[a];switch(i){case 1:return n;case 2:return new Date(1e3*s[a]).toISOString().substr(11,8);case 3:return n+"%";case 4:let e=ne.find(".mc-table tbody tr").eq(a).find("td");return e.eq(0).text()+" "+e.eq(1).text()}}},displayColors:!1}}})},initTable:function(e,t,i=!1){let s="<thead><tr>",a=t[Object.keys(t)[0]].length-1,n=[];for(var o=0;o<e.length;o++)s+=`<th>${e[o]}</th>`;for(var r in s+="</tr></thead><tbody>",t)0!=t[r][a]&&n.push([r,t[r][a]]);i&&n.reverse();for(o=0;o<n.length;o++)s+=`<tr><td><div>${n[o][0]}</div></td><td>${n[o][1]}</td></tr>`;s+="</tbody>",ne.find("table").html(s)},initReport:function(e=!1,t=!1){let i=ne.find(".mc-tab > .mc-content");t=MCF.null(t)?[!1,!1]:t.split(" - "),i.mcLoading(!0),e&&(this.active_report=e),this.active_report&&(this.active_date_range=t,this.getData(this.active_report,t[0],t[1],(e=>{0==e?i.addClass("mc-no-results-active"):(i.removeClass("mc-no-results-active"),this.initChart(e.data,e.chart_type,e.label_type),this.initTable(e.table,e.data,e.table_inverse),ne.find(".mc-reports-title").html(e.title),ne.find(".mc-reports-text").html(e.description),ne.find(".mc-collapse-btn").remove(),xe||qe(ne.find(".mc-collapse"),ne.find("canvas").outerHeight()-135)),i.mcLoading(!1)})))},getData:function(e,t=!1,i=!1,s){MCF.ajax({function:"reports",name:e,date_start:t,date_end:i,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone},(e=>{s(e)}))},initDatePicker:function(){let e={ranges:{},locale:{format:"DD/MM/YYYY",separator:" - ",applyLabel:Pe("Apply"),cancelLabel:Pe("Cancel"),fromLabel:Pe("From"),toLabel:Pe("To"),customRangeLabel:Pe("Custom"),weekLabel:Pe("W"),daysOfWeek:[Pe("Su"),Pe("Mo"),Pe("Tu"),Pe("We"),Pe("Th"),Pe("Fr"),Pe("Sa")],monthNames:[Pe("January"),Pe("February"),Pe("March"),Pe("April"),Pe("May"),Pe("June"),Pe("July"),Pe("August"),Pe("September"),Pe("October"),Pe("November"),Pe("December")],firstDay:1},showCustomRangeLabel:!0,alwaysShowCalendars:!0,autoApply:!0,opens:t.hasClass("mc-rtl")?"left":"right"};e.ranges[Pe("Today")]=[moment(),moment()],e.ranges[Pe("Yesterday")]=[moment().subtract(1,"days"),moment().subtract(1,"days")],e.ranges[Pe("Last 7 Days")]=[moment().subtract(6,"days"),moment()],e.ranges[Pe("Last 30 Days")]=[moment().subtract(29,"days"),moment()],e.ranges[Pe("This Month")]=[moment().startOf("month"),moment().endOf("month")],e.ranges[Pe("Last Month")]=[moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")],ne.find("#mc-date-picker").daterangepicker(e).val("")},export:function(e){MCF.ajax({function:"reports-export",name:this.active_report,date_start:this.active_date_range[0],date_end:this.active_date_range[1],timezone:Intl.DateTimeFormat().resolvedOptions().timeZone},(t=>{e(t)}))},open:function(e){i.find(".mc-admin-nav #mc-reports").click(),setTimeout((()=>{ne.find("#"+e).click().get(0).scrollIntoView()}),300)}},ot={real_time:null,datetime_last_user:"2000-01-01 00:00:00",sorting:["creation_time","DESC"],user_types:["visitor","lead","user"],user_main_fields:["id","first_name","last_name","email","password","profile_image","user_type","creation_time","token","last_activity","department"],search_query:"",init:!1,busy:!1,table_extra:!1,history:[],get:function(e,t=!1,i=!1){let s=[];for(var a=0;a<_.length;a++)s.push(_.eq(a).find("li.mc-active").data("value"));i||Ge(m),MCF.ajax({function:t?"get-online-users":"get-users",sorting:t?this.sorting[0]:this.sorting,pagination:!!i&&be,user_types:this.user_types,search:this.search_query,extra:this.table_extra,department:s[0],source:s[1],tag:s[2]},(t=>{e(t),m.mcLoading(!1)}))},filter:function(e){e="all"==e?["visitor","lead","user"]:"agent"==e?["agent","admin"]:[e],this.user_types=e,be=1,ve=1,this.get((e=>{this.populate(e)}),"online"==e[0])},sort:function(e,t="DESC"){this.sorting=[e,t],be=1,ve=1,this.get((e=>{this.populate(e)}))},search:function(t){Oe(t,((t,i)=>{be=1,ve=1,this.search_query=t,this.get((t=>{this.user_types=["visitor","lead","user"],this.populate(t),e(i).mcLoading(!1),S.find("li").mcActive(!1).eq(0).mcActive(!0)}))}))},populate:function(t){let i="",s=t.length;if(s)for(var a=0;a<s;a++)i+=this.getRow(new MCUser(t[a],t[a].extra));else i=`<p class="mc-no-results">${Pe("No users found.")}</p>`;m.parent().scrollTop(0),m.find("tbody").html(i),this.user_types.includes("agent")&&MCF.ajax({function:"get-online-users",agents:!0},(t=>{let i=[];for(var s=0;s<t.length;s++)i.push(t[s].id);m.find("[data-user-id]").each((function(){e(this).find(".mc-td-profile").addClass("mc-"+(i.includes(e(this).attr("data-user-id"))?"online":"offline"))}))}))},update:function(){if(!this.busy){let e=["user","visitor","lead","agent"],t=e.includes(this.user_types[0])&&!this.search_query,i=S.find(".mc-active").data("type");"online"==i?this.filter(i):(this.busy=!0,MCF.ajax({function:"get-new-users",datetime:this.datetime_last_user},(s=>{let a=s.length;if(this.busy=!1,a>0){let o="";for(var n=0;n<a;n++){let e=new MCUser(s[n]);ge[e.id]=e,this.updateMenu("add",e.type),t&&(o+=this.getRow(e))}if(t&&(m.find("tbody").prepend(o),e.includes(i))){let t="";for(n=0;n<e.length;n++)t+=e[n]==i?"":`[data-user-type="${e[n]}"],`;m.find(t.slice(0,-1)).remove()}this.datetime_last_user=s[0].creation_time}})))}},getRow:function(e){if(e instanceof MCUser){let i="";for(var t=0;t<this.table_extra.length;t++){let s=this.table_extra[t];i+=`<td class="mc-td-${s}">${this.user_main_fields.includes(s)?e.get(s):e.getExtra(s)}</td>`}return`<tr data-user-id="${e.id}" data-user-type="${e.type}"><td><input type="checkbox" /></td><td class="mc-td-profile"><a class="mc-profile"><img loading="lazy" src="${e.image}" /><span>${e.name}</span></a></td>${i}<td class="mc-td-email">${e.get("email")}</td><td class="mc-td-ut">${Pe(e.type)}</td><td>${MCF.beautifyTime(e.get("last_activity"),!0)}</td><td>${MCF.beautifyTime(e.get("creation_time"))}</td></tr>`}return MCF.error("User not of type MCUser","MCUsers.getRow"),!1},updateRow:function(e){let i=m.find(`[data-user-id="${e.id}"]`);if(i.length){let s=S.find(".mc-active").data("type");if(e.type==s||"admin"==e.type&&"agent"==s||"all"==s)i.replaceWith(this.getRow(e));else{let s=t.find(`[data-type="${"admin"==e.type?"agent":e.type}"] span`),a=parseInt(s.attr("data-count"));s.html(a+1).attr("data-count",a+1),i.remove()}}else m.find("tbody").append(this.getRow(e))},updateMenu:function(e="all",t=!1){let i=["all","user","lead","visitor"];"all"==e?MCF.ajax({function:"count-users"},(e=>{for(var t=0;t<i.length;t++)this.updateMenuItem("set",i[t],e[i[t]])})):this.updateMenuItem(e,t)},updateMenuItem:function(e="set",t=!1,i=1){let s=S.find(`[data-type="${t}"] span`),a=["user","lead","visitor"];"set"!=e&&(i=parseInt(s.attr("data-count"))+1*("add"==e?1:-1)),s.html(`(${i})`).attr("data-count",i),i=0;for(var n=0;n<a.length;n++)i+=parseInt(S.find(`[data-type="${a[n]}"] span`).attr("data-count"));S.find('[data-type="all"] span').html(`(${i})`).attr("data-count",i)},delete:function(e){if(Ge(m),Array.isArray(e)){if(MC_ADMIN_SETTINGS.cloud&&!(e=MCCloud.removeAdminID(e)).length)return;MCF.ajax({function:"delete-users",user_ids:e},(()=>{for(var t=0;t<e.length;t++)delete ge[e[t]],m.find(`[data-user-id="${e[t]}"]`).remove(),l.find(`[data-user-id="${e[t]}"]`).remove(),MCF.event("MCUserDeleted",e[t]);0==m.find("[data-user-id]").length&&this.filter(S.find(".mc-active").data("type")),De("Users deleted"),this.updateMenu(),m.mcLoading(!1)}))}else ge[e].delete((()=>{let i=l.find(`[data-user-id="${e}"]`);Re().id==e&&Re(!1),i.mcActive()&&(MCChat.conversation=!1,setTimeout((()=>{rt.clickFirst()}),300)),delete ge[e],m.find(`[data-user-id="${e}"]`).remove(),i.remove(),t.mcHideLightbox(),De("User deleted"),this.updateMenu(),m.mcLoading(!1)}))},startRealTime:function(){MCPusher.active||(this.stopRealTime(),this.real_time=setInterval((()=>{this.update()}),1e3))},stopRealTime:function(){clearInterval(this.real_time)},csv:function(){MCF.ajax({function:"csv-users",users_id:ot.getSelected()},(e=>{Ke(e,"mc-export-users-close","Users exported"),window.open(e)}))},updateUsersActivity:function(){MCF.updateUsersActivity(Ce?MC_ACTIVE_AGENT.id:-1,Re()?Re().id:-1,(function(e){ot.setActiveUserStatus("online"==e)}))},setActiveAgentStatus:function(e=!0){let t=e?"online":"offline";Ce=e,i.find('[data-value="status"]').html(Pe(MCF.slugToString(t))).attr("class","mc-"+t),MCPusher.active&&(e?(MCPusher.presence(),MC_ADMIN_SETTINGS.routing_only&&MCF.ajax({function:"assign-conversations-active-agent"},(()=>{rt.update()}))):MCPusher.presenceUnsubscribe()),MC_ADMIN_SETTINGS.reports_disabled||MCF.ajax({function:"reports-update",name:t})},setActiveUserStatus:function(e=!0){let t=a.find(".mc-conversation .mc-top > .mc-labels");t.find(".mc-status-online").remove(),e&&t.prepend(`<span class="mc-status-online">${Pe("Online")}</span>`),MCChat.user_online=e},onlineUserNotification:function(e){let t=MC_ADMIN_SETTINGS.online_users_notification;if(t){let i=e.info.first_name+" "+e.info.last_name,s=this.userProfileImage(e.info.profile_image);MC_ADMIN_SETTINGS.push_notifications&&e.info.id&&!this.history.includes(e.info.id)?MCF.ajax({function:"push-notification",title:t,message:i,icon:s,interests:MC_ACTIVE_AGENT.id,user_id:e.info.id}):rt.desktop_notifications&&MCChat.desktopNotification(t,i,s,!1,e.info.id),this.history.push(e.info.id)}},userProfileImage:function(e){return!e||e.indexOf("user.svg")?MC_ADMIN_SETTINGS.notifications_icon:e},getSelected:function(){let t=[];return m.find("tr").each((function(){e(this).find('td input[type="checkbox"]').is(":checked")&&t.push(e(this).attr("data-user-id"))})),t},getExtraDetailsList:function(t=!1){return w.find(".mc-additional-details .mc-edit-box > "+(t?".mc-custom-detail":".mc-input")).map((function(){return[[e(this).attr("id"),e(this).find("span").html().trim()]]})).get()}},rt={real_time:null,datetime_last_conversation:"2000-01-01 00:00:00",user_typing:!1,desktop_notifications:!1,flash_notifications:!1,busy:!1,busy_2:!1,is_search:!1,menu_count_ajax:!1,previous_editor_text:!1,open:function(e=-1,s){-1!=e&&this.openConversation(e,s),t.mcHideLightbox(),i.find(".mc-admin-nav a").mcActive(!1).parent().find("#mc-conversations").mcActive(!0),t.find(" > main > div").mcActive(!1),a.mcActive(!0).find(".mc-board").removeClass("mc-no-conversation"),oe.find(" > p").attr("data-id","").attr("data-value","").html(Pe("None")),this.notes.update([]),this.tags.update([]),this.startRealTime()},openConversation:function(t,i=!1,s=!0){if(MCChat.label_date.mcActive(!1),MCChat.label_date_show=!1,this.busy_2!=t)if(!1===i&&t)this.busy_2=t,MCF.ajax({function:"get-user-from-conversation",conversation_id:t},(e=>{this.busy_2=!1,MCF.null(e.id)?MCF.error("Conversation not found","MCAdmin.openConversation"):this.openConversation(t,e.id,s)}));else{let o=MCF.null(ge[i])||!ge[i].details.email,d=a.find(`[data-conversation-id="${t}"]`),u=l.find("li");n.html(""),n.mcLoading(!0),o?(Re(new MCUser({id:i})),Re().update((()=>{ge[i]=Re(),this.updateUserDetails()}))):(Re(ge[i]),this.updateCurrentURL()),MCPusher.active&&(MCPusher.event("client-typing",(e=>{e.user_id==Re().id&&(rt.typing(!0),clearTimeout(Y),Y=setTimeout((()=>{rt.typing(!1)}),1e3))})),MCPusher.event("new-message",(()=>{MCChat.update()})),MCPusher.event("agent-active-conversation-changed",(e=>{e.previous_conversation_id==t&&a.find(".mc-conversation-busy").remove()}),"agents"),MCPusher.event("init",(e=>{rt.updateCurrentURL(e.current_url)})),MCPusher.event("message-status-update",(e=>{MCChat.conversation&&MCChat.conversation.updateMessagesStatus(e.message_ids)}))),MC_ADMIN_SETTINGS.smart_reply&&g.html(""),u.mcActive(!1),MC_ADMIN_SETTINGS.departments_show||u.attr("data-color",""),d.mcActive(!0),-1!=t?(this.busy_2=t,Re().getFullConversation(t,(i=>{let o=i.status_code,p=c.eq(0),h=p.find(".mc-active").attr("data-value");if(this.busy_2=!1,MCChat.setConversation(i),MCChat.populate(),this.setReadIcon(o),a.find(".mc-conversation-busy").remove(),this.updateUserDetails(),a.find(".mc-top > a").html(i.get("title")),ct.must_translate=MC_ADMIN_SETTINGS.translation&&Re().language&&MC_ADMIN_SETTINGS.active_agent_language!=Re().language,ct.must_translate){let e=[],s=[],a=[],n=[];for(var f=0;f<i.messages.length;f++){let t=i.messages[f];t.message&&(t.payload("original-message")&&(!t.payload("original-message-language")||t.payload("original-message-language")==MC_ADMIN_SETTINGS.active_agent_language)||t.payload("translation")&&(!t.payload("translation-language")||t.payload("translation-language")==MC_ADMIN_SETTINGS.active_agent_language)?n.push(t):(e.push(t.message),s.push(t.id),a.push(t.get("user_type"))))}e.length?it.dialogflow.translate(e,MC_ADMIN_SETTINGS.active_agent_language,(e=>{if(e)for(var t=0;t<e.length;t++){let i=MCChat.conversation.getMessage(s[t]);i.payload("translation",e[t]),i.payload("translation-language",MC_ADMIN_SETTINGS.active_agent_language),this.openConversation_2(s[t],a[t])}MC_ADMIN_SETTINGS.smart_reply&&this.openConversation_1(MCChat.conversation,g)}),s,t):MC_ADMIN_SETTINGS.smart_reply&&this.openConversation_1(MCChat.conversation,g);for(f=0;f<n.length;f++)this.openConversation_2(n[f].id,n[f].get("user_type"))}if(oe.length){let e=!!i.get("department")&&this.getDepartments(i.get("department")),s=e?e["department-color"]:"";MC_ADMIN_SETTINGS.departments_show||u.attr("data-color",""),tt(t).attr("data-color",s),oe.find(" > p").attr("data-id",e?e["department-id"]:"").attr("data-value",s).html(e?e["department-name"]+"<span></span>":Pe("None"))}let b=a.find("#conversation-agent");if(b.length){let e=b.find(`[data-id="${i.get("agent_id")}"]`);b.find(" > p").attr("data-value",e.data("id")).html(e.html())}[1,2,"1","2"].includes(o)&&(o=0),h==o||e(r).find(".mc-search-btn").mcActive()||rt.filters()[1]||rt.filters()[3]||(p.find(`[data-value="${o}"]`).click(),p.find("ul").mcActive(!1)),xe&&this.mobileOpenConversation(),d.length||h!=o&&(0!=h||1!=o)||l.prepend(rt.getListCode(i)),l.find("li").mcActive(!1),d.mcActive(!0),s&&this.scrollTo(),this.notificationsCounterReset(t,d),n.mcInitTooltips();let v=i.get("busy");v&&a.find(".mc-editor > .mc-labels").prepend(`<span data-agent="${v.id}" class="mc-status-warning mc-conversation-busy">${v.first_name} ${v.last_name} ${Pe("is replying to this conversation")}</span>`),it.is("woocommerce")&&it.woocommerce.conversationPanel(),it.is("ump")&&it.ump.conversationPanel(),it.is("perfex")&&it.perfex.conversationPanel(),it.is("whmcs")&&it.whmcs.conversationPanel(),it.is("aecommerce")&&it.aecommerce.conversationPanel(),it.is("martfury")&&it.martfury.conversationPanel(),it.is("armember")&&it.armember.conversationPanel(),it.is("zendesk")&&it.zendesk.conversationPanel(),it.is("opencart")&&it.opencart.conversationPanel(),Re()&&MC_ADMIN_SETTINGS.cloud&&(MCCloud.shopify.panel&&MCCloud.shopify.panel.html(""),MCCloud.shopify.conversationPanel()),this.notes.update(i.details.notes),this.tags.update(i.details.tags),this.attachments(),MC_ADMIN_SETTINGS.smart_reply&&!ct.must_translate&&this.openConversation_1(i,g);for(f=i.messages.length-1;f>0;f--){let e=i.messages[f].get("payload");if(e.rating){a.find(".mc-profile-list > ul").append(`<li data-id="rating"><i class="mc-icon mc-icon-${1==e.rating?"like":"dislike"}"></i><span>${Pe("User rating")}</span><label>${Pe(1==e.rating?"Helpful":"Not helpful")}${e.message?" - "+e.message:""}</label></li>`);break}}for(f=i.messages.length-1;f>0;f--){let e=i.messages[f].get("payload"),t=!1;if(e&&e["rich-messages"])for(var m in e["rich-messages"]){let i=e["rich-messages"][m];if("rating"==i.type){a.find(".mc-profile-list > ul").append(`<li data-id="rating"><i class="mc-icon mc-icon-${1==i.result.rating?"like":"dislike"}"></i><span>${Pe("User rating")}</span><label>${Pe(1==i.result.rating?"Helpful":"Not helpful")}</label></li>`),t=!0;break}}if(t)break}if("em"==i.get("source")&&this.cc(i.get("extra").split(",")),Re().getConversations((function(e){a.find(".mc-user-conversations").html(1==e.length?"":Re().getConversationsCode(e)).prev().setClass("mc-hide",1==e.length)})),this.is_search){let t=r.find(".mc-search-btn input").val();for(f=0;f<i.messages.length;f++)if(i.messages[f].message.toLowerCase().includes(t)){let t=i.messages[f].id;setTimeout((()=>{let i="";n.find("> div").each((function(){let s=e(this);s.attr("data-id")==t?(s.addClass("mc-highlight"),setTimeout((()=>{s.removeClass("mc-highlight")}),3600),MCChat.label_date.html(i),MCChat.label_date_show=!0,s.index()?s.prev()[0].scrollIntoView():s[0].scrollIntoView()):s.hasClass("mc-label-date")&&(i=s.html())}))}),300)}}n.mcLoading(!1)}))):(MCChat.clear(),l.find("li").mcActive(!1),n.mcLoading(!1),a.find(".mc-top > a").html(""),o||this.updateUserDetails()),a.find(".mc-board").removeClass("mc-no-conversation"),ot.updateUsersActivity(),this.startRealTime(),MCF.getURL("conversation")!=t&&-1!=t&&Je("?conversation="+t)}},openConversation_1:function(e,t){let i=e.getLastUserMessage();t.html(""),i&&i.payload("mc-human-takeover")&&(i=e.getLastUserMessage(i.get("index"))),i&&it.dialogflow.smartReply(i.message)},openConversation_2:function(e,t){let i=MCChat.conversation.getMessage(e);if(i){let s=MCF.isAgent(t)&&"bot"!=t;n.find(`[data-id="${e}"]`).replaceWith(i.getCode()),n.find(`[data-id="${e}"] .mc-menu`).prepend(`<li data-value="${s?"translation":"original"}">${Pe(s?"View translation":"View original message")}</li>`)}},populate:function(e,t,i){this.openConversation(e,t,i)},populateList:function(e){let t="";le=[];for(var i=0;i<e.length;i++)t+=this.getListCode(e[i]),le.push(new MCConversation([new MCMessage(e[i])],e[i]));t||(t=`<p class="mc-no-results">${Pe("No conversations found.")}</p>`),l.html(t),this.updateMenu(),MCF.event("MCAdminConversationsLoaded",{conversations:e})},update:function(){if(!this.busy&&0==c.eq(0).find("p").attr("data-value")){let t=rt.filters();if(this.busy=!0,MCF.ajax({function:"get-new-conversations",datetime:this.datetime_last_conversation,department:t[1],source:t[2],tag:t[3]},(t=>{if(this.busy=!1,t.length){let n,o="",r="",c=MCChat.conversation?MCChat.conversation.id:-1,d=!1,u=[];t[0].last_update_time&&(this.datetime_last_conversation=t[0].last_update_time);for(var i=0;i<t.length;i++)if(!u.includes(t[i].id)){let e=new MCMessage(t[i]),s=new MCConversation([e],t[i]),p=s.status_code,h=2==p||MC_ADMIN_SETTINGS.order_by_date&&(0==p||1==p),f=s.user_id,g=s.id,b=tt(g),v=b.length,m=e.get("user_type"),S=s.get("message"),_=c==g,y=!MCF.isAgent(m);if(!S&&e.payload("preview")&&(S=e.payload("preview")),h&&(!_||"hidden"==MCF.visibility_status)&&(y||e.payload("human-takeover-message-confirmation"))){let t=MCF.storage("notifications-counter");t||(t={}),t[g]||(t[g]=[]),t[g].includes(e.id)||(t[g].push(e.id),MCF.storage("notifications-counter",t))}let w=this.getListCode(s,null);_?(this.updateUserDetails(),v?(S&&b.replaceWith(w),this.setStatus(p,g)):d=!0):v&&(le[b.index()]=s,tt(g).remove()),f in ge||(ge[f]=new MCUser({id:f,first_name:s.get("first_name"),last_name:s.get("last_name"),profile_image:s.get("profile_image"),user_type:m})),_&&v||(h?(o+=w,le.unshift(s)):0!=p&&1!=p||(n=l.find('[data-conversation-status="2"]').last(),n.length?(le.splice(n.index()+1,0,s),r+=w):o+=w),Re()&&f==Re().id&&Re().getConversations((e=>{a.find(".mc-user-conversations").html(Re().getConversationsCode(e))})),MCF.event("MCAdminNewConversation",{conversation:s})),!Re()||"update-user"!=e.payload("event")&&ge[f].type==m||Re().update((()=>{this.updateUserDetails(),ge[Re().id]=Re()}));let k=e.payload("preview");if(!MCChat.tab_active&&2==p&&(y||k)&&(S||s.getAttachments().length||k)){if(this.desktop_notifications){let e=[ge[f].nameBeautified,ge[f].image];MCChat.desktopNotification(e[0],k||S,e[1],g,f)}this.flash_notifications&&MCChat.flashNotification(),MCChat.audio&&MC_ADMIN_SETTINGS.sound&&MCChat.playSound()}u.push(g)}rt.is_search||(o&&l.prepend(o),r&&e(r).insertAfter(n),d&&this.scrollTo(),this.updateMenu());for(i=0;i<MCChat.notifications.length;i++){let e=!1;for(var s=0;s<le.length;s++)if(le[s].id==MCChat.notifications[i][0]){e=2==le[s].status_code;break}e||(MCChat.notifications.splice(i,1),i--)}}})),MC_ADMIN_SETTINGS.assign_conversation_to_agent||MC_ACTIVE_AGENT.department){let t=l.find(" > li").map((function(){return e(this).attr("data-conversation-id")})).get();t.length&&MCF.ajax({function:"check-conversations-assignment",conversation_ids:t,agent_id:!!MC_ADMIN_SETTINGS.assign_conversation_to_agent&&MC_ACTIVE_AGENT.id,department:MC_ACTIVE_AGENT.department},(e=>{if(e)for(var t=0;t<e.length;t++)tt(e[t]).remove()}))}}},updateMenu:function(){let e=l.find('[data-conversation-status="2"]').length,t=c.eq(0),i=t.find(" > p span");if(100==e||this.menu_count_ajax||MC_ADMIN_SETTINGS.order_by_date){let e=t.find("li.mc-active").data("value");this.menu_count_ajax=!0,MCF.ajax({function:"count-conversations",status_code:0==e?2:e},(e=>{i.html(`(${e})`)}))}else i.html(`(${e})`)},messageMenu:function(e,t=!1){return`<i class="mc-menu-btn mc-icon-menu"></i><ul class="mc-menu">${(t&&MC_ADMIN_SETTINGS.chatbot_features?`<li data-value="bot">${Pe("Train chatbot")}</li>`:"")+(e&&!MC_ADMIN_SETTINGS.supervisor&&MC_ADMIN_SETTINGS.allow_agent_delete_message||MC_ADMIN_SETTINGS.supervisor&&MC_ADMIN_SETTINGS.allow_supervisor_delete_message?`<li data-value="delete">${Pe("Delete")}</li>`:"")}</ul>`},updateUserDetails(){Re()&&(a.find(`[data-user-id="${Re().id}"] .mc-name`).html(Re().name),a.find(".mc-top > a").html(MCChat.conversation?MCChat.conversation.title:Re().name),o.find(".mc-profile").setProfile(),lt.populate(Re(),a.find(".mc-profile-list")))},setReadIcon(e){let t=2==e;a.find('.mc-top [data-value="read"],.mc-top [data-value="unread"]').mcActive([0,1,2].includes(parseInt(e))).attr("data-value",t?"read":"unread").attr("data-mc-tooltip",Pe(t?"Mark as read":"Mark as unread")).parent().mcInitTooltips().find("i").attr("class",t?"mc-icon-check-circle":"mc-icon-circle")},setStatus(e,t=!1,i=!1){t||(t=MCChat.conversation.id),t&&(le[tt(t).index()].set("status_code",e),this.setReadIcon(e),i&&l.find(`[data-conversation-id="${t}"]`).attr("data-conversation-status",e))},getListCode:function(e,t){e instanceof MCConversation||(e=new MCConversation([new MCMessage(e)],e));let i=e.getCode(!0),s=MC_ADMIN_SETTINGS.tags_show?e.get("tags"):"",a=e.get("department"),n="",o=e.get("last_update_time"),r=MCChat.conversation&&MCChat.conversation.id==e.id;return MCF.null(t)&&(t=e.status_code),s&&(s=rt.tags.codeLeft(s)),MCChat.conversation&&r&&"hidden"!=MCF.visibility_status||(n=MCF.storage("notifications-counter"),n&&n[e.id]&&n[e.id].length?2==t?n=`<span class="mc-notification-counter">${n[e.id].length}</span>`:(n[e.id]=[],MCF.storage("notifications-counter",n),n=""):n=""),o||(o=e.getLastMessage()?e.getLastMessage().get("creation_time"):e.get("creation_time")),`<li${r?' class="mc-active"':""} data-user-id="${e.get("user_id")}" data-conversation-id="${e.id}" data-conversation-status="${t}"${a?` data-department="${a}"${MC_ADMIN_SETTINGS.departments_show?' data-color="'+this.getDepartments(a)["department-color"]+'"':""}`:""}${MCF.null(e.get("source"))?"":` data-conversation-source="${e.get("source")}"`}>${""+n}<div class="mc-profile"><img loading="lazy" src="${e.get("profile_image")}"><span class="mc-name">${e.get("first_name")+" "+e.get("last_name")}</span>${s}<span class="mc-time">${MCF.beautifyTime(o)}</span></div><p>${i}</p></li>`},startRealTime:function(){MCPusher.active||(this.stopRealTime(),this.real_time=setInterval((()=>{this.update(),this.updateCurrentURL()}),1e4),MCChat.startRealTime())},stopRealTime:function(){clearInterval(this.real_time),MCChat.stopRealTime()},transcript:function(e,t,i=!1,s=!1){MCF.ajax({function:"transcript",conversation_id:e},(e=>{"email"==i?Re()&&Re().id==t&&!Re().get("email")||MCChat.sendEmail(MC_ADMIN_SETTINGS.transcript_message,[[e,e]],t,(t=>{s&&s(!0===t?e:t)})):(s&&s(e),window.open(e))}))},typing:function(e){e?(MCChat.user_online||ot.setActiveUserStatus(!0),this.user_typing||(a.find(".mc-conversation .mc-top > .mc-labels").append('<span class="mc-status-typing">'+Pe("Typing")+"</span>"),this.user_typing=!0)):this.user_typing&&(a.find(".mc-conversation .mc-top .mc-status-typing").remove(),this.user_typing=!1)},scrollTo:function(){let e=l.find(".mc-active"),t=e.length?e[0].offsetTop:0;l.parent().scrollTop(t-(xe?120:80))},search:function(t){t&&Oe(t,((t,i)=>{if(fe=1,t.length>1)MCF.ajax({function:"search-conversations",search:t},(t=>{rt.populateList(t),e(i).mcLoading(!1),this.scrollTo(),this.is_search=!0}));else{let t=rt.filters();he=1,MCF.ajax({function:"get-conversations",status_code:t[0],department:t[1],source:t[2],tag:t[3]},(t=>{rt.populateList(t),e(i).mcLoading(!1),this.is_search=!1,MCChat.conversation&&(tt(MCChat.conversation.id).mcActive(!0),this.scrollTo())}))}}))},notificationsCounterReset:function(e,t=!1){let i=MCF.storage("notifications-counter");if(i&&i[e]){t||(t=l.find('[data-conversation-id="'+e+'"]'));let s=t.find(".mc-notification-counter");i[e]=[],MCF.storage("notifications-counter",i),s.addClass("mc-fade-out"),setTimeout((()=>{s.remove()}),200)}},updateCurrentURL:function(e=!1){e?this.ucurl(e):MCChat.user_online&&Re()&&Re().getExtra("current_url")&&MCF.ajax({function:"current-url"},(e=>{e&&this.ucurl(e)}))},ucurl(e){let t=Re().getExtra("current_url");e=Ye(e),a.find('.mc-profile-list [data-id="current_url"] label').attr("data-value",e).html(e),t&&(t.value=e,Re().setExtra("current_url",t))},assignDepartment:function(e,t,i){MCF.ajax({function:"update-conversation-department",conversation_id:e,department:t,message:MCChat.conversation.getLastMessage().message},(e=>{i(e)}))},assignAgent:function(e,t,i=!1){MCF.ajax({function:"update-conversation-agent",conversation_id:e,agent_id:t,message:MCChat.conversation.getLastMessage().message},(e=>{i&&i(e)}))},setActiveDepartment:function(e){if(MCChat.conversation&&MCChat.conversation.get("department")==e)return;let t=!!e&&this.getDepartments(e),i=t?t["department-color"]:"",s=tt(MCChat.conversation.id),a=rt.filters()[1];oe.find(" > p").attr("data-id",t?t["department-id"]:"").attr("data-value",i).html((t?t["department-name"]:Pe("None"))+"<span></span>").next().mcActive(!1),MCChat.conversation.set("department",e),a&&a!=e||"agent"==MC_ACTIVE_AGENT.user_type&&MC_ACTIVE_AGENT.department&&MC_ACTIVE_AGENT.department!=e?(s.remove(),rt.clickFirst()):s.attr("data-color",i),De("Department updated. The agents have been notified.")},getDepartments:function(e=!1){if(e){for(var t=0;t<MC_ADMIN_SETTINGS.departments.length;t++)if(MC_ADMIN_SETTINGS.departments[t]["department-id"]==e)return MC_ADMIN_SETTINGS.departments[t];return!1}return MC_ADMIN_SETTINGS.departments},setActiveAgent:function(e){let t=a.find("#conversation-agent"),i=t.find(`[data-id="${e}"]`);MCChat.conversation.set("agent_id",e),t.find(" > p").attr("data-value",i.data("id")).html(i.html()).next().mcActive(!1),"agent"!=MC_ACTIVE_AGENT.user_type||MC_ADMIN_SETTINGS.assign_conversation_to_agent&&!e||(tt(MCChat.conversation.id).remove(),rt.clickFirst()),e&&De("Agent assigned. The agent has been notified.")},mobileOpenConversation:function(){a.find(".mc-admin-list").mcActive(!1),s.mcActive(!0),i.addClass("mc-hide")},mobileCloseConversation:function(){l.find("li.mc-active").mcActive(!1),a.find(".mc-admin-list").mcActive(!0),a.find(".mc-conversation,.mc-user-details").removeClass("mc-active"),t.find('.mc-menu-mobile [data-value="panel"]').mcActive(!1),i.removeClass("mc-hide"),window.history.replaceState({},document.title,MCF.URL())},clickFirst:function(e=!1){e||(e=l.find("li:first-child")),e.length?(e.click(),rt.scrollTo()):(a.find(".mc-board").addClass("mc-no-conversation"),l.find("li").length||l.html(`<p class="mc-no-results">${Pe("No conversations found.")}</p>`),MCF.getURL("conversation")&&window.history.replaceState({},document.title,MCF.URL().replace("?conversation="+MCF.getURL("conversation"),"")))},savedReplies:function(t,i){let a=i.charAt(t.selectionStart-1),n=i.substr(0,i.lastIndexOf("#"));if("#"==a){if(i.length>1&&"#"==i.charAt(t.selectionStart-2))return e(t).val(n.substr(0,n.length-1)),s.find(".mc-btn-saved-replies").click();MCChat.editor_listening=!0}if(MCChat.editor_listening&&" "==a){let s=i.substr(i.lastIndexOf("#")+1).replace(" ","");MCChat.editor_listening=!1;for(var o=0;o<de.length;o++)if(de[o]["reply-name"]==s)return void e(t).val(n+de[o]["reply-text"])}},attachments:function(){if(p.length){let i=MCChat.conversation.getAttachments(),s="",a="",n=[];for(var t=i.length-1;t>-1;t--){let e=MCF.getFileType(i[t][1]);s+=`<a href="${i[t][1]}" target="_blank"><i class="mc-icon mc-icon-download"></i>${i[t][0]}</a>`,n.includes(e)||n.push(e)}if(i.length>4&&n.length>1){a=`<div id="mc-attachments-filter" class="mc-select"><p>${Pe("All")}</p><ul><li data-value="">${Pe("All")}</li>`;for(t=0;t<n.length;t++)a+=`<li data-value="${n[t]}">${Pe(MCF.slugToString(n[t])+"s")}</li>`;a+="</ul></div>"}e(p).html(s?`<h3${a?' class="mc-flex"':""}>${Pe("Attachments")}${a}</h3><div class="mc-list-items mc-list-links mc-list-icon">${s}</div>`:""),qe(p,160)}},filters:function(){let e=[];for(var t=0;t<c.length;t++)e.push(c.eq(t).find("li.mc-active").data("value"));return e},notes:{busy:!1,add:function(e,t,i,s,a=!1,n=!1){MCF.ajax({function:n?"update-note":"add-note",conversation_id:e,user_id:t,note_id:n,name:i,message:s},(e=>{a&&a(e)}))},update:function(e,t=!1){if(d.length){let s="",a=d.find(" > div");if(e){for(var i=0;i<e.length;i++){let t=e[i];s+=`<div data-id="${t.id}"><span${MC_ADMIN_SETTINGS.notes_hide_name?' class="mc-noname-note"':""}>${MC_ADMIN_SETTINGS.notes_hide_name?"":t.name}${MC_ACTIVE_AGENT.id==t.user_id?'<i class="mc-edit-note mc-icon-edit"></i><i class="mc-delete-note mc-icon-close"></i>':""}</span><span class="mc-note-text">${t.message.replace(/\n/g,"<br>")}</span></div>`}s=s.autoLink({target:"_blank"})}t?a.append(s):a.html(s),a.attr("style",""),d.find(".mc-collapse-btn").remove(),qe(d,155),this.busy=!1}},delete:function(e,t,i=!1){this.busy||(this.busy=!0,MCF.ajax({function:"delete-note",conversation_id:e,note_id:t},(e=>{this.busy=!1,i&&i(e)})))}},tags:{busy:!1,update:function(e){if(u.length){let i="",s=u.find(" > div");for(var t=0;t<e.length;t++){let s=this.get(e[t]);s&&(i+=this.code(s))}s.html(i),this.busy=!1}},get:function(e){for(var t=0;t<MC_ADMIN_SETTINGS.tags.length;t++)if(e==MC_ADMIN_SETTINGS.tags[t]["tag-name"])return MC_ADMIN_SETTINGS.tags[t]},getAll:function(e=[]){let t="";for(var i=0;i<MC_ADMIN_SETTINGS.tags.length;i++)t+=this.code(i,e.includes(MC_ADMIN_SETTINGS.tags[i]["tag-name"]));return t},code:function(e,t){let i=isNaN(e)?e:MC_ADMIN_SETTINGS.tags[e];if(i){let e=i["tag-name"];return`<span data-value="${e}" data-color="${i["tag-color"]}"${t?' class="mc-active"':""}>${Pe(e)}</span>`}return""},codeLeft:function(e){let t='<span class="mc-tags-area">';for(var i=0;i<e.length;i++){let s=this.get(e[i]);s&&(t+=`<i class="mc-icon-tag" data-color-text="${s["tag-color"]}"></i>`)}return t+"</span>"}},showDirectMessageBox:function(e,t=[]){if("whatsapp"==e)Ze(t);else{let i="custom_email"==e,s={sms:"text message",custom_email:"email",message:"chat message"};MCForm.clear(h),h.find(".mc-direct-message-users").val(t.length?t.join(","):"all"),h.find(".mc-bottom > div").html(""),h.find(".mc-top-bar > div:first-child").html(Pe(`Send a ${s[e]}`)),h.find(".mc-loading").mcLoading(!1),h.find(".mc-direct-message-subject").mcActive(i).find("input").attr("required",i),h.attr("data-type",e),h.mcShowLightbox()}},getDeliveryFailedMessage:function(e){return`<i class="mc-icon-warning mc-delivery-failed" data-mc-tooltip="${Pe("Message not delivered to {R}.").replace("{R}",it.getName(e))}" data-mc-tooltip-init></i>`},cc:function(e){let t="";for(var i=0;i<e.length;i++)e[i]&&(t+=`<li data-id="cc"><i class="mc-icon mc-icon-envelope"></i><span>CC</span><label>${e[i]}</label></li>`);a.find('[data-id="cc"]').remove(),a.find('[data-id="email"]').attr("data-em","true").after(t)}},lt={getAll:function(e){return MCForm.getAll(e)},get:function(e){return MCForm.get(e)},set:function(e,t){return MCForm.set(e,t)},show:function(e){Ue(),Re(new MCUser({id:e})),Re().update((()=>{this.populate(Re(),y.find(".mc-profile-list")),y.find(".mc-profile").setProfile(),Re().getConversations((t=>{let i=Re().type,s=Re().getConversationsCode(t);MCF.isAgent(i)&&this.agentData(),y.find(".mc-user-conversations").html(s).prev().setClass("mc-hide",!s),y.find(".mc-top-bar [data-value]").mcActive(!1),MCF.null(Re().get("email"))||y.find('.mc-top-bar [data-value="email"]').mcActive(!0),Re().getExtra("phone")&&(MC_ADMIN_SETTINGS.sms&&y.find('.mc-top-bar [data-value="sms"]').mcActive(!0),y.find('.mc-top-bar [data-value="whatsapp"]').mcActive(!0)),this.boxClasses(y,i),y.attr("data-user-id",Re().id).mcShowLightbox(),Ue(!1,!1),MCF.event("MCProfileBoxOpened",{user_id:e})})),ge[e]=Re(),MCF.getURL("user")==e||MCF.getURL("conversation")||Je("?user="+e)}))},showEdit:function(e){if(!(e instanceof MCUser))return MCF.error("User not of type MCUser","MCUsers.showEdit"),!1;{let i=w.find("#password input"),s=e.type,a=w.find("#user_type select");w.removeClass("mc-user-new").attr("data-user-id",e.id),w.find(".mc-top-bar .mc-save").html(`<i class="mc-icon-check"></i>${Pe("Save changes")}`),w.find(".mc-profile").setProfile(),w.find(".mc-unlisted-detail").remove(),w.find("input,select,textara").removeClass("mc-error");let n="",o=w.find(".mc-additional-details [id]").map((function(){return this.id})).get().concat(["wp-id","perfex-id","whmcs-id","aecommerce-id","facebook-id","ip","os","current_url","country_code","browser_language","browser","martfury-id","martfury-session"]);for(var t in e.extra)o.includes(t)||(n+=`<div id="${t}" data-type="text" class="mc-input mc-unlisted-detail"><span>${Pe(e.extra[t].name)}</span><input type="text"></div>`);w.find(".mc-additional-details .mc-edit-box").append(n),this.populateEdit(e,w),this.updateRequiredFields(s),"admin"==MC_ACTIVE_AGENT.user_type&&MCF.isAgent(s)&&a.html(`<option value="agent">${Pe("Agent")}</option><option value="admin"${"admin"==s?" selected":""}>${Pe("Admin")}</option>`),i.val()&&i.val("********"),MC_ADMIN_SETTINGS.cloud&&w.setClass("mc-cloud-admin",1==e.id),this.boxClasses(w,s),w.mcShowLightbox(),MCF.event("MCProfileEditBoxOpened",{user_id:e.id})}},populate:function(e,t){let i=["first_name","last_name","password","profile_image"],s="";if(t.hasClass("mc-profile-list-conversation")&&MCChat.conversation){let e=MCChat.conversation.get("source");s=this.profileRow("conversation-id",MCChat.conversation.id,Pe("Conversation ID")),MCF.null(e)||(s+=this.profileRow("conversation-source",it.getName(e),Pe("Source")))}for(var a in"admin"!=MC_ACTIVE_AGENT.user_type&&i.push("token"),e.details)i.includes(a)||(s+=this.profileRow(a,e.get(a),"id"==a?"User ID":a));if(t.html(`<ul>${s}</ul>`),s="",e.isExtraEmpty())MCF.ajax({function:"get-user-extra",user_id:e.id},(i=>{for(var a=0;a<i.length;a++){let t=i[a].slug;e.setExtra(t,i[a]),s+=this.profileRow(t,i[a].value,i[a].name)}t.find("ul").append(s),qe(t,145)}));else{for(var a in e.extra){let t=e.getExtra(a);s+=this.profileRow(a,t.value,t.name)}t.find("ul").append(s),qe(t,145)}},profileRow:function(e,t,i=e){if(!t)return"";let s,a={id:"user",full_name:"user",email:"envelope",phone:"phone",user_type:"user",last_activity:"calendar",creation_time:"calendar",token:"shuffle",currency:"currency",location:"marker",country:"marker",address:"marker",city:"marker",postal_code:"marker",browser:"desktop",os:"desktop",current_url:"next",timezone:"clock"},n=`<i class="mc-icon mc-icon-${e in a?a[e]:"plane"}"></i>`,o=!1;switch(e){case"last_activity":case"creation_time":t=MCF.beautifyTime(t);break;case"user_type":t=MCF.slugToString(t);break;case"country_code":case"language":case"browser_language":n=`<img src="${MC_URL}/media/flags/${t.toLowerCase()}.png" />`;break;case"browser":s=t.toLowerCase(),s.includes("chrome")?o="chrome":s.includes("edge")?o="edge":s.includes("firefox")?o="firefox":s.includes("opera")?o="opera":s.includes("safari")&&(o="safari");break;case"os":s=t.toLowerCase(),s.includes("windows")?o="windows":s.includes("mac")||s.includes("apple")||s.includes("ipad")||s.includes("iphone")?o="apple":s.includes("android")?o="android":s.includes("linux")?o="linux":s.includes("ubuntu")&&(o="ubuntu");break;case"conversation-source":o=t.toLowerCase();case"browser":case"os":case"conversation-source":o&&(n=`<img src="${MC_URL}/media/${"conversation-source"==e?"apps":"devices"}/${o}.svg" />`);break;case"current_url":t=Ye(t)}return`<li data-id="${e}">${n}<span>${Pe(MCF.slugToString(i))}</span><label>${t}</label></li>`},populateEdit:function(t,i){i.find(".mc-details .mc-input").each(((i,s)=>{this.set(s,t.details[e(s).attr("id")])})),i.find(".mc-additional-details .mc-input").each(((i,s)=>{let a=e(s).attr("id");a in t.extra?this.set(s,t.extra[a].value):this.set(s,"")}))},clear:function(e){MCForm.clear(e)},errors:function(e){return MCForm.errors(e.find(".mc-details"))},showErrorMessage:function(e,t){MCForm.showErrorMessage(e,t)},agentData:function(){let e=`<div class="mc-title">${Pe("Feedback rating")}</div><div class="mc-rating-area mc-loading"></div>`,t=y.find(".mc-agent-area");t.html(e),MCF.ajax({function:"get-rating"},(i=>{if(0==i[0]&&0==i[1])e=`<p class="mc-no-results">${Pe("No ratings yet.")}</p>`;else{let t=i[0]+i[1],s=100*i[0]/t,a=100*i[1]/t;e=`<div><div>${Pe("Helpful")}</div><span data-count="${i[0]}" style="width: ${Math.round(2*s)}px"></span><div>${s.toFixed(2)} %</div></div><div><div>${Pe("Not helpful")}</div><span data-count="${i[1]}" style="width: ${Math.round(2*a)}px"></span><div>${a.toFixed(2)} %</div></div><p class="mc-rating-count">${t} ${Pe("Ratings")}</p>`}t.find(".mc-rating-area").html(e).mcLoading(!1)}))},boxClasses:function(t,i=!1){e(t).removeClass("mc-type-admin mc-type-agent mc-type-lead mc-type-user mc-type-visitor").addClass(`${0!=i?`mc-type-${i}`:""} mc-agent-${MC_ACTIVE_AGENT.user_type}`)},updateRequiredFields:function(e){let t=MCF.isAgent(e);w.find("#password input").prop("required",t),w.find("#email input").prop("required",t)}},ct={infoBottom:function(e,i=!1){var s=t.find(".mc-info-card");i?"error"==i?(s.addClass("mc-info-card-error"),(e.includes("Error")||e.includes("error")||e.includes("failed")||e.includes("invalid"))&&(e="We encountered an issue while processing your request. Please try again or contact support if the problem persists.")):s.addClass("mc-info-card-info"):(s.removeClass("mc-info-card-error mc-info-card-warning mc-info-card-info"),clearTimeout(z),z=setTimeout((()=>{s.mcActive(!1)}),5e3)),s.html(`<h3>${Pe(e)}</h3>`).mcActive(!0)},infoPanel:function(i,s="info",a=!1,n="",o="",r=!1,l=!1,c=!1){if(l&&a)return a();"error"===s&&(i.includes("Error")||i.includes("error")||i.includes("Exception")||i.includes("failed")||i.includes("invalid"))&&(i=i.includes("<pre>")?"<pre>We encountered a technical issue. Please try again or contact support if the problem persists.</pre>":"We encountered an issue while processing your request. Please try again or contact support if the problem persists.");let d=t.find(".mc-dialog-box").attr("data-type",s),u=d.find("p");d.attr("id",n).setClass("mc-scroll-area",r).css("height",r?parseInt(e(window).height())-200+"px":""),d.find(".mc-title").html(Pe(o)),u.html(("alert"==s?Pe("Are you sure?")+" ":"")+Pe(i)),d.mcActive(!0).css({"margin-top":d.outerHeight()/-2+"px","margin-left":d.outerWidth()/-2+"px"}),K.mcActive(!0),J=a,X=c,setTimeout((()=>{ct.open_popup=d}),500),e(document).trigger("MCDialogOpen",[d])},genericPanel:function(e,i,s,a=[],n="",o=!1){let r="",l=t.find("#mc-generic-panel");for(var c=0;c<a.length;c++)a[c]=ze(a[c])||a[c]instanceof String?[a[c],!1]:a[c],r+=`<a id="mc-${MCF.stringToSlug(a[c][0])}" class="mc-btn${a[c][1]?" mc-icon":""}">${a[c][1]?`<i class="mc-icon-${a[c][1]}"></i>`:""} ${Pe(a[c][0])}</a>`;l.html(`<div class="mc-lightbox mc-${e}-box"${n}><div class="mc-info"></div><div class="mc-top-bar"><div>${Pe(i)}</div>\n                        <div>\n                            ${r}\n                            <a class="mc-close mc-btn-icon mc-btn-red">\n                                <i class="mc-icon-close"></i>\n                            </a>\n                        </div>\n                    </div>\n                    <div class="mc-main${o?" mc-scroll-area":""}">\n                        ${s}\n                    </div>\n             </div>`),l.find("> div").mcShowLightbox()},activeUser:function(e){if(typeof e==Ne)return window.mc_current_user;window.mc_current_user=e},loadingGlobal:function(i=!0,s=!0){t.find(".mc-loading-global").mcActive(i),s&&(K.mcActive(i),e("body").setClass("mc-lightbox-active",i))},loading:function(t){return!!e(t).mcLoading()||(e(t).mcLoading(!0),!1)},collapse(t,i){let s=(t=e(t)).find("> div, > ul");s.css({height:"","max-height":""}),t.find(".mc-collapse-btn").remove(),t.hasClass("mc-collapse")&&e(s).prop("scrollHeight")>i&&(t.mcActive(!0).attr("data-height",i),t.append(`<a class="mc-btn-text mc-collapse-btn">${Pe("View more")}</a>`),s.css({height:i+"px","max-height":i+"px"}))},open_popup:!1,must_translate:!1,is_logout:!1,conversations:rt,users:ot,settings:st,profile:lt,apps:it};function dt(){MCF.ajax({function:"get-conversations"},(e=>{Array.isArray(e)?(e.length||a.find(".mc-board").addClass("mc-no-conversation"),rt.populateList(e),xe&&a.find(".mc-admin-list").mcActive(!0),MCF.getURL("conversation")?(a.mcActive()||i.find(".mc-admin-nav #mc-conversations").click(),rt.openConversation(MCF.getURL("conversation"))):xe||MCF.getURL("user")||MCF.getURL("setting")||MCF.getURL("report")||MCF.getURL("area")&&"conversations"!=MCF.getURL("area")||rt.clickFirst(),rt.startRealTime(),rt.datetime_last_conversation=MC_ADMIN_SETTINGS.now_db):(ct.handleError(e,"get-conversations"),a.find(".mc-board").addClass("mc-no-conversation")),Ue(!1)})),MCF.serviceWorker.init(),MC_ADMIN_SETTINGS.push_notifications&&MCF.serviceWorker.initPushNotifications(),setInterval((function(){MCF.ajax({function:"get-active-user",db:!0},(e=>{e||MCF.reset()}))}),36e5)}window.MCAdmin=ct,e(document).ready((function(){if(t=e(".mc-admin"),i=t.find("> .mc-header"),a=t.find(".mc-area-conversations"),s=a.find(".mc-conversation"),n=a.find(".mc-conversation .mc-list"),r=a.find(".mc-admin-list"),l=r.find(".mc-scroll-area ul"),c=r.find(".mc-select"),o=a.find(".mc-user-details"),v=t.find(".mc-area-users"),m=v.find(".mc-table-users"),S=v.find(".mc-menu-users"),_=v.find(".mc-filter-btn .mc-select"),y=t.find(".mc-profile-box"),w=t.find(".mc-profile-edit-box"),k=t.find(".mc-area-settings"),x=k.find(".mc-automations-area"),T=x.find(".mc-conditions"),A=x.find(" > .mc-select"),B=x.find(" > .mc-tab > .mc-nav > ul"),ne=t.find(".mc-area-reports"),I=t.find(".mc-area-articles"),C=I.find(".mc-content-articles"),$=I.find(".mc-content-categories"),F=t.find("#article-parent-categories"),N=t.find("#article-categories"),ce=a.find(".mc-replies"),K=t.find(".mc-lightbox-overlay"),ee=typeof MC_URL!=Ne?MC_URL.substr(0,MC_URL.indexOf("-content")-3):"",ue=a.find(".mc-woocommerce-products"),pe=ue.find(" > div > ul"),d=a.find(".mc-panel-notes"),u=a.find(".mc-panel-tags"),p=a.find(".mc-panel-attachments"),h=t.find(".mc-direct-message-box"),Fe=it.is("wordpress")&&e(".wp-admin").length,f=t.find(".mc-dialogflow-intent-box"),g=a.find(".mc-editor > .mc-suggestions"),b=a.find(".mc-btn-open-ai"),oe=a.find("#conversation-department"),q=t.find(".mc-upload-form-admin .mc-upload-files"),E=t.find(".mc-area-chatbot"),L=E.find("#mc-table-chatbot-files"),D=E.find("#mc-table-chatbot-website"),M=E.find("#mc-chatbot-qea"),R=E.find(".mc-playground-editor"),G=E.find(".mc-playground .mc-scroll-area"),P=E.find('[data-id="flows"] > .mc-content'),U=E.find("#mc-flows-nav"),window.onpopstate=function(){t.mcHideLightbox(),xe&&a.mcActive()&&s.mcActive()&&rt.mobileCloseConversation(),MCF.getURL("user")?(v.mcActive()||i.find(".mc-admin-nav #mc-users").click(),lt.show(MCF.getURL("user"))):MCF.getURL("area")?i.find(".mc-admin-nav #mc-"+MCF.getURL("area")).click():MCF.getURL("conversation")?(a.mcActive()||i.find(".mc-admin-nav #mc-conversations").click(),rt.openConversation(MCF.getURL("conversation"))):MCF.getURL("setting")?(k.mcActive()||i.find(".mc-admin-nav #mc-settings").click(),k.find("#tab-"+MCF.getURL("setting")).click()):MCF.getURL("report")&&(ne.mcActive()||i.find(".mc-admin-nav #mc-reports").click(),ne.find("#"+MCF.getURL("report")).click())},MCF.getURL("area")&&setTimeout((()=>{i.find(".mc-admin-nav #mc-"+MCF.getURL("area")).click()}),300),typeof MC_ADMIN_SETTINGS==Ne){let i=t.find(".mc-intall"),s=window.location.href.replace("/admin","").replace(".php","").replace(/#$|\/$/,"");return e(t).on("click",".mc-submit-installation",(function(){if(Ge(this))return;let t=!1,a=i.find("#first-name").length;MCForm.errors(i)?t=a?"All fields are required. Minimum password length is 8 characters. Be sure you've entered a valid email.":"All fields are required.":a&&i.find("#password input").val()!=i.find("#password-check input").val()?t="The passwords do not match.":(MCF.cookie("SA_VGCKMENS",0,0,"delete"),s.includes("?")&&(s=s.substr(0,s.indexOf("?"))),e.ajax({method:"POST",url:s+"/include/ajax.php",data:{function:"installation",details:e.extend(MCForm.getAll(i),{url:s})}}).done((a=>{if(ze(a)&&(a=JSON.parse(a)),0!=a){if(!0===(a=a[1]))return void setTimeout((()=>{window.location.href=s+"/admin.php?refresh=true"}),1e3);switch(a){case"connection-error":t="We cannot connect to the database. Please check the database information and try again.";break;case"missing-details":t="Missing database details! Please check the database information and try again.";break;case"missing-url":t="We cannot get the application URL.";break;default:t=a}}else t=a;!1!==t&&(MCForm.showErrorMessage(i,t),e("html, body").animate({scrollTop:0},500)),e(this).mcLoading(!1)}))),!1!==t&&(MCForm.showErrorMessage(i,t),e("html, body").animate({scrollTop:0},500),e(this).mcLoading(!1))})),void fetch("https://app.masichat.com/synch/verification.php?x="+s)}if(!t.length)return;if(Ue(),t.removeAttr("style"),(window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone||document.referrer.includes("android-app://"))&&t.addClass("mc-pwa"),Be&&je(),t.find(" > .mc-rich-login").length)return;MCF.storage("notifications-counter",[]),MC_ADMIN_SETTINGS.pusher?(MCPusher.active=!0,MCPusher.init((()=>{MCPusher.presence(1,(()=>{ot.updateUsersActivity()})),MCPusher.event("update-conversations",(()=>{rt.update()}),"agents"),MCPusher.event("set-agent-status",(e=>{e.agent_id==MC_ACTIVE_AGENT.id&&(ot.setActiveAgentStatus("online"==e.status),me=!1)}),"agents"),dt()}))):(dt(),setInterval((function(){ot.updateUsersActivity()}),1e4)),ot.table_extra=m.find("th[data-extra]").map((function(){return e(this).attr("data-field")})).get(),typeof MC_CLOUD_FREE!=Ne&&MC_CLOUD_FREE&&setTimeout((()=>{location.reload()}),36e5),e(window).on("beforeunload",(function(){Re()&&e.ajax({method:"POST",url:MC_AJAX_URL,data:{function:"on-close"}})})),e(window).keydown((function(e){let i=e.which,s=!1;if(ae=i,[13,27,32,37,38,39,40,46,90].includes(i)){if(t.find(".mc-dialog-box").mcActive()){let e=t.find(".mc-dialog-box");switch(i){case 46:case 27:e.find(".mc-cancel").click();break;case 32:case 13:e.find("info"!=e.attr("data-type")?".mc-confirm":".mc-close").click()}s=!0}else if([38,40,46,90].includes(i)&&a.mcActive()&&!t.find(".mc-lightbox").mcActive()){let t=a.find(".mc-editor textarea"),n=t.is(":focus");if(46==i){if(n||"INPUT"==e.target.tagName)return;let t=a.find(" > div > .mc-conversation");t.find('.mc-top [data-value="'+(3==t.attr("data-conversation-status")?"delete":"archive")+'"]').click(),s=!0}else if(e.ctrlKey){let e=l.find(".mc-active");40==i?e.next().click():38==i?e.prev().click():90==i&&n&&rt.previous_editor_text&&(t.val(rt.previous_editor_text),rt.previous_editor_text=!1,s=!0),38!=i&&40!=i||(s=!0,rt.scrollTo())}}else if([37,39].includes(i)&&v.mcActive()&&t.find(".mc-lightbox").mcActive()){let e=m.find(`[data-user-id="${Re().id}"]`);e=39==i?e.next():e.prev(),e.length&&(t.mcHideLightbox(),lt.show(e.attr("data-user-id"))),s=!0}else if(27==i&&t.find(".mc-lightbox").mcActive())t.mcHideLightbox(),s=!0;else if(46==i){let e=t.find(".mc-search-btn.mc-active");e.length&&(e.find("i").click(),s=!0)}else 13==i&&E.find(".mc-playground-editor textarea").is(":focus")&&(E.find('.mc-playground-editor [data-value="send"]').click(),s=!0);s&&e.preventDefault()}})),e(window).keyup((function(e){ae=!1})),e(document).on("click keydown mousemove",(function(){MCF.debounce((()=>{MCChat.tab_active||MCF.visibilityChange(),MCChat.tab_active=!0,clearTimeout($e),$e=setTimeout((()=>{MCChat.tab_active=!1}),1e4)}),"#3",8e3),!xe&&MC_ADMIN_SETTINGS.away_mode&&MCF.debounce((()=>{me&&(ot.setActiveAgentStatus(),clearTimeout(Q),Q=setTimeout((()=>{ot.setActiveAgentStatus(!1)}),6e5))}),"#4",558e3)})),document.onpaste=function(e){let t=e.clipboardData.items[0];if(0===t.type.indexOf("image")){var i=new FileReader;i.onload=function(e){let t=e.target.result.split(","),i=t[0].indexOf("base64")>=0?atob(t[1]):decodeURI(t[1]),s=new Uint8Array(i.length);for(let e=0;e<i.length;e++)s[e]=i.charCodeAt(e);let a=new FormData;a.append("file",new Blob([s],{type:t[0].split(":")[1].split(";")[0]}),"image_print.jpg"),MCF.upload(a,(function(e){MCChat.uploadResponse(e)}))},i.readAsDataURL(t.getAsFile())}};let Y=[Pe("Please go to Settings > Miscellaneous and enter the Envato Purchase Code of Masi Chat."),`${Pe("Your license key is expired. Please purchase a new license")} <a href="https://app.masichat.com/shop/{R}" target="_blank">${Pe("here")}</a>.`];function _e(e,i,s,a=0,n=[],o,r=!1,l=!1,c=!1,d=!1,u=!1){let p={whatsapp:["whatsapp-send-template","messages"," a phone number.","direct-whatsapp"],email:["create-email","emails"," an email address.",!1],custom_email:["send-custom-email","emails"," an email address.","direct-emails"],sms:["send-sms","text messages"," a phone number.","direct-sms"]}[o];MCF.ajax({function:p[0],to:e[i][a].value,recipient_id:e[i][a].id,sender_name:MC_ACTIVE_AGENT.full_name,sender_profile_image:MC_ACTIVE_AGENT.profile_image,subject:r,message:s,template_name:l,parameters:c,template_languages:d,template:!1,phone_id:u,user_name:!!e.first_name&&(e.first_name[a].value+" "+e.last_name[a].value).trim(),user_email:!!e.email&&e.email[a].value},(f=>{let g=e[i].length,b="whatsapp"==o?t.find("#mc-whatsapp-send-template-box"):h;if(b.find(".mc-bottom > div").html(`${Pe("Sending")} ${Pe(p[1])}... ${a+1} / ${g}`),f){if(!0!==f&&("status"in f&&400==f.status||"error"in f&&![131030,131009].includes(f.error.code)))return MCForm.showErrorMessage(b,"We encountered an issue while processing your request. Please try again or contact support if the problem persists."),console.log("Response processing issue detected"),b.find(".mc-loading").mcLoading(!1),void b.find(".mc-bottom > div").html("");if(a<g-1)return _e(e,i,s,a+1,n,o,r,l,c,d,u);n.length?_e(n,i,s,0,[],"sms",!1):(t.mcHideLightbox(),p[3]&&MCF.ajax({function:"reports-update",name:p[3],value:s.substr(0,18)+" | "+g})),De(1==g?"The message has been sent.":Pe("The message was sent to all users who have"+p[2]))}else console.warn(f)}))}e(i).on("click",".mc-version",(function(){let e=t.find(".mc-updates-box");MCF.ajax({function:"get-versions"},(t=>{let i="",s={mc:"Masi Chat",slack:"Slack",dialogflow:"Artificial Intelligence",tickets:"Tickets",woocommerce:"Woocommerce",ump:"Ultimate Membership Pro",perfex:"Perfex",whmcs:"WHMCS",aecommerce:"Active eCommerce",messenger:"Messenger",whatsapp:"WhatsApp",armember:"ARMember",telegram:"Telegram",viber:"Viber",line:"LINE",wechat:"WeChat",zalo:"Zalo",twitter:"Twitter",zendesk:"Zendesk",martfury:"Martfury",opencart:"OpenCart",zalo:"Zalo"},a=!1;for(var n in t)if(it.is(n)){let e=MC_VERSIONS[n]==t[n];e||(a=!0),i+=`<div class="mc-input"><span>${s[n]}</span><div${e?' class="mc-green"':""}>${Pe(e?"You are running the latest version.":"Update available! Please update now.")} ${Pe("Your version is")} V ${MC_VERSIONS[n]}.</div></div>`}a?e.find(".mc-update").removeClass("mc-hide"):e.find(".mc-update").addClass("mc-hide"),Ue(!1),e.find(".mc-main").prepend(i),e.mcShowLightbox()})),Ue(),e.mcActive(!1),e.find(".mc-input").remove()})),e(t).on("click",".mc-updates-box .mc-update",(function(){if(Ge(this))return;let i=t.find(".mc-updates-box");MCF.ajax({function:"update",domain:MC_URL},(t=>{let s="";if(MCF.errorValidation(t,"envato-purchase-code-not-found"))s=Y[0];else if(MCF.errorValidation(t))s=MCF.slugToString(t[1]);else{let e=!0;for(var a in t)if("success"!=t[a]){e=!1,"expired"==t[a]&&(s=Y[1].replace("{R}",a)),"license-key-not-found"==t[a]&&(s=Pe("License key for the {R} app missing. Add it in Settings > Apps.").replace("{R}",MCF.slugToString(a.replace("dialogflow","Artificial Intelligence"))));break}e||s||(s=JSON.stringify(t))}je(),s?MCForm.showErrorMessage(i,s):(De("Update completed."),location.reload()),e(this).mcLoading(!1)}))})),setTimeout((function(){let e=MCF.storage("last-update-check"),t=[Te.getMonth(),Te.getDate()];MC_ADMIN_SETTINGS.cloud||(0==e||t[0]!=e[0]||t[1]>e[1]+10)&&(MCF.storage("last-update-check",t),MC_ADMIN_SETTINGS.auto_updates?MCF.ajax({function:"update",domain:MC_URL},(e=>{ze(e)||Array.isArray(e)||(De("Automatic update completed. Reload the admin area to apply the update."),je())})):"admin"==MC_ACTIVE_AGENT.user_type&&MCF.ajax({function:"updates-available"},(e=>{!0===e&&De(`${Pe("Update available.")} <span onclick="$('.mc-version').click()">${Pe("Click here to update now")}</span>`,"info")})))}),1e3),e(t).on("click",".mc-apps > div:not(.mc-disabled)",(function(){let i=t.find(".mc-app-box"),s=e(this).data("app"),a=MC_ADMIN_SETTINGS.cloud,n=it.is(s)&&(!a||MC_CLOUD_ACTIVE_APPS.includes(s)),o="?utm_source=plugin&utm_medium=admin_area&utm_campaign=plugin";a||MCF.ajax({function:"app-get-key",app_name:s},(e=>{i.find("input").val(e)})),i.setClass("mc-active-app",n),i.find("input").val(""),i.find(".mc-top-bar > div:first-child").html(e(this).find("h2").html()),i.find("p").html(e(this).find("p").html()),i.attr("data-app",s),i.find(".mc-btn-app-setting").mcActive(n),i.find(".mc-btn-app-puchase").attr("href","https://app.masichat.com/shop/"+s+o),i.find(".mc-btn-app-details").attr("href",(a?WEBSITE_URL:"https://app.masichat.com/")+s+o),i.mcShowLightbox()})),e(t).on("click",".mc-app-box .mc-activate",(function(){let i=t.find(".mc-app-box"),s=i.find("input").val(),a=i.attr("data-app");if(s||MC_ADMIN_SETTINGS.cloud){if(Ge(this))return;MCF.ajax({function:"app-activation",app_name:a,key:s},(t=>{if(MCF.errorValidation(t)){let n="";n="envato-purchase-code-not-found"==(t=t[1])?Y[0]:"invalid-key"==t?"It looks like your license key is invalid. If you believe this is an error, please contact support.":"expired"==t?Y[1].replace("{R}",s):"app-purchase-code-limit-exceeded"==t?MCF.slugToString(a)+" app purchase code limit exceeded.":"Error: "+t,MCForm.showErrorMessage(i,n),e(this).mcLoading(!1)}else De("Activation complete! Page reload in progress..."),setTimeout((function(){location.reload()}),1e3)}))}else MCForm.showErrorMessage(i,"Please insert the license key.")})),e(t).on("click",".mc-app-box .mc-btn-app-setting",(function(){k.find("#tab-"+e(this).closest("[data-app]").attr("data-app")).click(),t.mcHideLightbox()})),typeof Notification===Ne||"all"!=MC_ADMIN_SETTINGS.desktop_notifications&&"agents"!=MC_ADMIN_SETTINGS.desktop_notifications||MC_ADMIN_SETTINGS.push_notifications||(rt.desktop_notifications=!0),["all","agents"].includes(MC_ADMIN_SETTINGS.flash_notifications)&&(rt.flash_notifications=!0),Te.getDate()!=MCF.storage("admin-clean")&&setTimeout((function(){MCF.ajax({function:"cron-jobs"}),MCF.storage("admin-clean",Te.getDate())}),1e4),e(t).on("click",".mc-collapse-btn",(function(){let t=e(this).mcActive(),i=t?e(this).parent().data("height")+"px":"";e(this).html(Pe(t?"View more":"Close")),e(this).parent().find("> div, > ul").css({height:i,"max-height":i}),e(this).mcActive(!t)})),e(t).on("click",".mc-popup-close",(function(){t.mcHideLightbox()})),xe?(o.find("> .mc-scroll-area").prepend('<div class="mc-profile"><img><span class="mc-name"></span></div>'),e(t).on("click",".mc-menu-mobile > i",(function(){e(this).toggleClass("mc-active"),ct.open_popup=e(this).parent()})),e(t).on("click",".mc-menu-mobile a",(function(){e(this).closest(".mc-menu-mobile").find(" > i").mcActive(!1)})),e(t).on("click",".mc-menu-wide,.mc-nav",(function(){e(this).toggleClass("mc-active")})),e(t).on("click",".mc-menu-wide > ul > li, .mc-nav > ul > li",(function(i){let s=e(this).parent().parent();return s.find("li").mcActive(!1),s.find("> div:not(.mc-menu-wide):not(.mc-btn)").html(e(this).html()),s.mcActive(!1),s.find("> .mc-menu-wide").length&&s.closest(".mc-scroll-area").scrollTop(s.next()[0].offsetTop-(t.hasClass("mc-header-hidden")?70:130)),i.preventDefault(),!1})),e(t).find(".mc-admin-list .mc-scroll-area, main > div > .mc-scroll-area,.mc-area-settings > .mc-tab > .mc-scroll-area,.mc-area-reports > .mc-tab > .mc-scroll-area").on("scroll",(function(){let i=e(this).scrollTop();Ae.last<i-10&&Ae.header?(t.addClass("mc-header-hidden"),Ae.header=!1):Ae.last>i+10&&!Ae.header&&!Ae.always_hidden&&(t.removeClass("mc-header-hidden"),Ae.header=!0),Ae.last=i})),e(t).on("click",".mc-search-btn i,.mc-filter-btn i",(function(){e(this).parent().mcActive()?(t.addClass("mc-header-hidden"),Ae.always_hidden=!0):(Ae.always_hidden=!1,l.parent().scrollTop()<10&&t.removeClass("mc-header-hidden"))})),e(t).on("click",".mc-top .mc-btn-back",(function(){rt.mobileCloseConversation()})),e(m).find("th:first-child").html(Pe("Order by")),e(m).on("click","th:first-child",(function(){e(this).parent().toggleClass("mc-active")})),document.addEventListener("touchstart",(e=>{ie=e.changedTouches[0].clientX,se=e.changedTouches[0].clientY}),!1),document.addEventListener("touchend",(()=>{et()}),!1),document.addEventListener("touchmove",(e=>{var i=e.changedTouches[0].clientX,s=ie-i,o=e.changedTouches[0].clientY,r=se-o;if(Math.abs(s)>Math.abs(r)){var c=[];te=a.mcActive()?[l.find(".mc-active"),n,1]:[m.find(`[data-user-id="${Re().id}"]`),y,2],s>150?(1==te[2]?te[0].next().click():c=te[0].next(),et()):s<-150&&(1==te[2]?te[0].prev().click():c=te[0].prev(),et()),2==te[2]&&c.length&&(t.mcHideLightbox(),lt.show(c.attr("data-user-id"))),(s>80||s<-80)&&(te[1].css("transform","translateX("+-1*s+"px)"),te[1].addClass("mc-touchmove"))}}),!1)):MC_ADMIN_SETTINGS.hide_conversation_details?a.find('.mc-menu-mobile [data-value="panel"]').mcActive(!0):o.mcActive(!0),e(window).width()<913&&e(a).on("click","> .mc-btn-collapse",(function(){e(this).toggleClass("mc-active"),a.find(e(this).hasClass("mc-left")?".mc-admin-list":".mc-user-details").toggleClass("mc-active")})),e(i).on("click"," .mc-admin-nav a",(function(){switch(re=e(this).attr("id").substr(3),ct.active_admin_area=re,i.find(".mc-admin-nav a").mcActive(!1),t.find(" > main > div").mcActive(!1),t.find(".mc-area-"+re).mcActive(!0),e(this).mcActive(!0),MCF.deactivateAll(),re){case"conversations":xe||MCF.getURL("conversation")||rt.clickFirst(),rt.update(),rt.startRealTime(),ot.stopRealTime();break;case"users":ot.startRealTime(),rt.stopRealTime(),ot.init||(Ue(),be=1,ve=1,ot.get((e=>{ot.populate(e),ot.updateMenu(),ot.init=!0,ot.datetime_last_user=MCF.dateDB("now"),it.is("whatsapp")&&it.whatsapp.hasTemplates((e=>{let t=v.find('.mc-top-bar [data-value="whatsapp"]').closest("li");e?t.show():t.hide()})),Ue(!1)})));break;case"settings":st.init||(Ue(),MCF.ajax({function:"get-all-settings"},(i=>{if(i){let e=i["external-settings-translations"];if(i["slack-agents"]){let e="";for(var s in i["slack-agents"][0])e+=`<div data-id="${s}"><select><option value="${i["slack-agents"][0][s]}"></option></select></div>`;k.find("#slack-agents .input").html(e)}for(var s in st.translations.translations=Array.isArray(e)&&!e.length?{}:e,delete i["external-settings-translations"],i)st.set(s,i[s])}MCF.getURL("refresh_token")&&(t.find("#google-refresh-token input").val(MCF.getURL("refresh_token")),st.save(),De("Synchronization completed."),t.find("#google")[0].scrollIntoView()),k.find("textarea").each((function(){e(this).autoExpandTextarea(),e(this).manualExpandTextarea()})),k.find("[data-setting] .mc-language-switcher-cnt").each((function(){e(this).mcLanguageSwitcher(st.translations.getLanguageCodes(e(this).closest("[data-setting]").attr("id")),"settings")})),st.init=!0,Ue(!1),i&&!MC_ADMIN_SETTINGS.cloud&&st.visibility(0,i["push-notifications"]&&"pusher"==i["push-notifications"][0]["push-notifications-provider"][0]),st.visibility(1,!i.messenger||"manual"!=i.messenger[0]["messenger-sync-mode"][0]),st.visibility(2,!i["open-ai"]||"assistant"!=i["open-ai"][0]["open-ai-mode"][0]),MCF.event("MCSettingsLoaded",i)}))),ot.stopRealTime(),rt.stopRealTime();break;case"reports":ne.mcLoading()&&e.getScript(MC_URL+"/vendor/moment.min.js",(()=>{e.getScript(MC_URL+"/vendor/daterangepicker.min.js",(()=>{e.getScript(MC_URL+"/vendor/chart.min.js",(()=>{nt.initDatePicker(),nt.initReport("conversations"),ne.mcLoading(!1)}))}))})),ot.stopRealTime(),rt.stopRealTime();break;case"articles":let i=I.find(".mc-menu-wide li").eq(0);I.mcLoading()?(i.mcActive(!0).next().mcActive(!1),MCF.ajax({function:"init-articles-admin"},(e=>{at.categories.list=e[1],at.translations.list=e[2],at.page_url=e[3],at.is_url_rewrite=e[4],at.cloud_chat_id=e[5],at.populate(e[0]),at.populate(e[1],!0),at.categories.update(),I.mcLoading(!1)}))):i.click(),ot.stopRealTime(),rt.stopRealTime();break;case"chatbot":L.mcLoading()&&it.openAI.init(),it.openAI.troubleshoot()}MCF.getURL("area")!=re&&("conversations"==re&&!MCF.getURL("conversation")||"users"==re&&!MCF.getURL("user")||"settings"==re&&!MCF.getURL("setting")||"reports"==re&&!MCF.getURL("report")||"articles"==re&&!MCF.getURL("article")||"chatbot"==re&&!MCF.getURL("chatbot"))&&Je("?area="+re)})),e(i).on("click",".mc-profile",(function(){e(this).next().toggleClass("mc-active")})),e(i).on("click",'[data-value="logout"],.logout',(function(){ct.is_logout=!0,MCF.ajax({function:"on-close"}),ot.stopRealTime(),rt.stopRealTime(),setTimeout((()=>{MCF.logout()}),300)})),e(i).on("click",'[data-value="edit-profile"],.edit-profile',(function(){Ue();let e=new MCUser({id:MC_ACTIVE_AGENT.id});e.update((()=>{Re(e),a.find(".mc-board").addClass("mc-no-conversation"),l.find(".mc-active").mcActive(!1),lt.showEdit(e)}))})),e(i).on("click",'[data-value="status"]',(function(){let t=!e(this).hasClass("mc-online");ot.setActiveAgentStatus(t),me=t})),e(i).find(".mc-account").setProfile(MC_ACTIVE_AGENT.full_name,MC_ACTIVE_AGENT.profile_image),e(l).on("click","li",(function(){17==ae?e(this).mcActive(!e(this).mcActive()):(rt.openConversation(e(this).attr("data-conversation-id"),e(this).attr("data-user-id"),!1),MCF.deactivateAll())})),e(t).on("click",".mc-user-conversations li",(function(){rt.openConversation(e(this).attr("data-conversation-id"),Re().id,e(this).attr("data-conversation-status"))})),e(a).on("click",".mc-top ul a",(function(){let t,i=l.find(".mc-active").map((function(){return{id:e(this).attr("data-conversation-id"),user_id:e(this).attr("data-user-id"),status_code:e(this).attr("data-conversation-status")}})).toArray(),s=i.length,a=s>1?"All the selected conversations will be ":"The conversation will be ",n=e(this).attr("data-value"),r=(t,i)=>{let s=t.includes(".txt");e(this).mcLoading(!1),"email"==i&&actioninfoBottom(s?Pe("Transcript sent to user's email.")+' <a href="'+t+'" target="_blank">'+Pe("View transcript")+"</a>":"Transcript sending error: "+t,s?"":"error")};switch(n){case"inbox":t=1,a+="restored.";break;case"archive":a+="archived.",t=3;break;case"delete":a+="deleted.",t=4;break;case"empty-trash":t=5,a="All conversations in the trash (including their messages) will be deleted permanently.";break;case"transcript":let s=e(this).attr("data-action");"email"!=s||Re()&&Re().get("email")||(s=""),rt.transcript(i[0].id,i[0].user_id,s,(e=>r(e,s))),Ge(this);break;case"read":t=1,a+="marked as read.";break;case"unread":t=2,a+="marked as unread.";break;case"panel":e([o,this]).toggleClass("mc-active")}t&&Me(a,"alert",(function(){let e=c.eq(0).find("p").attr("data-value"),a=i[s-1].id;for(var n=0;n<s;n++){let s=i[n];MCF.ajax({function:"update-conversation-status",conversation_id:s.id,status_code:t},(()=>{let i=l.find(`[data-conversation-id="${s.id}"]`);if([0,3,4].includes(t))for(var n=0;n<le.length;n++)if(le[n].id==s.id){le[n].set("status_code",t);break}if(MC_ADMIN_SETTINGS.close_message&&3==t&&(MCF.ajax({function:"close-message",conversation_id:s.id,bot_id:MC_ADMIN_SETTINGS.bot_id}),MC_ADMIN_SETTINGS.close_message_transcript&&rt.transcript(s.id,s.user_id,"email",(e=>r(e)))),[0,1,2].includes(t)&&(i.attr("data-conversation-status",t),rt.updateMenu()),MCChat.conversation&&it.is("slack")&&[3,4].includes(t)&&MCF.ajax({function:"archive-slack-channels",conversation_user_id:MCChat.conversation.get("user_id")}),0==e&&[3,4].includes(t)||3==e&&[0,1,2,4].includes(t)||4==e&&4!=t){let e=!1;rt.updateMenu(),MCChat.conversation&&MCChat.conversation.id==s.id&&(e=i.prev(),MCChat.conversation=!1),i.remove(),s.id==a&&rt.clickFirst(e)}4==e&&5==t&&(l.find("li").remove(),rt.updateMenu(),rt.clickFirst())})),MCChat.conversation&&MCChat.conversation.id==s.id&&(MCChat.conversation.set("status_code",t),rt.setReadIcon(t))}}))})),MCF.ajax({function:"saved-replies"},(e=>{let t=`<p class="mc-no-results">${Pe("No saved replies found. Add new saved replies via Settings > Admin.")}</p>`;if(Array.isArray(e)&&e.length&&e[0]["reply-name"]){t="",de=e;for(var i=0;i<e.length;i++)t+=`<li><div>${e[i]["reply-name"]}</div><div>${e[i]["reply-text"].replace(/\\n/g,"\n")}</div></li>`}ce.find(".mc-replies-list > ul").html(t).mcLoading(!1)})),e(a).on("click",".mc-btn-saved-replies",(function(){ce.mcTogglePopup(this),ce.find(".mc-search-btn").mcActive(!0).find("input").get(0).focus()})),e(ce).on("click",".mc-replies-list li",(function(){MCChat.insertText(e(this).find("div:last-child").text().replace(/\\n/g,"\n")),MCF.deactivateAll(),t.removeClass("mc-popup-active")})),e(ce).on("input",".mc-search-btn input",(function(){Qe(e(this).val().toLowerCase())})),e(ce).on("click",".mc-search-btn i",(function(){MCF.searchClear(this,(()=>{Qe("")}))})),e(t).on("click",".mc-btn-open-ai",(function(){if(!MCChat.conversation||Ge(this))return;let t=e(this).hasClass("mc-btn-open-ai-editor"),i=t?a.find(".mc-editor textarea"):f.find("textarea");it.openAI.rewrite(i.val(),(s=>{e(this).mcLoading(!1),s[0]&&(i.val(t?"":s[1]),t&&MCChat.insertText(s[1]))}))})),e(r).find(".mc-scroll-area").on("scroll",(function(){if(!Ie&&!rt.is_search&&He(this,!0)&&fe){let e=a.find(".mc-admin-list"),t=rt.filters();Ie=!0,e.append('<div class="mc-loading-global mc-loading"></div>'),MCF.ajax({function:"get-conversations",pagination:he,status_code:t[0],department:t[1],source:t[2],tag:t[3]},(t=>{if(setTimeout((()=>{Ie=!1}),500),fe=t.length){let e="";for(var i=0;i<fe;i++){let s=new MCConversation([new MCMessage(t[i])],t[i]);e+=rt.getListCode(s),le.push(s)}he++,l.append(e)}e.find(" > .mc-loading").remove(),MCF.event("MCAdminConversationsLoaded",{conversations:t})}))}})),e(document).on("MCMessageDeleted",(function(){let e=MCChat.conversation.getLastMessage();0!=e?l.find("li.mc-active p").html(e.message):(l.find("li.mc-active").remove(),rt.clickFirst(),rt.scrollTo())})),e(document).on("MCMessageSent",(function(t,i){let s=i.conversation_id,n=(tt(s),Pe("Error. Message not sent to")),o=i.conversation,r=i.user;i.conversation_status_code&&rt.updateMenu(),it.messenger.check(o)&&it.messenger.send(r.getExtra("facebook-id").value,o.get("extra"),i.message,i.attachments,i.message_id,i.message_id,(e=>{for(var t=0;t<e.length;t++)e[t]&&e[t].error&&Me(n+" Messenger: "+e[t].error.message,"info",!1,"error-fb")})),it.whatsapp.check(o)&&it.whatsapp.send(it.whatsapp.activeUserPhone(r),i.message,i.attachments,o.get("extra"),(e=>{(e.ErrorCode||e.meta&&!e.meta.success)&&Me(n+" WhatsApp: "+("ErrorCode"in e?e.errorMessage:e.meta.developer_message),"info",!1,"error-wa")})),it.telegram.check(o)&&it.telegram.send(o.get("extra"),i.message,i.attachments,s,(e=>{e&&e.ok||Me(n+" Telegram: "+JSON.stringify(e),"info",!1,"error-tg")})),it.viber.check(o)&&it.viber.send(r.getExtra("viber-id").value,i.message,i.attachments,(e=>{e&&"ok"==e.status_message||Me(n+" Viber: "+JSON.stringify(e),"info",!1,"error-vb")})),it.zalo.check(o)&&it.zalo.send(r.getExtra("zalo-id").value,i.message,i.attachments,(e=>{e&&e.error.error&&Me(n+" Zalo: "+e.error.message?e.error.message:e.message,"info",!1,"error-za")})),it.twitter.check(o)&&it.twitter.send(r.getExtra("twitter-id").value,i.message,i.attachments,(e=>{e&&!e.event?Me(JSON.stringify(e),"info",!1,"error-tw"):i.attachments.length>1&&De("Only the first attachment was sent to Twitter.")})),it.line.check(o)&&it.line.send(r.getExtra("line-id").value,i.message,i.attachments,s,(e=>{e.error&&Me(n+" LINE: "+JSON.stringify(e),"info",!1,"error-ln")})),it.wechat.check(o)&&it.wechat.send(r.getExtra("wechat-id").value,i.message,i.attachments,(e=>{e&&"ok"==e.errmsg||Me(n+" WeChat: "+JSON.stringify(e),"info",!1,"error-wc")})),MC_ADMIN_SETTINGS.smart_reply&&g.html(""),MC_ADMIN_SETTINGS.assign_conversation_to_agent&&MCF.null(o.get("agent_id"))&&rt.assignAgent(s,MC_ACTIVE_AGENT.id,(()=>{MCChat.conversation.id==s&&(MCChat.conversation.set("agent_id",MC_ACTIVE_AGENT.id),e(a).find("#conversation-agent > p").attr("data-value",MC_ACTIVE_AGENT.id).html(MC_ACTIVE_AGENT.full_name))}))})),e(document).on("MCNewMessagesReceived",(function(e,t){let i=t.messages;for(var a=0;a<i.length;a++){let e=i[a],n=e.payload(),o=MCF.isAgent(e.get("user_type"));if(setTimeout((function(){s.find(".mc-top .mc-status-typing").remove()}),300),ct.must_translate){let i=s.find(`[data-id="${e.id}"]`),a=!!n["original-message"]&&n["original-message"];a?(i.replaceWith(e.getCode()),s.find(`[data-id="${e.id}"] .mc-menu`).prepend(`<li data-value="translation">${Pe("View translation")}</li>`),MC_ADMIN_SETTINGS.smart_reply&&it.dialogflow.smartReply(MCF.escape(a))):e.message&&it.dialogflow.translate([e.message],MC_ADMIN_SETTINGS.active_agent_language,(a=>{a&&(e.payload("translation",a[0]),e.payload("translation-language",MC_ADMIN_SETTINGS.active_agent_language),i.replaceWith(e.getCode()),s.find(`[data-id="${e.id}"] .mc-menu`).prepend(`<li data-value="original">${Pe("View original message")}</li>`)),MC_ADMIN_SETTINGS.smart_reply&&it.dialogflow.smartReply(a[0]),l.find(`[data-conversation-id="${t.conversation_id}"] p`).html(a[0])}),[e.id],MCChat.conversation.id)}else MC_ADMIN_SETTINGS.smart_reply&&it.dialogflow.smartReply(e.message);n&&(n.department&&rt.setActiveDepartment(n.department),n.agent&&rt.setActiveAgent(n.agent)),("ErrorCode"in n||n.errors&&n.errors.length)&&Me("Error. Message not sent to WhatsApp. Error message: "+(n.ErrorCode?n.ErrorCode:n.errors[0].title)),"whatsapp-templates"in n&&De("Message sent as text message."+("whatsapp-template-fallback"in n?" The user has been notified via WhatsApp Template notification.":"")),"whatsapp-template-fallback"in n&&!("whatsapp-templates"in n)&&De("The user has been notified via WhatsApp Template notification."),o||MCChat.conversation.id!=t.conversation_id||MCChat.user_online||ot.setActiveUserStatus()}rt.update()})),e(document).on("MCNewConversationCreated",(function(){rt.update()})),e(document).on("MCEmailSent",(function(){De("The user has been notified by email.")})),e(document).on("MCSMSSent",(function(){De("The user has been notified by text message.")})),e(document).on("MCNotificationsSent",(function(e,t){De(`The user ${t.includes("cron")?"will be":"has been"} notified by email${t.includes("sms")?" and text message":""}.`)})),e(document).on("MCTyping",(function(e,t){rt.typing(t)})),e(r).on("input",".mc-search-btn input",(function(){rt.search(this)})),e(a).on("click",".mc-admin-list .mc-search-btn i",(function(){MCF.searchClear(this,(()=>{rt.search(e(this).next())}))})),e(c).on("click","li",(function(t){let i=l.parent();if(Ge(i))return t.preventDefault(),!1;setTimeout((()=>{let t=rt.filters();he=1,fe=1,MCF.ajax({function:"get-conversations",status_code:t[0],department:t[1],source:t[2],tag:t[3]},(n=>{if(rt.populateList(n),s.attr("data-conversation-status",t[0]),n.length){if(!xe){if(MCChat.conversation){let e=tt(MCChat.conversation.id);e.length?e.mcActive(!0):t[0]==MCChat.conversation.status_code?l.prepend(rt.getListCode(MCChat.conversation)):rt.clickFirst()}else rt.clickFirst();rt.scrollTo()}}else a.find(".mc-board").addClass("mc-no-conversation"),MCChat.conversation=!1;e(this).closest(".mc-filter-btn").attr("data-badge",c.slice(1).toArray().reduce(((t,i)=>t+!!e(i).find("li.mc-active").data("value")),0)),i.mcLoading(!1)}))}),100)})),e(a).on("click",".mc-user-details .mc-profile,.mc-top > a",(function(){let e=l.find(".mc-active").attr("data-user-id");Re().id!=e&&Re(ge[e]),lt.show(Re().id)})),e(t).on("click",".mc-profile-list li",(function(){let t=e(this).find("label"),i=t.html();switch(e(this).attr("data-id")){case"location":Me('<iframe src="https://maps.google.com/maps?q='+i.replace(", ","+")+'&output=embed"></iframe>',"map");break;case"timezone":MCF.getLocationTimeString(Re().extra,(e=>{Ue(!1),Me(e)}));break;case"current_url":window.open("//"+(MCF.null(t.attr("data-value"))?i:t.attr("data-value")));break;case"conversation-source":let e=i.toLowerCase();"whatsapp"==e&&Re().getExtra("phone")?window.open("https://wa.me/"+it.whatsapp.activeUserPhone()):"facebook"==e?window.open("https://www.facebook.com/messages/t/"+MCChat.conversation.get("extra")):"instagram"==e?window.open("https://www.instagram.com/direct/inbox/"):"twitter"==e&&window.open("https://twitter.com/messages/");break;case"wp-id":window.open(window.location.href.substr(0,window.location.href.lastIndexOf("/"))+"/user-edit.php?user_id="+Re().getExtra("wp-id").value);break;case"envato-purchase-code":Ue(),MCF.ajax({function:"envato",purchase_code:i},(e=>{let t="";if(e&&e.item){for(var i in e.name=e.item.name,e)!ze(e[i])&&isNaN(e[i])||(t+=`<b>${MCF.slugToString(i)}</b> ${e[i]} <br>`);Ue(!1),Me(t,"info",!1,"mc-envato-box")}else De(MCF.slugToString(e))}));break;case"email":case"cc":if(MCChat.conversation&&"em"==MCChat.conversation.get("source")){let e=MCChat.conversation.get("extra").split(","),t='<div data-type="repeater" class="mc-setting mc-type-repeater"><div class="input"><div class="mc-repeater">';for(var s=0;s<e.length;s++)t+=`<div class="repeater-item"><div><input data-id="cc" type="text" value="${e[s]}"></div><i class="mc-icon-close"></i></div>`;t+=`</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${Pe("Add new item")}</div></div></div>`,ct.genericPanel("cc","Manage CC",t,["Save changes"],"",!0)}}})),e(o).on("click",".mc-user-details-close",(function(){a.find('.mc-menu-mobile [data-value="panel"]').click().mcActive(!0)})),e(a).on("click",'.mc-menu [data-value="bot"]',(function(){it.dialogflow.showCreateIntentBox(e(this).closest("[data-id]").attr("data-id"))})),e(f).on("click",'.mc-intent-add [data-value="add"]',(function(){f.find("> div > .mc-type-text").last().after('<div class="mc-setting mc-type-text"><input type="text"></div>')})),e(f).on("click",'.mc-intent-add [data-value="previous"],.mc-intent-add [data-value="next"]',(function(){let t=f.find(".mc-first input"),i=t.val(),s="next"==e(this).attr("data-value"),a=MCChat.conversation.getUserMessages(),n=a.length;for(var o=0;o<n;o++)if(a[o].message==i&&(s&&o<n-1||!s&&o>0)){o+=s?1:-1,t.val(a[o].message),f.attr("data-message-id",a[o].id),it.openAI.generateQuestions(a[o].message);break}})),e(f).on("click",".mc-send",(function(){it.dialogflow.submitIntent(this)})),e(f).on("input",".mc-search-btn input",(function(){it.dialogflow.searchIntents(e(this).val())})),e(f).on("click",".mc-search-btn i",(function(){MCF.searchClear(this,(()=>{it.dialogflow.searchIntents(e(this).val())}))})),e(f).on("click","#mc-intent-preview",(function(){it.dialogflow.previewIntentDialogflow(f.find("#mc-intents-select").val())})),e(f).on("click","#mc-qea-preview",(function(){it.dialogflow.previewIntent(f.find("#mc-qea-select").val())})),e(f).on("change","#mc-intents-select",(function(){let t=e(this).val();f.find(".mc-bot-response").css("opacity",t?.5:1).find("textarea").val(t?it.dialogflow.getIntent(t).messages[0].text.text[0]:it.dialogflow.original_response),f.find("#mc-train-chatbots").val(t?"dialogflow":"")})),e(f).on("change","#mc-qea-select",(function(){let t=e(this).val();f.find(".mc-bot-response").setClass("mc-disabled",t).find("textarea").val(t?it.dialogflow.qea[t][1]:it.dialogflow.original_response),f.find("#mc-train-chatbots").val(t?"dialogflow":"")})),e(f).on("change","textarea",(function(){clearTimeout(z),z=setTimeout((()=>{it.dialogflow.original_response=f.find("textarea").val()}),500)})),e(f).on("change","#mc-train-chatbots",(function(){f.find(".mc-type-text:not(.mc-first)").setClass("mc-hide","open-ai"==e(this).val())})),e(oe).on("click","li",(function(t){let i=e(this).parent().parent();return e(this).data("id")==i.find(" > p").attr("data-id")?(setTimeout((()=>{e(this).mcActive(!1)}),100),!0):MCChat.conversation?(i.mcLoading()||Me(`${Pe("All agents assigned to the new department will be notified. The new department will be")} ${e(this).html()}.`,"alert",(()=>{let t=e(this).data("id");i.mcLoading(!0),rt.assignDepartment(MCChat.conversation.id,t,(()=>{rt.setActiveDepartment(t),i.mcLoading(!1)}))})),t.preventDefault(),!1):(e(this).parent().mcActive(!1),t.preventDefault(),!1)})),e(a).on("click","#conversation-agent li",(function(t){let i=e(this).parent().parent(),s=e(this).data("id");return s==i.find(" > p").attr("data-value")||(MCChat.conversation?(i.mcLoading()||Me(`${Pe("The new agent will be")} ${e(this).html()}.`,"alert",(()=>{i.mcLoading(!0),rt.assignAgent(MCChat.conversation.id,s,(()=>{rt.setActiveAgent(s),i.mcLoading(!1)}))})),t.preventDefault(),!1):(e(this).parent().mcActive(!1),t.preventDefault(),!1))})),d.on("click","> i,.mc-edit-note",(function(i){let s=!!e(this).hasClass("mc-edit-note")&&e(this).closest("[data-id]");if(ct.genericPanel("notes",s?"Edit note":"Add new note",`<div class="mc-setting mc-type-textarea"><textarea${s?' data-id="'+s.attr("data-id")+'"':""} placeholder="${Pe("Write here your note...")}">${s?s.find(".mc-note-text").html().replace(/<a\s+href="([^"]*)".*?>(.*?)<\/a>/gi,"$1").replaceAll("<br>","\n"):""}</textarea></div>`,[[s?"Update note":"Add note",s?"check":"plus"]]),it.is("dialogflow")&&MC_ADMIN_SETTINGS.note_data_scrape){let e="";for(var a in MC_ADMIN_SETTINGS.note_data_scrape)e+=`<option value="${a}">${MC_ADMIN_SETTINGS.note_data_scrape[a]}</option>`;t.find("#mc-add-note").parent().prepend(`<div id="note-ai-scraping" class="mc-setting mc-type-select"><select><option value="">${Pe("Data scraping")}</option>${e}</select></div>`)}return i.preventDefault(),!1})),e(t).on("change","#note-ai-scraping select",(function(){let i=e(this).val();i&&!Ge(e(this).parent())&&MCF.ajax({function:"data-scraping",conversation_id:MCChat.conversation.id,prompt_id:i},(i=>{if(i&&i.error)return console.log("Data scraping issue detected"),De("We encountered an issue while processing your request. Please try again or contact support if the problem persists.","error");e(this).parent().mcLoading(!1);let s=t.find(".mc-notes-box textarea");s.val((s.val()+"\n"+i).trim())}))})),d.on("click",".mc-delete-note",(function(){let t=e(this).parents().eq(1);rt.notes.delete(MCChat.conversation.id,t.attr("data-id"),(e=>{!0===e?t.remove():MCF.error(e)}))})),e(t).on("click","#mc-add-note, #mc-update-note",(function(){let i=e(this).parent().parents().eq(1).find("textarea"),s=i.val(),a=i.attr("data-id");if(0==s.length)MCForm.showErrorMessage(t.find(".mc-notes-box"),"Please write something...");else{if(Ge(this))return;s=MCF.escape(s),rt.notes.add(MCChat.conversation.id,MC_ACTIVE_AGENT.id,MC_ACTIVE_AGENT.full_name,s,(n=>{Number.isInteger(n)||!0===n?(e(this).mcLoading(!1),t.mcHideLightbox(),a&&d.find(`[data-id="${a}"]`).remove(),rt.notes.update([{id:a||n,conversation_id:MCChat.conversation.id,user_id:MC_ACTIVE_AGENT.id,name:MC_ACTIVE_AGENT.full_name,message:s}],!0),i.val(""),De(a?"Note successfully updated.":"New note successfully added.")):MCForm.showErrorMessage(n)}),a)}})),u.on("click","> i",(function(e){let t=rt.tags.getAll(MCChat.conversation.details.tags);MCChat.conversation.details.tags;return ct.genericPanel("tags","Manage tags",t?'<div class="mc-tags-cnt">'+t+"</div>":"<p>"+Pe("Add tags from Settings > Admin > Tags.")+"</p>",["Save tags"]),e.preventDefault(),!1})),e(t).on("click",".mc-tags-cnt > span",(function(){e(this).toggleClass("mc-active")})),e(t).on("click","#mc-add-tag",(function(){e('<input type="text">').insertBefore(this)})),e(t).on("click","#mc-save-tags",(function(){if(Ge(this))return;let i=t.find(".mc-tags-box").find("span.mc-active").map((function(){return e(this).attr("data-value")})).toArray(),s=MCChat.conversation.id;MCF.ajax({function:"update-tags",conversation_id:s,tags:i},(a=>{if(e(this).mcLoading(!1),!0===a&&(rt.tags.update(i),MCChat.conversation&&s==MCChat.conversation.id)){let t=rt.filters()[3],a=tt(s);if(MCChat.conversation.set("tags",i),t&&!i.includes(t))a.remove(),rt.clickFirst();else if(MC_ADMIN_SETTINGS.tags_show){let t=a.find(".mc-tags-area"),s=rt.tags.codeLeft(i);t.length?t.replaceWith(s):e(s).insertAfter(a.find(".mc-name"))}}t.mcHideLightbox(),De(!0===a?"Tags have been successfully updated.":a)}))})),e(g).on("click","span",(function(){MCChat.insertText(e(this).text()),g.html("")})),e(g).on("mouseover","span",(function(){z=setTimeout((()=>{e(this).addClass("mc-suggestion-full")}),2500)})),e(g).on("mouseout","span",(function(){clearTimeout(z),g.find("span").removeClass("mc-suggestion-full")})),e(a).on("click",".mc-list .mc-menu > li",(function(){let t=e(this).closest("[data-id]"),i=t.attr("data-id"),s=MCChat.conversation.getMessage(i).get("user_type"),a=e(this).attr("data-value");switch(a){case"delete":MCChat.user_online?MCF.ajax({function:"update-message",message_id:i,message:"",attachments:[],payload:{event:"delete-message"}},(()=>{MCChat.conversation.deleteMessage(i),t.remove()})):MCChat.deleteMessage(i);break;case"translation":case"original":let e="translation"==a,n=(MCF.isAgent(s),MCChat.conversation.getMessage(i)),o=["translation","translation-language","original-message","original-message-language"],r=o.map((e=>n.payload(e))).concat(n.details.message);n.set("message",e?n.payload("translation")||n.get("message"):n.payload("original-message")||n.get("message")),o.forEach((e=>delete n.details.payload[e])),t.replaceWith(n.getCode().replace('mc-menu">',`mc-menu"><li data-value="${e?"original":"translation"}">${Pe(e?"View original message":"View translation")}</li>`)),o.forEach(((e,t)=>n.payload(e,r[t]))),n.details.message=r[4]}})),e(a).on("click",".mc-filter-btn i",(function(){e(this).parent().toggleClass("mc-active")})),e(a).on("click",".mc-filter-star",(function(){e(this).parent().find("li").mcActive(!1),e(this).parent().find(`li[data-value="${e(this).mcActive()?"":e(this).attr("data-value")}"]`).last().mcActive(!0).click(),e(this).toggleClass("mc-active")})),p.on("click","#mc-attachments-filter li",(function(){let t=p.find("a:not(.mc-collapse-btn)"),i=e(this).attr("data-value");t.each((function(){e(this).setClass("mc-hide",i&&MCF.getFileType(e(this).attr("href"))!=i)})),qe(p,160)})),e(t).on("click",".mc-cc-box #mc-save-changes",(function(){let i=t.find(".mc-cc-box .repeater-item input").map((function(){return e(this).val()})).get().join(",");Ge(this),MCF.ajax({function:"update-conversation-extra",conversation_id:MCChat.conversation.id,extra:i||"NULL"},(()=>{MCChat.conversation.set("extra",i),rt.cc(i.split(",")),e(this).mcLoading(!1),t.mcHideLightbox()}))})),MCF.getURL("user")&&(i.find(".mc-admin-nav #mc-users").click(),setTimeout((()=>{lt.show(MCF.getURL("user"))}),500)),e(m).on("click","th :checkbox",(function(){m.find("td :checkbox").prop("checked",e(this).prop("checked"))})),e(m).on("click",":checkbox",(function(){let e=v.find('[data-value="delete"]');m.find("td input:checked").length?e.removeAttr("style"):e.hide()})),e(S).on("click","li",(function(){ot.filter(e(this).data("type"))})),e(_).on("click","li",(function(){let t=_.closest(".mc-filter-btn");setTimeout((()=>{ot.get((e=>{ot.populate(e)})),t.attr("data-badge",_.toArray().reduce(((t,i)=>t+!!e(i).find("li.mc-active").data("value")),0))}),100),t.mcActive(!1)})),e(v).on("input",".mc-search-btn input",(function(){ot.search(this)})),e(v).on("click",".mc-search-btn i",(function(){MCF.searchClear(this,(()=>{ot.search(e(this).next())}))})),e(m).on("click","th:not(:first-child)",(function(){let t=e(this).hasClass("mc-order-asc")?"DESC":"ASC";e(this).toggleClass("mc-order-asc"),e(this).siblings().mcActive(!1),e(this).mcActive(!0),ot.sort(e(this).data("field"),t)})),e(m).parent().on("scroll",(function(){!Ie&&!ot.search_query&&He(this,!0)&&ve&&(Ie=!0,v.append('<div class="mc-loading-global mc-loading mc-loading-pagination"></div>'),ot.get((e=>{if(setTimeout((()=>{Ie=!1}),500),ve=e.length){let i="";for(var t=0;t<ve;t++){let s=new MCUser(e[t],e[t].extra);i+=ot.getRow(s),ge[s.id]=s}be++,m.find("tbody").append(i)}v.find(" > .mc-loading-pagination").remove()}),!1,!0))})),e(w).on("click",".mc-delete",(function(){if(MC_ACTIVE_AGENT.id==Re().id)return De("You cannot delete yourself.","error");Me("This user will be deleted permanently including all linked data, conversations, and messages.","alert",(function(){ot.delete(Re().id)}))})),e(m).on("click","td:not(:first-child)",(function(){lt.show(e(this).parent().attr("data-user-id"))})),e(y).on("click",".mc-top-bar .mc-edit",(function(){lt.showEdit(Re())})),e(v).on("click",".mc-new-user",(function(){w.addClass("mc-user-new"),w.find(".mc-top-bar .mc-profile span").html(Pe("Add new user")),w.find(".mc-top-bar .mc-save").html(`<i class="mc-icon-check"></i>${Pe("Add user")}`),w.find("input,select,textara").removeClass("mc-error"),w.removeClass("mc-cloud-admin"),"admin"==MC_ACTIVE_AGENT.user_type&&w.find("#user_type").find("select").html(`<option value="user">${Pe("User")}</option><option value="agent">${Pe("Agent")}</option><option value="admin">${Pe("Admin")}</option>`),lt.clear(w),lt.boxClasses(w),lt.updateRequiredFields("user"),w.mcShowLightbox()})),e(w).on("click",".mc-save",(function(){if(Ge(this))return;let s=!!w.hasClass("mc-user-new"),n=w.attr("data-user-id"),o=lt.getAll(w.find(".mc-details")),r=lt.getAll(w.find(".mc-additional-details"));return e.map(o,(function(e,t){o[t]=e[0]})),lt.errors(w)?(lt.showErrorMessage(w,MCF.isAgent(w.find("#user_type :selected").val())?"First name, last name, password and a valid email are required. Minimum password length is 8 characters.":w.find("#password").val().length<8?"Minimum password length is 8 characters.":"First name is required."),void e(this).mcLoading(!1)):MC_ACTIVE_AGENT.id==Re().id&&"agent"==o.user_type[0]&&"admin"==MC_ACTIVE_AGENT.user_type?(lt.showErrorMessage(w,"You cannot change your status from admin to agent."),void e(this).mcLoading(!1)):(o.user_type||(o.user_type="user"),void MCF.ajax({function:s?"add-user":"update-user",user_id:n,settings:o,settings_extra:r},(o=>{if(MCF.errorValidation(o,"duplicate-email")||MCF.errorValidation(o,"duplicate-phone"))return lt.showErrorMessage(w,`This ${MCF.errorValidation(o,"duplicate-email")?"email":"phone number"} is already in use.`),void e(this).mcLoading(!1);s&&(n=o,Re(new MCUser({id:n}))),Re().update((()=>{ge[n]=Re(),s?(lt.clear(w),ot.update()):(ot.updateRow(Re()),a.mcActive()&&rt.updateUserDetails(),n==MC_ACTIVE_AGENT.id&&(MCF.loginCookie(o[1]),MC_ACTIVE_AGENT.full_name=Re().name,MC_ACTIVE_AGENT.profile_image=Re().image,i.find(".mc-account").setProfile())),s&&w.find(".mc-profile").setProfile(Pe("Add new user")),e(this).mcLoading(!1),s||t.mcHideLightbox(),De(s?"New user added":"User updated")})),MCF.event("MCUserUpdated",{new_user:s,user_id:n})})))})),e(w).on("change","#user_type",(function(){let t=e(this).find("option:selected").val();lt.boxClasses(w,t),lt.updateRequiredFields(t)})),e(y).on("click",".mc-user-conversations li",(function(){rt.open(e(this).attr("data-conversation-id"),e(this).find("[data-user-id]").attr("data-user-id"))})),e(y).on("click",".mc-start-conversation",(function(){rt.open(-1,Re().id),rt.openConversation(-1,Re().id),xe&&rt.mobileOpenConversation()})),e(y).on("click",".mc-top-bar [data-value]",(function(){rt.showDirectMessageBox(e(this).attr("data-value"),[Re().id])})),e(v).on("click",".mc-top-bar [data-value]",(function(){let t=e(this).data("value"),i=ot.getSelected();switch(t){case"whatsapp":Ze(i);break;case"message":case"custom_email":case"sms":rt.showDirectMessageBox(t,i);break;case"csv":ot.csv();break;case"delete":if(i.includes(MC_ACTIVE_AGENT.id))return De("You cannot delete yourself.","error");Me("All selected users will be deleted permanently including all linked data, conversations, and messages.","alert",(()=>{ot.delete(i),e(this).hide(),m.find("th:first-child input").prop("checked",!1)}))}})),e(t).on("click",".mc-send-direct-message",(function(){let i=e(this).attr("data-type")?e(this).attr("data-type"):h.attr("data-type"),s="whatsapp"==i,a=s?t.find("#mc-whatsapp-send-template-box"):h,n=a.find(".mc-direct-message-subject input").val(),o=s?"":a.find("textarea").val(),r=a.find(".mc-direct-message-users").val().replace(/ /g,""),l=!1,c=!1,d=[],u=!1;if(s){let e=a.find("#mc-whatsapp-send-template-list");l=e.val(),c=e.find("option:selected").attr("data-languages"),u=e.find("option:selected").attr("data-phone-id"),d=["header","body","button"].map((e=>a.find(`#mc-whatsapp-send-template-${e}`).val()))}if(MCForm.errors(a))MCForm.showErrorMessage(a,"Please complete the mandatory fields.");else{if(Ge(this))return;let s=[];if((o.includes("recipient_name")||d.join("").includes("recipient_name"))&&s.push("first_name","last_name"),(o.includes("recipient_email")||d.join("").includes("recipient_email"))&&s.push("email"),"message"==i)MCF.ajax({function:"direct-message",user_ids:r,message:o},(l=>{e(this).mcLoading(!1);let c=MC_ADMIN_SETTINGS.notify_user_email,d=MC_ADMIN_SETTINGS.sms_active_users;if(MCF.errorValidation(l))return MCForm.showErrorMessage(a,"An error has occurred. Please make sure all user ids are correct.");(c||d)&&MCF.ajax({function:"get-users-with-details",user_ids:r,details:s.concat(c&&d?["email","phone"]:[c?"email":"phone"])},(e=>{c&&e.email.length?_e(e,"email",o,0,d?e.phone:[],"email",n):d&&e.phone.length?_e(e,"phone",o,0,[],"sms"):t.mcHideLightbox()})),De(`${MCF.slugToString(i)} sent to all users.`)}));else{let t="custom_email"==i?"email":"phone";MCF.ajax({function:"get-users-with-details",user_ids:r,details:s.concat([t])},(s=>{if(!s[t].length)return e(this).mcLoading(!1),MCForm.showErrorMessage(a,"No users found.");_e(s,t,o,0,[],i,n,l,d,c,u)}))}}})),e(v).on("click",".mc-filter-btn > i",(function(){e(this).parent().toggleClass("mc-active")})),MCF.getURL("setting")&&st.open(MCF.getURL("setting"),!0),e(k).on("click"," > .mc-tab > .mc-nav [id]",(function(){let t=e(this).attr("id").substr(4);MCF.getURL("setting")!=t&&Je("?setting="+t)})),e(t).on("click",'.mc-repeater-upload, [data-type="upload-image"] .image, [data-type="upload-file"] .mc-btn, #mc-chatbot-add-files, #mc-import-settings a, #mc-import-users a',(function(){let t="";O=this,"mc-chatbot-add-files"==e(this).attr("id")?(t=".pdf,.txt",L.find(".mc-pending").remove(),it.openAI.train.skip_files=[],H=function(){let e=q.prop("files"),t="";for(var i=0;i<e.length;i++){let s=parseInt(e[i].size/1e3);t+=`<tr class="mc-pending" data-name="${e[i].name}"><td><input type="checkbox" /></td><td>${e[i].name}<label>${Pe("Pending")}</label></td><td>${s||1} KB</td><td><i class="mc-icon-delete"></i></td></tr>`}L.append(t)}):"mc-import-settings"==e(this).parent().parent().attr("id")?(t=".json",V=t=>{Ge(this)||(MCF.ajax({function:"import-settings",file_url:t},(t=>{t?De("Settings saved. Reload to apply the changes."):Me(t),e(this).mcLoading(!1)})),V=!1)}):"mc-import-users"==e(this).parent().parent().attr("id")?(t=".csv",V=t=>{Ge(this)||(MCF.ajax({function:"import-users",file_url:t},(t=>{t?De("Users imported successfully."):Me(t),e(this).mcLoading(!1)})),V=!1)}):e(this).hasClass("image")?t=".png,.jpg,.jpeg,.gif,.webp":e(this).hasClass("mc-repeater-upload")&&(V=t=>{let i=e(this).parent();i.find(".repeater-item:last-child input").val()&&st.repeater.add(this),i.find(".repeater-item:last-child input").val(t)}),q.attr("accept",t).prop("value","").click()})),e(k).on("click",'[data-type="upload-image"] .image > i',(function(t){return MCF.ajax({function:"delete-file",path:e(this).parent().attr("data-value")}),e(this).parent().removeAttr("data-value").css("background-image",""),t.preventDefault(),!1})),e(t).on("click",".mc-repeater-add",(function(){st.repeater.add(this)})),e(t).on("click",".repeater-item > i",(function(){setTimeout((()=>{st.repeater.delete(this)}),100)})),st.initColorPicker(),e(k).find('[data-type="color"]').focusout((function(){let t=e(this).find("input").val();"rgb(255, 255, 255)"==t&&["color-admin-1","color-1"].includes(e(this).attr("id"))&&(t=""),setTimeout((()=>{e(this).find("input").val(t),e(this).find(".color-preview").css("background-color",t)}),300),st.set(e(this).attr("id"),[t,"color"])})),e(k).on("click",".mc-type-color .input i",(function(t){e(this).parent().find("input").removeAttr("style").val("")})),e(k).on("click",".mc-color-palette span",(function(){let t=e(this).mcActive();e(this).closest(".mc-repeater").find(".mc-active").mcActive(!1),e(this).mcActive(!t)})),e(k).on("click",".mc-color-palette ul li",(function(){e(this).parent().parent().attr("data-value",e(this).data("value")).find("span").mcActive(!1)})),e(k).on("click",'[data-type="select-images"] .input > div',(function(){e(this).siblings().mcActive(!1),e(this).mcActive(!0)})),e(k).on("click",".mc-select-checkbox-input",(function(){e(this).toggleClass("mc-active")})),e(k).on("click",".mc-select-checkbox input",(function(){let t=e(this).closest("[data-type]");t.find(".mc-select-checkbox-input").val(st.get(t)[1].join(", "))})),e(k).on("click",".mc-save-changes",(function(){st.save(this)})),e(k).on("change",'#saved-replies [data-id="reply-name"], [data-id="rich-message-name"]',(function(){e(this).val(e(this).val().replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,"").replace(/ /g,""))})),e(k).on("change",'#user-additional-fields [data-id="extra-field-name"]',(function(){e(this).parent().next().find("input").val(MCF.stringToSlug(e(this).val()))})),e(k).on("click","#timetable-utc input",(function(){e(this).val()||e(this).val(Te.getTimezoneOffset()/60)})),e(k).on("click","#dialogflow-sync-btn a",(function(e){let t="https://accounts.google.com/o/oauth2/auth?scope=https%3A%2F%2Fwww.googleapis.com/auth/dialogflow%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-translation%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-language&response_type=code&access_type=offline&redirect_uri="+MC_URL+"/apps/dialogflow/functions.php&client_id={client_id}&prompt=consent";if(!MC_ADMIN_SETTINGS.cloud||"auto"!=k.find("#google-sync-mode select").val()){let i=k.find("#google-client-id input").val();return i&&k.find("#google-client-secret input").val()?window.open(t.replace("{client_id}",i)):Me("Before continuing enter Client ID and Client secret. Check the docs for more details."),e.preventDefault(),!1}if(MC_ADMIN_SETTINGS.credits)return window.open(t.replace("{client_id}",MC_ADMIN_SETTINGS.google_client_id)),e.preventDefault(),!1})),e(k).on("click","#dialogflow-redirect-url-btn a",(function(e){return Me(`<pre>${MC_URL}/apps/dialogflow/functions.php</pre>`),e.preventDefault(),!1})),e(k).on("click","#dialogflow-saved-replies a",(function(t){return Me("","alert",(()=>{Ge(this)||MCF.ajax({function:"dialogflow-saved-replies"},(()=>{e(this).mcLoading(!1)}))})),t.preventDefault(),!1})),e(k).on("click","#test-email-user a, #test-email-agent a",(function(){let t=e(this).parent().find("input").val();t&&t.indexOf("@")>0&&!Ge(this)&&MCF.ajax({function:"send-test-email",to:t,email_type:"test-email-user"==e(this).parent().parent().attr("id")?"user":"agent"},(t=>{Me(!0===t?"The message has been sent.":t,"info"),e(this).mcLoading(!1)}))})),e(k).on("click","#email-server-troubleshoot a",(function(e){return k.find("#test-email-user input").val(MC_ACTIVE_AGENT.email).next().click()[0].scrollIntoView({behavior:"smooth"}),e.preventDefault(),!1})),e(k).on("click","#test-sms-user a, #test-sms-agent a",(function(){let t=e(this).parent().find("input").val();t&&!Ge(this)&&MCF.ajax({function:"send-sms",message:"Hello World!",to:t},(t=>{Me(t&&["sent","queued"].includes(t.status)?"The message has been sent.":`<pre>${JSON.stringify(t)}</pre>`),e(this).mcLoading(!1)}))})),e(k).on("click",".mc-timetable > div > div > div",(function(){let t=e(this).closest(".mc-timetable"),i=e(this).mcActive();if(e(t).find(".mc-active").mcActive(!1),i)e(this).mcActive(!1).find(".mc-custom-select").remove();else{let i=e(t).find("> .mc-custom-select").html();e(t).find(" > div .mc-custom-select").remove(),e(this).append(`<div class="mc-custom-select">${i}</div>`).mcActive(!0)}})),e(k).on("click",".mc-timetable .mc-custom-select span",(function(){let t=[e(this).html(),e(this).attr("data-value")];e(this).closest(".mc-timetable").find("> div > div > .mc-active").html(t[0]).attr("data-value",t[1]),e(this).parent().mcActive(!1)})),e(k).on("click","#system-requirements a",(function(e){let t="";return MCF.ajax({function:"system-requirements"},(e=>{for(var i in e)t+=`<div class="mc-input"><span>${Pe(MCF.slugToString(i))}</span><div${e[i]?' class="mc-green"':""}>${e[i]?Pe("Success"):Pe("Error")}</div></div>`;Ue(!1),ct.genericPanel("requirements","System requirements",t)})),Ue(),e.preventDefault(),!1})),e(k).on("click","#mc-path a",(function(e){return MCF.ajax({function:"path"},(e=>{Me(`<pre>${e}</pre>`)})),e.preventDefault(),!1})),e(k).on("click","#mc-url a",(function(e){return Me(`<pre>${MC_URL}</pre>`),e.preventDefault(),!1})),e(k).on("click","#delete-leads a",(function(t){return e(this).mcLoading()||Me("All leads, including all the linked conversations and messages, will be deleted permanently.","alert",(()=>{e(this).mcLoading(!0),MCF.ajax({function:"delete-leads"},(()=>{Me("Leads and conversations successfully deleted."),e(this).mcLoading(!1)}))})),t.preventDefault(),!1})),e(k).on("click","#mc-export-settings a",(function(t){if(t.preventDefault(),!Ge(this))return MCF.ajax({function:"export-settings"},(t=>{Ke(t,"mc-export-settings-close","Settings exported"),e(this).mcLoading(!1)})),!1})),e(t).on("click","#mc-export-settings-close .mc-close, #mc-export-users-close .mc-close, #mc-export-report-close .mc-close",(function(){MCF.ajax({function:"delete-file",path:t.find(".mc-dialog-box p pre").html()})})),MC_ADMIN_SETTINGS.cloud||(e(k).on("change","#push-notifications-provider select",(function(){let t="pusher"==e(this).val();st.visibility(0,t),MCF.ajax({function:"update-sw",url:t?"https://js.pusher.com/beams/service-worker.js":"https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.sw.js"})})),e(k).on("click","#push-notifications-sw-path a",(function(e){let t=Ye(location.href.replace(location.host,"")).replace("admin.php","");return t.includes("?")&&(t=t.substring(0,t.indexOf("?"))),Me("<pre>"+t+"</pre>"),e.preventDefault(),!1}))),e(k).on("click","#push-notifications-btn a",(function(e){return MC_ADMIN_SETTINGS.cloud||"onesignal"==k.find("#push-notifications-provider select").val()?typeof OneSignal!=Ne?OneSignal.Slidedown.promptPush({force:!0}):MCF.serviceWorker.initPushNotifications():Notification.requestPermission(),e.preventDefault(),!1})),e(k).on("input","#mc-search-settings",(function(){let t=e(this).val().toLowerCase();MCF.search(t,(()=>{let i="",s=k.find(".mc-search-dropdown-items");if(t.length>2){let s=k.find("> .mc-tab > .mc-nav li").map((function(){return e(this).text().trim()})).get();k.find(".mc-setting").each((function(){let a=e(this).attr("data-keywords"),n=e(this).attr("id");if(a&&a.includes(t)||n&&n.replaceAll("-","-").includes(t)||e(this).find(".mc-setting-content").text().toLowerCase().includes(t)){let t=e(this).parent().index();i+=`<div data-tab-index="${t}" data-setting="${e(this).attr("id")}">${s[t]} > ${e(this).find("h2").text()}</div>`}}))}s.html(i),s.outerHeight()>e(window).height()-100?(s.css("max-height",e(window).height()-100),s.addClass("mc-scroll-area")):s.removeClass("mc-scroll-area")}))})),e(k).on("click",".mc-search-dropdown-items div",(function(){let t=e(this).attr("data-tab-index"),i=k.find("> .mc-tab > .mc-nav li").eq(t);i.click(),i.get(0).scrollIntoView(),k.find("#"+e(this).attr("data-setting"))[0].scrollIntoView()})),e(k).on("click","#slack-button a",(()=>(window.open("https://app.masichat.com/synch/?service=slack&plugin_url="+MC_URL+Xe()),!1))),e(k).on("click","#slack-test a",(function(t){if(!Ge(this))return MCF.ajax({function:"send-slack-message",user_id:!1,full_name:MC_ACTIVE_AGENT.full_name,profile_image:MC_ACTIVE_AGENT.profile_image,message:"Lorem ipsum dolor sit amete consectetur adipiscing elite incidido labore et dolore magna aliqua.",attachments:[["Example link",MC_URL+"/media/user.svg"],["Example link two",MC_URL+"/media/user.svg"]],channel:k.find("#slack-channel input").val()},(t=>{MCF.errorValidation(t)?"slack-not-active"==t[1]?Me("Please first activate Slack, then save the settings and reload the admin area."):Me("Error. Response: "+JSON.stringify(t)):Me("success"==t[0]?"Slack message successfully sent. Check your Slack app!":JSON.stringify(t)),e(this).mcLoading(!1)})),t.preventDefault(),!1})),e(k).on("click","#tab-slack",(function(){let e=k.find("#slack-agents .input");e.html('<div class="mc-loading"></div>'),MCF.ajax({function:"slack-users"},(t=>{let i="";if(MCF.errorValidation(t,"slack-token-not-found"))i=`<p>${Pe("Synchronize Slack and save changes before linking agents.")}</p>`;else{let e='<option value="-1"></option>';for(var s=0;s<t.agents.length;s++)e+=`<option value="${t.agents[s].id}">${t.agents[s].name}</option>`;for(s=0;s<t.slack_users.length;s++)i+=`<div data-id="${t.slack_users[s].id}"><label>${t.slack_users[s].name}</label><select>${e}</select></div>`}e.html(i),st.set("slack-agents",[t.saved,"double-select"])}))})),e(k).on("click","#slack-archive-channels a",(function(t){t.preventDefault(),Ge(this)||MCF.ajax({function:"archive-slack-channels"},(t=>{!0===t&&Me("Slack channels archived successfully!"),e(this).mcLoading(!1)}))})),e(k).on("click","#slack-channel-ids a",(function(t){t.preventDefault(),Ge(this)||MCF.ajax({function:"slack-channels",code:!0},(t=>{Me(t,"info",!1,"","",!0),e(this).mcLoading(!1)}))})),e(k).on("click",'#whatsapp-twilio-btn a, #whatsapp-twilio-get-configuartion-btn a, #sms-btn a, #wechat-btn a, #twitter-callback a, #viber-webhook a, #zalo-webhook a, [data-id="line-webhook"], #messenger-path-btn a',(function(t){let i=e(this).closest("[id]").attr("id"),s="";if(t.preventDefault(),"line"==i){if(s=e(this).closest(".repeater-item").find('[data-id="line-secret"]').val(),!s)return;s="?line_secret="+s}return Me(`<pre>${MC_URL+("sms-btn"==i?"/include/api.php":"/apps/"+(i.includes("-")?i.substring(0,i.indexOf("-")):i)+"/post.php")+s+Xe().replace("&",s?"&":"?")}</pre>`),!1})),e(k).on("click",'[data-id="telegram-numbers-button"], #viber-button a, #whatsapp-360-button a',(function(t){let i,s={"telegram-button":["#telegram-token input","telegram-synchronization",["result",!0]],"viber-button":["#viber-token input","viber-synchronization",["status_message","ok"]],"whatsapp-360-button":["#whatsapp-360-key input","whatsapp-360-synchronization",["success",!0]]},a=e(this).parent().attr("id"),n=!1;if(a)i=k.find(s[a][0]).val().trim();else{let t={"telegram-button":"telegram-numbers-token"};a={"telegram-numbers-button":"telegram-button"}[e(this).attr("data-id")],i=e(this).closest(".repeater-item").find(`[data-id="${t[a]}"]`).val().trim(),n=!0}return s=s[a],t.preventDefault(),!i||Ge(this)||MCF.ajax({function:s[1],token:i,cloud_token:Xe(),is_additional_number:n},(t=>{Me(s[2][0]in t&&t[s[2][0]]==s[2][1]?"Synchronization completed.":JSON.stringify(t)),e(this).mcLoading(!1)})),!1})),e(k).on("click","#whatsapp-test-template a",(function(t){t.preventDefault();let i=e(this).parent().find("input").val();if(i&&!Ge(this))return MCF.ajax({function:"whatsapp-send-template",to:i},(t=>{Me(t?"error"in t?t.error.message?t.error.message:t.error:"Message sent, check your WhatsApp!":t),e(this).mcLoading(!1)})),!1})),e(k).on("click","#twitter-subscribe a",(function(t){return t.preventDefault(),Ge(this)||MCF.ajax({function:"twitter-subscribe",cloud_token:Xe()},(t=>{Me(!0===t?"Synchronization completed.":JSON.stringify(t)),e(this).mcLoading(!1)})),!1})),MC_ADMIN_SETTINGS.cloud||e(k).on("click","#messenger-sync-btn a",(()=>(window.open("https://app.masichat.com/synch/?service=messenger&plugin_url="+MC_URL+Xe()),!1))),e(k).on("change","#messenger-sync-mode select",(function(){st.visibility(1,"manual"!=e(this).val())})),e(k).on("click","#messenger-unsubscribe a",(function(t){return t.preventDefault(),Me("","alert",(()=>{if(Ge(this))return!1;MCF.ajax({function:"messenger-unsubscribe"},(t=>{e(this).mcLoading(!1),t?Me(JSON.stringify(t)):(k.find("#messenger-pages .repeater-item > i").click(),setTimeout((()=>{st.save(),Me("Operation successful.")}),300))}))})),!1})),e(k).on("change","#open-ai-mode select",(function(){st.visibility(2,"assistant"!=e(this).val())})),e(k).on("click","#wp-sync a",(function(t){return t.preventDefault(),Ge(this)||it.wordpress.ajax("wp-sync",{},(t=>{!0===t||"1"===t?(ot.update(),Me("WordPress users successfully imported.")):Me("Error. Response: "+JSON.stringify(t)),e(this).mcLoading(!1)})),!1})),e("body").on("click","#wp-admin-bar-logout",(function(){MCF.logout(!1)})),e(k).on("click","#whatsapp-clear-flows a",(function(e){e.preventDefault(),MCF.ajax({function:"whatsapp-clear-flows"})})),e(k).on("click","#tab-translations",(function(){let e=k.find(".mc-translations > .mc-nav > ul");if(!e.html()){let i="";for(var t in MC_LANGUAGE_CODES)"en"!=t&&(i+=`<li data-code="${t}"><img src="${MC_URL}/media/flags/${t}.png" />${MC_LANGUAGE_CODES[t]}</li>`);e.html(i)}})),e(k).on("click",".mc-translations .mc-nav li",(function(){st.translations.load(e(this).data("code"))})),e(k).on("click",".mc-translations .mc-menu-wide li",(function(){k.find(`.mc-translations [data-area="${e(this).data("value")}"]`).mcActive(!0).siblings().mcActive(!1)})),e(k).on("click",".mc-add-translation",(function(){k.find(".mc-translations-list > .mc-active").prepend(`<div class="mc-setting mc-type-text mc-new-translation"><input type="text" placeholder="${Pe("Enter original text...")}"><input type="text" placeholder="${Pe("Enter translation...")}"></div></div>`)})),e(k).on("input",".mc-search-translation input",(function(){let t=e(this).val().toLowerCase();MCF.search(t,(()=>{t.length>1&&k.find(".mc-translations .mc-content > .mc-active label").each((function(){let i=e(this).html().toLowerCase();if(i.includes(t)&&i!=Z){let t=k.find(".mc-scroll-area");return t[0].scrollTop=0,t[0].scrollTop=e(this).position().top-80,Z=i,!1}}))}))})),e(k).on("click",'[data-id="email-piping-sync"]',(function(t){Ge(this)||(MCF.ajax({function:"email-piping",force:!0},(t=>{Me(!0===t?"Syncronization completed.":t),e(this).mcLoading(!1)})),t.preventDefault())})),e(k).on("click","#tab-automations",(function(){st.automations.get((()=>{st.automations.populate(),Ue(!1)}),!0),Ue()})),e(t).on("click",".mc-add-condition",(function(){st.automations.addCondition(e(this).prev())})),e(t).on("change",".mc-condition-1 select",(function(){st.automations.updateCondition(this)})),e(t).on("change",".mc-condition-2 select",(function(){e(this).parent().next().setClass("mc-hide",["is-set","is-not-set"].includes(e(this).val()))})),e(A).on("click","li",(function(){st.automations.populate(e(this).data("value"))})),e(B).on("click","li",(function(){st.automations.show(e(this).attr("data-id"))})),e(x).on("click",".mc-add-automation",(function(){st.automations.add()})),e(B).on("click","li i",(function(){Me("The automation will be deleted permanently.","alert",(()=>{st.automations.delete(this)}))}));let Ce=["Settings > {R} won't work if Settings > {R2} is active."];e(k).on("click",".mc-setting",(function(t){let i=e(this).attr("id");switch(e(this).hasClass("mc-type-multi-input")&&(i=e(t.target).parent().attr("id")),i){case"close-chat":case"close-message":k.find("#close-active input").is(":checked")&&k.find("#close-chat input").is(":checked")&&De(Ce[0].replace("{R}","Messages > Close message").replace("{R2}","Chat > Close chat"),"info");break;case"chat-timetable":case"follow-message":case"queue":case"routing":("queue"==i&&k.find("#queue-active input").is(":checked")||"routing"==i&&k.find("#routing input").is(":checked")||"follow-message"==i&&k.find("#follow-active input").is(":checked")||"chat-timetable"==i&&k.find("#chat-timetable-active input").is(":checked"))&&k.find("#dialogflow-human-takeover-active input").is(":checked")&&De("Since Settings > Artificial Intelligence > Human takeover is active, this option will only take effect during human takeover.","info");break;case"notify-agent-email":case"push-notifications":case"sms":("sms"==i&&k.find("#sms-active-agents input").is(":checked")||"push-notifications"==i&&k.find("#push-notifications-active input").is(":checked")||"notify-agent-email"==i&&k.find("#notify-agent-email input").is(":checked"))&&k.find("#dialogflow-human-takeover-active input").is(":checked")&&De("Since Settings > Artificial Intelligence > Human takeover is active, notifications will be sent only after the human takeover.","info");break;case"privacy":k.find("#privacy-active input").is(":checked")&&k.find("#registration-required select").val()&&De(Ce[0].replace("{R}","Messages > Privacy message").replace("{R2}","Users > Require registration"),"info");break;case"google-multilingual-translation":e(t.target).is(":checked")&&k.find("#open-ai-active input").is(":checked")&&!k.find("#open-ai-training-data-language select").val()&&De("If your OpenAI training data isn't in English, set the default language under OpenAI > Training data language.","info")}})),e(S).on("click",'[data-type="online"]',(function(){MC_ADMIN_SETTINGS.visitors_registration||De("Settings > Users > Register all visitors must be enabled to see online users.","info")})),e(k).on("click","#dialogflow-active,#dialogflow-welcome,#dialogflow-departments",(function(e){De("Warning! We will stop supporting Dialogflow by the end of 2025. All its features will be available in Masi Chat through OpenAI. Please use OpenAI instead of Dialogflow.","info")})),e(k).on("change","#registration-required select",(function(){let t=e(this).val();["registration-login"].includes(t)&&!k.find('[id="reg-email"] input').is(":checked")&&De("The email field is required to activate the login form.","info"),t&&k.find("#privacy-active input").is(":checked")&&De(Ce[0].replace("{R}","Messages > Privacy message").replace("{R2}","Users > Require registration"),"info")})),e(E).on("click","#mc-flow-add",(function(){ct.genericPanel("flow-add","Enter the flow name",'<div class="mc-setting"><input type="text"></div>',["Add new flow"])})),e(t).on("click","#mc-add-new-flow",(function(){it.openAI.flows.set(t.find(".mc-flow-add-box input").val().replace(/[^a-zA-Z\u00C0-\u1FFF\u2C00-\uD7FF\uAC00-\uD7A3]/g,"")),t.mcHideLightbox()})),e(P).on("click",".mc-flow-block",(function(){P.find(".mc-flow-block,.mc-flow-add-block").mcActive(!1),e(this).mcActive(!0);let i,s=it.openAI.flows.blocks.get(),a="",n=e(this).attr("data-type"),o=`<div class="mc-title">{R}</div><div data-type="repeater" class="mc-setting mc-type-repeater"><div class="input"><div class="mc-repeater">{R2}</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${Pe("Add new item")}</div></div></div>`,r=`<div class="mc-title">${Pe("Conditions")}</div><div class="mc-flow-conditions"></div><div class="mc-add-condition mc-btn mc-icon mc-btn-white"><i class="mc-icon-plus"></i>${Pe("Add condition")}</div>`,l=`<div class="mc-title">${Pe("Message")}</div><div class="mc-setting"><textarea placeholder="${Pe("The message sent to the user...")}">${s.message}</textarea></div>`,c=it.openAI.getCode.select_user_details();switch(n){case"start":i=`<div class="mc-title">${Pe("Start event")}</div><div class="mc-setting"><select class="mc-flow-start-select"><option value="message"${"message"==s.start?" selected":""}>${Pe("User message")}</option><option value="conversation"${"conversation"==s.start?" selected":""}>${Pe("New conversation started")}</option><option value="load"${"load"==s.start?" selected":""}>${Pe("On page load")}</option></select></div><div class="mc-title mc-title-flow-start${"message"==s.start?"":" mc-hide"}">${Pe("User message")}</div><div data-type="repeater" class="mc-setting mc-flow-start-messages mc-type-repeater"><div class="input"><div class="mc-repeater"><div class="repeater-item"><div class="mc-setting"><textarea data-id="message"></textarea></div><i class="mc-icon-close"></i></div></div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${Pe("Add message")}</div></div></div>${r}<div class="mc-title">${Pe("Disabled")}</div><div class="mc-setting"><input type="checkbox" id="mc-flow-disabled"${s.disabled?" checked":""}></div>`;break;case"button_list":s.options&&s.options.length||(s.options=[""]);for(var d=0;d<s.options.length;d++)a+=`<div class="repeater-item"><div><input data-id type="text" value="${s.options[d]}"></div><i class="mc-icon-close"></i></div>`;i=l+o.replace("{R}",Pe("Buttons")).replace("{R2}",a);break;case"message":s.attachments&&s.attachments.length||(s.attachments=[""]);for(d=0;d<s.attachments.length;d++)a+=`<div class="repeater-item"><div><input data-id type="text" value="${s.attachments[d]}" placeholder="${Pe("Enter a link...")}"></div><i class="mc-icon-close"></i></div>`;i=l+o.replace("{R}",Pe("Attachments")).replace("{R2}",a).replace("</div></div></div>",'</div><i class="mc-repeater-upload mc-btn-icon mc-icon-clip"></i></div></div>');break;case"video":i=`${l}<div class="mc-title">${Pe("Video URL")}</div><div class="mc-setting"><input type="url" placeholder="${Pe("Enter a YouTube or Vimeo link...")}" value="${s.url}"></div>`;break;case"get_user_details":s.details&&s.details.length||(s.details=[["","",!1]]);for(d=0;d<s.details.length;d++)a+=`<div class="repeater-item"><div>${c.replace(`"${s.details[d][0]}"`,`"${s.details[d][0]}" selected`)}<div class="mc-setting"><input type="text" placeholder="${Pe("Enter a description...")}" value="${s.details[d][1]}" /></div><div class="mc-setting"><label>${Pe("Required")}</label><input type="checkbox" ${s.details[d][2]?" checked":""}></div></div><i class="mc-icon-close"></i></div>`;i=l+o.replace("{R}",Pe("User details")).replace("{R2}",a).replace("mc-type-repeater","mc-type-repeater mc-repeater-block-user-details");break;case"set_data":i=it.openAI.getCode.set_data(s.data);break;case"action":i=it.openAI.getCode.actions(s.actions);break;case"rest_api":let e=["headers","save_response"];i=`<div class="mc-title">${Pe("URL")}</div><div class="mc-setting"><input type="url" class="mc-rest-api-url" value="${s.url}"></div><div class="mc-title">${Pe("Method")}</div><div class="mc-setting"><select class="mc-rest-api-method"><option value="GET"${"GET"==s.method?" selected":""}>GET</option><option value="POST"${"POST"==s.method?" selected":""}>POST</option><option value="PUT"${"PUT"==s.method?" selected":""}>PUT</option><option value="PATH"${"PATH"==s.method?" selected":""}>PATH</option><option value="DELETE"${"DELETE"==s.method?" selected":""}>DELETE</option></select></div><div class="mc-title">${Pe("Body")}</div><div class="mc-setting"><textarea placeholder="JSON">${s.body}</textarea></div>`;for(d=0;d<e.length;d++){let t=s[e[d]];t&&t.length||(t=[["",""]]),i+=`<div class="mc-title">${Pe(MCF.slugToString(e[d]))}</div><div data-type="repeater" class="mc-setting mc-type-repeater mc-repeater-block-rest-api mc-rest-api-${e[d]}"><div class="input"><div class="mc-repeater">`;for(var u=0;u<t.length;u++)i+=`<div class="repeater-item"><div>${1==d?c.replace(`"${t[u][0]}"`,`"${t[u][0]}" selected`):`<div class="mc-setting"><input type="text" placeholder="${Pe("Key")}" value="${t[u][0]}" /></div>`}<div class="mc-setting"><input type="text" placeholder="${Pe(1==d?"e.g. data.id":"Value")}" value="${t[u][1]}" /></div></div><i class="mc-icon-close"></i></div>`;i+=`</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${Pe("Add new item")}</div></div></div>`}break;case"condition":i=r}if(i+=`<div id="mc-block-delete" class="mc-btn-text"><i class="mc-icon-delete"></i>${Pe("Delete")}</div>`,ct.genericPanel("flow-block",MCF.slugToString(n),i,["Save changes"],"",!0),"start"==n||"condition"==n){Array.isArray(s.message)||(s.message=[{message:s.message}]);let e=t.find(".mc-flow-start-messages .mc-repeater"),i=st.repeater.set(s.message,e.find(".repeater-item:last-child"));st.automations.setConditions(s.conditions,t.find(".mc-flow-conditions")),i&&e.html(i)}ke=[],P.find("> div").each((function(){ke.push(e(this).find("> div")[0].scrollTop)}))})),e(t).on("click",".mc-flow-block-box #mc-save-changes",(function(){let i=it.openAI.flows.blocks.get(),s=t.find(".mc-flow-block-box");switch(i.message=s.find("textarea").val(),i.type){case"start":if(i.message=st.repeater.get(s.find(".mc-flow-start-messages .repeater-item")),i.start=s.find("select").val(),i.disabled=s.find("#mc-flow-disabled").is(":checked"),i.conditions=st.automations.getConditions(s.find(".mc-flow-conditions")),i.message.length&&i.message[0].message.trim().split(" ").length<3)return s.find(".mc-info").mcActive(!0).html(Pe("The message must contain at least 3 words."));break;case"button_list":i.options=s.find(".mc-repeater input").map((function(){return e(this).val().trim()})).get().filter((function(e){return""!=e}));break;case"message":i.attachments=s.find(".mc-repeater input").map((function(){return e(this).val().trim()})).get().filter((function(e){return""!=e}));break;case"video":i.url=s.find("input").val();break;case"get_user_details":i.details=s.find(".repeater-item").map((function(){return[[e(this).find("select").val(),e(this).find("input[type=text]").val(),e(this).find("input[type=checkbox]").is(":checked")]]})).get();break;case"action":case"set_data":i["action"==i.type?"actions":"data"]=s.find(".repeater-item").map((function(){return[[e(this).find("select").val(),e(this).find("input").length?e(this).find("input").val().replace(/https?:\/\/|["|:]/g,""):""]]})).get();break;case"rest_api":i.headers=s.find(".mc-rest-api-headers .repeater-item").map((function(){return[[e(this).find("input").eq(0).val(),e(this).find("input").eq(1).val()]]})).get(),i.save_response=s.find(".mc-rest-api-save_response .repeater-item").map((function(){return[[e(this).find("select").val(),e(this).find("input").val()]]})).get(),i.url=s.find(".mc-rest-api-url").val(),i.method=s.find(".mc-rest-api-method").val(),i.body=s.find("textarea").val(),delete i.message;break;case"condition":i.conditions=st.automations.getConditions(s.find(".mc-flow-conditions"))}it.openAI.flows.blocks.set(i),P.find("> div").each((function(){e(this).find("> div")[0].scrollTop=ke[e(this).index()]})),t.mcHideLightbox()})),e(t).on("change",".mc-repeater-block-actions select",(function(){e(this).parent().next().remove(),e(this).parent().parent().append(it.openAI.getCode.action(e(this).val(),""))})),e(t).on("change",".mc-flow-start-select",(function(){t.find(".mc-title-flow-start, .mc-flow-start-messages").setClass("mc-hide","message"!=e(this).val())})),e(P).on("mouseleave",".mc-flow-connectors > div, .mc-flow-block",(function(){P.find(".mc-flow-block-cnt").mcActive(!1),ye=!1,e(this).parent().hasClass("mc-flow-connectors")?it.openAI.flows.blocks.activateLinkedCnts(e(this).closest(".mc-flow-block")):P.find(".mc-flow-connectors > div").mcActive(!1)})),e(P).on("mouseenter",".mc-flow-connectors > div",(function(){let t=e(this).closest(".mc-flow-block").parent(),i=it.openAI.flows.blocks.getNextCntIndexes(it.openAI.flows.getActiveIndex(),t.parent().parent().index(),t.index());ye=!0,P.find("> div").eq(t.parent().parent().index()+1).find(".mc-flow-block-cnt").mcActive(!1).eq(i[e(this).index()]).mcActive(!0)})),e(P).on("mouseenter",".mc-flow-block",(function(){it.openAI.flows.blocks.activateLinkedCnts(this)})),e(P).on("click",".mc-flow-add-block",(function(){P.find(".mc-flow-block,.mc-flow-add-block").mcActive(!1),e(this).mcActive(!0);let t=it.openAI.flows.steps.get()[it.openAI.flows.blocks.getActiveCntIndex()].map((e=>e.type)),i=!t.some((e=>["message","button_list","video","get_user_details","condition"].includes(e))),s=[["set_data","Set data"],["action","Action"],["condition","Condition"],["rest_api","REST API"]],a="";for(var n=0;n<s.length;n++)t.includes(s[n][0])||(a+=`<li data-value="${s[n][0]}">${Pe(s[n][1])}</li>`);ct.genericPanel("flows-blocks-nav","",`<ul class="mc-menu">${i?`<li>${Pe("Messages")} <ul><li data-value="message">${Pe("Send message")}</li><li data-value="button_list">${Pe("Send button list")}</li><li data-value="video">${Pe("Send video")}</li></ul></li>`:""}<li>${Pe("More")} <ul>${i?`<li data-value="get_user_details">${Pe("Get user details")}</li>`:""}${a}</ul></li></ul>`)})),e(t).on("click","#mc-block-delete",(function(){it.openAI.flows.blocks.delete(),t.mcHideLightbox()})),e(U).on("click","li",(function(){it.openAI.flows.show(e(this).attr("data-value"))})),e(U).on("click","li i",(function(t){return Me("The flow will be deleted.","alert",(()=>{it.openAI.flows.delete(e(this).parent().attr("data-value"))})),t.preventDefault(),!1})),e(t).on("click",".mc-flows-blocks-nav-box [data-value]",(function(){it.openAI.flows.blocks.add(e(this).data("value")),t.mcHideLightbox()})),e(t).on("mouseenter",".mc-flow-scroll",(function(){let t=e(this).hasClass("mc-icon-arrow-left");j=setInterval((()=>{P[0].scrollLeft+=10*(t?-1:1)}),10)})),e(t).on("mouseleave",".mc-flow-scroll",(function(){clearInterval(j)})),e(E).on("click","#mc-train-chatbot",(function(t){t.preventDefault();let i,s=null,a=!1;if(console.log("MC_ADMIN_SETTINGS entire object:",JSON.stringify(MC_ADMIN_SETTINGS,null,2)),console.log("MC_ADMIN_SETTINGS['mc_open-ai-sync-mode']:",MC_ADMIN_SETTINGS?MC_ADMIN_SETTINGS["mc_open-ai-sync-mode"]:"MC_ADMIN_SETTINGS is undefined"),console.log("MC_ADMIN_SETTINGS['mc_open-ai'] object:",MC_ADMIN_SETTINGS?JSON.stringify(MC_ADMIN_SETTINGS["mc_open-ai"],null,2):"MC_ADMIN_SETTINGS is undefined"),console.log("MC_ADMIN_SETTINGS['open-ai-sync-mode']:",MC_ADMIN_SETTINGS?MC_ADMIN_SETTINGS["open-ai-sync-mode"]:"MC_ADMIN_SETTINGS is undefined"),console.log("MC_ADMIN_SETTINGS['open-ai'] object:",MC_ADMIN_SETTINGS?JSON.stringify(MC_ADMIN_SETTINGS["open-ai"],null,2):"MC_ADMIN_SETTINGS is undefined"),MC_ADMIN_SETTINGS.hasOwnProperty("open_ai_sync_mode")?(a=!0,i=MC_ADMIN_SETTINGS.open_ai_sync_mode):MC_ADMIN_SETTINGS.hasOwnProperty("mc_open-ai-sync-mode")?(a=!0,i=MC_ADMIN_SETTINGS["mc_open-ai-sync-mode"]):MC_ADMIN_SETTINGS.hasOwnProperty("open-ai-sync-mode")?(a=!0,i=MC_ADMIN_SETTINGS["open-ai-sync-mode"]):MC_ADMIN_SETTINGS.hasOwnProperty("mc_open-ai")&&Array.isArray(MC_ADMIN_SETTINGS["mc_open-ai"])&&MC_ADMIN_SETTINGS["mc_open-ai"].length>0&&Array.isArray(MC_ADMIN_SETTINGS["mc_open-ai"][0])&&MC_ADMIN_SETTINGS["mc_open-ai"][0].length>0&&"object"==typeof MC_ADMIN_SETTINGS["mc_open-ai"][0][0]&&null!==MC_ADMIN_SETTINGS["mc_open-ai"][0][0]&&MC_ADMIN_SETTINGS["mc_open-ai"][0][0].hasOwnProperty("open-ai-sync-mode")?(a=!0,i=MC_ADMIN_SETTINGS["mc_open-ai"][0][0]["open-ai-sync-mode"]):MC_ADMIN_SETTINGS.hasOwnProperty("open-ai")&&Array.isArray(MC_ADMIN_SETTINGS["open-ai"])&&MC_ADMIN_SETTINGS["open-ai"].length>0&&Array.isArray(MC_ADMIN_SETTINGS["open-ai"][0])&&MC_ADMIN_SETTINGS["open-ai"][0].length>0&&"object"==typeof MC_ADMIN_SETTINGS["open-ai"][0][0]&&null!==MC_ADMIN_SETTINGS["open-ai"][0][0]&&MC_ADMIN_SETTINGS["open-ai"][0][0].hasOwnProperty("open-ai-sync-mode")?(a=!0,i=MC_ADMIN_SETTINGS["open-ai"][0][0]["open-ai-sync-mode"]):MC_ADMIN_SETTINGS.hasOwnProperty("mc_open-ai")&&"object"==typeof MC_ADMIN_SETTINGS["mc_open-ai"]&&null!==MC_ADMIN_SETTINGS["mc_open-ai"]&&MC_ADMIN_SETTINGS["mc_open-ai"].hasOwnProperty("open-ai-sync-mode")?(a=!0,i=MC_ADMIN_SETTINGS["mc_open-ai"]["open-ai-sync-mode"]):MC_ADMIN_SETTINGS.hasOwnProperty("open-ai")&&"object"==typeof MC_ADMIN_SETTINGS["open-ai"]&&null!==MC_ADMIN_SETTINGS["open-ai"]&&MC_ADMIN_SETTINGS["open-ai"].hasOwnProperty("open-ai-sync-mode")&&(a=!0,i=MC_ADMIN_SETTINGS["open-ai"]["open-ai-sync-mode"]),a&&(s=Array.isArray(i)&&i.length>0?i[0]:Array.isArray(i)&&0===i.length?null:i),console.log("OpenAI Sync Mode Key Found:",a),console.log("OpenAI Sync Mode (raw value from settings):",i),console.log("OpenAI Sync Mode (processed value for check):",s),null==s||!1===s||""===s){let e='<div class="mc-error-message">';return e+="<p>"+Pe("Before you train the chatbot, please choose and save a Sync Mode.")+"</p>",e+="<p>"+Pe('Go to <a href="https://app.masichat.com/?setting=dialogflow">Settings > Artificial Intelligence > OpenAI > Sync mode</a> and select either:')+"</p>",e+="<ul>",e+="<li>"+Pe("Automatic")+" - "+Pe("Uses our API key (requires credits)")+' <a href="https://app.masichat.com/account/?tab=membership">'+Pe("Get credits")+"</a></li>",e+="<li>"+Pe("Manual")+" - "+Pe("Uses your own API key")+"</li>",e+="</ul>",e+="<p>"+Pe("After selecting a Sync Mode, save your settings before training.")+"</p>",e+='<p><a href="https://masichat.com/knowledge-base/#open-ai" target="_blank">'+Pe("More information")+"</a></p>",e+="</div>",Me(e,"info",!1,!1,Pe("OpenAI Configuration Required")),!1}let n="The chatbot has been successfully trained.",o=E.find('.mc-nav [data-value="conversations"]');if(Me("<br><br><br><br><br>","info",!1,"mc-embeddings-box"),MC_ADMIN_SETTINGS.cloud&&MCCloud.creditsAlert(this,t))return!1;if(Ge(this))return!1;if("flows"==E.find(".mc-menu-chatbot .mc-active").attr("data-type"))return it.openAI.flows.save((e=>{Me(n,"info",!1,!1,"Success")})),void e(this).mcLoading(!1);if(o.mcActive()){let t=[];for(var r=0;r<we.length;r++){let i=E.find(`#mc-chatbot-conversations [data-index="${r}"]`);i.length?(i=[i.find("input").val(),i.find("textarea").val()],i[0]==e("<textarea />").html(we[r].question).text()&&i[1]==e("<textarea />").html(we[r].answer).text()||t.push([we[r],!(!i[0]||!i[1])&&i])):t.push([we[r],!1])}return void(t.length?MCF.ajax({function:"open-ai-save-conversation-embeddings",qea:t},(t=>{!0===t[0]?Me(n,"info",!1,!1,"Success"):it.openAI.train.isError(t[0])||Me(t[0]),o.click(),e(this).mcLoading(!1)})):(e(this).mcLoading(!1),Me(n,"info",!1,!1,"Success")))}it.openAI.train.errors=[],it.openAI.train.training_aborted=!1;return it.openAI.train.files((t=>{it.openAI.train.training_aborted?e(this).mcLoading(!1):(it.openAI.train.urls=E.find('[data-id="open-ai-sources-url"]').map((function(){return e(this).val().trim()})).get(),it.openAI.train.extract_url=E.find('[data-id="open-ai-sources-extract-url"]').map((function(){return e(this).is(":checked")})).get(),it.openAI.train.website((t=>{it.openAI.train.training_aborted?e(this).mcLoading(!1):it.openAI.train.qea((t=>{it.openAI.train.training_aborted?e(this).mcLoading(!1):it.openAI.train.articles((t=>{if(it.openAI.train.training_aborted)return void e(this).mcLoading(!1);if(it.openAI.init(),E.find("#mc-repeater-chatbot-website .repeater-item i").click(),it.openAI.train.training_aborted)return void e(this).mcLoading(!1);let i=!1;for(let e=0;e<it.openAI.train.errors.length;e++){let t=it.openAI.train.errors[e],s="string"==typeof t?t:JSON.stringify(t);if(s.includes("500")||s.includes("internal error")||s.includes("server")){i=!0;break}}if(i)e(this).mcLoading(!1);else{if(it.openAI.train.errors.length){let t={url_404:[],url_access:[],sitemap:[],quota:[],pdf_errors:[],other:[]},i=[],s=[];for(let e=0;e<it.openAI.train.errors.length;e++){let a=it.openAI.train.errors[e],n="string"==typeof a?a:JSON.stringify(a);if(n.includes("404")||n.includes("could not be found")){let e=n.match(/URL\s+([^\s]+)\s+could not be found/i),s=e?e[1]:"";s&&!i.includes(s)?(i.push(s),t.url_404.push(n)):s||n.includes("The URL could not be found (404)")||t.url_404.push(n)}else if(n.includes("cannot be accessed")||n.includes("not accessible")||n.includes("http-error")||n.includes("403")||n.includes("500")||n.includes("server")||n.includes("internal error"))t.url_access.push(n);else if(n.includes("sitemap")||n.includes("XML")||n.includes("invalid format"))t.sitemap.push(n);else if(n.includes("character limit")||n.includes("quota")||n.includes("limit")||n.includes("chars-limit-exceeded"))t.quota.push(n);else if(n.includes("PDF")||n.includes("pdf")||n.includes("unreadable")||n.includes("empty-pdf-data")||n.includes("image-only")){let e=n.match(/PDF file "([^"]+)"/i),i=e?e[1]:"";i&&!s.includes(i)?(s.push(i),t.pdf_errors.push(n)):i||t.pdf_errors.push(n)}else t.other.push(n)}console.error("Chatbot training errors:",it.openAI.train.errors);let a='<div class="mc-error-message">';if(a+="<h3>"+Pe("Chatbot Training Completed with Issues")+"</h3>",t.url_404.length>0){a+='<div class="mc-error-section">',a+="<h4>⚠️ "+Pe("404 Not Found Errors")+" ("+i.length+")</h4>",a+="<p>"+Pe("Some URLs could not be found (404 errors). These pages may have been moved or deleted:")+"</p>",a+='<ul class="mc-error-tips">',a+="<li>"+Pe("Verify that all URLs are correct and up-to-date")+"</li>",a+="<li>"+Pe("Check for typos in the URLs")+"</li>",a+="<li>"+Pe("Consider updating links to point to existing pages")+"</li>",a+="</ul>",a+='<div class="mc-collapsible-content">',a+='<p class="mc-error-details-header">'+Pe("Not Found URLs:")+"</p>",a+="<ul>";for(let e=0;e<t.url_404.length;e++)a+="<li>"+t.url_404[e]+"</li>";a+="</ul>",a+="</div>",a+='<button class="mc-toggle-errors">'+Pe("Show/Hide URLs")+"</button>",a+="</div>"}if(t.pdf_errors.length>0){a+='<div class="mc-error-section">',a+="<h4>⚠️ "+Pe("PDF Processing Errors")+" ("+s.length+")</h4>",a+="<p>"+Pe("Some PDF files could not be processed properly:")+"</p>",a+='<ul class="mc-error-tips">',a+="<li>"+Pe("Check if PDFs contain actual text and are not image-only")+"</li>",a+="<li>"+Pe("Ensure PDFs are not password-protected or encrypted")+"</li>",a+="<li>"+Pe("Consider converting image-only PDFs to text using OCR software")+"</li>",a+="</ul>",a+='<div class="mc-collapsible-content">',a+='<p class="mc-error-details-header">'+Pe("Problem PDFs:")+"</p>",a+="<ul>";for(let e=0;e<t.pdf_errors.length;e++)a+="<li>"+t.pdf_errors[e]+"</li>";a+="</ul>",a+="</div>",a+='<button class="mc-toggle-errors">'+Pe("Show/Hide Details")+"</button>",a+="</div>"}if(t.url_access.length>0){a+='<div class="mc-error-section">',a+="<h4>⚠️ "+Pe("Content Access Issues")+" ("+t.url_access.length+")</h4>",a+="<p>"+Pe("Some content sources could not be accessed. Please check the following:")+"</p>",a+='<ul class="mc-error-tips">',a+="<li>"+Pe("Verify that all URLs are correct and publicly accessible")+"</li>",a+="<li>"+Pe("Check if the content requires authentication")+"</li>",a+="<li>"+Pe("Ensure the website is online and not returning errors")+"</li>",a+="</ul>",a+='<div class="mc-collapsible-content">',a+='<p class="mc-error-details-header">'+Pe("Inaccessible URLs:")+"</p>",a+="<ul>";for(let e=0;e<t.url_access.length;e++)a+="<li>"+t.url_access[e]+"</li>";a+="</ul>",a+="</div>",a+='<button class="mc-toggle-errors">'+Pe("Show/Hide URLs")+"</button>",a+="</div>"}if(t.sitemap.length>0){a+='<div class="mc-error-section">',a+="<h4>⚠️ "+Pe("Sitemap Issues")+" ("+t.sitemap.length+")</h4>",a+="<p>"+Pe("Issues were encountered with sitemap processing:")+"</p>",a+='<ul class="mc-error-tips">',a+="<li>"+Pe("Verify that your sitemap is a valid XML file")+"</li>",a+="<li>"+Pe("Ensure the sitemap is publicly accessible")+"</li>",a+="<li>"+Pe("Check that the sitemap contains valid URLs")+"</li>",a+="</ul>",a+='<div class="mc-collapsible-content">',a+='<p class="mc-error-details-header">'+Pe("Sitemap issues:")+"</p>",a+="<ul>";for(let e=0;e<t.sitemap.length;e++)a+="<li>"+t.sitemap[e]+"</li>";a+="</ul>",a+="</div>",a+='<button class="mc-toggle-errors">'+Pe("Show/Hide Details")+"</button>",a+="</div>"}if(t.quota.length>0){a+='<div class="mc-error-section">',a+="<h4>⚠️ "+Pe("Character Limit Issues")+" ("+t.quota.length+")</h4>",a+="<p>"+Pe("You have reached character limits for your plan:")+"</p>",a+='<ul class="mc-error-tips">',a+="<li>"+Pe("Try reducing the amount of content")+"</li>",a+="<li>"+Pe("Consider upgrading your plan for higher limits")+"</li>",a+="<li>"+Pe("Focus on the most important content first")+"</li>",a+="</ul>",a+='<div class="mc-collapsible-content">',a+='<p class="mc-error-details-header">'+Pe("Limit issues:")+"</p>",a+="<ul>";for(let e=0;e<t.quota.length;e++)a+="<li>"+t.quota[e]+"</li>";a+="</ul>",a+="</div>",a+='<button class="mc-toggle-errors">'+Pe("Show/Hide Details")+"</button>",a+="</div>"}if(t.other.length>0){a+='<div class="mc-error-section">',a+="<h4>⚠️ "+Pe("Other Issues")+" ("+t.other.length+")</h4>",a+="<p>"+Pe("Additional issues were encountered during training:")+"</p>",a+='<ul class="mc-error-tips">',a+="<li>"+Pe("Check if your OpenAI API key is valid and has sufficient credits")+"</li>",a+="<li>"+Pe("Ensure your content is in a supported format")+"</li>",a+="<li>"+Pe("Try reducing the amount of content if you're hitting size limits")+"</li>",a+="</ul>",a+='<div class="mc-collapsible-content">',a+='<p class="mc-error-details-header">'+Pe("Error details:")+"</p>",a+="<ul>";for(let e=0;e<t.other.length;e++)a+="<li>"+t.other[e]+"</li>";a+="</ul>",a+="</div>",a+='<button class="mc-toggle-errors">'+Pe("Show/Hide Details")+"</button>",a+="</div>"}a+='<div class="mc-error-note">',a+="<p>"+Pe("Note: Your chatbot has been trained with the successfully processed content.")+"</p>",a+='<p class="mc-error-help">'+Pe("For more information, check our documentation or contact support.")+"</p>",a+="</div>",a+="</div>",a+="<style>.mc-error-message { font-family: Arial, sans-serif; color: #333; }.mc-error-message h3 { color: #e67e22; margin-bottom: 15px; }.mc-error-message h4 { margin: 15px 0 10px 0; color: #555; }.mc-error-section { background: #f8f9fa; border-left: 4px solid #e67e22; padding: 15px; margin: 15px 0; border-radius: 4px; }.mc-error-tips { margin: 10px 0; padding-left: 20px; }.mc-error-tips li { margin-bottom: 5px; color: #555; }.mc-error-details-header { font-weight: bold; margin: 10px 0 5px 0; }.mc-collapsible-content { max-height: 150px; overflow-y: auto; margin: 10px 0; background: #f1f1f1; padding: 10px; border-radius: 3px; }.mc-collapsible-content ul { margin: 0; padding-left: 20px; }.mc-collapsible-content li { margin-bottom: 5px; word-break: break-all; font-family: monospace; font-size: 12px; }.mc-toggle-errors { background: #e67e22; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; font-size: 12px; }.mc-toggle-errors:hover { background: #d35400; }.mc-error-note { background: #f1f1f1; padding: 10px; border-radius: 4px; margin-top: 15px; }.mc-error-help { font-style: italic; margin-top: 10px; font-size: 13px; }</style>",a+='<div id="mc-error-toggle-script"></div>',Me(a,"info",!1,"mc-errors-list-box","Training Results",!0),setTimeout((()=>{e(".mc-toggle-errors").on("click",(function(){e(this).prev(".mc-collapsible-content").slideToggle(200)})),e(".mc-collapsible-content").hide()}),100)}else it.openAI.train.isError(t)||Me(n,"info",!1,!1,"Success");e(this).mcLoading(!1)}}))}))})))})),!1})),e(E).on("click","#mc-table-chatbot-files td i, #mc-table-chatbot-website td i, #mc-chatbot-delete-files, #mc-chatbot-delete-website, #mc-chatbot-delete-all-training, #mc-chatbot-delete-all-training-conversations",(function(){let t=e(this).is("i"),i=!!t&&e(this).closest("tr");return t&&i.hasClass("mc-pending")?(it.openAI.train.skip_files.push(i.attr("data-name")),void i.remove()):(Me("The training data will be permanently deleted.","alert",(()=>{let s=[],a=e(this).attr("id");if(t)s=[i.attr("data-url")];else if("mc-chatbot-delete-all-training"==a)s="all";else if("mc-chatbot-delete-all-training-conversations"==a)s="all-conversations";else{let t=e("mc-chatbot-delete-files"==a?L:D);t.find("input:checked").length||t.find("input").prop("checked",!0),t.find("tr").each((function(){if(e(this).find("input:checked").length)if(e(this).hasClass("mc-pending"))it.openAI.train.skip_files.push(e(this).attr("data-name")),e(this).remove();else{let t=e(this).attr("data-url");s.push(t),it.openAI.train.sitemap_processed_urls.indexOf(t)>-1&&(it.openAI.train.sitemap_processed_urls[it.openAI.train.sitemap_processed_urls.indexOf(t)]=!1)}}))}if(s.length){if(Ge(this))return;MCF.ajax({function:"open-ai-embeddings-delete",sources_to_delete:s},(t=>{it.openAI.init(),"all"==s?E.find('.mc-nav [data-value="info"]').click():"all-conversations"==s&&E.find('.mc-nav [data-value="conversations"]').click(),!0===t||!1===t?De("Training data deleted successfully."):Me("string"==typeof t?t:"Training data deleted successfully."),e(this).mcLoading(!1)}))}})),!1)})),e(E).on("click",'.mc-nav [data-value="conversations"]',(function(){let e=E.find("#mc-chatbot-conversations");Ge(e)||MCF.ajax({function:"open-ai-get-conversation-embeddings"},(t=>{if(t.length){let s="";we=t;for(var i=0;i<t.length;i++)s+=`<div class="repeater-item" data-value="${t[i].id}" data-index=${i}><div><label>${Pe("Question")}</label><input data-id="q" type="text" value="${t[i].question}" /></div><div class="mc-qea-repeater-answer"><label>${Pe("Answer")}</label><textarea data-id="a">${t[i].answer}</textarea></div><i class="mc-icon-close"></i></div>`;e.find(".mc-repeater").html(s)}else e.html(`<p class="mc-no-results">${Pe("No conversations found.")}</p>`);e.mcLoading(!1)}))})),e(E).on("click",'.mc-nav [data-value="info"]',(function(){let e=E.find("#mc-chatbot-info");Ge(e)||MCF.ajax({function:"open-ai-get-information"},(t=>{let i=[["files","Files"],["website","Website URLs"],["qea","Q&A"],["flows","Flows"],["articles","Articles"],["conversations","Conversations"]],s=`<h2>${Pe("Sources")}</h2><p>`;for(var a=0;a<i.length;a++)s+=t[i[a][0]]?`${t[i[a][0]][1]} ${Pe(i[a][1])} (${t[i[a][0]][0]} ${Pe("chars")})<br>`:"";s+=`</p><h2>${Pe("Total detected characters")}</h2><p>${t.total} ${Pe("chars")+(t.limit?" / "+t.limit+" "+Pe("limit"):"")}</p><hr><div id="mc-chatbot-delete-all-training" class="mc-btn mc-btn-white">${Pe("Delete all training data")}</div>`,e.html(s),e.mcLoading(!1)}))})),e(E).on("click",".mc-menu-chatbot [data-type]",(function(t){let i=e(this).data("type");switch(i){case"flows":case"training":case"playground":let e=E.find(`> [data-id="${i}"]`);E.find("> [data-id]").mcActive(!1),e.mcActive(!0),"flows"==i&&e.mcLoading()&&MCF.ajax({function:"open-ai-flows-get"},(t=>{for(var i=0;i<t.length;i++)t[i]&&t[i].steps&&t[i].name&&it.openAI.flows.flows.push(t[i]);let s="";for(i=0;i<t.length;i++)t[i]&&(s+=it.openAI.flows.navCode(t[i].name));U.html(s),U.find("li:first-child").click(),e.mcLoading(!1)}));break;case"settings":return st.open("dialogflow",!0),t.preventDefault,!1}})),e(M).on("click",".mc-enlarger-function-calling",(function(){e(this).parent().parent().find(".mc-qea-repeater-answer").addClass("mc-hide")})),e(M).on("change",'[data-id="open-ai-faq-set-data"] select',(function(){e(this).parent().next().find("input").setClass("mc-hide",["transcript","transcript_email","human_takeover","archive_conversation"].includes(e(this).val()))})),e(M).on("input click",'[data-id="open-ai-faq-answer"]',(function(){e(this).prev().find("i").mcActive(e(this).val().length>2&&e(this).val().indexOf(" "))})),e(M).on("click",".mc-qea-repeater-answer > label > i",(function(){let t=e(this).closest(".repeater-item").find('[data-id="open-ai-faq-answer"]');Ge(this)||it.openAI.rewrite(t.val(),(i=>{e(this).mcLoading(!1),i[0]&&t.val(i[1])}))})),e(R).on("click",'[data-value="add"], [data-value="send"]',(function(){let t=R.find("textarea"),i=t.val().trim();if(t.val(""),i&&it.openAI.playground.addMessage(i,R.find('[data-value="user"], [data-value="assistant"]').attr("data-value")),"send"==e(this).data("value")){let t=it.openAI.playground.messages.length;t&&!Ge(this)&&MCF.ajax({function:"open-ai-playground-message",messages:it.openAI.playground.messages},(i=>{if(i[0]){if(i[1]&&(it.openAI.playground.addMessage(i[1],"assistant",i[6]),i[4])){let e="";for(var s in i[4].usage)["string","number"].includes(typeof i[4].usage[s])&&(e+=`<b>${MCF.slugToString(s)}</b>: ${i[4].usage[s]}<br>`);it.openAI.playground.last_response=i[4],E.find(".mc-playground-info").html(e+`<div id="mc-playground-query" class="mc-btn-text">${Pe("View code")}</div>${i[4].embeddings?`<div id="mc-playground-embeddings" class="mc-btn-text">${Pe("Embeddings")}</div>`:""}`),i[4].payload&&it.openAI.playground.messages[t-1].push(i[4].payload)}i[8]&&l.find(`[data-conversation-id="${i[8]}"]`).remove()}else Me(i),console.error(i);e(this).mcLoading(!1)}))}})),e(E).on("click","#mc-playground-query",(function(){Me("<pre>"+JSON.stringify(it.openAI.playground.last_response.query,null,4).replaceAll('\\"','"')+"</pre>","info",!1,"mc-playground-query-panel",!1,!0)})),e(E).on("click","#mc-playground-embeddings",(function(){let e="",t=it.openAI.playground.last_response.embeddings;for(var i=t.length-1;i>-1;i--)e+=`<span><b>${Pe("Source")}</b>: ${t[i].source?t[i].source.autoLink({target:"_blank"}):""}<br><b>${Pe("Score")}</b>: ${t[i].score}<br><span>${t[i].text}</span></span>`;Me(e,"info",!1,"mc-playground-embeddings-panel",!1,!0)})),e(R).on("click",'[data-value="clear"]',(function(){it.openAI.playground.messages=[],G.html(""),E.find(".mc-playground-info").html("")})),e(G).on("click",".mc-icon-close",(function(){let t=e(this).closest("[data-type]");it.openAI.playground.messages.splice(t.index(),1),t.remove()})),e(G).on("click",".mc-rich-chips .mc-btn",(function(){R.find("textarea").val(e(this).html()),R.find('[data-value="send"]').click()})),e(R).on("click",'[data-value="user"], [data-value="assistant"]',(function(){let t="user"==e(this).attr("data-value");e(this).attr("data-value",t?"assistant":"user").html('<i class="mc-icon-reload"></i> '+Pe(t?"Assistant":"User"))})),e(t).on("click","#open-ai-troubleshoot a, #google-troubleshoot a",(function(t){let i=e(this).parent().attr("id");return t.preventDefault(),!("google-troubleshoot"!=i&&![!0,"mode"].includes(it.openAI.troubleshoot()))&&(Ge(this)?void 0:(MCF.ajax({function:e(this).parent().attr("id")},(t=>{!0===t?De("Success. No issues found."):Me(t),e(this).mcLoading(!1),e(a).find(".mc-admin-list .mc-select li.mc-active").click()})),!1))})),e(I).on("click",".ce-settings__button--delete.ce-settings__button--confirm",(function(){let e=I.find(".image-tool--filled img").attr("src");e&&MCF.ajax({function:"delete-file",path:e})})),e(I).on("click",".ul-articles li",(function(t){Me("The changes will be lost.","alert",(()=>{at.show(e(this).attr("data-id")),I.find(".mc-scroll-area:not(.mc-nav)").scrollTop(0)}),!1,!1,!1,!Se,(()=>{e(this).parent().find("li").mcActive(!1),e(this).parent().find(`[data-id="${at.activeID()}"]`).mcActive(!0)}))})),e(I).on("click",".ul-categories li",(function(t){at.categories.show(e(this).attr("data-id"))})),e(I).on("click",".mc-add-article",(function(){at.add()})),e(I).on("click",".mc-add-category",(function(){at.categories.add()})),e(I).on("click",".mc-nav i",(function(t){let i=e(this).parent(),s=i.closest("ul"),a=s.hasClass("ul-categories");return Me(`The ${a?"category":"article"} will be deleted permanently.`,"alert",(()=>{let t=i.attr("data-id");if(a)at.categories.delete(t);else{if(I.find("#editorjs .image-tool__image-picture").each((function(){MCF.ajax({function:"delete-file",path:e(this).attr("src")})})),!t)return i.remove();Ge(C),at.delete(t,(e=>{C.mcLoading(!1),We(),s.find("li").length>1&&setTimeout((()=>{i.prev().length?i.prev().click():i.next().click(),i.remove()}),300)}))}})),t.preventDefault(),!1})),e(I).on("click",".mc-menu-wide li",(function(){let t=e(this).data("type");"settings"==t?st.open("articles",!0):"reports"==t?nt.open("articles-searches"):(I.attr("data-type",t),at.categories.update())})),e(I).on("click",".mc-save-articles",(function(){Ge(this)||("categories"==I.attr("data-type")?at.categories.save((t=>{e(this).mcLoading(!1)})):at.save((t=>{e(this).mcLoading(!1)})))})),e(I).on("change input","input, textarea, select",(function(){Se=!0})),e(N).on("change",(function(){F.val()||(De("Select a parent category first.","error"),e(this).val(""))})),e(ne).on("click",".mc-nav [id]",(function(){let t=e(this).attr("id");nt.active_report=!1,ne.find("#mc-date-picker").val(""),ne.attr("class","mc-area-reports mc-active mc-report-"+t),nt.initReport(e(this).attr("id")),MCF.getURL("report")!=t&&Je("?report="+t)})),e(ne).on("change","#mc-date-picker",(function(){nt.initReport(!1,e(this).val())})),e(ne).on("click",".mc-report-export",(function(){e(this).mcLoading()||nt.export((t=>{e(this).mcLoading(!1),t&&Ke(t,"mc-export-report-close","Report exported")}))})),MCF.getURL("report")&&(ne.mcActive()||i.find(".mc-admin-nav #mc-reports").click(),setTimeout((()=>{ne.find("#"+MCF.getURL("report")).click()}),500)),e(a).on("click",".mc-panel-woocommerce > i",(function(){it.woocommerce.conversationPanel()})),e(a).on("click",".mc-woocommerce-orders > div > span",(function(t){let i=e(this).parent();e(t.target).is("span")&&(i.mcActive()||it.woocommerce.conversationPanelOrder(i.attr("data-id")))})),e(a).on("click",".mc-btn-woocommerce",(function(){(pe.mcLoading()||0!=Re()&&Re().language!=it.itemsPanel.panel_language)&&it.itemsPanel.populate("woocommerce"),ue.find(".mc-search-btn").mcActive(!0).find("input").get(0).focus(),ue.mcTogglePopup(this)})),e(ue).find(".mc-woocommerce-products-list").on("scroll",(function(){He(this,!0)&&it.itemsPanel.pagination(this,"woocommerce")})),e(ue).on("click",".mc-select li",(function(){it.itemsPanel.filter(this,"woocommerce")})),e(ue).on("input",".mc-search-btn input",(function(){it.itemsPanel.search(this,"woocommerce")})),e(ue).on("click",".mc-search-btn i",(function(){MCF.searchClear(this,(()=>{it.itemsPanel.search(e(this).next(),"woocommerce")}))})),e(ue).on("click",".mc-woocommerce-products-list li",(function(){let i=ue.attr("data-action"),s=e(this).data("id");MCF.null(i)?MCChat.insertText(`{product_card id="${s}"}`):(pe.mcLoading(!0),a.find(".mc-add-cart-btn").mcLoading(!0),MCChat.sendMessage(-1,"",[],(e=>{e&&(it.woocommerce.conversationPanelUpdate(s),t.mcHideLightbox())}),{event:"woocommerce-update-cart",action:"cart-add",id:s})),MCF.deactivateAll(),t.removeClass("mc-popup-active")})),e(a).on("click",".mc-panel-woocommerce .mc-add-cart-btn",(function(){e(this).mcLoading()||(MCChat.user_online?(it.itemsPanel.populate("woocommerce"),ue.mcShowLightbox(!0,"cart-add")):Me("The user is offline. Only the carts of online users can be updated."))})),e(a).on("click",".mc-panel-woocommerce .mc-list-items > a > i",(function(t){let i=e(this).parent().attr("data-id");return MCChat.sendMessage(-1,"",[],(()=>{it.woocommerce.conversationPanelUpdate(i,"removed")}),{event:"woocommerce-update-cart",action:"cart-remove",id:i}),e(this).mcLoading(!0),t.preventDefault(),!1})),e(a).on("click",".mc-panel-ump > i",(function(){it.ump.conversationPanel()})),e(a).on("click",".mc-panel-armember > i",(function(){it.armember.conversationPanel()})),e(a).on("click",".mc-panel-opencart > i",(function(){it.opencart.conversationPanel()})),e(a).on("click",".mc-opencart-orders > a",(function(){it.opencart.openOrder(e(this).attr("data-id"))})),e(k).on("click","#opencart-sync a",(function(t){t.preventDefault(),Ge(this)||MCF.ajax({function:"opencart-sync"},(t=>{e(this).mcLoading(!1),Me(!0===t?"Users successfully imported.":t)}))})),e(k).on("click","#perfex-sync a, #whmcs-sync a, #perfex-articles-sync a, #whmcs-articles-sync a, #aecommerce-sync a, #aecommerce-sync-admins a, #aecommerce-sync-sellers a, #martfury-sync a, #martfury-sync-sellers a",(function(t){if(Ge(this))return;let i=e(this).closest("[id]").attr("id"),s=i.indexOf("article")>0;MCF.ajax({function:i},(t=>{!0===t?(s||ot.update(),Me(s?"Articles successfully imported.":"Users successfully imported.")):Me("Error. Response: "+JSON.stringify(t)),e(this).mcLoading(!1)})),t.preventDefault()})),e(a).on("click","#mc-zendesk-btn",(function(t){Ge(this)||(MCF.ajax({function:"zendesk-create-ticket",conversation_id:MCChat.conversation.id},(t=>{!0===t?it.zendesk.conversationPanel():Me("Error. Response: "+JSON.stringify(t)),e(this).mcLoading(!1)})),t.preventDefault())})),e(a).on("click","#mc-zendesk-update-ticket",(function(t){if(!Ge(this))return MCF.ajax({function:"zendesk-update-ticket",conversation_id:MCChat.conversation.id,zendesk_ticket_id:e(this).closest("[data-id]").attr("data-id")},(()=>{e(this).mcLoading(!1)})),t.preventDefault(),!1})),e(t).on("click",".mc-enlarger",(function(){e(this).mcActive(!0)})),e(t).on("mouseenter","[data-mc-tooltip-init]",(function(){e(this).parent().mcInitTooltips(),e(this).removeAttr("data-mc-tooltip-init"),e(this).trigger("mouseenter")})),e(t).on("click",".mc-language-switcher > i",(function(){let t=e(this).parent(),i=t.find("[data-language]").map((function(){return e(this).attr("data-language")})).get(),s="";for(var a in i.push("en"),MC_LANGUAGE_CODES)i.includes(a)||(s+=`<div data-language="${a}"><img src="${MC_URL}/media/flags/${a}.png" />${Pe(MC_LANGUAGE_CODES[a])}</div>`);W=t,ct.genericPanel("languages","Choose a language",s,[],' data-source="'+t.attr("data-source")+'"',!0)})),e(t).on("click",".mc-language-switcher img",(function(){let t=e(this).parent(),i=t.mcActive(),s=!i&&t.attr("data-language");switch(t.parent().attr("data-source")){case"article-categories":at.categories.show(at.categories.activeID(),s);break;case"articles":let e=C.find(".mc-language-switcher .mc-active");Me("The changes will be lost.","alert",(()=>{let e=t.attr("data-id");e||i?at.show(e&&!i?e:at.activeID(!0)):at.clear()}),!1,!1,!1,!Se,(()=>{t.mcActive(!1),e.mcActive(!0)}));break;case"automations":st.automations.show(!1,s);break;case"settings":let a=t.parent().find("[data-language].mc-active");st.translations.save(t,i?t.attr("data-language"):!!a.length&&a.attr("data-language")),st.translations.activate(t,s)}t.siblings().mcActive(!1),t.mcActive(!i)})),e(t).on("click",".mc-language-switcher span > i",(function(){let t=e(this).parent(),i=t.attr("data-language");Me(Pe("The {T} translation will be deleted.").replace("{T}",Pe(MC_LANGUAGE_CODES[i])),"alert",(()=>{switch(t.parent().attr("data-source")){case"article-categories":at.categories.translations.delete(i);break;case"articles":at.translations.delete(i);break;case"automations":st.automations.deleteTranslation(!1,i),st.automations.show();break;case"settings":st.translations.delete(t,i)}t.remove()}))})),e(t).on("click",".mc-languages-box [data-language]",(function(){let i=e(this).parents().eq(1),s=e(this).attr("data-language"),a=!0;switch(i.attr("data-source")){case"article-categories":at.categories.translations.add(s);break;case"articles":Me("The changes will be lost.","alert",(()=>{at.translations.add(s),t.mcHideLightbox()}),!1,!1,!1,!Se),a=!1;break;case"automations":st.automations.addTranslation(!1,!1,s),st.automations.show(!1,s);break;case"settings":st.translations.add(s)}a&&t.mcHideLightbox()})),e(t).on("click",".mc-lightbox .mc-top-bar .mc-close",(function(){t.mcHideLightbox()})),e(t).on("click",".mc-lightbox .mc-info",(function(){e(this).mcActive(!1)})),e(t).on("click",".mc-dialog-box a",(function(){let i=e(this).closest(".mc-lightbox"),s=i.find("p").text().toLowerCase().includes("error")&&E.find(".mc-tab-training").mcActive();e(this).hasClass("mc-confirm")&&J(),e(this).hasClass("mc-cancel")&&X&&X(),1==t.find(".mc-lightbox.mc-active").length&&K.mcActive(!1),ct.open_popup=!1,i.mcActive(!1),s&&(console.log("Refreshing page after chatbot error dialog close via anchor"),setTimeout((function(){window.location.reload()}),100))})),e(t).on("click",".mc-dialog-box .mc-close",(function(){let i=e(this).closest(".mc-lightbox"),s=i.find("p").text().toLowerCase().includes("error")&&E.find(".mc-tab-training").mcActive();1==t.find(".mc-lightbox.mc-active").length&&K.mcActive(!1),ct.open_popup=!1,i.mcActive(!1),s&&(console.log("Refreshing page after chatbot error dialog close via close button"),setTimeout((function(){window.location.reload()}),100))})),e(document).on("MCDialogOpen",(function(e,t){let i=t.find("p").text().toLowerCase();E.find(".mc-tab-training").mcActive()&&i.includes("error")&&(t.find("p").append('<div class="mc-refresh-notice"><br><b>'+Pe("The page will refresh automatically when you close this message to reset the training state.")+"</b></div>"),console.log("Added refresh notice to dialog"))})),e(t).on("click",".mc-menu-wide li, .mc-nav li",(function(){e(this).siblings().mcActive(!1),e(this).mcActive(!0)})),e(t).on("click",".mc-nav:not(.mc-nav-only) li:not(.mc-tab-nav-title)",(function(){let t=e(this).closest(".mc-tab"),i=e(t).find(" > .mc-content > div").mcActive(!1).eq(e(this).parent().find("li:not(.mc-tab-nav-title)").index(this));i.mcActive(!0),i.find("textarea").each((function(){e(this).autoExpandTextarea(),e(this).manualExpandTextarea()})),t.find(".mc-scroll-area:not(.mc-nav)").scrollTop(0)})),e(t).mcInitTooltips(),e(t).on("click",'[data-button="toggle"]',(function(){let i=t.find("."+e(this).data("show")),s=t.find("."+e(this).data("hide"));i.addClass("mc-show-animation").show(),s.hide(),ct.open_popup=!(!i.hasClass("mc-lightbox")&&!i.hasClass("mc-popup"))&&i})),e(t).on("click",".mc-info-card",(function(){e(this).mcActive(!1)})),e(q).on("change",(function(){H?(H(),H=!1):(e(O).mcLoading(e(this).prop("files").length),e(this).mcUploadFiles((t=>{if(e(O).mcLoading(!1),"success"==(t=JSON.parse(t))[0]){"upload-image"==e(O).closest("[data-type]").data("type")&&(e(O).attr("data-value")&&MCF.ajax({function:"delete-file",path:e(O).attr("data-value")}),e(O).attr("data-value",t[1]).css("background-image",`url("${t[1]}")`)),V&&V(t[1])}else console.log(t[1])})))})),e(t).on("click",".mc-accordion > div > span",(function(t){let i=e(this).parent(),s=e(i).mcActive();e(t.target).is("span")&&(i.siblings().mcActive(!1),i.mcActive(!s))})),e(t).on("mousedown",(function(i){if(e(ct.open_popup).length){let s=e(ct.open_popup);s.is(i.target)||0!==s.has(i.target).length||["mc-btn-saved-replies","mc-btn-emoji","mc-btn-woocommerce","mc-btn-shopify","mc-btn-open-ai"].includes(e(i.target).attr("class"))||(s.hasClass("mc-popup")?s.mcTogglePopup():s.hasClass("mc-select")?s.find("ul").mcActive(!1):s.hasClass("mc-menu-mobile")?s.find("i").mcActive(!1):s.hasClass("mc-menu")?s.mcActive(!1):ct.open_popup&&["mc-embeddings-box"].includes(ct.open_popup.attr("id"))||t.mcHideLightbox(),ct.open_popup=!1)}}))})),ct.handleError=function(e,t="",i=!1){let s=MCF.handleAjaxError(e,t,i);return"security-error"===s.type||"invalid-session"===s.type?setTimeout((()=>{MCF.reset()}),1e3):"server-error"!==s.type&&"500-error"!==s.type||ct.infoPanel(`<p>A server error occurred. This might be a temporary issue.</p>\n                <p>Please try the following:</p>\n                <ul>\n                    <li>Refresh the page and try again</li>\n                    <li>Check server logs if you have access</li>\n                    <li>Contact your system administrator if the issue persists</li>\n                </ul>\n                <pre class="mc-technical-details">${s.message}</pre>`,"info",!1,"server-error"),s}}(jQuery),function(e,t){"object"==typeof exports?module.exports=t(e):"function"==typeof define&&define.amd?define("colors",[],(function(){return t(e)})):e.Colors=t(e)}(this,(function(e,t){function i(e,i,a,n,o){if("string"==typeof i)a=(i=m.txt2color(i)).type,p[a]=i[a],o=o!==t?o:i.alpha;else if(i)for(var c in i)e[a][c]=r(i[c]/l[a][c][1],0,1);return o!==t&&(e.alpha=r(+o,0,1)),s(a,n?e:t)}function s(e,t){var i,s,r,h=t||p,f=m,g=u.options,b=l,v=h.RND,S="",_="",y={hsl:"hsv",rgb:e},w=v.rgb;if("alpha"!==e){for(var k in b)if(!b[k][k])for(S in e!==k&&(_=y[k]||"rgb",h[k]=f[_+"2"+k](h[_])),v[k]||(v[k]={}),i=h[k])v[k][S]=d(i[S]*b[k][S][1]);w=v.rgb,h.HEX=f.RGB2HEX(w),h.equivalentGrey=g.grey.r*h.rgb.r+g.grey.g*h.rgb.g+g.grey.b*h.rgb.b,h.webSave=s=a(w,51),h.webSmart=r=a(w,17),h.saveColor=w.r===s.r&&w.g===s.g&&w.b===s.b?"web save":w.r===r.r&&w.g===r.g&&w.b===r.b?"web smart":"",h.hueRGB=m.hue2RGB(h.hsv.h),t&&(h.background=function(e,t,i){var s=u.options.grey,a={};return a.RGB={r:e.r,g:e.g,b:e.b},a.rgb={r:t.r,g:t.g,b:t.b},a.alpha=i,a.equivalentGrey=d(s.r*e.r+s.g*e.g+s.b*e.b),a.rgbaMixBlack=o(t,{r:0,g:0,b:0},i,1),a.rgbaMixWhite=o(t,{r:1,g:1,b:1},i,1),a.rgbaMixBlack.luminance=n(a.rgbaMixBlack,!0),a.rgbaMixWhite.luminance=n(a.rgbaMixWhite,!0),u.options.customBG&&(a.rgbaMixCustom=o(t,u.options.customBG,i,1),a.rgbaMixCustom.luminance=n(a.rgbaMixCustom,!0),u.options.customBG.luminance=n(u.options.customBG,!0)),a}(w,h.rgb,h.alpha))}var x,A,B,T=h.rgb,I=h.alpha,C="luminance",$=h.background;return(x=o(T,{r:0,g:0,b:0},I,1))[C]=n(x,!0),h.rgbaMixBlack=x,(A=o(T,{r:1,g:1,b:1},I,1))[C]=n(A,!0),h.rgbaMixWhite=A,g.customBG&&((B=o(T,$.rgbaMixCustom,I,1))[C]=n(B,!0),B.WCAG2Ratio=function(e,t){var i=1;return i=e>=t?(e+.05)/(t+.05):(t+.05)/(e+.05),d(100*i)/100}(B[C],$.rgbaMixCustom[C]),h.rgbaMixBGMixCustom=B,B.luminanceDelta=c.abs(B[C]-$.rgbaMixCustom[C]),B.hueDelta=function(e,t,i){return(c.max(e.r-t.r,t.r-e.r)+c.max(e.g-t.g,t.g-e.g)+c.max(e.b-t.b,t.b-e.b))*(i?255:1)/765}($.rgbaMixCustom,B,!0)),h.RGBLuminance=n(w),h.HUELuminance=n(h.hueRGB),g.convertCallback&&g.convertCallback(h,e),h}function a(e,t){var i={},s=0,a=t/2;for(var n in e)s=e[n]%t,i[n]=e[n]+(s>a?t-s:-s);return i}function n(e,t){for(var i=t?1:255,s=[e.r/i,e.g/i,e.b/i],a=u.options.luminance,n=s.length;n--;)s[n]=s[n]<=.03928?s[n]/12.92:c.pow((s[n]+.055)/1.055,2.4);return a.r*s[0]+a.g*s[1]+a.b*s[2]}function o(e,i,s,a){var n={},o=s!==t?s:1,r=a!==t?a:1,l=o+r*(1-o);for(var c in e)n[c]=(e[c]*o+i[c]*r*(1-o))/l;return n.a=l,n}function r(e,t,i){return e>i?i:t>e?t:e}var l={rgb:{r:[0,255],g:[0,255],b:[0,255]},hsv:{h:[0,360],s:[0,100],v:[0,100]},hsl:{h:[0,360],s:[0,100],l:[0,100]},alpha:{alpha:[0,1]},HEX:{HEX:[0,16777215]}},c=e.Math,d=c.round,u={},p={},h={r:.298954,g:.586434,b:.114612},f={r:.2126,g:.7152,b:.0722},g=function(e){this.colors={RND:{}},this.options={color:"rgba(0,0,0,0)",grey:h,luminance:f,valueRanges:l},b(this,e||{})},b=function(e,s){var a,n=e.options;for(var o in v(e),s)s[o]!==t&&(n[o]=s[o]);a=n.customBG,n.customBG="string"==typeof a?m.txt2color(a).rgb:a,p=i(e.colors,n.color,t,!0)},v=function(e){u!==e&&(u=e,p=e.colors)};g.prototype.setColor=function(e,a,n){return v(this),e?i(this.colors,e,a,t,n):(n!==t&&(this.colors.alpha=r(n,0,1)),s(a))},g.prototype.setCustomBackground=function(e){return v(this),this.options.customBG="string"==typeof e?m.txt2color(e).rgb:e,i(this.colors,t,"rgb")},g.prototype.saveAsBackground=function(){return v(this),i(this.colors,t,"rgb",!0)},g.prototype.toString=function(e,t){return m.color2text((e||"rgb").toLowerCase(),this.colors,t)};var m={txt2color:function(e){var t={},i=e.replace(/(?:#|\)|%)/g,"").split("("),s=(i[1]||"").split(/,\s*/),a=i[1]?i[0].substr(0,3):"rgb",n="";if(t.type=a,t[a]={},i[1])for(var o=3;o--;)n=a[o]||a.charAt(o),t[a][n]=+s[o]/l[a][n][1];else t.rgb=m.HEX2rgb(i[0]);return t.alpha=s[3]?+s[3]:1,t},color2text:function(e,t,i){var s=!1!==i&&d(100*t.alpha)/100,a="number"==typeof s&&!1!==i&&(i||1!==s),n=t.RND.rgb,o=t.RND.hsl,r="hex"===e&&a,l="hex"===e&&!r,c="rgb"===e||r?n.r+", "+n.g+", "+n.b:l?"#"+t.HEX:o.h+", "+o.s+"%, "+o.l+"%";return l?c:(r?"rgb":e)+(a?"a":"")+"("+c+(a?", "+s:"")+")"},RGB2HEX:function(e){return((e.r<16?"0":"")+e.r.toString(16)+(e.g<16?"0":"")+e.g.toString(16)+(e.b<16?"0":"")+e.b.toString(16)).toUpperCase()},HEX2rgb:function(e){return{r:+("0x"+(e=e.split(""))[0]+e[e[3]?1:0])/255,g:+("0x"+e[e[3]?2:1]+(e[3]||e[1]))/255,b:+("0x"+(e[4]||e[2])+(e[5]||e[2]))/255}},hue2RGB:function(e){var t=6*e,i=~~t%6,s=6===t?0:t-i;return{r:d(255*[1,1-s,0,0,s,1][i]),g:d(255*[s,1,1,1-s,0,0][i]),b:d(255*[0,0,s,1,1,1-s][i])}},rgb2hsv:function(e){var t,i,s=e.r,a=e.g,n=e.b,o=0;return n>a&&(a=n+(n=a,0),o=-1),i=n,a>s&&(s=a+(a=s,0),o=-2/6-o,i=c.min(a,n)),t=s-i,{h:1e-15>(s?t/s:0)?p&&p.hsl&&p.hsl.h||0:t?c.abs(o+(a-n)/(6*t)):0,s:s?t/s:p&&p.hsv&&p.hsv.s||0,v:s}},hsv2rgb:function(e){var t=6*e.h,i=e.s,s=e.v,a=~~t,n=t-a,o=s*(1-i),r=s*(1-n*i),l=s*(1-(1-n)*i),c=a%6;return{r:[s,r,o,o,l,s][c],g:[l,s,s,r,o,o][c],b:[o,o,l,s,s,r][c]}},hsv2hsl:function(e){var t=(2-e.s)*e.v,i=e.s*e.v;return i=e.s?1>t?t?i/t:0:i/(2-t):0,{h:e.h,s:e.v||i?i:p&&p.hsl&&p.hsl.s||0,l:t/2}},rgb2hsl:function(e,t){var i=m.rgb2hsv(e);return m.hsv2hsl(t?i:p.hsv=i)},hsl2rgb:function(e){var t=6*e.h,i=e.s,s=e.l,a=.5>s?s*(1+i):s+i-i*s,n=s+s-a,o=~~t,r=a*(a?(a-n)/a:0)*(t-o),l=n+r,c=a-r,d=o%6;return{r:[a,c,n,n,l,a][d],g:[l,a,a,c,n,n][d],b:[n,n,l,a,a,c][d]}}};return g})),function(e,t){"object"==typeof exports?module.exports=t(e,require("jquery"),require("colors")):"function"==typeof define&&define.amd?define(["jquery","colors"],(function(i,s){return t(e,i,s)})):t(e,e.jQuery,e.Colors)}(this,(function(e,t,i,s){function a(e){return e.value||e.getAttribute("value")||t(e).css("background-color")||"#FFF"}function n(e){return(e=e.originalEvent&&e.originalEvent.touches?e.originalEvent.touches[0]:e).originalEvent?e.originalEvent:e}function o(e){return t(e.find(v.doRender)[0]||e[0])}function r(i){var s=t(this),n=s.offset(),r=t(e),d=v.gap;i?((m=o(s))._colorMode=m.data("colorMode"),g.$trigger=s,(S||l()).css(v.positionCallback.call(g,s)||{left:(S._left=n.left)-((S._left+=S._width-(r.scrollLeft()+r.width()))+d>0?S._left+d:0),top:(S._top=n.top+s.outerHeight())-((S._top+=S._height-(r.scrollTop()+r.height()))+d>0?S._top+d:0)}).show(v.animationSpeed,(function(){!0!==i&&(x.toggle(!!v.opacity)._width=x.width(),y._width=y.width(),y._height=y.height(),_._height=_.height(),b.setColor(a(m[0])),h(!0))})).off(".tcp").on(C,".cp-xy-slider,.cp-z-slider,.cp-alpha",c)):g.$trigger&&t(S).hide(v.animationSpeed,(function(){h(!1),g.$trigger=null})).off(".tcp")}function l(){return t("head")[v.cssPrepend?"prepend":"append"]('<style type="text/css" id="tinyColorPickerStyles">'+(v.css||L)+(v.cssAddon||"")+"</style>"),t(E).css({margin:v.margin}).appendTo("body").show(0,(function(){g.$UI=S=t(this),N=v.GPU&&S.css("perspective")!==s,_=t(".cp-z-slider",this),y=t(".cp-xy-slider",this),w=t(".cp-xy-cursor",this),k=t(".cp-z-cursor",this),x=t(".cp-alpha",this),A=t(".cp-alpha-cursor",this),v.buildCallback.call(g,S),S.prepend("<div>").children().eq(0).css("width",S.children().eq(0).width()),S._width=this.offsetWidth,S._height=this.offsetHeight})).hide()}function c(e){var i=this.className.replace(/cp-(.*?)(?:\s*|$)/,"$1").replace("-","_");(e.button||e.which)>1||(e.preventDefault&&e.preventDefault(),e.returnValue=!1,m._offset=t(this).offset(),(i="xy_slider"===i?d:"z_slider"===i?u:p)(e),h(),B.on($,(function(){B.off(".tcp")})).on(I,(function(e){i(e),h()})))}function d(e){var t=n(e),i=t.pageX-m._offset.left,s=t.pageY-m._offset.top;b.setColor({s:i/y._width*100,v:100-s/y._height*100},"hsv")}function u(e){var t=n(e).pageY-m._offset.top;b.setColor({h:360-t/_._height*360},"hsv")}function p(e){var t=(n(e).pageX-m._offset.left)/x._width;b.setColor({},"rgb",t)}function h(e){var t=b.colors,i=t.hueRGB,a=(t.RND.rgb,t.RND.hsl,v.dark),n=v.light,o=b.toString(m._colorMode,v.forceAlpha),r=t.HUELuminance>.22?a:n,l=t.rgbaMixBlack.luminance>.22?a:n,c=(1-t.hsv.h)*_._height,d=t.hsv.s*y._width,u=(1-t.hsv.v)*y._height,p=t.alpha*x._width,h=N?"translate3d":"",g=m[0].value,S=m[0].hasAttribute("value")&&""===g&&e!==s;y._css={backgroundColor:"rgb("+i.r+","+i.g+","+i.b+")"},w._css={transform:h+"("+d+"px, "+u+"px, 0)",left:N?"":d,top:N?"":u,borderColor:t.RGBLuminance>.22?a:n},k._css={transform:h+"(0, "+c+"px, 0)",top:N?"":c,borderColor:"transparent "+r},x._css={backgroundColor:"#"+t.HEX},A._css={transform:h+"("+p+"px, 0, 0)",left:N?"":p,borderColor:l+" transparent"},m._css={backgroundColor:S?"":o,color:S?"":t.rgbaMixBGMixCustom.luminance>.22?a:n},m.text=S?"":g!==o?o:"",e!==s?f(e):F(f)}function f(e){y.css(y._css),w.css(w._css),k.css(k._css),x.css(x._css),A.css(A._css),v.doRender&&m.css(m._css),m.text&&m.val(m.text),v.renderCallback.call(g,m,"boolean"==typeof e?e:s)}var g,b,v,m,S,_,y,w,k,x,A,B=t(document),T=t(),I="touchmove.tcp mousemove.tcp pointermove.tcp",C="touchstart.tcp mousedown.tcp pointerdown.tcp",$="touchend.tcp mouseup.tcp pointerup.tcp",N=!1,F=e.requestAnimationFrame||e.webkitRequestAnimationFrame||function(e){e()},E='<div class="cp-color-picker"><div class="cp-z-slider"><div class="cp-z-cursor"></div></div><div class="cp-xy-slider"><div class="cp-white"></div><div class="cp-xy-cursor"></div></div><div class="cp-alpha"><div class="cp-alpha-cursor"></div></div></div>',L=".cp-color-picker{position:absolute;overflow:hidden;padding:6px 6px 0;background-color:#444;color:#bbb;font-family:Arial,Helvetica,sans-serif;font-size:12px;font-weight:400;cursor:default;border-radius:5px}.cp-color-picker>div{position:relative;overflow:hidden}.cp-xy-slider{float:left;height:128px;width:128px;margin-bottom:6px;background:linear-gradient(to right,#FFF,rgba(255,255,255,0))}.cp-white{height:100%;width:100%;background:linear-gradient(rgba(0,0,0,0),#000)}.cp-xy-cursor{position:absolute;top:0;width:10px;height:10px;margin:-5px;border:1px solid #fff;border-radius:100%;box-sizing:border-box}.cp-z-slider{float:right;margin-left:6px;height:128px;width:20px;background:linear-gradient(red 0,#f0f 17%,#00f 33%,#0ff 50%,#0f0 67%,#ff0 83%,red 100%)}.cp-z-cursor{position:absolute;margin-top:-4px;width:100%;border:4px solid #fff;border-color:transparent #fff;box-sizing:border-box}.cp-alpha{clear:both;width:100%;height:16px;margin:6px 0;background:linear-gradient(to right,#444,rgba(0,0,0,0))}.cp-alpha-cursor{position:absolute;margin-left:-4px;height:100%;border:4px solid #fff;border-color:#fff transparent;box-sizing:border-box}",D=function(e){b=this.color=new i(e),v=b.options,g=this};D.prototype={render:h,toggle:r},t.fn.colorPicker=function(i){var s=this,n=function(){};return i=t.extend({animationSpeed:150,GPU:!0,doRender:!0,customBG:"#FFF",opacity:!0,renderCallback:n,buildCallback:n,positionCallback:n,body:document.body,scrollResize:!0,gap:4,dark:"#222",light:"#DDD"},i),!g&&i.scrollResize&&t(e).on("resize.tcp scroll.tcp",(function(){g.$trigger&&g.toggle.call(g.$trigger[0],!0)})),T=T.add(this),this.colorPicker=g||new D(i),this.options=i,t(i.body).off(".tcp").on(C,(function(e){-1===T.add(S).add(t(S).find(e.target)).index(e.target)&&r()})),this.on("focusin.tcp click.tcp",(function(e){g.color.options=t.extend(g.color.options,v=s.options),r.call(this,e)})).on("change.tcp",(function(){b.setColor(this.value||"#FFF"),s.colorPicker.render(!0)})).each((function(){var e=a(this),s=e.split("("),n=o(t(this));n.data("colorMode",s[1]?s[0].substr(0,3):"HEX").attr("readonly",v.preventFocus),i.doRender&&n.css({"background-color":e,color:function(){return b.setColor(e).rgbaMixBGMixCustom.luminance>.22?i.dark:i.light}})}))},t.fn.colorPicker.destroy=function(){t("*").off(".tcp"),g.toggle(!1),T=t()}}));