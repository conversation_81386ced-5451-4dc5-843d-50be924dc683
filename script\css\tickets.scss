
/*
*
* ==========================================================
* TICKETS.SCSS
* ==========================================================
*
* Main style file of the Tickets App. Written in SCSS.
*
*/

@import "shared.scss";

@keyframes mc-collapse-btn-right {
    0% {
        opacity: 0;
    }

    30% {
        opacity: 0;
        right: -50px;
    }

    60% {
        opacity: 0;
        right: -50px;
    }

    100% {
        opacity: 1;
        right: 0;
    }
}

@keyframes mc-collapse-btn-left {
    0% {
        opacity: 0;
    }

    30% {
        opacity: 0;
        left: -50px;
    }

    60% {
        opacity: 0;
        left: -50px;
    }

    100% {
        opacity: 1;
        left: 0;
    }
}

@keyframes mc-typing {
    0% {
        width: 0;
    }

    100% {
        width: 15px;
    }
}

/*

# GLOBAL
==========================================================

*/

.mc-main ::-webkit-input-placeholder {
    color: #a5aeb6;
}

.mc-main ::-moz-placeholder {
    color: #a5aeb6;
}

.mc-main :-ms-input-placeholder {
    color: #a5aeb6;
}

.mc-main :-moz-placeholder {
    color: #a5aeb6;
}

.mc-tickets {
    font-size: 15px;
    line-height: 27px;
    border: 1px solid rgb(212, 212, 212);
    border-radius: 4px;
    background: $white;
    color: $color-black;

    > .mc-tickets-area {
        display: flex;
        justify-content: space-between;
        height: 100%;
        position: relative;
    }

    &.mc-loading > .mc-tickets-area {
        visibility: hidden;
        opacity: 0;
    }

    ul {
        padding: 0;
        margin: 0;
        list-style: none;
    }

    > div > div > .mc-top {
        display: flex;
        align-items: center;
        border-bottom: 1px solid $border-color;
        padding: 15px 20px;
        height: 60px;
        min-height: 60px;
        box-sizing: border-box;
        justify-content: space-between;
    }

    .mc-no-conversation-message {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        justify-content: center;
        align-items: center;
        text-align: center;
        background: rgb(255, 255, 255);
        padding: 15px;
        display: none;
        z-index: 9;

        label {
            font-weight: 500;
            font-size: 20px;
            line-height: 30px;
        }

        p {
            margin: 15px 0;
            font-size: 15px;
            line-height: 25px;
            color: $color-gray;
        }
    }

    .mc-panel-left, .mc-panel-right {
        transition: width .5s, min-width .5s;
        overflow: hidden;

        &.mc-collapsed {
            min-width: 0;
            width: 0;
            border-width: 0 !important;
            transition: width .5s, min-width .5s, border-width .5s !important;

            > div, > ul {
                opacity: 0 !important;
                transition: .5s;
            }
        }
    }

    .mc-top .mc-btn, .mc-create-ticket, .mc-panel-right .mc-btn {
        font-weight: 500;
        font-size: 13px;
        background: $white;
        color: rgb(86, 96, 105);
        border: 1px solid rgb(204, 210, 213);
        border-radius: 4px;
        box-shadow: 0 1px 1px rgba(0,0,0,0.12);
        height: 30px;
        line-height: 30px;
        padding: 0 10px;
        white-space: nowrap;

        &:hover {
            border-color: $color-orange;
            background-color: $color-orange;
            color: $white;
        }

        &.mc-icon {
            padding-left: 30px;

            > i {
                left: 10px;
                font-size: 10px;
                line-height: 33px;

                &:before {
                    line-height: 30px;
                }
            }
        }
    }

    .mc-tickets-area .mc-top .mc-title {
        font-size: 17px;
        font-weight: 500;
        line-height: 30px;
    }
}

.mc-load div, .mc-load span, .mc-load input, .mc-load textarea {
    transition: none !important;
}

.mc-overflow-hidden, .mc-collapsing {
    overflow: hidden !important;
}

.mc-collapsing .mc-panel-right, .mc-collapsing .mc-panel-left {
    > div, > ul {
        min-width: 360px;
        opacity: 1 !important;
        transition: .5s;
    }
}

.mc-ticket-fields, .mc-popup-message {
    display: none !important;
}

.mc-tickets-area {
    .mc-input > span {
        font-weight: 500;
        font-size: 13px;
    }

    .mc-search-btn {
        i {
            z-index: 10;
        }

        > input {
            z-index: 9;
        }
    }

    .mc-form {
        .mc-input > span {
            padding-right: 15px;
        }

        .mc-input-select-input input {
            padding-top: 0;
        }
    }
}

[data-panel="registration"] .mc-input-select-input {
    position: relative;

    span + div {
        margin-left: 165px;
        opacity: 1;
    }
}

/*

# LEFT PANEL
==========================================================

*/

.mc-tickets {

    .mc-panel-left {
        width: 400px;
        min-width: 400px;
        border-right: 1px solid rgb(212, 212, 212);
        background-color: $white;
        position: relative;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;

        .mc-scroll-bar {
            opacity: 0.2 !important;
        }

        .mc-scroll-area {
            height: calc(100% - 59px);
            border-bottom-left-radius: 6px;
        }

        > ul {

            > li {
                background: $white;

                .mc-message {
                    font-size: 13px;
                }

                .mc-name {
                    font-weight: 500;
                    font-size: 13px;
                }

                &:hover {
                    background-color: rgb(245, 247, 250);
                }

                &:first-child {
                    border-top: none;
                }
            }

            > p {
                margin: 30px 20px;
                color: $color-gray;
            }

            &[data-profile-image="false"] > li > div {
                padding-left: 0;

                > img {
                    display: none;
                }
            }
        }

        li:not([data-conversation-status="1"]) {
            background: rgba(245,245,245,0.75);

            .mc-name, .mc-message {
                font-weight: 400;
            }

            [data-count] {
                border-color: $background-gray;
            }
        }

        .mc-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-left: 60px;
        }
    }

    .mc-user-conversations > li > [data-count] {
        left: 10px;
        top: 38px;
    }
}

/*

# RIGHT PANEL
==========================================================

*/

.mc-tickets {

    .mc-panel-right {
        min-width: 400px;
        width: 400px;
        border-left: 1px solid rgb(212, 212, 212);
        background-color: rgb(245, 247, 250);
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        position: relative;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;

        & > .mc-scroll-area {
            padding: 20px;
        }

        > .mc-top {
            padding-right: 60px;

            .mc-profile {
                height: 30px;
            }

            .mc-no-profile-image {
                padding-left: 0;

                img {
                    display: none;
                }
            }
        }

        .mc-title {
            letter-spacing: 0.5px;
            font-size: 13px;
            font-weight: 500;
            line-height: 21px;
        }

        .mc-btn, .mc-input.mc-input-btn > div {
            background: rgb(245, 247, 250);
            box-shadow: none;
        }

        .mc-input.mc-input-btn > div {
            color: $color-black;
            border: 1px solid rgb(204, 210, 213);
            height: 40px;
        }

        .mc-input.mc-input-btn > div:hover, .mc-input.mc-input-btn input:focus + div {
            background: $color-blue;
            border-color: $color-blue;
            color: $white;
        }

        .mc-agent-label, .mc-department {
            display: none;
            align-items: center;
            font-size: 13px;
            letter-spacing: 0.3px;
            line-height: 20px;
            margin-top: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &.mc-active {
                display: flex;
            }
        }

        .mc-agent-label img {
            border-radius: 50%;
            width: 20px;
            height: 20px;
            margin: 0 18px 0 5px;
        }

        .mc-department {
            font-size: 13px;

            > div {
                display: flex;
                align-items: center;
                margin: 0;
                border-radius: 15px;
                padding-right: 20px;

                > div {
                    font-weight: 500;
                    color: #4a5056;
                }
            }

            > span {
                padding-right: 15px;
            }

            > div span {
                display: none;
            }

            img {
                width: 30px;
                height: 30px;
                margin: 0 12px 0 0;
            }
        }

        .mc-articles {
            padding: 20px 0;
        }

        .mc-dashboard-articles {
            .mc-title {
                margin-bottom: 20px;
            }

            .mc-input-btn input {
                background-color: #fff;
            }
        }
    }

    .mc-agent-label, .mc-ticket-details, .mc-department, .mc-conversation-attachments, .mc-panel-right .mc-dashboard-articles {
        border-top: 1px solid rgb(212, 212, 212);
        margin-top: 15px;
        padding-top: 15px;
    }

    .mc-profile-empty + div:empty + div, .mc-profile-empty + div:empty + div:empty + div, .mc-profile-empty + div:empty + div:empty + div:empty + div, .mc-profile-empty + div:empty + div:empty + div:empty + div:empty + div {
        border-top: none;
        margin-top: 0;
        padding-top: 0;
    }

    .mc-ticket-details {
        padding: 12px 0 0 7px;

        > div {
            padding-left: 36px;
            position: relative;
            font-size: 13px;
            line-height: 27px;
            letter-spacing: 0.3px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            > span {
                font-weight: 500;
                padding-right: 10px;
                font-size: 13px;
            }

            > div {
                display: inline;
            }

            .mc-icon {
                position: absolute;
                left: 0;
                top: 8px;
                font-size: 14px;
                width: 16px;
                height: 16px;
                color: $color-dark-blue;

                &:before {
                    width: 16px;
                    height: 16px;
                    opacity: 0.8;
                    position: absolute;
                }
            }
        }

        &:first-child {
            border-top: none;
            margin-top: 0;
            padding-top: 0;
        }

        &:empty {
            display: none;
        }
    }

    .mc-conversation-attachments {

        .mc-title {
            margin: 0 0 13px 0;
        }

        a {
            line-height: 20px;
            margin: 5px 0 0 0;
            display: block;
            letter-spacing: 0.3px;
            text-decoration: none;
            font-size: 13px;
            color: $color-black;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: $transition;

            i {
                color: $color-dark-blue;
                font-size: 15px;
                transform: translateY(3px);
                display: inline-block;
                margin-right: 10px;
            }

            &:hover {
                color: $color-blue;
            }
        }

        &:empty {
            display: none;
        }
    }

    .mc-profile-agent {
        text-align: left;
        display: none;

        > div > span {
            display: block;
        }

        img {
            width: 31px;
            height: 31px;
        }

        .mc-name {
            letter-spacing: 0.5px;
            font-size: 13px;
            font-weight: 500;
            line-height: 21px;
        }

        .mc-status {
            position: relative;
            font-weight: 400;
            font-size: 13px;
            letter-spacing: 0.3px;
            line-height: 20px;
            padding-left: 15px;
            color: rgba(70, 79, 87, 0.75);

            &:empty {
                display: none;
            }

            &:before {
                content: "";
                width: 8px;
                height: 8px;
                position: absolute;
                border-radius: 50%;
                margin-top: -4px;
                top: 50%;
                left: 0;
                background: rgb(212, 212, 212);
            }

            &.mc-status-online:before {
                background: rgb(19, 202, 126);
            }
        }

        .mc-status-typing {
            padding-left: 0;
            float: left;

            &:before {
                display: none;
            }

            &:after {
                content: "...";
                position: absolute;
                width: 15px;
                left: calc(100% + 5px);
                bottom: 0;
                font-weight: 500;
                letter-spacing: 1px;
                overflow: hidden;
                white-space: nowrap;
                animation: mc-typing 1s infinite;
            }
        }

        &.mc-active {
            display: flex;
        }

        &:not(.mc-active) + .mc-agent-label:not(.mc-active) + .mc-ticket-details {
            padding-top: 0;
            padding-left: 0;
            margin-top: 0;
            border-top: 0;
        }
    }
}

/*

# MAIN PANEL
==========================================================

*/

.mc-tickets {
    .mc-panel-main {
        width: 100%;
        position: relative;

        > .mc-top {
            .mc-title {
                font-weight: 500;
                position: relative;
                padding: 0 15px 0 0;
                margin: 0;
                padding: 0;
                cursor: default;
                text-decoration: none;
                line-height: 30px;
                color: $color-black;
                font-size: 17px;
                letter-spacing: .4px;
                white-space: nowrap;
            }

            .mc-btn-back, .mc-close {
                display: none;
            }

            .mc-close {
                position: absolute;
                right: 14px;
                top: 12px;
                z-index: 9;

                i {
                    font-size: 14px;
                }
            }

            .mc-label-date-top {
                top: 54px;
            }
        }
    }

    .mc-panel {
        padding: 20px;
        height: calc(100% - 60px);
        box-sizing: border-box;
        display: none;

        > .mc-top {
            display: none;
        }

        > .mc-loading {
            width: 30px;
            height: 30px;
        }

        .mc-info {
            margin: 0 20px 20px 150px;
            color: $color-red;
            font-size: 13px;
            line-height: 25px;
            letter-spacing: 0.3px;
            font-weight: 500;
            display: none;
            cursor: default;

            &.mc-active {
                display: block;
                animation: mc-fade-animation 0.4s;
            }
        }

        .mc-text, .mc-link-area, .mc-privacy a:not([class]) {
            font-size: 15px;
            line-height: 27px;
            color: $color-gray;
        }

        .mc-article {
            > .mc-title {
                font-weight: 500;
                font-size: 18px;
                margin-bottom: 15px;

                > .mc-close {
                    display: none !important;
                }
            }

            > .mc-btn-text {
                margin-top: 20px;
            }
        }
    }

    &:not(.mc-panel-active) .mc-collapsed + .mc-panel-main > .mc-top {
        padding-left: 65px;
    }

    &.mc-no-arrows .mc-panel-left > .mc-top {
        padding-left: 15px;
    }

    &.mc-panel-active {

        .mc-panel {
            display: block;
        }

        .mc-panel-main {
            > .mc-top .mc-close {
                display: block;
            }

            .mc-conversation {
                display: none;
            }
        }
    }

    &.mc-no-conversations .mc-panel-main > .mc-top .mc-close, &[data-panel="registration"] .mc-panel-main > .mc-top .mc-close,
    &[data-panel="login"] .mc-panel-main > .mc-top .mc-close, &[data-panel="edit-profile"] .mc-login-area, &[data-panel="edit-profile"] .mc-panel .mc-text,
    &[data-panel="privacy"] .mc-panel-main > .mc-top .mc-close {
        display: none !important;
    }

    &[data-panel="registration"], &[data-panel="login"] {
        > .mc-tickets-area {
            height: auto !important;
            max-height: 100%;

            > .mc-collapsed {
                height: 0;
            }
        }
    }
}

.mc-panel-form {

    .mc-form {
        margin: 22px 0 30px 0;
        max-width: 500px;
    }

    [id="otp"]:not(.mc-active) {
        display: none;
    }

    .mc-form-extra {
        margin: -20px 0 30px 0;
    }

    .mc-form-extra:empty {
        display: none;
    }

    .mc-link-area {
        font-size: 13px !important;
    }

    .mc-link-area a {
        color: $color-gray;
    }

    .mc-buttons {
        margin: 30px 0 15px 0;
        display: flex;
        align-items: center;

        div + div {
            margin-left: 15px;
        }

        .mc-btn-text.mc-registration-area, .mc-btn-text.mc-login-area {
            font-size: 13px;
            letter-spacing: .3px;
        }
    }

    .mc-panel {
        height: 100%;

        .mc-info {
            margin: -20px 0 0 165px;
        }
    }


    .mc-top + .mc-form, &[data-panel="edit-profile"] .mc-form-main {
        margin-top: 10px;
    }
}

.mc-privacy .mc-buttons {
    margin-top: 30px;
}

.mc-tickets .mc-top-hide {
    height: 0 !important;
    border: none !important;
    min-height: 0 !important;
    padding: 0 !important;

    .mc-profile, .mc-title {
        display: none;
    }

    .mc-label-date-top {
        top: -6px !important;
    }

    & + .mc-conversation {
        height: 100%;
    }
}

/*

# CONVERSATIONS AREA
==========================================================

*/

.mc-tickets {

    .mc-conversation {
        height: calc(100% - 60px);
        position: relative;
        display: flex;
        flex-direction: column;

        .mc-list {
            height: 100%;
            padding: 0 0 5px 0;
            overflow-y: scroll;

            > div:last-child {
                animation: none;
            }
        }
    }

    .mc-editor {
        flex-shrink: 0;
        margin: 1px 20px 20px 20px;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
        transition: box-shadow linear 40ms, border-color linear 0.2s;
        background-color: $white;

        .mc-textarea {
            border: none;
            padding: 0 !important;
        }

        .mc-bar {
            margin-top: 10px;
            position: relative;

            .mc-btn-attachment {
                display: block !important;
            }

            .mc-btn {
                background-color: $color-blue;
                color: $white;
                border-color: $color-blue;
                font-size: 12px;
                line-height: 27px;
                height: 25px;
                transition: $transition;

                &:hover {
                    background-color: $color-dark-blue;
                    border-color: $color-dark-blue;
                }
            }
        }

        .mc-attachments:not(:empty) {
            padding-top: 15px;
        }

        .mc-title {
            position: absolute;
            font-weight: 500;
            font-size: 15px;
            letter-spacing: 0.3px;
            cursor: default;
            top: 0;
            left: -150px;
            color: $color-gray;
        }

        .mc-btn-saved-replies {
            display: none;
        }

        &.mc-error {
            border: 1px solid $color-red;
            box-shadow: 0 0 5px rgba(202, 52, 52, 0.25);
        }

        &.mc-audio-message-active {
            .mc-textarea, .mc-bar-icons {
                display: none;
            }

            .mc-bar {
                margin-top: 0;
                justify-content: right;
            }
        }
    }

    .mc-editor-cnt {
        align-items: flex-start;
    }

    &.mc-no-conversations > div > .mc-collapsed {
        transition: none !important;
    }
}

[data-panel="new-ticket"] {
    .mc-panel {

        input, select {
            padding: 15px;
        }

        > div {
            max-width: 800px;
        }
    }

    .mc-input-select {
        justify-content: flex-start;

        > span {
            flex-grow: 0;
        }

        p {
            font-weight: 400;
            margin: 0;
        }
    }

    .mc-input > div {
        padding-right: 0;
    }

    .mc-editor {
        margin: 0;
        box-shadow: none;
        background-color: rgb(248, 248, 249);
        max-width: calc(100% - 150px);
        width: 100%;
        box-sizing: border-box;
        transition: border-color .4s, box-shadow .4s, background-color .4s;

        .mc-submit {
            display: none !important;
        }

        .mc-loader {
            bottom: -60px;
            right: auto;
            left: -60px;
        }

        &.mc-focus {
            border: 1px solid $color-blue;
            box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);
            background: $white;
        }
    }

    .mc-create-ticket {
        margin: 15px auto 0 150px;
        width: auto;
        display: inline-block;
    }

    .mc-label-date-top, .mc-btn-audio-clip {
        display: none;
    }
}

@media (min-width: 768px) {
    .mc-popup.mc-emoji {
        left: 67px !important;
        top: auto !important;
        bottom: 55px;
        position: absolute !important;
    }
}

/*

# COMPONENTS
==========================================================

*/

.mc-profile {
    position: relative;
    color: $color-black;
    padding-left: 45px;
    text-decoration: none;
    display: flex;
    align-items: center;

    img {
        position: absolute;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        left: 0;
    }

    > span {
        font-size: 16px;
        line-height: 19px;
        font-weight: 500;
        white-space: nowrap;
        letter-spacing: 0.3px;
    }
}

.mc-profile-menu {
    cursor: pointer;
    display: inline-block;

    .mc-menu {
        display: none;
    }

    &:hover {
        .mc-name {
            color: $color-blue;
        }

        .mc-menu {
            display: block;
            animation: mc-fade-bottom-animation .5s;
        }
    }
}

.mc-btn-collapse {
    position: absolute;
    top: 15px;
    transition: color 0.4s;
    cursor: pointer;
    width: 30px;
    height: 30px;
    line-height: 35px;
    font-size: 15px;
    text-align: center;
    z-index: 5;

    &.mc-right {
        left: auto;
        right: 20px;
        text-align: right;

        &.mc-active {
            animation: mc-collapse-btn-right 1s;
            transform: rotate(180deg);
            right: 0;
            top: 0;
            border-right: 1px solid $border-color;
            border-top: 1px solid $border-color;
            border-radius: 4px;
            border-bottom-right-radius: 0;
            border-top-left-radius: 0;
            padding-right: 12px;
        }
    }

    &.mc-left {
        right: auto;
        left: 20px;
        text-align: left;

        &.mc-active {
            animation: mc-collapse-btn-left 1s;
            transform: rotate(180deg);
            left: 0;
            top: 0;
            border-left: 1px solid $border-color;
            border-top: 1px solid $border-color;
            border-radius: 4px;
            border-bottom-left-radius: 0;
            border-top-right-radius: 0;
            padding-left: 12px;
        }
    }

    &.mc-active {
        background: $white;
        border-radius: 15px;
    }

    &:hover {
        color: $color-blue;
    }

    &:before {
        line-height: 30px;
    }
}

.mc-btn-icon i:before {
    line-height: 35px;
}

.mc-overlay-panel.mc-active {
    z-index: 96;
}

.mc-tickets #mc-audio-clip {
    background: $white;

    .mc-icon:before {
        line-height: 50px;
    }
}

/*

# RTL
==========================================================

*/

.mc-tickets.mc-rtl {
    .mc-panel-right {
        border-right: 1px solid rgb(212, 212, 212);
        border-left: none;

        > .mc-top {
            padding-right: 20px;
            padding-left: 60px;

            .mc-no-profile-image {
                padding-right: 0;
            }
        }

        .mc-profile-agent .mc-status {
            padding-right: 15px;
            padding-left: 0;
            text-align: right;

            &:before {
                left: auto;
                right: 0;
            }
        }
    }

    .mc-panel-left {
        border-right: none;
        border-left: 1px solid rgb(212, 212, 212);

        > .mc-top {
            padding-left: 20px;
            padding-right: 60px;
        }
    }

    .mc-editor .mc-bar {
        padding: 0;
    }

    .mc-btn-collapse.mc-left {
        right: 20px;
        left: auto;
    }

    .mc-btn-collapse.mc-right {
        left: 20px;
        right: auto;
    }

    .mc-btn-collapse.mc-left.mc-active {
        transform: none;
        text-align: right;
        animation: mc-collapse-btn-right 1s;
    }

    .mc-btn-collapse.mc-right.mc-active {
        transform: none;
        text-align: left;
        animation: mc-collapse-btn-left 1s;
    }

    .mc-btn-collapse:not(.mc-active) {
        transform: rotate(180deg);
    }

    &:not(.mc-panel-active) .mc-collapsed + .mc-panel-main > .mc-top {
        padding-left: 20px;
        padding-right: 65px;
    }

    .mc-panel-main .mc-top .mc-close {
        right: auto;
        left: 14px;
    }

    .mc-panel .mc-info {
        margin: 0 150px 20px 20px;
    }

    &.mc-800 .mc-panel-left {
        left: auto;
        right: 0;
    }

    &.mc-800 .mc-panel-right {
        left: 0;
        right: auto;
    }

    .mc-profile {
        padding-left: 0;
        padding-right: 45px;

        img {
            right: 0;
            left: auto;
        }
    }

    &[data-panel="new-ticket"] .mc-editor {
        padding-right: 20px;
        padding-left: 0;
    }
}

/*

# RESPONSIVE
==========================================================

*/

.mc-1300 {
    .mc-panel-left, .mc-panel-right {
        min-width: 310px;
        width: 310px;
    }

    &.mc-collapsing .mc-panel-right, &.mc-collapsing .mc-panel-left {
        > div, > ul {
            min-width: 270px;
        }
    }

    .mc-panel-left .mc-search-btn > input {
        min-width: 205px;
    }
}

.mc-1000 {
    .mc-panel-left, .mc-panel-right {
        min-width: 230px;
        width: 230px;
    }

    &.mc-collapsing .mc-panel-right, &.mc-collapsing .mc-panel-left {
        > div, > ul {
            min-width: 190px;
        }
    }

    .mc-panel-left .mc-search-btn > input {
        min-width: 60px;
        width: 125px;
    }
}

.mc-800 {
    .mc-panel-left, .mc-panel-right {
        min-width: 260px;
        width: 260px;
        position: absolute;
        left: 0;
        z-index: 9;
        height: 100%;
    }

    .mc-panel-right {
        left: auto;
        right: 0;
    }

    &.mc-collapsing .mc-panel-right, &.mc-collapsing .mc-panel-left {
        > div, > ul {
            min-width: 220px;
        }
    }

    .mc-panel-left .mc-search-btn > input {
        min-width: 60px;
        width: 125px;
    }

    .mc-btn-collapse {
        z-index: 95;
    }

    .mc-btn-collapse:not(.mc-active) + .mc-right, .mc-panel-right:not(.mc-collapsed) + .mc-left {
        display: none;
    }

    &.mc-panel-form .mc-input, &[data-panel="new-ticket"] .mc-input, &.mc-panel-form .mc-select, &[data-panel="new-ticket"] .mc-select {
        display: block;
    }

    &[data-panel="new-ticket"] {
        .mc-editor {
            max-width: 100%;
        }

        .mc-create-ticket {
            margin-left: 0;
        }
    }

    .mc-panel .mc-info {
        margin-left: 0;
    }

    .mc-article > .mc-rating {
        margin-left: 0 !important;
        padding-left: 0 !important;
        border-left: 0 !important;
        display: flex;
    }

    &.mc-panel-form .mc-input-select-input span + div {
        margin-left: 0;
        top: 40px;
    }

    .mc-tickets-area .mc-form .mc-input > span {
        padding-right: 0;
    }
}

@media (max-width: 464px) {

    .mc-btn-emoji {
        display: none;
    }
}

@import "rtl.scss";
