<?php

/*
 * ==========================================================
 * POST.PHP
 * ==========================================================
 *
 * Slack response listener. This file receive the Slack messages of the agents forwarded by app.masichat.com. This file requires the Slack App.
 * � 2017-2025 app.masichat.com. All rights reserved.
 *
 */

$raw = file_get_contents('php://input');
flush();
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
}
if ($raw) {
    $response = json_decode($raw, true);
    if (isset($response['event'])) {
        require('../../include/functions.php');
        $response = $response['event'];
        $subtype = isset($response['subtype']) ? $response['subtype'] : '';
        $GLOBALS['MC_FORCE_ADMIN'] = true;
        if (mc_is_cloud()) {
            mc_cloud_load_by_url();
            mc_cloud_membership_validation(true);
        }
        if (isset($response['type']) && $response['type'] == 'message' && $subtype != 'channel_join' && (!$subtype || $subtype == 'file_share') && ($response['text'] != '' || (is_array($response['files']) && count($response['files']) > 0))) {

            // Get the user id of the slack message
            $user_id = mc_slack_api_user_id($response['channel']);

            // Elaborate the Slack message
            if ($user_id) {
                $last_message = mc_slack_last_user_message($user_id);
                $message = $response['text'];
                $conversation_id = $last_message['conversation_id'];

                // Check for duplicated message
                if (strpos(mc_isset(mc_db_get('SELECT payload FROM mc_messages WHERE conversation_id = ' . $conversation_id . ' ORDER BY creation_time DESC LIMIT 1'), 'payload'), mc_isset($response, 'event_ts'))) {
                    $GLOBALS['MC_FORCE_ADMIN'] = false;
                    return;
                }

                // Emoji
                $emoji = explode(':', $message);
                if (count($emoji)) {
                    $emoji_slack = json_decode(file_get_contents(MC_PATH . '/apps/slack/emoji.json'), true);
                    for ($i = 0; $i < count($emoji); $i++) {
                        if ($emoji[$i]) {
                            $emoji_code = ':' . $emoji[$i] . ':';
                            if (isset($emoji_slack[$emoji_code])) {
                                $message = str_replace($emoji_code, $emoji_slack[$emoji_code], $message);
                            }
                        }
                    }
                }

                // Message
                $message = mc_slack_response_message_text($message);

                // Attachments
                $attachments = $subtype == 'file_share' ? mc_slack_response_message_attachments($response['files']) : [];

                // Set the user login
                global $MC_LOGIN;
                $MC_LOGIN = ['id' => $user_id,  'user_type' => 'user'];

                // Get the agent id
                $agent_id = mc_db_escape(mc_slack_api_agent_id($response['user']));

                // Send the message
                $response_message = mc_send_message($agent_id, $conversation_id, $message, $attachments, 1, $response);

                // Messaging apps
                $conversation_details = defined('MC_MESSENGER') || defined('MC_WHATSAPP') || defined('MC_TWITTER') || defined('MC_VIBER')|| defined('MC_ZALO') || defined('MC_LINE') || defined('MC_TELEGRAM') || defined('MC_WECHAT') ? mc_db_get('SELECT source, extra FROM mc_conversations WHERE id = ' . $conversation_id) : false;
                if ($conversation_details) {
                    mc_messaging_platforms_send_message($message, $conversation_id, $response_message['id'], $attachments);
                }

                // Pusher online status
                if (mc_pusher_active()) {
                    mc_pusher_trigger('private-user-' . $user_id, 'add-user-presence', [ 'agent_id' => $agent_id]);
                }

                // Slack notification message
                if (!empty($response_message['notifications'])) {
                    mc_send_slack_message(mc_get_bot_id(), mc_get_setting('bot-name'), mc_get_setting('bot-image'), '_' . mc_('The user has been notified by email' . (in_array('sms', $response_message['notifications']) ? ' and text message' : '') . '.') . '_', [], $conversation_id, $response['channel']);
                }
            }
        }
        switch ($subtype) {
            case 'message_deleted':
                $user_id = mc_db_escape(mc_slack_api_user_id($response['channel']));
                $agent_id = mc_slack_api_agent_id($response['previous_message']['user']);
                $last_message = mc_slack_last_user_message($user_id);
                $online = mc_update_users_last_activity(-1, $user_id) === 'online';
                $previous_message = mc_db_escape($response['previous_message']['text']);
                mc_db_query(($online ? 'UPDATE mc_messages SET message = "", attachments = "", payload = "{ \"event\": \"delete-message\" }", creation_time = "' . mc_gmt_now() . '"' : 'DELETE FROM mc_messages') . ' WHERE (user_id = ' . $agent_id . ' OR user_id = ' . $user_id . ') AND conversation_id = "' . $last_message['conversation_id'] . '" AND ' . ($previous_message ?  'message = "' . $previous_message . '"' : 'attachments LIKE "%' . mc_db_escape($response['previous_message']['attachments'][0]['title']) . '%"') .' LIMIT 1');
                break;
            case 'message_changed':
                $agent_id = mc_db_escape(mc_slack_api_agent_id($response['previous_message']['user']));
                mc_db_query('UPDATE mc_messages SET message = "' . mc_db_escape(mc_slack_response_message_text($response['message']['text'])) . '", creation_time = "' . mc_gmt_now() . '" WHERE user_id = ' . $agent_id . ' AND payload LIKE "%' .  mc_db_escape($response['message']['ts']) . '%" LIMIT 1');
                break;
            case 'channel_archive':
                $conversation_id = mc_slack_last_user_message(mc_slack_api_user_id($response['channel']))['conversation_id'];
                mc_update_conversation_status($conversation_id, 3);
                break;
        }
        $GLOBALS['MC_FORCE_ADMIN'] = false;
    }
}
die();

?>