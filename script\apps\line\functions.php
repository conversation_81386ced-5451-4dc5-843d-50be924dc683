<?php

/*
 * ==========================================================
 * LINE APP
 * ==========================================================
 *
 * Line app. © 2017-2025 app.masichat.com. All rights reserved.
 *
 * 1. Send a message to Line
 * 2. Convert Masi Chat rich messages to Line rich messages
 * 3. Line curl
 *
 */

define('MC_LINE', '1.0.4');

function mc_line_send_message($line_id, $message = '', $attachments = [], $conversation_id = false) {
    if (empty($message) && empty($attachments)) {
        return false;
    }
    $user_id = defined('MC_DIALOGFLOW') ? mc_get_user_by('line-id', $line_id)['id'] : false;
    $response = false;
    $token = mc_isset(mc_db_get('SELECT extra_2 FROM mc_conversations WHERE id = ' . mc_db_escape($conversation_id)), 'extra_2'); // Deprecated
    if (empty($token) || strpos($token, 'thread_') !== false) { // Deprecated. Remove if.
        $token = mc_isset(mc_db_get('SELECT extra_3 FROM mc_conversations WHERE id = ' . mc_db_escape($conversation_id)), 'extra_3');
    } // Deprecated. Remove if.

    // Send the message
    $query = ['to' => $line_id];
    $message = mc_line_rich_messages($message, ['user_id' => $user_id]);
    $attachments = array_merge($attachments, $message[1]);
    if ($message[0] || $message[2]) {
        if ($message[0]) {
            $query['messages'] = [['type' => 'text', 'text' => $message[0]]];
        }
        if ($message[2]) {
            $query['messages'] = $message[2];
        }
        $response = mc_line_curl('message/push', $token, $query);
    }

    // Attachments
    $count = count($attachments);
    if ($count) {
        $messages = [];
        for ($i = 0; $i < count($attachments); $i++) {
            $url = $attachments[$i][1];
            $extension = substr($url, strripos($url, '.') + 1);
            switch ($extension) {
                case 'jpg':
                case 'jpeg':
                case 'png':
                    array_push($messages, ['type' => 'image', 'originalContentUrl' => $url, 'previewImageUrl' => $url]);
                    break;
                case 'mp4':
                    array_push($messages, ['type' => 'video', 'originalContentUrl' => $url, 'previewImageUrl' => MC_URL . '/media/thumb.png']);
                    break;
                default:
                    array_push($messages, ['type' => 'text', 'text' => $url]);
            }
        }
        $query['messages'] = $messages;
        $response = mc_line_curl('message/push', $token, $query);
    }
    return ['error' => empty($response) || isset($response['sentMessages']) ? false : $response];
}

function mc_line_rich_messages($message, $extra = false) {
    $messages = [];
    $message = mc_clear_text_formatting($message);
    $shortcodes = mc_get_shortcode($message);
    $attachments = [];
    for ($j = 0; $j < count($shortcodes); $j++) {
        $shortcode = $shortcodes[$j];
        $shortcode_id = mc_isset($shortcode, 'id', '');
        $shortcode_name = $shortcode['shortcode_name'];
        $message = trim(str_replace($shortcode['shortcode'], '', $message) . mc_isset($shortcode, 'title', '') . PHP_EOL . mc_(mc_isset($shortcode, 'message', '')));
        switch ($shortcode_name) {
            case 'slider-images':
            case 'slider':
            case 'card':
                $cards = [];
                if ($shortcode_name == 'slider-images') {
                    $images = explode(',', $shortcode['images']);
                    for ($i = 0; $i < count($images); $i++) {
                        array_push($cards, ['imageUrl' => $images[$i], 'action' => ['type' => 'postback', 'label' => ' ', 'data' => '#']]);
                    }
                    array_push($messages, ['type' => 'template', 'altText' => 'Error', 'template' => ['type' => 'image_carousel', 'columns' => $cards]]);
                } else {
                    $index = $shortcode_name == 'slider' ? 1 : 0;
                    while (isset($shortcode['image' . ($index ? '-' . $index : '')])) {
                        $suffix = $index ? '-' . $index : '';
                        $text = $shortcode['description' . $suffix] . PHP_EOL . mc_isset($shortcode, 'extra' . $suffix, '');
                        if (strlen($text) > 60) {
                            $text = substr($text, 0, 57) . '...';
                        }
                        array_push($cards, ['thumbnailImageUrl' => mc_isset($shortcode, 'image' . $suffix), 'title' => $shortcode['header' . $suffix], 'text' => $text, 'actions' => [['type' => 'uri', 'label' => mc_isset($shortcode, 'link-text' . $suffix), 'uri' => mc_isset($shortcode, 'link' . $suffix)]]]);
                        $index++;
                    }
                    array_push($messages, ['type' => 'template', 'altText' => 'Error', 'template' => ['type' => 'carousel', 'columns' => $cards]]);
                }
                break;
            case 'list-image':
            case 'list':
                $index = 0;
                if ($shortcode_name == 'list-image') {
                    $shortcode['values'] = str_replace('://', '', $shortcode['values']);
                    $index = 1;
                }
                $values = explode(',', $shortcode['values']);
                if (strpos($values[0], ':')) {
                    for ($i = 0; $i < count($values); $i++) {
                        $value = explode(':', $values[$i]);
                        $message .= PHP_EOL . '• ' . trim($value[$index]) . ' ' . trim($value[$index + 1]);
                    }
                } else {
                    for ($i = 0; $i < count($values); $i++) {
                        $message .= PHP_EOL . '• ' . trim($values[$i]);
                    }
                }
                $message = trim($message);
                break;
            case 'select':
            case 'buttons':
            case 'chips':
                $values = explode(',', $shortcode['options']);
                $buttons = [];
                $count = count($values);
                if ($count > 13)
                    $count = 13;
                for ($i = 0; $i < $count; $i++) {
                    array_push($buttons, ['type' => 'action', 'action' => ['type' => 'message', 'label' => substr($values[$i], 0, 20), 'text' => $values[$i]]]);
                }
                array_push($messages, ['type' => 'text', 'text' => $message, 'quickReply' => ['items' => $buttons]]);
                $message = '';
                if ($shortcode_id == 'mc-human-takeover' && defined('MC_DIALOGFLOW')) {
                    mc_dialogflow_set_active_context('human-takeover', [], 2, false, mc_isset($extra, 'user_id'));
                }
                break;
            case 'share':
            case 'button':
                $message .= ($message ? PHP_EOL : '') . $shortcode['link'];
                break;
            case 'video':
                $message .= ($message ? PHP_EOL : '') . ($shortcode['type'] == 'youtube' ? 'https://www.youtube.com/embed/' : 'https://player.vimeo.com/video/') . $shortcode['id'];
                break;
            case 'image':
                $attachments = [[$shortcode['url'], $shortcode['url']]];
                break;
            case 'rating':
                $labels = [mc_isset($shortcode, 'label-positive'), mc_isset($shortcode, 'label-negative')];
                if ($labels[0] && $labels[1]) {
                    $buttons = [];
                    for ($i = 0; $i < 2; $i++) {
                        array_push($buttons, ['type' => 'action', 'action' => ['type' => 'message', 'label' => substr($labels[$i], 0, 20), 'text' => $labels[$i]]]);
                    }
                    array_push($messages, ['type' => 'text', 'text' => $message, 'quickReply' => ['items' => $buttons]]);
                    $message = '';
                }
                if (defined('MC_DIALOGFLOW')) {
                    mc_dialogflow_set_active_context('rating', [], 2, false, mc_isset($extra, 'user_id'));
                }
                break;
            case 'articles':
                if (isset($shortcode['link'])) {
                    $message = $shortcode['link'];
                }
                break;
        }
    }
    return [$message, $attachments, $messages];
}

function mc_line_curl($url_part, $token, $query = '', $method = 'POST') {
    $response = mc_curl('https://api.line.me/v2/bot/' . $url_part, $query ? json_encode($query) : '', ['Content-Type: application/json', 'Authorization: Bearer ' . $token], $method);
    return $method == 'GET' ? json_decode($response, true) : $response;
}

?>